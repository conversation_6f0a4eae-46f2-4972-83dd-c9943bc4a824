variables:
  NOONA_IMAGE_REGISTRY: "${CI_REGISTRY}/varian-noona/development/core/docker-image"
  PROJECTS_PATH: "projects/**/*"
  RELEASE_BRANCH_PATTERN: '/^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/'

include:
  - component: gitlab.com/varian-noona/development/core/ci-cd-components/blackduck@3.1.1
    inputs:
      job_name: 'blackduck'
      project_name: 'Noona-Shared'

  - component: gitlab.com/varian-noona/development/core/npm-component/full-pipeline@2.0.0
    inputs:
      build_command: npm run build:shared-core
      build_artifacts_directory:
        - $CI_PROJECT_DIR/dist/
      sonar_project_key: VAR-noona-shared-core
      sonar_project_name: Noona-Shared-Core
      sonar_changes:
        - $PROJECTS_PATH
      sonar_exclusions: ',node_modules/**,coverage/**,src/**,projects/node_modules/**, src/lib/ds/components/range-slider/**/*.spec.ts, src/lib/form-engine/symptom/multiple-symptom-selection/*.html'
      jobs_prefix: ''

default:
  interruptible: true
  image: $NOONA_IMAGE_REGISTRY/node-sonar:20.11.1-slim
  tags:
    - gitlab-aws-autoscaler
  cache: &global_cache
    key:
      files:
        - package.json
    paths:
      - node_modules/
    policy: pull-push

stages:
  - install
  - test
  - build
  - report
  - publish

.rules-default:
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^NOONA-\d+(.+)?$/ && ($CI_PIPELINE_SOURCE == "merge_request_event" || $CI_PIPELINE_SOURCE == "push")'

.rules-on-merge:
  rules:
    - if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"'
    - if: '$CI_COMMIT_REF_NAME =~ $RELEASE_BRANCH_PATTERN && $CI_PIPELINE_SOURCE == "push"'

test:
  cache:
    <<: *global_cache
    policy: pull

sonar:
  cache:
    <<: *global_cache
    policy: pull
  rules:
    - if: '$CI_MERGE_REQUEST_TITLE =~ /SKIP_SONARQUBE$/'
      when: manual
      allow_failure: true
      changes:
        paths:
          - $PROJECTS_PATH
    - if: '$CI_MERGE_REQUEST_ID'
      when: always
      changes:
        paths:
          - $PROJECTS_PATH
    - if: '$CI_DEFAULT_BRANCH == $CI_COMMIT_BRANCH || $CI_COMMIT_BRANCH =~ $RELEASE_BRANCH_PATTERN'
      when: always
      changes:
        paths:
          - $PROJECTS_PATH

.publish:
  script:
    - cd $CI_PROJECT_DIR/dist/shared-core
    - echo "@scope:registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/" > .npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}" >> .npmrc

publish dry-run:
  stage: publish
  needs: [build]
  script:
    - !reference [.publish, script]
    - NPM_TOKEN=$CI_JOB_TOKEN npm publish --ignore-scripts --dry-run
  rules: !reference [.rules-default, rules]
  cache:
    <<: *global_cache
    policy: pull

publish:
  stage: publish
  needs: [build]
  script:
    - !reference [.publish, script]
    - NPM_TOKEN=$CI_JOB_TOKEN npm publish --ignore-scripts
  rules: !reference [.rules-on-merge, rules]
  cache:
    <<: *global_cache
    policy: pull
