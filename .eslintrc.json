{
  "root": true,
  "ignorePatterns": ["src/lib/generated/*", "projects/**/*"],
  "overrides": [
    {
      "files": ["*.ts"],
      "parserOptions": {
        "project": ["tsconfig.json"],
        "createDefaultProgram": true
      },
      "extends": ["plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates"],
      "plugins": ["eslint-plugin-prettier", "eslint-plugin-import", "eslint-plugin-react", "@typescript-eslint"],
      "rules": {
        "@typescript-eslint/member-ordering": "off",
        "@typescript-eslint/member-delimiter-style": [
          "off",
          {
            "multiline": {
              "delimiter": "none",
              "requireLast": true
            },
            "singleline": {
              "delimiter": "semi",
              "requireLast": false
            }
          }
        ],
        "@typescript-eslint/no-this-alias": "error",
        "@typescript-eslint/quotes": [
          "off",
          "single",
          {
            "allowTemplateLiterals": true
          }
        ],
        "@typescript-eslint/semi": ["off", null],
        "@typescript-eslint/type-annotation-spacing": "off",
        "arrow-parens": ["off", "always"],
        "arrow-body-style": "off",
        "@angular-eslint/template/eqeqeq": "off",
        "eqeqeq": ["off", "always"],
        "brace-style": ["off", "off"],
        "@typescript-eslint/no-shadow": [
          "off",
          {
            "hoist": "all"
          }
        ],
        "prefer-arrow/prefer-arrow-functions": "off",
        "eol-last": "off",
        "import/no-extraneous-dependencies": "off",
        "import/no-internal-modules": "off",
        "no-underscore-dangle": "off",
        "linebreak-style": "off",
        "max-len": "off",
        "new-parens": "off",
        "newline-per-chained-call": "off",
        "no-duplicate-case": "error",
        "no-duplicate-imports": "error",
        "no-extra-bind": "error",
        "no-extra-semi": "off",
        "no-irregular-whitespace": "off",
        "no-new-func": "error",
        "no-redeclare": "error",
        "no-return-await": "error",
        "no-sequences": "error",
        "no-sparse-arrays": "error",
        "no-template-curly-in-string": "error",
        "no-trailing-spaces": "off",
        "prefer-object-spread": "error",
        "quote-props": "off",
        "react/jsx-curly-spacing": "off",
        "react/jsx-equals-spacing": "off",
        "react/jsx-tag-spacing": [
          "off",
          {
            "afterOpening": "allow",
            "closingSlash": "allow"
          }
        ],
        "react/jsx-wrap-multilines": "off",
        "space-before-function-paren": "off",
        "space-in-parens": ["off", "never"],
        "prettier/prettier": "error",
        "jsdoc/newline-after-description": "off"
      }
    },
    {
      "files": ["*.html"],
      "extends": ["plugin:@angular-eslint/template/accessibility", "plugin:@angular-eslint/template/recommended"],
      "rules": {
        "@angular-eslint/template/eqeqeq": "off",
        "@angular-eslint/component-selector": "off",
        "@angular-eslint/template/no-negated-async": "off",
        "@angular-eslint/component-class-suffix": "off",
        "@angular-eslint/directive-class-suffix": "off",
        "@angular-eslint/directive-selector": "off",
        "@angular-eslint/no-output-on-prefix": "off",
        // TODO: NOONA-24672 - fix accessibility issues and remove rules below when fixed. "@angular-eslint/template/accessibility" plugin will show these as errors in the future.
        "@angular-eslint/template/click-events-have-key-events": "warn",
        "@angular-eslint/template/elements-content": "warn",
        "@angular-eslint/template/interactive-supports-focus": "warn",
        "@angular-eslint/template/label-has-associated-control": "warn",
        "@angular-eslint/template/mouse-events-have-key-events": "warn",
        "@angular-eslint/template/no-autofocus": "warn",
        "@angular-eslint/template/no-distracting-elements": "warn",
        "@angular-eslint/template/role-has-required-aria": "warn",
        "@angular-eslint/template/table-scope": "warn",
        "@angular-eslint/template/valid-aria": "warn",
        "@typescript-eslint/naming-convention": [
          "error",
          {
            "selector": "property",
            "format": ["camelCase", "UPPER_CASE", "snake_case"],
            "filter": {
              "regex": "^_",
              "match": false
            }
          },
          {
            "selector": "property",
            "format": null,
            "modifiers": ["private"],
            "filter": {
              "regex": "^_",
              "match": true
            }
          }
        ]
      }
    },
    {
      "files": ["*.spec.ts"],
      "env": {
        "jest/globals": true
      },
      "plugins": ["jest"],
      "rules": {
        "jest/no-disabled-tests": "warn",
        "jest/no-focused-tests": "error",
        "jest/no-identical-title": "error",
        "jest/prefer-to-have-length": "warn",
        "jest/valid-expect": "error",
        "jest/padding-around-expect-groups": "error"
      }
    }
  ],
  "env": {
    "es6": true
  }
}
