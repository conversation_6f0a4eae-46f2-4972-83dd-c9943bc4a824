const snapshotSerializers = require('jest-preset-angular/build/serializers');

module.exports = {
  setupFilesAfterEnv: ['<rootDir>/setup-jest.ts'],
  moduleFileExtensions: ['ts', 'html', 'js', 'json', 'mjs'],
  resolver: 'jest-preset-angular/build/resolvers/ng-jest-resolver.js',
  snapshotSerializers,
  testEnvironment: 'jsdom',
  transformIgnorePatterns: ['node_modules/(?!.*\\.mjs$|url-join|uuid|@angular-slider/ngx-slider)'],
  transform: {
    '^.+\\.(ts|js|mjs|html|svg)$': [
      'jest-preset-angular',
      {
        tsconfig: '<rootDir>/src/tsconfig.spec.json',
        stringifyContentPathRegex: '\\.(html|svg)$'
      }
    ]
  },
  moduleNameMapper: {
    '@shared-core/testing': '<rootDir>/projects/shared-core/src/test-utils/public-api.ts',
    '^@bwip-js/browser$': '<rootDir>/node_modules/@bwip-js/browser'
  },
  coverageDirectory: 'coverage',
  testMatch: ['**/?(*.)+(spec).[tj]s?(x)']
};
