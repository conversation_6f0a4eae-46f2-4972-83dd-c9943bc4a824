{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"form-engine-demo": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "nh", "schematics": {"@ngrx/angular:component": {"style": "scss", "lintFix": true}, "@ngrx/angular:container": {"style": "scss", "lintFix": true}, "@ngrx/angular:pipe": {"lintFix": true}, "@ngrx/angular:class": {"lintFix": true}, "@ngrx/angular:module": {"lintFix": true}, "@ngrx/angular:service": {"lintFix": true}, "@ngrx/angular:directive": {"lintFix": true, "prefix": "app"}, "@ngrx/angular:store": {"lintFix": true, "flat": false}, "@ngrx/angular:entity": {"lintFix": true, "flat": false}, "@ngrx/angular:action": {"lintFix": true}, "@ngrx/angular:reducer": {"lintFix": true}, "@ngrx/angular:effect": {"lintFix": true}, "@ngrx/angular:feature": {"lintFix": true, "flat": false, "group": true}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/form-engine-demo"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "src/tsconfig.app.json", "preserveSymlinks": true, "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "./node_modules/@varian-noona/noona-design-system-lib/assets/", "output": "./assets/"}], "styles": ["src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["node_modules/@varian-noona/noona-design-system-lib/styles", "."]}, "scripts": [], "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "src/main.ts"}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}]}, "development": {}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {}, "configurations": {"production": {"buildTarget": "form-engine-demo:build:production"}, "development": {"buildTarget": "form-engine-demo:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "form-engine-demo:build"}}}}, "shared-core": {"schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/shared-core", "sourceRoot": "projects/shared-core/src", "projectType": "library", "prefix": "ns", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"tsConfig": "projects/shared-core/tsconfig.lib.json", "project": "projects/shared-core/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/shared-core/tsconfig.lib.prod.json"}, "development": {}}, "defaultConfiguration": "production"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["projects/shared-core/**/*.ts", "projects/shared-core/**/*.html"]}}}}}, "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"], "cache": {"enabled": false}}}