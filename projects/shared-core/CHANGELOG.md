## 10.2.15 - 23-06-25
- Remove custom summary and thank you modal for EQ-5D-5L QoL [NOONA-26172](https://vocscs.atlassian.net/browse/NOONA-26172)

## 10.2.14 - 13-06-25
- Fix clicking checkbox issue on symptom management cases [NOONA-26123]|(https://vocscs.atlassian.net/browse/NOONA-26123)

## 10.2.13 - 11-06-25
- Improve accessibility for form elements [NOONA-25265]|(https://vocscs.atlassian.net/browse/NOONA-25265)

## 10.2.12 - 10-06-25
- Fix pain-pointer navigation order
- Update pain pointer focus-color

## 10.2.11 - 26-05-25
- Improve keyboard nagigation for tooltip [NOONA-25471](https://vocscs.atlassian.net/browse/NOONA-25471)

## 10.2.10 - 20-05-25
- Fix a11y issues in slider component [NOONA-25268](https://vocscs.atlassian.net/browse/NOONA-25268)

## 10.2.9 - 19-05-25
- Fix a11y issues in checkboxes and radio buttons [NOONA-25267](https://vocscs.atlassian.net/browse/NOONA-25267)

## 10.2.8 - 15-05-25
- Fixed slider missing elements layout and logic
[NOONA-25769](https://vocscs.atlassian.net/browse/NOONA-25769)

## 10.2.7 - 12-05-25
- Fixed slider default value, correct color, restore indicators labels, keyboard navigation handling
[NOONA-25741](https://vocscs.atlassian.net/browse/NOONA-25741)
[NOONA-25769](https://vocscs.atlassian.net/browse/NOONA-25769)

## 10.2.6 - 08-05-25
- Fix missing error message in photo-uploader [NOONA-25763](https://vocscs.atlassian.net/browse/NOONA-25763)

## 10.2.5 - 07-05-25
- Revert renaming of existing CURRENTLY_BLEEDING value of acute-symptoms model [NOONA-25092](https://vocscs.atlassian.net/browse/NOONA-25092)

## 10.2.4 - 07-05-25
- Update acute-symptoms model to match new changes [NOONA-25092](https://vocscs.atlassian.net/browse/NOONA-25092)

## 10.2.3 - 05-05-25
- Fixed missing "invert" feature and default value is in mid point of range slider [NOONA-25741](https://vocscs.atlassian.net/browse/NOONA-24618)

## 10.2.2 - 05-05-25
- Fix type error on ModalActionInit [NOONA-25316](https://vocscs.atlassian.net/browse/NOONA-25316)

## 10.2.1 - 02-05-25
- Relaxed peer dependency requirement for @angular-slider/ngx-slider from ^17.0.0 to "^17.0.0 || ^18.0.0" to support Angular 18+ applications [NOONA-24618](https://vocscs.atlassian.net/browse/NOONA-24618)

## 10.2.0 - 30-04-25
[NOONA-25316](https://vocscs.atlassian.net/browse/NOONA-25316)  
Fix semantic issues for:

- ds-components
  - actions-dropdown
  - buttons
  - checkbox
  - icon
  - modal
  - spinner
- about page
- utils
  - error-indicator
  - input-error-indicator
  - pain-pointer
  - photo-uploader

## 10.1.0 - 30-04-25
- New distress meter component [NOONA-24618](https://vocscs.atlassian.net/browse/NOONA-24618)

## 10.0.19 - 28.04-25
- Add the label 'Selected locations: ' for the reported locations in the pain-pinter-list [NOONA-25195](https://vocscs.atlassian.net/browse/NOONA-25195)

## 10.0.18 - 16-04-25
- Fix male-diagram for selected groin locations [NOONA-25596](https://vocscs.atlassian.net/browse/NOONA-25596)

## 10.0.17 - 20-03-25
- Add accessibility rules to ESLint [NOONA-25236](https://vocscs.atlassian.net/browse/NOONA-25236)
- Fix missing alt texts in images [NOONA-25236](https://vocscs.atlassian.net/browse/NOONA-25236)

## 10.0.16 - 19-03-25
- Update pipeline config to allow SonarQube skip [NOONA-25365](https://vocscs.atlassian.net/browse/NOONA-25365)

## 10.0.15 - 06-03-25
- Update bwip-js version [NOONA-25227](https://vocscs.atlassian.net/browse/NOONA-25227)

## 10.0.14 - 04-03-25
- Fix pain pointer styling issues [NOONA-25058](https://vocscs.atlassian.net/browse/NOONA-25058)

## 10.0.13 - 28-02-25
- Add eslint rules for Jest unit tests [NOONA-25125](https://vocscs.atlassian.net/browse/NOONA-25125)
- Fix publish dry run [NOONA-25202](https://vocscs.atlassian.net/browse/NOONA-25202)

## 10.0.12 - 17-02-25
- Remove Danish language from Noona [NOONA-24656](https://vocscs.atlassian.net/browse/NOONA-24656)

## 10.0.11 - 13-02-25
- Use latest npm CI/CD component [NOONA-23608](https://vocscs.atlassian.net/browse/NOONA-23608)

## 10.0.10 - 12-02-25
- Fix Gitlab pipelines [NOONA-23608](https://vocscs.atlassian.net/browse/NOONA-23608)

## 10.0.9 - 04-02-25
- Fix inquiry effects: Add return EMPTY to prevent TypeError in switchMap [NOONA-24116](https://vocscs.atlassian.net/browse/NOONA-24116)

## 10.0.8 - 31-01-25
- Add missing header to the pain pointer component [NOONA-15212](https://vocscs.atlassian.net/browse/NOONA-15212)

## 10.0.7 - 21-01-25
- Fix cancel button issue in symptoms [NOONA-24739](https://vocscs.atlassian.net/browse/NOONA-24739)
- Fix cancel and back to top buttons in qol and symptom questionnaires [NOONA-24609](https://vocscs.atlassian.net/browse/NOONA-24609)

## 10.0.6 - 19-01-25
-  Add Polish translations for Clinic UI [NOONA-24661](https://vocscs.atlassian.net/browse/NOONA-24661)

## 10.0.5 - 17-01-25
- Trigger change detection after value change to update error state immediately [NOONA-24187](https://vocscs.atlassian.net/browse/NOONA-24187)

## 10.0.4 - 13-01-25
- Reset pain pointer currentValue to empty array when component is not visible [NOONA-24224](https://vocscs.atlassian.net/browse/NOONA-24224)

## 10.0.3 - 08-01-25
- Fix photo uploaded but preview is still loading [NOONA-24008](https://vocscs.atlassian.net/browse/NOONA-24008)

## 10.0.2 - 18-12-24
- Revert to distress metter v9.0.22 [NOONA-23504](https://vocscs.atlassian.net/browse/NOONA-23504)

## 10.0.1 - 17-12-24
- Can not add photos after removing errored ones [NOONA-24251](https://vocscs.atlassian.net/browse/NOONA-24251)

## 10.0.0 - 09-12-24
- Update ng to v18 [NOONA-23787](https://vocscs.atlassian.net/browse/NOONA-23787)

## 9.0.36 - 11-12-24
- Cannot read properties of undefined in symptom submit [NOONA-24147](https://vocscs.atlassian.net/browse/NOONA-24147)

## 9.0.35 - 10-12-24
- Preview not in sync on symptom questionair [NOONA-24008](https://vocscs.atlassian.net/browse/NOONA-24008)

## 9.0.34 - 09-12-24
- Pain pointer error handling breaks [NOONA-24166](https://vocscs.atlassian.net/browse/NOONA-24166)

## 9.0.33 - 09-12-24
- Replaced field-status indicator with form-error indicator for text-input field [NOONA-24185](https://vocscs.atlassian.net/browse/NOONA-24185)

## 9.0.32 - 04-12-24
- Fixed issues with YYYY-MM-DD dateformat, improved input hint [NOONA-23750](https://vocscs.atlassian.net/browse/NOONA-23750)

## 9.0.31 - 04-12-24
- Form error handling breaks in earlier symptom component [NOONA-24024](https://vocscs.atlassian.net/browse/NOONA-24024)

## 9.0.30 - 03-12-24
- Fixed Unstable distress meter for symptom questionnaires on android (3rd attempt) [NOONA-23504] (https://vocscs.atlassian.net/browse/NOONA-23504)

## 9.0.29 - 29-11-24
- Symptom reporting: all radio buttons can be selected, ask about symptoms missing questions and empty diary page with infinite spinner [NOONA-24133](https://vocscs.atlassian.net/browse/NOONA-24133)

## 9.0.28 - 29-11-24
- Symptom forms TypeError [NOONA-24041](https://vocscs.atlassian.net/browse/NOONA-24041)

## 9.0.27 - 27-11-24
- Fixed Unstable distress meter for symptom questionnaires on android (2nd attempt) [NOONA-23504] (https://vocscs.atlassian.net/browse/NOONA-23504)

## 9.0.26 - 27-11-24
- Refactor [NOONA-23980](https://vocscs.atlassian.net/browse/NOONA-23980)

## 9.0.25 - 25-11-24
- Remove 'visibilities' from form in form submit [NOONA-23980](https://vocscs.atlassian.net/browse/NOONA-23980)

## 9.0.24 - 22-11-24
- Pain body diagram is visible right away while it's should be visible only when "Elsewhere in the body" is selected [NOONA-23980](https://vocscs.atlassian.net/browse/NOONA-23980)

## 9.0.23 - 19-11-24
- Fixed unstable distress metter on android [NOONA-23504](https://vocscs.atlassian.net/browse/NOONA-23504)

## 9.0.22 - 14-11-24
- Incorrect trigger of mandatory questions on symptom questionnaire [NOONA-23872](https://vocscs.atlassian.net/browse/NOONA-23872)

## 9.0.21 - 11-11-24
- Fixed duplicated upload on android (without refactoring) [NOONA-23513](https://vocscs.atlassian.net/browse/NOONA-23513)

## 9.0.20 - 11-11-24
- Update shared-core logic to handle summary content generation and error handling for date-input fields [NOONA-23750](https://vocscs.atlassian.net/browse/NOONA-23750)

## 9.0.19 - 07-11-24
- Fix max number of photos error still shown after photo removal preventing uploading another [NOONA-23955](https://vocscs.atlassian.net/browse/NOONA-23955)

## 9.0.18 - 04-11-24
- Get back to prio duplicated uploaded photo fix for photo-uploaded, symptom-summary-modal [NOONA-23513](https://vocscs.atlassian.net/browse/NOONA-23513)
(The update files are now identical to the version from September 10, 2024 (commit 2c93ed25b2bbd4765ae2e022095068a0ff3fb6db).)

## 9.0.17 - 31-10-24
- Reverted photo upload fix (9.0.15, 9.0.14) + call detectChanges after photoUploadService.updateVisibility [NOONA-23513](https://vocscs.atlassian.net/browse/NOONA-23513)

## 9.0.16 - 29-10-24
- Fixed month displayed twice when picking peak symptom dates for treatment visit and clinic appointment questionnaires [NOONA-23572](https://vocscs.atlassian.net/browse/NOONA-23572)

## 9.0.15 - 25-10-24
- Fixed thumbnail missing after successfully uploaded [NOONA-23513](https://vocscs.atlassian.net/browse/NOONA-23513)

## 9.0.14 - 24-10-24
- Fixed preview is shown on the section only when user triggers some other option [NOONA-23513](https://vocscs.atlassian.net/browse/NOONA-23513)

## 9.0.13 - 21-10-24
- default value for secondary button in section wizard to fix missing label in QOL introduced by the fix to symptom questionnaire labels [NOONA-23042](https://vocscs.atlassian.net/browse/NOONA-23042)

## 9.0.12 - 17-10-24
- Fixed secondary button label in earlier symptom and summary components for symptom questionnaires [NOONA-23042](https://vocscs.atlassian.net/browse/NOONA-23042)

## 9.0.11 - 15-10-24
- Fixed behaviour cancel buttons in symptom questionnaires [NOONA-23042](https://vocscs.atlassian.net/browse/NOONA-23042)

## 9.0.10 - 03-10-24
- Fixed previewer of uploaded photo to Diary Notes & Photo description field are missing [NOONA-23622](https://vocscs.atlassian.net/browse/NOONA-23622)

## 9.0.9 - 25-09-24

- Hotfixed photo description missing - Patient [NOONA-23622](https://vocscs.atlassian.net/browse/NOONA-23622)

## 9.0.8 - 24-09-24

- Enable SonarQube in Shared Core [NOONA-23608](https://vocscs.atlassian.net/browse/NOONA-23608)

## 9.0.7 - 23-09-24

- Exposed photo-data.service to external app - Clinic [NOONA-23513](https://vocscs.atlassian.net/browse/NOONA-23513)

## 9.0.7 - 19-09-24

- Fixed duplicated uploaded photos issues for android - Patient [NOONA-23513](https://vocscs.atlassian.net/browse/NOONA-23513)

## 9.0.5 - 16-09-24

- Copyright text update in "About box" - Clinic [NOONA-23430](https://vocscs.atlassian.net/browse/NOONA-23430)

## 9.0.4 - 10-09-24

- Fix photo uploader fail on Android app [NOONA-22918](https://vocscs.atlassian.net/browse/NOONA-22918)

## 9.0.3 - 28-08-24

- Remove unused config property [NOONA-23331](https://vocscs.atlassian.net/browse/NOONA-23331)

## 9.0.2 - 27-08-24

- Fix double slash in assets url. Add unit tests [NOONA-23331](https://vocscs.atlassian.net/browse/NOONA-23331)

## 9.0.1 - 26-08-24

- Add configurable assets path [NOONA-23331](https://vocscs.atlassian.net/browse/NOONA-23331)

## 9.0.0 - 19-08-24

- Upgrade Angular version to v17 [NOONA-23331](https://vocscs.atlassian.net/browse/NOONA-23331)

## 8.3.34 - 10-07-24

- Clinic side rating modal deletion [NOONA-22689](https://vocscs.atlassian.net/browse/NOONA-22689)

## 8.3.33 - 31-05-24

- Fix symptom summary array issues: Remove extra spacing before comma in "Other" and retain original casing to keep abbreviations intact [NOONA-21298](https://vocscs.atlassian.net/browse/NOONA-21298)

## 8.3.32 - 22-05-24

- Peak date is not required to be entered into datepicker if consecutive days are selected [NOONA-22655](https://vocscs.atlassian.net/browse/NOONA-22655)

## 8.3.31 - 21-05-24

- Cannot read properties of undefined (reading 'datepicker') [NOONA-22648](https://vocscs.atlassian.net/browse/NOONA-22648)

## 8.3.30 - 21-05-24

- Dates not separated correctly in symptom summary [NOONA-22636](https://vocscs.atlassian.net/browse/NOONA-22636)

## 8.3.29 - 16-05-24

- Issues with date picker element (symptoms/questionnaires) [NOONA-22525](https://vocscs.atlassian.net/browse/NOONA-22525)

## 8.3.28 - 07-05-24

- Add a symptom - Date selection: 'Today' or 'Symptom is chronic' is displayed as 'Symptom is persistent' in the summary [NOONA-22310](https://vocscs.atlassian.net/browse/NOONA-22310)

## 8.3.27 - 12-04-24

- Update Status Check questionnaire [NOONA-21805](https://vocscs.atlassian.net/browse/NOONA-21805)

## 8.3.26 - 13-03-24

- Fix red border showing in wrong scenarios [NOONA-21497](https://vocscs.atlassian.net/browse/NOONA-21497)

## 8.3.25 - 12-03-24

- Fix red border not showing when clicking chronic and today values. Also fixed validation issues when clicking today and chronic selections[NOONA-21497](https://vocscs.atlassian.net/browse/NOONA-21497)

## 8.3.24 - 07-03-24

- QOL Questionnaires: Inputted text in "Other" option is retained even if the checkbox is unchecked [NOONA-21656](https://vocscs.atlassian.net/browse/NOONA-21656)

## 8.3.23 - 04-03-24

- Add 'Interpersonal Safety' to 'CaseType' [NOONA-21818](https://vocscs.atlassian.net/browse/NOONA-21818)

## 8.3.22 - 18-12-23

- Fix red border not showing around missing required action in earlier symptom summary [NOONA-21192](https://vocscs.atlassian.net/browse/NOONA-21192)

## 8.3.21 - 28-12-23

- revert removal of unused function emitNextFormSection in field-service.ts as it's used in noona

## 8.3.20 - 28-12-23

- Fix red border not showing around missing required fields in status check questionnaire [NOONA-20560](https://vocscs.atlassian.net/browse/NOONA-20560)
- Fix typos and remove unused function in field-service.ts

## 8.3.19 - 27-12-23

- Fix text area maxlength [NOONA-21146](https://vocscs.atlassian.net/browse/NOONA-21146)

## 8.3.18 - 18-12-23

- Fix date range validation error showing when field is hidden [NOONA-21073](https://vocscs.atlassian.net/browse/NOONA-21073)

## 8.3.17 - 04-12-23

- Fix date range item red outline issue [NOONA-20559](https://vocscs.atlassian.net/browse/NOONA-20559)
- Add unit tests to gitlab ci build before script

## 8.3.16 - 21-11-23

- Fix toggle-item required field and red outline issue [NOONA-20549](https://vocscs.atlassian.net/browse/NOONA-20549)
- Disable angular cli cache

## 8.3.15 - 01-11-23

- Remove console log, fix lodash import [NOONA-20291](https://vocscs.atlassian.net/browse/NOONA-20291)
- Add no restricted imports and no console rules to eslint config
- Add lint to gitlab ci build before script

## 8.3.14 - 27-10-23

- Fix pain pointer component cd issue [NOONA-20291](https://vocscs.atlassian.net/browse/NOONA-20291)

## 8.3.13 - 16-10-23

- Fix required field red outline not displayed on the clinic side [NOONA-20291](https://vocscs.atlassian.net/browse/NOONA-20291)

## 8.3.12 - 13-10-23

- Fix both radio list buttons checked [NOONA-20563](https://vocscs.atlassian.net/browse/NOONA-20563)

## 8.3.11 - 10-10-23

- Fix pain pointer select location issue [NOONA-20474](https://vocscs.atlassian.net/browse/NOONA-20474)

## 8.3.10 - 05-10-23

- Fix wizard section scroll to random place [NOONA-20379](https://vocscs.atlassian.net/browse/NOONA-20379)

## 8.3.9 - 05-10-23

- Fix required field indicator on radiio-list when there are only yes and no options
- Improve required field indicator on checkbox-list with a text input field attached [NOONA-20417](https://vocscs.atlassian.net/browse/NOONA-20417)

## 8.3.8 - 02-10-23

- Fix red outline required question error indicator for questionnaires [NOONA-20417](https://vocscs.atlassian.net/browse/NOONA-20417)
- Improve required field indicator for text input field [NOONA-20417](https://vocscs.atlassian.net/browse/NOONA-20417)
- Fix the issue of pain-pointer diagram size change when selecting a location [NOONA-20474](https://vocscs.atlassian.net/browse/NOONA-20474)

## 8.3.7 - 28-09-23

- Fix photo uploader error with allowing upload more photos than limit [NOONA-19726](https://vocscs.atlassian.net/browse/NOONA-19726)

## 8.3.6 - 26-09-23

- Fix requried field error indicator on slider-item and pain-pointer [NOONA-20342](https://vocscs.atlassian.net/browse/NOONA-20342)

## 8.3.5 - 22-09-23

- Fix requried field error indicator on date-range-item [NOONA-20342](https://vocscs.atlassian.net/browse/NOONA-20342)

## 8.3.4 - 21-09-23

- Fix photo preview not working in the Clinic app [NOONA-20257](https://vocscs.atlassian.net/browse/NOONA-20257)

## 8.3.3 - 20-09-23

- Fix bug related to error indicator customKey [NOONA-20274](https://vocscs.atlassian.net/browse/NOONA-20274)

## 8.3.2 - 18-09-23

- Fix date-range-picker form validation error on peakDate field [NOONA-20190](https://vocscs.atlassian.net/browse/NOONA-20190)

## 8.3.1 - 11-09-23 

- Bug fixes for photo uploader [NOONA-20203](https://vocscs.atlassian.net/browse/NOONA-20203) and [NOONA-20178](https://vocscs.atlassian.net/browse/NOONA-20178)

## 8.3.0 - 07-09-23

- Remove Arabic and Hebrew from supported languages [NOONA-20160](https://vocscs.atlassian.net/browse/NOONA-20160)

## 8.2.2 - 06-09-23

- Remove the Asterisk from questionnaires and symptom forms [NOONA-20176](https://vocscs.atlassian.net/browse/NOONA-20176)

## 8.2.1 - 29-08-23 

- Fix bug with pain pointer chart [NOONA-20133](https://vocscs.atlassian.net/browse/NOONA-20133)

## 8.2.0 - 13-08-23

- Update form engine to indicate required field for questionnaires and symptom from [NOONA-19949](https://vocscs.atlassian.net/browse/NOONA-19949)

## 8.1.9 - 08-08-23

- Remove GoogleAnalytics from shared core and its usage from the clinic app [NOONA-19955](https://vocscs.atlassian.net/browse/NOONA-19955)

## 8.1.8 - 03-07-23

- RuleParserMain creates unintended changes to automaticadvicetype [NOONA-17457](https://vocscs.atlassian.net/browse/NOONA-17457)

## 8.1.7 - 16-05-23

- Fix bug related to extra expected modals for patient side [NOONA-19586](https://vocscs.atlassian.net/browse/NOONA-19586)

## 8.1.6 - 16-05-23 

- Added new param to remove decimals in wellbeing indicator [NOONA-19290](https://vocscs.atlassian.net/browse/NOONA-19290)

## 8.1.5 - 12-04-23

- Fixed hint and validation issues [NOONA-18685](https://vocscs.atlassian.net/browse/NOONA-18685), [NOONA-19228](https://vocscs.atlassian.net/browse/NOONA-19228)

## 8.1.4 - 03-03-23

- Added new value indicator for integer inputs [NOONA-18685](https://vocscs.atlassian.net/browse/NOONA-18685)

## 8.1.3 - 02-03-23

- Fixed symptom date range issue with missed February (once a year issue) [NOONA-18983](https://vocscs.atlassian.net/browse/NOONA-18983)

## 8.1.2 - 20-02-23

- Fixed photo getting attached to every symptom and causing JSON parse exceptions on the backend [NOONA-18882](https://vocscs.atlassian.net/browse/NOONA-18882)

## 8.1.1 - 16-02-23

- Fix photo still visible in the symptom summary and diary after photo upload was canceled [NOONA-18738](https://vocscs.atlassian.net/browse/NOONA-18738)

## 8.1.0 - 01-02-23

- Update ng-select and rxjs in per dependency. [NOONA-16501](https://vocscs.atlassian.net/browse/NOONA-16501)
- Remove jasmine and karma dependencies

## 8.0.2 - 26-01-23

- Fixed photo-uploader service. Used to send null patientId...[NOONA-18454](https://vocscs.atlassian.net/browse/NOONA-18454)
- Fixed new case symptom chekmark not showing... [NOONA-18013](https://vocscs.atlassian.net/browse/NOONA-18013)

## 8.0.1 - 19-12-22

- Small fixes related to changes from previous release [NOONA-18013](https://vocscs.atlassian.net/browse/NOONA-18013)


## 8.0.0 - 30-11-22

- Updated Angular version to 15 [NOONA-18013](https://vocscs.atlassian.net/browse/NOONA-18013)
- Changed Stylus to SCSS due to Angular dropping support

## 7.2.2 - 23-11-22

- Fixed Photo description is not hidden after removing uploaded photo in symptom report [NOONA-18085](https://vocscs.atlassian.net/browse/NOONA-18085)

## 7.2.1 - 22-11-22 

- Fixed intervention rules not showing with low internet bandwith [NOONA-17914](https://vocscs.atlassian.net/browse/NOONA-17914)

## 7.2.0 - 2022-11-04 

- Refactor photo uploader component. Get rid of dropzone library [NOONA-17897](https://vocscs.atlassian.net/browse/NOONA-17897)

## 7.1.3 -2022-10-26

- Fixed text being bold in text areas [NOONA-15464](https://vocscs.atlassian.net/browse/NOONA-15464)
- Fixed certain text areas that only appear when selecting "other" from checkboxes to decode special characters properly [NOONA-17760](https://vocscs.atlassian.net/browse/NOONA-17760)

## 7.1.2 - 2022-10-25

- Fixed thumbnail image creation after updated to dropzone v5 [NOONA-17802](https://vocscs.atlassian.net/browse/NOONA-17802)
- Updated dropzone to v5 in peer dependencies
- Removed noona-design-system from peer dependencies

## 7.1.1 - 2022-10-18

- Fixed DSConfig routing to into consideration the sprite sheet path [NOONA-17724](https://vocscs.atlassian.net/browse/NOONA-17724)

## 7.1.0 - 2022-10-10

- Updated dropzone and change DSconfig to optional in shared-core config, 
- Fix photo uploader for handling multiple photos [NOONA-17288](https://vocscs.atlassian.net/browse/NOONA-17288)

## 7.0.0 - 2022-10-06

- Removed noona-design-system as a dependency [NOONA-17718](https://vocscs.atlassian.net/browse/NOONA-17718)

## 6.1.9 - 2022-08-17

- Removed previous button from earlier-symptom.html to stop it from appearing in after baseline questionnaires. [NOONA-17331](https://vocscs.atlassian.net/browse/NOONA-17331)

## 6.1.8 - 2022-07-29

-  Refactored questionnaire summary and entry to be more flexible for the future

## 6.1.7 - 2022-07-26

-  Created an extra final screen for EQ5D5L that can be used for others as well [NOONA-17107](https://vocscs.atlassian.net/browse/NOONA-17107)

## 6.1.6 - 2022-07-14

-  Range slider changes for EQ-5D-5L [NOONA-17036](https://vocscs.atlassian.net/browse/NOONA-17036)

## 6.1.5 - 2022-07-14

-  Summary view changes [NOONA-17105](https://vocscs.atlassian.net/browse/NOONA-17105)

## 6.1.4 - 2022-07-05

-  Other cosmetic changes for EQ-5D-5L [NOONA-16631](https://vocscs.atlassian.net/browse/NOONA-16631)

## 6.1.3 - 2022-06-29

-  Switched icon rendering in shared-core questionnaires to use img and file asset paths for patient [NOONA-17010](https://vocscs.atlassian.net/browse/NOONA-17010)

## 6.1.2 - 2022-06-28

-  Made scrollIntoView variable be ignored if previous functionality is enabled [NOONA-16775](https://vocscs.atlassian.net/browse/NOONA-16775)

## 6.1.1 - 2022-06-23

-  Inputted text is not saved and displayed for IPQ questionnaire when score displayed to patient is disabled [NOONA-16833](https://vocscs.atlassian.net/browse/NOONA-16833)

## 6.1.0 - 2022-06-21

-  Add previous button functionality for forms [NOONA-16629](https://vocscs.atlassian.net/browse/NOONA-16629)

## 6.0.12 - 2022-06-20

-  Add a new non-clinical topic type [NOONA-16609](https://vocscs.atlassian.net/browse/NOONA-16609)

## 6.0.11 - 2022-06-17

-  String to string [NOONA-16582](https://vocscs.atlassian.net/browse/NOONA-16582)

## 6.0.10 - 2022-06-17

-  Fetching icons from different folders based on isDevMode() [NOONA-16582](https://vocscs.atlassian.net/browse/NOONA-16582)

## 6.0.9 - 2022-06-16

- Fixed clinic side icons not showing and migrated all symptom icons to the correct folder [NOONA-16582](https://vocscs.atlassian.net/browse/NOONA-16582)

## 6.0.8 - 2022-05-13

- Fixed locations not showing if only other values added [NOONA-16451](https://vocscs.atlassian.net/browse/NOONA-16451)

## 6.0.7 - 2022-04-26

- Fix unsafe code in symptom.service.ts [NOONA-16337](https://vocscs.atlassian.net/browse/NOONA-16337)

## 6.0.6 - 2022-04-01

- Fix blackduck high-level warning for ansi-html 0.0.7 [NOONA-16113](https://vocscs.atlassian.net/browse/NOONA-16113)

## 6.0.5 - 2022-03-10

- Fix bug with date range picker ([NOONA-15842](https://vocscs.atlassian.net/browse/NOONA-15842))

## 6.0.4 - 2022-03-05

- Decouple the strongly typed connection between treatment module types in shared-core ([NOONA-15891](https://vocscs.atlassian.net/browse/NOONA-15891))

## 6.0.3 - 2021-12-15

- Fixed modal errors being cleared on cancel and missing field status icons ([STUDY-163](https://vocscs.atlassian.net/browse/STUDY-163))

## 6.0.2 - 2021-12-08

- Change ModalEventComponent to allow saving with accepted termination error ([STUDY-163](https://vocscs.atlassian.net/browse/STUDY-163))

## 6.0.1 - 2021-12-07

- Update ng-select to Angular 12 compatible version ([NOONA-13512](https://vocscs.atlassian.net/browse/NOONA-13512))

## 6.0.0 - 2021-11-12

- Update Angular to version 12

## 5.1.58 - 2021-11-12

- fix form-engine issues with prefilled symptom-forms ([NOONA-10591](https://vocscs.atlassian.net/browse/NOONA-10591))
- fix pain-pointer-component not being interactive when form initialized with existing data (ContinueForm action)
- fix photo-item-component complaining about patient being undefined when it's not needed

## 5.1.57 - 2021-11-03

- clear field statuses with keys that starts with given key ([STUDY-161](https://vocscs.atlassian.net/browse/STUDY-161))
- radiolist with exrra fields, move clear all statuses to begining when handling change ([STUDY-161](https://vocscs.atlassian.net/browse/STUDY-162))

## 5.1.56 - 2021-11-03

-   Add `medicalRecords` and `treatmentScheduling` to `CaseType` ([NOONA-13936](https://vocscs.atlassian.net/browse/NOONA-13936))

## 5.1.55 - 2021-11-03 

- Change type of maleSymptoms and femaleSymptoms to any ([NOONA-14439](https://vocscs.atlassian.net/browse/NOONA-14439))

## 5.1.54 - 2021-10-28

- Add `openPhotoPreview`-output to FormGeneratorComponent ([NOONA-14346](https://vocscs.atlassian.net/browse/NOONA-14346))

## 5.1.53 - 2021-10-26

- Change the typing of generated symptom type and questionnaire type into any, to ensure that medical content can be created without changes to shared-core. ([NOONA-14439](https://vocscs.atlassian.net/browse/NOONA-14439))

## 5.1.52 - 2021-10-14

- Fix QR code to work with real versions ([NOONA-14268](https://vocscs.atlassian.net/browse/NOONA-14268))

## 5.1.51 - 2021-10-11

- Add PHQ-9 form ([NOONA-13731](https://vocscs.atlassian.net/browse/NOONA-13731))

## 5.1.50 - 2021-10-11

- Refactor SymptomSummaryContentComponent, removed options property ([NOONA-14027](https://vocscs.atlassian.net/browse/NOONA-14027))

## 5.1.49 - 2021-10-11

- Reverted back changes done in 5.1.47 and 5.1.48,
- Added exception handling to BarcodeComponent to tackle the original problem  ([NOONA-14024](https://vocscs.atlassian.net/browse/NOONA-14024))
- Added tests for BarcodeComponent to check boundaries of acceptable input for "text"

## 5.1.48 - 2021-10-08

-  Refactor AboutComponent.udiVersionNumber() completely to return long enough string for QR-code always ([NOONA-14024](https://vocscs.atlassian.net/browse/NOONA-14024))

## 5.1.47 - 2021-10-07

-  Allow fullVersion "minor" to be undefined in about component ([NOONA-14024](https://vocscs.atlassian.net/browse/NOONA-14024))

## 5.1.46 - 2021-09-26

- New inputs for `SymptomSummaryContentComponent` ([NOONA-10245](https://vocscs.atlassian.net/browse/NOONA-10245))

## 5.1.43 - 2021-09-28

- Combining the ESAS, CPS and another item into one form ([NOONA-13565](https://vocscs.atlassian.net/browse/NOONA-13565))

## 5.1.41 - 2021-09-24

- Export `FormGeneratorComponent` and `FieldService` ([NOONA-12429](https://vocscs.atlassian.net/browse/NOONA-12429))

## 5.1.40 - 2021-09-23

- Add `required` validation to EventModal ([STUDY-132](https://vocscs.atlassian.net/browse/STUDY-132))

## 5.1.39 - 2021-09-21

- Fix bugs with numeric input ([STUDY-130](https://vocscs.atlassian.net/browse/STUDY-130))

## 5.1.38 - 2021-09-17

- Breast-Q (V2.0): Breast-Q Breast Conservation Surgery - Satisfaction with Breasts - Questionnaire and Scoring - ICHOM (English and Dutch) ([NOONA-13287](https://vocscs.atlassian.net/browse/NOONA-13287))

## 5.1.37 - 2021-09-17

- Fix termination error being cleared when canceling EventModal ([STUDY-124](https://vocscs.atlassian.net/browse/STUDY-124))

## 5.1.36 - 2021-09-16

- Breast-Q (V2.0): Patient Satisfaction with Breasts Mastectomy Questionnaire and Scoring - ICHOM (English and Dutch) ([NOONA-13286](https://vocscs.atlassian.net/browse/NOONA-13286))

## 5.1.35 - 2021-09-15

- Breast-Q (V2.0): Adverse Effects of Radiation - ICHOM (English and Dutch) ([NOONA-13270](https://vocscs.atlassian.net/browse/NOONA-13270))

## 5.1.34 - 2021-09-15

- Fix invalid value error not being triggered for unknown date ([STUDY-127](https://vocscs.atlassian.net/browse/STUDY-127))

## 5.1.33 - 2021-09-14

- Add short debounce to CRFFormEntryComponent submit handler to allow form handlers to finish validation ([STUDY-114](https://vocscs.atlassian.net/browse/STUDY-114))

## 5.1.32 - 2021-09-13

- Fix CRFFormEntryComponent to use latest field statuses in submit handler ([STUDY-114](https://vocscs.atlassian.net/browse/STUDY-114))

## 5.1.31 - 2021-09-13

- Satisfaction Survey Questionnaire request ([NOONA-12443](https://vocscs.atlassian.net/browse/NOONA-12443))

## 5.1.30 - 2021-09-08

- Fix clearing of unknown date in RadioListWithExtraFields ([STUDY-120](https://vocscs.atlassian.net/browse/STUDY-120))

## 5.1.29 - 2021-08-30

- Fix numeric value not showing in Event summary if it's coming from an extra field

## 5.1.28 - 2021-08-26

- Create generic radiation module [NOONA-12896](https://vocscs.atlassian.net/browse/NOONA-12896)

## 5.1.26 - 2021-08-24

- Add support for multiple radio options in CheckboxListWithExtraFields ([NOONA-13417](https://vocscs.atlassian.net/browse/NOONA-13417))

## 5.1.21 - 2021-08-06

- Related to [NOONA-13204](https://vocscs.atlassian.net/browse/NOONA-13204) fixed incorrect peer dependency definition

## 5.1.20 - 2021-08-06

- Fix bug [NOONA-13204](https://vocscs.atlassian.net/browse/NOONA-13204) where Noona locale of format xx_XX caused String.toLocaleLowerCase() function to crash.

- Fix bug [NOONA-13204](https://vocscs.atlassian.net/browse/NOONA-13204) where Noona locale of format xx_XX caused String.toLocaleLowerCase() function to crash.

## 5.1.16 - 2021-07-23

- Let German localisation go through lowercase function without casting the symptom name or pain locations to lowercase [NOONA-6285](https://vocscs.atlassian.net/browse/NOONA-6285)

## 5.1.11 - 2021-06-23

- Page hangs and throws application error, If nurse tries to complete scheduled appointment which has already been completed by patient [NOONA-12804](https://vocscs.atlassian.net/browse/NOONA-12804)

## 5.1.10 - 2021-06-23

- Fix immutability issues and DsModal usage in EventModalComponent [STUDY-74](https://vocscs.atlassian.net/browse/STUDY-74)
- Change RegexReplaceInTextPipe to use DomSanitizer instead of Sanitizer [STUDY-74](https://vocscs.atlassian.net/browse/STUDY-74)

## 5.1.9 - 2021-06-22

- Add new topic types form [NOONA-12147](https://vocscs.atlassian.net/browse/NOONA-12147)

## 5.1.8 - 2021-06-16

- Add an ESAS H & N form [NOONA-12765](https://vocscs.atlassian.net/browse/NOONA-12765)

## 5.1.7 - 2021-06-14

- Fix photo uploading for multiple photos ([NOONA-12632](https://vocscs.atlassian.net/browse/NOONA-12632))

## 5.1.6 - 2021-06-14

- Add Park Pharmacy and Med Adherence form ([NOONA-12402](https://vocscs.atlassian.net/browse/NOONA-12402))

## 5.1.5 - 2021-06-09

- Added padding to particularly long button text ([NOONA-12724](https://vocscs.atlassian.net/browse/NOONA-12724))

## 5.1.4 - 2021-06-09

-   Change CrfFormEntryComponent to use the latest form state on submit ([STUDY-63](https://vocscs.atlassian.net/browse/STUDY-63))

# 5.1.3 - 2021-06-07

- Fix the evaluation of baseline and follow-up inquiries

# 5.1.2 - 2021-06-07

- Correct the behaviour of baseline and follow-up inquiries to look past into 30 days. [NOONA-10763](https://vocscs.atlassian.net/browse/NOONA-10763)

## 5.1.1 - 2021-06-03

-   Create AbbrPipe [NOONA-12380][https://vocscs.atlassian.net/browse/noona-12380]

## 5.1.0 - 2021-05-25

-   Import pain pointer SVGs in typescript to have them in library [NOONA-12279](https://vocscs.atlassian.net/browse/NOONA-12279)

## 5.0.7 - 2021-05-25

-   Revert SVGs changes in v5.0.5

## 5.0.6 - 2021-05-19

-   Minor update to status check questionniare [NOONA-12407](https://vocscs.atlassian.net/browse/NOONA-12407)

## 5.0.5 - 2021-05-19

-   Import pain pointer SVGs in typescript to have them in library [NOONA-12279](https://vocscs.atlassian.net/browse/NOONA-12279)

## 5.0.5 - 2021-05-19

-   Import pain pointer SVGs in typescript to have them in library [NOONA-12279](https://vocscs.atlassian.net/browse/NOONA-12279)

## 5.0.4 - 2021-05-11

-   Change date-range-picker behavior to not close calendar regardless of date selections [NOONA-12134](https://vocscs.atlassian.net/browse/NOONA-12134)

## 5.0.3 - 2021-04-21

-   Add uuid dependency & peerDependency [NOONA-12139](https://vocscs.atlassian.net/browse/NOONA-12139)

## 5.0.2 - 2021-04-21

-   Fix MomentJS imports [NOONA-12140](https://vocscs.atlassian.net/browse/NOONA-12140)

## 5.0.1 - 2021-04-15

-   Fix photo uploader [NOONA-12072](https://vocscs.atlassian.net/browse/NOONA-12072)

## 5.0.0 - 2021-04-12

-   Upgrade Angular from 10 to 11 [NOONA-12040](https://adelmann.atlassian.net/browse/NOONA-12040)

## 4.0.20 - 2021-04-09

-   Fix radio button ([NOONA-11977](https://adelmann.atlassian.net/browse/NOONA-11977))

## 4.0.19 - 2021-03-26

-   Make selectedValue for radioList component as string type([NOONA-11861](https://adelmann.atlassian.net/browse/NOONA-11861))

## 4.0.18 - 2021-03-26

-   Show date picker only when date is not today.([NOONA-11862](https://adelmann.atlassian.net/browse/NOONA-11862))

## 4.0.17 - 2021-03-24

-   Provide a Humana <> Varian data sharing consent form for Humana Patients ([NOONA-11625](https://adelmann.atlassian.net/browse/NOONA-11625))

## 4.0.16 - 2021-03-18

-   Fix date range picker not populating dates from further into history
-   Fix peak date picker not populating correctly when editing a symptom
    ([NOONA-11667](https://adelmann.atlassian.net/browse/NOONA-11667))

## 4.0.15 - 2021-03-16

-   Fixed date range picker to populate fields correctly on edit ([NOONA-11667](https://adelmann.atlassian.net/browse/NOONA-11667))

## 4.0.14 - 2021-03-09

-   Create copy of Chemo-IO Combination Module II ([NOONA-11620](https://adelmann.atlassian.net/browse/NOONA-11620))

## 4.0.13 - 2021-03-03

-   Add CARTI Distress Screening Questionnaire ([NOONA-11377](https://adelmann.atlassian.net/browse/NOONA-11377))

## 4.0.12 - 2021-02-26

-   Add `placeholder` & `disabled` to `nh-dropdown` component ([NOONA-11441](https://adelmann.atlassian.net/browse/NOONA-11441))

## 4.0.11 - 2021-02-22

-   `input-group` component changes and add `ADD_ANSWERS` actions ([NOONA-11257](https://adelmann.atlassian.net/browse/NOONA-11257))

## 4.0.10 - 2021-02-18

-   Message.symptomreport changed to string and renamed to symptomReportId

## 4.0.9 - 2021-02-16

-   Questionniare h1 should show full text ([NOONA-11003](https://adelmann.atlassian.net/browse/NOONA-11003))
-   Fix EQ5D: extra line break ([NOONA-11003](https://adelmann.atlassian.net/browse/NOONA-11003))

## 4.0.8 - 2021-02-10

-   Add EQ5D-3L form ([NOONA-10968](https://adelmann.atlassian.net/browse/NOONA-10968))

## 4.0.7 - 2021-02-10

-   Update design system version to get latest slider ([NOONA-11006](https://adelmann.atlassian.net/browse/NOONA-11006))

## 4.0.6 - 2021-02-08

-   Fix translation for slider label ([NOONA-11006](https://adelmann.atlassian.net/browse/NOONA-11006))

## 4.0.5 - 2021-02-01

-   Add label for slider and add `templateOptions` parser ([NOONA-11006](https://adelmann.atlassian.net/browse/NOONA-11006))

## 4.0.4 - 2020-02-04

-   Add footer to `WizardSection` ([NOONA-11005](https://adelmann.atlassian.net/browse/NOONA-11005))

## 4.0.3 - 2020-02-03

-   Create sarcoma module (TAYS) ([NOONA-10437](https://adelmann.atlassian.net/browse/NOONA-10437))

## 4.0.2 - 2021-02-02

-   Import tinygradient in a way that works in patient jest ([NOONA-10473](https://adelmann.atlassian.net/browse/NOONA-10473))

## 4.0.1 - 2021-01-27

-   Change some form engine components to use `innerHTML` for CSV translations ([NOONA-11003](https://adelmann.atlassian.net/browse/NOONA-11003))
-   Add `TextBlockComponent` to form engine ([NOONA-11003](https://adelmann.atlassian.net/browse/NOONA-11003))

## 4.0.0

-   Update to Angular 10
-   Merged changes from 3.x.x branch (version 3.2.36), changelog (here)[https://gitlab.com/varian-noona/development/core/shared-core/-/blob/737169b0d2ef8c13803ea5cac4192a19f7415f63/projects/shared-core/CHANGELOG.md]

## 1.6.72 - 2021-01-22

-   Add min/max input support for slider component ([NOONA-11052](https://adelmann.atlassian.net/browse/NOONA-11052))

## 1.6.71 - 2021-01-21

-   Create BPI form for CCMB ([NOONA-10943](https://adelmann.atlassian.net/browse/NOONA-10943)

## 1.6.70 - 2021-01-18

-   Scoll to summary should stop at header ([NOONA-11026](https://adelmann.atlassian.net/browse/NOONA-11026))

## 1.6.69 - 2021-01-14

-   Fix Slider out of view ([NOONA-10931](https://adelmann.atlassian.net/browse/NOONA-10931))

## 1.6.68 - 2021-01-14

-   Styling fix for patient app ([NOONA-10769](https://adelmann.atlassian.net/browse/NOONA-10769))

## 1.6.67 - 2021-01-14

-   Status check questionnaire, hide summary error messages when all required fileds filled ([NOONA-10995](https://adelmann.atlassian.net/browse/NOONA-10995))

## 1.6.66 - 2021-01-14

-   Fix Date picker, two radio buttons selected at the same time ([NOONA-10993](https://adelmann.atlassian.net/browse/NOONA-10993))

## 1.6.65 - 2021-01-14

-   Add `questionnaire-button-next` ID to first button in `InquiryEntryComponent` ([NOONA-10984](https://adelmann.atlassian.net/browse/NOONA-10984))

## 1.6.64 - 2021-01-13

-   Fix console error read 'formValid' of undefined ([NOONA-10992](https://adelmann.atlassian.net/browse/NOONA-10992))

## 1.6.63 - 2021-01-12

-   Calendar should keep open when first day user select is 'today' ([NOONA-10917](https://adelmann.atlassian.net/browse/NOONA-10917))

## 1.6.62 - 2021-01-12

-   Change `CheckboxListComponent` to use async pipe for `showOther` boolean ([NOONA-10920](https://adelmann.atlassian.net/browse/NOONA-10920))

## 1.6.61 - 2021-01-11

-   Center the buttons for questionniares ([NOONA-10769](https://adelmann.atlassian.net/browse/
    NOONA-10769))

## 1.6.60 - 2021-01-18

-   Fix earlier symptom hide extra questions for status check ([NOONA-10842](https://adelmann.atlassian.net/browse/
    NOONA-10842))

## 1.6.59 - 2021-01-18

-   Move the questionnaire id to a higher level for pendo ([NOONA-10795](https://adelmann.atlassian.net/browse/
    NOONA-10795))

## 1.6.58 - 2021-01-18

-   Add hidding condition for early symptom ([NOONA-10876](https://adelmann.atlassian.net/browse/
    NOONA-10876))

## 1.6.57 - 2021-01-08

-   Change `PhotoPreviewService` to be provided in the root injector instead of `InquiryEntryComponent` ([NOONA-10914](https://adelmann.atlassian.net/browse/NOONA-10914))
-   Add `openPhotoPreview` output to `QuestionnaireSummaryContentComponent` ([NOONA-10922](https://adelmann.atlassian.net/browse/NOONA-10922))

## 1.6.56 - 2021-01-07

-   Status check questionnaire: showing the contact method for all the topics ([NOONA-10861](https://adelmann.atlassian.net/browse/

## 1.6.55 - 2021-01-07

-   Fixed the styling for Status check questionnaire ([NOONA-10861](https://adelmann.atlassian.net/browse/
    NOONA-10861))

## 1.6.54 - 2021-01-07

-   Fixed new case creation for patient ([NOONA-10874](https://adelmann.atlassian.net/browse/
    NOONA-10874))

## 1.6.53 - 2021-01-05

-   Add `questionnaireLoaded` event to `InquiryEntryComponent` and `QuestionnaireEntryComponent` ([NOONA-10906](https://adelmann.atlassian.net/browse/NOONA-10906))

## 1.6.52 - 2021-01-03

-   Build UI for new questinnaire ([NOONA-10861](https://adelmann.atlassian.net/browse/NOONA-10861))

## 1.6.51 - 2020-12-22

-   Add photo preview output to `InquiryEntryComponent` ([NOONA-10755](https://adelmann.atlassian.net/browse/NOONA-10755))

## 1.6.50 - 2020-12-19

-   Add `SymptomFromQuestionaire` to `CaseType` ([NOONA-10491](https://adelmann.atlassian.net/browse/NOONA-10491))

## 1.6.49 - 2020-12-17

-   Add "Next unanswered question" functionality to symptom inquiry ([NOONA-10574](https://adelmann.atlassian.net/browse/NOONA-10574))

## 1.6.48 - 2020-12-17

-   Add hiding field condition for new questinnaire ([NOONA-10842](https://adelmann.atlassian.net/browse/NOONA-10842))

## 1.6.47 - 2020-12-15

-   Re-added `SYMPTOM_SWOLLEN_ARM` to `SymptomType`, which was mistakenly removed in version 1.6.45

## 1.6.46 - 2020-12-15

-   Add `TO_POST_TREATMENT` type into Inquiry type ([NOONA-10635](https://adelmann.atlassian.net/browse/NOONA-10635))

## 1.6.45 - 2020-12-14

-   Form engine fixes required by patient app ([NOONA-10482](https://adelmann.atlassian.net/browse/NOONA-10482))

## 1.6.44 - 2020-12-12

-   Add `PracticalProblems` to `CaseType` ([NOONA-10489](https://adelmann.atlassian.net/browse/NOONA-10489))

## 1.6.43 - 2020-12-11

-   Update breast Radiotherapy module ([NOONA-10565](https://adelmann.atlassian.net/browse/NOONA-10565))

## 1.6.42 - 2020-12-07

-   Add `TO_POST_TREATMENT` type into Inquiry type ([NOONA-10635](https://adelmann.atlassian.net/browse/NOONA-10635))

## 1.6.41 - 2020-12-04

-   Add `questionnaire` form type for hiding questions in questionnaires ([NOONA-10728](https://adelmann.atlassian.net/browse/NOONA-10728))

## 1.6.40 - 2020-11-30

-   Create urologic modules for TAYS ([NOONA-10249](https://adelmann.atlassian.net/browse/NOONA-10249))

## 1.6.39 - 2020-11-25

-   Add `isDirty` to clearable view value in form submission ([NOONA-10603](https://adelmann.atlassian.net/browse/NOONA-10603))

## 1.6.38 - 2020-11-24

-   GoogleAnalytics - Patient App - Custom dimension 'clinic' is not populated on patient side ([NOONA-9338]([https://adelmann.atlassian.net/browse/NOONA-9338))

## 1.6.37 - 2020-11-24

-   Add close button to questionnaires ([NOONA-10577](https://adelmann.atlassian.net/browse/NOONA-10577))

## 1.6.36 - 2020-11-23

-   Add `isDirty` flag to the Form Engine "form" store ([NOONA-10603](https://adelmann.atlassian.net/browse/NOONA-10603))

## 1.6.35 - 2020-11-05

-   Add questionnaire title to the summary of QoL questionnaires

## 1.6.34 - 2020-11-04

-   Add `PatientWrapperService` to do special handling for the patient app and change patient effects to use it instead

## 1.6.33 - 2020-11-03

-   Add new CaseStatus type (Deleted) ([NOONA-8292](https://adelmann.atlassian.net/browse/NOONA-8292))

## 1.6.32 - 2020-11-02

-   Add 4 new case outcome types ([NOONA-10085](https://adelmann.atlassian.net/browse/NOONA-10085))

## 1.6.31 - 2020-10-21

-   Add ESAS and CPC tools for CCMB ([NOONA-10063](https://adelmann.atlassian.net/browse/NOONA-10063))

## 1.6.30 - 2020-10-14

-   Fix bug with i18nStateful pipe not returning translation ([NOONA-9734](https://adelmann.atlassian.net/browse/NOONA-9734))

## 1.6.29 - 2020-10-08

-   Create new IO Combo module ([NOONA-9458](https://adelmann.atlassian.net/browse/NOONA-9458))

## 1.6.28 - 2020-09-29

-   Add QLQ-C30 and BR23 and translations for QOL15D and IPSS for Luxembourg ([NOONA-9407](https://adelmann.atlassian.net/browse/NOONA-9407))

## 1.6.27 - 2020-09-21

-   Add `providerAnalyticsVisibleForAllowedClinicUsers` field to `ConfigurationItems` ([NOONA-9826](https://adelmann.atlassian.net/browse/NOONA-9826))
-   Update all missing and outdated models

## 1.6.26 - 2020-09-16

-   Add `ClinicDataExportRequestDto` and `ClinicDataExportRequestStatus` models ([NOONA-9260](https://adelmann.atlassian.net/browse/NOONA-9260))

## 1.6.25 - 2020-08-19

-   [NOONA-9574](https://adelmann.atlassian.net/browse/NOONA-9574) Set patient's id first when landing to questionnaire entry component so that it can be used in calling component

## 1.6.24 - 2020-08-12

-   Fix bug with incorrectly showing max score of 10 for all questionnaires by default ([NOONA-9505](https://adelmann.atlassian.net/browse/NOONA-9505))

## 1.6.23 - 2020-07-08

-   Rename ImpactOnPhoneCallsData -> CaseVolumesData ([NOONA-9416](https://adelmann.atlassian.net/browse/NOONA-9416))

## 1.6.22 - 2020-07-08

-   Add covid screening form for Cancer Care Manitoba ([NOONA-9372](https://adelmann.atlassian.net/browse/NOONA-9372))

## 1.6.21 - 2020-07-03

-   Questionnaire content work for Luxembourg (add two forms and translations for existing forms) ([NOONA-9347](https://adelmann.atlassian.net/browse/NOONA-9347))

## 1.6.20 - 2020-07-01

-   Add EPIC-26 questionnaire for TAYS and Bounce QoL questionnaire ([NOONA-8375](https://adelmann.atlassian.net/browse/NOONA-8375))

## 1.6.19 - 2020-06-30

-   Add `careTeam` property to QuestionnaireResponseData ([NOONA-9358](https://adelmann.atlassian.net/browse/NOONA-9358))

## 1.6.18 - 2020-06-17

-   Remove dead code from Measurement form engine component ([PHARMA-984](https://adelmann.atlassian.net/browse/PHARMA-984))

## 1.6.17 - 2020-06-17

-   Fix bug with reducer manipulating immutable state

## 1.6.16 - 2020-06-17

-   Fix measurement component row ID generation in form engine and changed dropdown to use design system dropdown ([PHARMA-984](https://adelmann.atlassian.net/browse/PHARMA-984))

## 1.6.15 - 2020-06-15

-   Add FACT-SF-6 and FACT-HN_ENG forms for Henry Ford ([NOONA-9282](https://adelmann.atlassian.net/browse/NOONA-9282))

## 1.6.14 - 2020-06-11

-   Add Oral Compliance QoL Survey request by UT Tyler and FACT-G form for Henry Ford ([NOONA-8971](https://adelmann.atlassian.net/browse/NOONA-8971))

## 1.6.13 - 2020-06-11

-   Added transaction models for questionnaire tracking ([NOONA-9050](https://adelmann.atlassian.net/browse/NOONA-9050))

## 1.6.12 - 2020-06-11

-   Update provider analytics related models ([NOONA-9256](https://adelmann.atlassian.net/browse/NOONA-9256))

## 1.6.11 - 2020-06-05

-   Add ZRTI, EPIC-26 and I-PSS forms ([NOONA-8534](https://adelmann.atlassian.net/browse/NOONA-8534))

## 1.6.10 - 2020-06-02

-   Modify Patient Diary related styling color variables to enable white labeling ([NOONA-8961](https://adelmann.atlassian.net/browse/NOONA-8961))

## 1.6.9 - 2020-06-02

-   Added varian tenant id to clinic model

## 1.6.8 - 2020-05-29

-   Introduce models ([PHARMA-957](https://adelmann.atlassian.net/browse/PHARMA-957)):
    -   [PagedDelayedCasesData](./src/lib/generated/models/paged-delayed-cases-data.ts)
    -   [PagedProviderAnalyticsRequest](./src/lib/generated/models/paged-provider-analytics-request.ts)
    -   [DelayReasonsData](./src/lib/generated/models/delay-reasons-data.ts) (Renamed from DelayedCasesData)
    -   [DelayedCasesData](./src/lib/generated/models/delayed-cases-data.ts)

## 1.6.7 - 2020-05-29

-   Fix Regulatory About modal UDI on IE11 ([NOONA-9104](https://adelmann.atlassian.net/browse/NOONA-9104))

## 1.6.6 - 2020-05-26

-   Modify UDI text in Regulatory About modal ([NOONA-8974](https://adelmann.atlassian.net/browse/NOONA-8974))

## 1.6.5 - 2020-05-25

-   Introduce [DelayedCasesData](./src/lib/generated/models/delay-reasons-data.ts) model ([PHARMA-937](https://adelmann.atlassian.net/browse/PHARMA-937))

## 1.6.4 - 2020-05-25

-   Split plain UDI text into separate rows in Regulatory About modal ([NOONA-8974](https://adelmann.atlassian.net/browse/NOONA-8974))

## 1.6.3 - 2020-05-18

-   Relocate UDI deviceIdentifier to messages.properties & fix full version format in Regulatory About modal
    ([NOONA-8974](https://adelmann.atlassian.net/browse/NOONA-8974))

## 1.6.2 - 2020-05-13

-   Fix UDI text & version format in Regulatory About modal ([NOONA-8854](https://adelmann.atlassian.net/browse/NOONA-8854))

## 1.6.1 - 2020-05-11

-   Introduce [QuestionnaireResponseData](./src/lib/generated/models/questionnaire-response-data.ts) model ([PHARMA-896](https://adelmann.atlassian.net/browse/PHARMA-896))

## 1.6.0 - 2020-05-08

-   NOONA-8693 and NOONA-8675 cherry picked to this branch
-   Changed master to be 1.x branch. Angular 9 branch will be in 3.x.x
-   Correctly generate barcode uid code from the full version

## 1.5.7 - 2020-05-07

-   Add Tamoxifene, AI and HER2 modules ([NOONA-8512](https://adelmann.atlassian.net/browse/NOONA-8512))

## 1.5.6 - 2020-05-04

-   Introduce [OpenedCases](./src/lib/generated/models/opened-cases.ts) model

## 1.5.5 - 2020-05-04

-   Add `TAXANES_TREATMENT` to [TreatmentModuleType](./src/lib/generated/models/treatment-module-type.ts)

## 1.5.4 - 2020-04-28

-   Introduce new statuses SCHEDULED_ACTIVE and SKIPPED_ACTIVE in [InquiryStatus](./src/lib/generated/models/inquiry-status.ts)

## 1.5.3 - 2020-04-23

-   Fix misbehaving tooltip styles from MDR About modal, part of [NOONA-8610](https://adelmann.atlassian.net/browse/NOONA-8610)

## 1.5.2 - 2020-04-22

-   Add `careTeam` property to [NewPortalPatients](./src/lib/generated/models/new-portal-patients.ts)

## 1.5.1 - 2020-04-20

-   Introduce [NewPortalPatients](./src/lib/generated/models/new-portal-patients.ts) model

## 1.5.0 - 2020-04-12

-   Add regulatory about component ([NOONA-7927](https://adelmann.atlassian.net/browse/NOONA-7927))
-   Update `@varian-noona/noona-design-system-lib` to version `0.9.5`

## 1.4.4 - 2020-04-15

-   Introduce models:
    -   [ProviderAnalyticsFilterOptions](./src/lib/generated/models/provider-analytics-filter-options.ts)
    -   [ProviderAnalyticsDiagnosisOption](./src/lib/generated/models/provider-analytics-diagnosis-option.ts)
    -   [ProviderAnalyticsCaseTypeOption](./src/lib/generated/models/provider-analytics-case-type-option.ts)
    -   [ProviderAnalyticsCaseStatusOption](./src/lib/generated/models/provider-analytics-case-status-option.ts)
    -   [ProviderAnalyticsCareTeamOption](./src/lib/generated/models/provider-analytics-care-team-option.ts)
-   Renamed `AnsweredCase` to `AnsweredCases`

## 1.4.3 - 2020-04-14

-   Introduce models:
    -   [AnsweredCase](./src/lib/generated/models/answered-cases.ts)
    -   [PatientsTouchedFact](./src/lib/generated/models/patients-touched-fact.ts)
    -   [ProviderAnalyticsFilters](./src/lib/generated/models/provider-analytics-filters.ts)
-   Ignore `*.iml` files

## 1.4.2 - 2020-04-08

-   Add `COVID_19` to case-outcome ([NOONA-8454](https://adelmann.atlassian.net/browse/NOONA-8454))

## 1.4.0 - 7.04.2020

-   Add `LAB_RESULT` to topic-type ([NOONA-7879](https://adelmann.atlassian.net/browse/NOONA-7879))
-   Removed `yarn.lock` from the library folder
