{"extends": "../../.eslintrc.json", "ignorePatterns": ["/src/app/shared/models/*", "!**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["projects/shared-core/tsconfig.(lib|spec).json"], "createDefaultProgram": true}, "plugins": ["eslint-plugin-react"], "rules": {"@angular-eslint/template/eqeqeq": "off", "@angular-eslint/component-selector": "off", "@angular-eslint/directive-selector": ["off", {"type": "attribute", "prefix": "ns", "style": "camelCase"}], "@typescript-eslint/naming-convention": ["error", {"selector": "default", "format": ["camelCase"], "leadingUnderscore": "allow", "trailingUnderscore": "allow"}, {"selector": "classProperty", "format": ["camelCase", "snake_case", "PascalCase", "UPPER_CASE"], "leadingUnderscore": "allow"}, {"selector": "enumMember", "format": ["camelCase", "PascalCase", "UPPER_CASE", "snake_case"]}, {"selector": "function", "format": ["camelCase", "PascalCase"]}, {"selector": "objectLiteralProperty", "format": ["camelCase", "UPPER_CASE", "snake_case"]}, {"selector": "parameter", "format": ["camelCase", "PascalCase", "UPPER_CASE", "snake_case"], "leadingUnderscore": "allow"}, {"selector": "parameterProperty", "format": ["camelCase", "PascalCase"]}, {"selector": "typeAlias", "format": ["PascalCase", "UPPER_CASE"]}, {"selector": "variable", "format": ["camelCase", "PascalCase", "snake_case", "UPPER_CASE"], "leadingUnderscore": "allow", "trailingUnderscore": "allow"}, {"selector": "typeLike", "format": ["PascalCase", "UPPER_CASE"]}], "@typescript-eslint/member-delimiter-style": ["off", {"multiline": {"delimiter": "none", "requireLast": true}, "singleline": {"delimiter": "semi", "requireLast": false}}], "@typescript-eslint/no-this-alias": "error", "@typescript-eslint/quotes": ["off", "single", {"allowTemplateLiterals": true}], "@typescript-eslint/semi": ["off", null], "@typescript-eslint/type-annotation-spacing": "off", "arrow-parens": ["off", "always"], "brace-style": ["off", "off"], "eol-last": "off", "import/no-extraneous-dependencies": "error", "import/no-internal-modules": "off", "linebreak-style": "off", "max-len": "off", "new-parens": "off", "newline-per-chained-call": "off", "no-duplicate-case": "error", "no-duplicate-imports": "error", "no-extra-bind": "error", "no-extra-semi": "off", "no-irregular-whitespace": "off", "no-new-func": "error", "no-redeclare": "error", "no-return-await": "error", "no-sequences": "error", "no-sparse-arrays": "error", "no-template-curly-in-string": "error", "no-trailing-spaces": "off", "prefer-object-spread": "error", "quote-props": "off", "react/jsx-curly-spacing": "off", "react/jsx-equals-spacing": "off", "react/jsx-tag-spacing": ["off", {"afterOpening": "allow", "closingSlash": "allow"}], "react/jsx-wrap-multilines": "off", "space-before-function-paren": "off", "space-in-parens": ["off", "never"], "no-restricted-imports": ["error", {"paths": ["lodash"]}], "no-console": ["error", {"allow": ["warn", "error"]}]}}, {"files": ["*.html"], "rules": {}}]}