{"name": "@varian-noona/shared", "version": "10.2.15", "peerDependencies": {"@angular-slider/ngx-slider": "^17.0.0 || ^18.0.0", "@angular/animations": ">=15", "@angular/common": ">=15", "@angular/core": ">=15", "@angular/forms": ">=15", "@angular/platform-browser": ">=15", "@angular/router": ">=15", "@ng-select/ng-select": ">=10", "@ngrx/effects": ">=11", "@ngrx/entity": ">=11", "@ngrx/store": ">=11", "@bwip-js/browser": "^4.5.1", "core-js": "^3.6.5", "jquery": "^3.4.1", "lodash": "^4.17.21", "moment": "^2.24.0", "ng-mocks": "^14.4.0", "rxjs": "^7.8.0", "tinygradient": "^1.0.0", "url-join": "^5.0.0", "uuid": "^8.3.2"}, "dependencies": {"tslib": "^2.5.0"}, "publishConfig": {"@varian-noona:registry": "https://gitlab.com/api/v4/projects/16085590/packages/npm/"}}