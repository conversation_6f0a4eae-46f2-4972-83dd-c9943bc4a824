import { Type } from '@angular/core';
import { ConfigurationProviderService } from './abstract-services/configuration-provider.service';
import { FormSpecificHandlerFactory } from './abstract-services/form-specific-handler-factory.service';
import { LoadingSpinnerService } from './abstract-services/loading-spinner.service';
import { NoonaLocaleService } from './abstract-services/noona-locale.service';
import { SharedApi } from './abstract-services/shared-api.service';
import { SharedAssetService } from './abstract-services/shared-asset.service';
import { SharedNativePictureSelectionService } from './abstract-services/shared-native-picture-selection.service';
import { SharedSchemaService } from './abstract-services/shared-schema.service';
import { DsLibraryConfig } from './ds/services/config.service';

export interface SharedCoreConfig {
    localeService?: Type<NoonaLocaleService>;
    api?: Type<SharedApi>;
    schemaService?: Type<SharedSchemaService>;
    configProvider?: Type<ConfigurationProviderService>;
    loadingService?: Type<LoadingSpinnerService>;
    assetService?: Type<SharedAssetService>;
    nativePictureService?: Type<SharedNativePictureSelectionService>;
    formSpecificHandlerFactory?: Type<FormSpecificHandlerFactory>;
    environment: Partial<{ assetPath: string }>;
    dsConfig?: DsLibraryConfig;
}
