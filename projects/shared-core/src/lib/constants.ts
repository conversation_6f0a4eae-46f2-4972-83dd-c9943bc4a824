import { CrfFormType } from './form-engine/models/crf/crf-form-type';
import { QuestionaryType } from './generated/models/questionary-type';
import { SymptomType } from './generated/models/symptom-type';

export const photoGroupDescriptionFormKey = 'photoDescription';

export const maleSymptoms = [SymptomType.SYMPTOM_LIBIDO_AND_ERECTION] as any[];
export const femaleSymptoms = [SymptomType.SYMPTOM_VAGINA_SYMPTOMS] as any[];

export type FormType = SymptomType | QuestionaryType | CrfFormType | string;

export const wellnessSymptomTypes = [
    SymptomType.SYMPTOM_GENERAL_CONDITION,
    SymptomType.SYMPTOM_DISTRESS,
    SymptomType.SYMPTOM_WEIGHT,
] as FormType[];

export const genderSpecificIllustrations = [SymptomType.SYMPTOM_SEXUAL_FUNCTIONS];
export const SKIPPED_PHONE_NUMBER = '+3580000';

export enum ERROR_POSITION {
    TOP,
    BOTTOM,
}

export const ISO_DATE_FORMAT = 'YYYY-MM-DD';
export const DEFAULT_ASSETS_PATH = 'assets/';
