import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Observer } from 'rxjs';
import { ConfigurationProviderService } from '../../abstract-services/configuration-provider.service';
import { Dictionary } from '../../common-types';
import { BlobInformation } from './blob-information';

@Injectable({
    providedIn: 'root',
})
export class BlobUrlService {
    constructor(private http: HttpClient, private configService: ConfigurationProviderService) {}

    public getBlobUrl(fileUrl): Observable<BlobInformation> {
        return Observable.create((obs: Observer<BlobInformation>) => {
            const authHeaders: Dictionary<string, string> = this.configService.authorizationHeader();
            const headers = new HttpHeaders({
                ...authHeaders,
            });

            this.http
                .get(fileUrl, { responseType: 'arraybuffer', observe: 'response', headers })
                .toPromise()
                .then((response) => {
                    if (response && response.body) {
                        const parameters = {};
                        const blob = new Blob([response.body], { type: response.headers.get('Content-Type') });
                        const disposition: string = response.headers.get('Content-Disposition');
                        if (disposition) {
                            const values = disposition.split(';');
                            values.forEach((keyValuePair) => {
                                const pair = keyValuePair.split('=');
                                if (pair.length > 1) {
                                    const key = pair[0];
                                    const value = pair[1];

                                    parameters[key] = value;
                                }
                            });
                        }
                        const blobUrl = URL.createObjectURL(blob);
                        const blobs = sessionStorage.getItem('blobs');

                        if (blobs) {
                            sessionStorage.setItem('blobs', `${blobs};${blobUrl}`);
                        } else {
                            sessionStorage.setItem('blobs', blobUrl);
                        }

                        obs.next(new BlobInformation(blobUrl, blob, parameters));
                    } else {
                        obs.next(new BlobInformation(''));
                    }
                    obs.complete();
                });
        });
    }
}
