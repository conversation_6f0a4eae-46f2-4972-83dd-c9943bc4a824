import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  Output,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import moment from 'moment';

import { Subject } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { SharedNativePictureSelectionService } from '../../abstract-services/shared-native-picture-selection.service';
import { PhotoPreviewService } from '../../form-engine/services/photo-preview.service';
import { PhotoGroup } from '../../generated/models/photo-group';
import { PhotoUploadApiService } from './photo-upload-api.service';
import { PhotoStatus, PhotoUploadItem, PhotoUploadService } from './photo-upload.service';
import { PreviewOption } from './preview-option.interface';
import { I18NPipe } from '../../pipes/i18n.pipe';

interface HTMLInputEvent extends Event {
  target: HTMLInputElement & EventTarget;
}

const NOT_NATIVE_ERROR_TEXT = 'Only supported in native context!';

/**
 * Component to provide photo uploading controls to a page
 */
@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'photo-uploader',
  templateUrl: './photo-uploader.component.html',
  styleUrls: ['./photo-uploader.component.scss'],
  encapsulation: ViewEncapsulation.None,
  providers: [PhotoUploadService]
})
export class PhotoUploaderComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() maximumFileCount: number;
  @Input() viewOnly: boolean;
  @Input()
  public set photoGroup(photoGroup: PhotoGroup) {
    if (photoGroup) {
      this.uploaderPhotoGroup = photoGroup;
      this.initialize();
    }
  }
  @Input() containsPhotos = false;
  @Input() patientId?: string;
  @Input() onlySubmittedPhotos = true;
  @Input() showPhotoUploadBox = false;

  // Need unique id since default photo id will be used in summaries and their may be multiple summaries
  @Input() photoId = 'photo-uploader-' + moment().toDate().getTime();
  @Output() photoGroupChange = new EventEmitter<PhotoGroup>();
  @Output() containsPhotosChange = new EventEmitter<boolean>();

  @Output() uploadInProgress = this.photoUploadService.uploadInProgress;
  @Output() openPreview = new EventEmitter<PreviewOption>();
  @Input() altText = this.i18nPipe.transform('general.a11y.photoUpload.defaultAlt');

  public uploaderPhotoGroup;
  public uploadContainer;
  public previewsContainer;
  public desktopInstructions = this.i18nPipe.transform('general.photoUpload.desktopInstructions', {
    link: '<a role="button" tabindex="0" id="browse-file-link">',
    linkEnd: '</a>'
  });
  public clientIsMobile = false;

  public canAcceptUploads = true;

  private $element: HTMLElement;
  @ViewChild('fileInput') fileInput: ElementRef;

  PhotoStatus = PhotoStatus;
  destroy$ = new Subject<void>();

  constructor(
    private readonly elementRef: ElementRef,
    private readonly photoPreviewService: PhotoPreviewService,
    public photoUploadService: PhotoUploadService,
    private readonly photoUploadApi: PhotoUploadApiService,
    private readonly cd: ChangeDetectorRef,
    private readonly i18nPipe: I18NPipe,
    @Optional() private readonly nativePictureSelectionService: SharedNativePictureSelectionService
  ) {}

  ngAfterViewInit() {
    if (this.viewOnly) {
      this.$element.classList.add('view');
    }

    const browseLink = document.getElementById('browse-file-link');
    if (browseLink) {
      browseLink.addEventListener('click', event => {
        event.preventDefault();
        this.openFileSelectDialog();
      });

      browseLink.addEventListener('keydown', e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.openFileSelectDialog();
        }
      });
    }
  }

  ngOnInit(): void {
    this.initialize();
  }

  initialize() {
    this.photoUploadService.patientId = this.patientId;
    if (this.uploaderPhotoGroup) {
      this.photoUploadService.photoGroup = this.uploaderPhotoGroup;
      this.photoUploadService.initialiseExistingPhotos(this.onlySubmittedPhotos).subscribe(data => {
        if (data) {
          this.showPhotoUploadBox = true;
          this.updateUploaderState();
        }
      });
    }

    this.uploadContainer = `${this.photoId}-container`;
    this.previewsContainer = `${this.photoId}-previews`;

    this.setClientIsMobileFlag();

    this.$element = this.elementRef.nativeElement;

    this.photoUploadService.emitCheckUploadAllow$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.updateUploaderState();
    });

    this.photoUploadService.uploadedPhoto$.pipe(takeUntil(this.destroy$)).subscribe(photo => {
      if (!photo) {
        return;
      }
      const { id, status, thumbnail } = photo;
      // ensure the photo status and thumbnail is in sync
      const matchingPhoto = this.photoUploadService.photos.find(photo => photo.id === id);
      if (matchingPhoto) {
        matchingPhoto.status = status;
        matchingPhoto.thumbnail = thumbnail;
      }
    });
  }

  ngOnDestroy(): void {
    this.photoUploadService.stopRetryInterval();
    this.uploaderPhotoGroup = undefined;
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Check if the user is on a mobile device by checking if it supports a touch gesture or it's one of known
   * mobile browsers or tablet browsers
   */
  private setClientIsMobileFlag(): void {
    this.clientIsMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobi/i.test(navigator.userAgent.toLowerCase());
  }

  private updateContainsPhotosFlag(newValue: boolean) {
    this.containsPhotos = newValue;
    this.containsPhotosChange.emit(this.containsPhotos);
  }

  private getNumberOfValidPhotos(): number {
    return this.photoUploadService.photos?.filter(photo => {
      return photo.status !== PhotoStatus.Error;
    }).length;
  }

  private updateUploaderState(): void {
    const numberOfValidPhotos = this.getNumberOfValidPhotos();
    this.canAcceptUploads = numberOfValidPhotos < this.maximumFileCount;
    this.updateContainsPhotosFlag(numberOfValidPhotos > 0);
    this.toggleUploadBlockedClass();
  }

  private toggleUploadBlockedClass() {
    if (!this.canAcceptUploads) {
      this.$element.classList.add('blocked');
    } else if (this.$element.classList.contains('blocked')) {
      this.$element.classList.remove('blocked');
    }
    this.cd.markForCheck();
  }

  private selectFileViaNativeService(): void {
    this.nativePictureSelectionService
      .getPicture()
      .then((fileOrBlob: any) => {
        this.photoUploadService
          .checkPhotoGroup()
          .pipe(take(1))
          .subscribe(group => {
            if (group) {
              this.photoGroupChange.emit(group);
              this.containsPhotosChange.emit(true);
            }
            if (fileOrBlob) {
              this.photoUploadService.setFiles([fileOrBlob]);
            }
          });
      })
      .catch(err => {
        if (err === NOT_NATIVE_ERROR_TEXT) {
          this.openFileWithBrowser();
        }
      });
  }

  private setFiles(files: File[]) {
    const validUploadedPhotos = this.getNumberOfValidPhotos();

    if (validUploadedPhotos > this.maximumFileCount) {
      return;
    }

    if (validUploadedPhotos + files.length > this.maximumFileCount) {
      const allowedToUploadPhotos = this.maximumFileCount - validUploadedPhotos;
      files = files.slice(0, allowedToUploadPhotos);
    }

    this.photoUploadService
      .checkPhotoGroup()
      .pipe(take(1))
      .subscribe(group => {
        if (group) {
          this.photoGroupChange.emit(group);
          this.containsPhotosChange.emit(true);
        }
        this.photoUploadService.setFiles(files);
      });
  }

  onFileSelected(event: Event): void {
    const { target } = event as HTMLInputEvent;
    const files: File[] = Array.from(target.files);
    target.value = null;
    this.setFiles(files);
  }

  onPreviewClick(event: Event, item: PhotoUploadItem, photoIndex: number): void {
    event.stopPropagation();
    const previewOption: PreviewOption = {
      index: photoIndex,
      element: $(this.$element),
      photos: this.photoUploadService.photos.map(photo => {
        return photo.preview;
      })
    };
    this.openPreview.next(previewOption);
    this.photoPreviewService.openPreview(previewOption);
  }

  removePhoto(event: Event, item: PhotoUploadItem, photoIndex: number): void {
    event.preventDefault();
    event.stopPropagation();
    item.sub?.unsubscribe();

    if (item.status !== PhotoStatus.Waiting) {
      this.photoUploadApi.removePhoto(item).subscribe(res => {
        const photosToRemove = [...this.uploaderPhotoGroup.photosToRemove, item.id];
        this.photoGroupChange.emit({ ...this.uploaderPhotoGroup, photosToRemove });
        const numberOfValidPhotos = this.photoUploadService.photos?.filter(photo => {
          return photo.status !== PhotoStatus.Error;
        }).length;
        this.canAcceptUploads = numberOfValidPhotos < this.maximumFileCount;
        this.updateContainsPhotosFlag(numberOfValidPhotos > 0);
      });
    }
    this.photoUploadService.photos.splice(photoIndex, 1);
  }

  openFileWithBrowser() {
    this.fileInput.nativeElement.click();
  }

  openFileSelectDialog(): void {
    if (!this.canAcceptUploads) {
      return;
    }
    if (this.clientIsMobile) {
      this.selectFileViaNativeService();
    } else {
      this.openFileWithBrowser();
    }
  }

  onFileDrop(event: DragEvent) {
    if (!this.canAcceptUploads) {
      return;
    }

    const files = event.dataTransfer.files;
    if (files) {
      this.setFiles(Array.from(files));
    }
    event.preventDefault();
    event.stopPropagation();
  }

  onInputCancel(event) {
    event.preventDefault();
    event.stopPropagation();
  }

  onDragLeaveAndOver(event: Event) {
    event.preventDefault();
    event.stopPropagation();
  }

  getPreviewAlt(index: number) {
    return this.photoUploadService.photos.length > 1 ? this.altText + ' ' + (index + 1) : this.altText;
  }
}
