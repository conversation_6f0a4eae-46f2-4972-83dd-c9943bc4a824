<ng-container *ngIf="clientIsMobile; then mobileUploader; else desktopUploader"></ng-container>

<ng-template #mobileUploader>
  <button
    class="photo-uploader"
    [id]="uploadContainer"
    (drop)="onFileDrop($event)"
    (dragover)="onDragLeaveAndOver($event)"
    (dragleave)="onDragLeaveAndOver($event)"
    (click)="openFileSelectDialog()"
  >
    <ds-action-button *ngIf="!showPhotoUploadBox && !viewOnly" icon="icon-camera" action="add" (buttonClick)="showPhotoUploadBox = true">
      {{ 'photouploader.button.addphoto' | i18n }}
    </ds-action-button>
    <div class="photo-uploader-box" [ngClass]="{ view: viewOnly }" [hidden]="!showPhotoUploadBox">
      <div class="interaction">
        <div class="dropzone">
          <ds-icon *ngIf="!viewOnly" class="icon-dropzone" name="icon-camera" size="50"></ds-icon>
          <div class="message" *ngIf="canAcceptUploads">
            <h2 class="heading">{{ 'general.photoUpload.addPhotos' | i18n }}</h2>
            <p class="description">{{ 'general.photoUpload.tapToUpload' | i18n }}</p>
          </div>
          <div class="info" [hidden]="viewOnly">
            <div class="message" *ngIf="!canAcceptUploads">
              <p class="description blocked">{{ 'general.photoUpload.tooManyFiles' | i18n }}</p>
            </div>
          </div>
          <ul
            class="previews"
            *ngIf="photoUploadService.photos.length > 0"
            id="previewsContainer"
            [attr.aria-label]="'general.a11y.photoUpload.previews.ariaLabel' | i18n"
          >
            <li
              *ngFor="let item of photoUploadService.photos; let i = index"
              class="dz-preview dz-file-preview"
              [class.dz-error]="item.status === PhotoStatus.Error"
              [ngSwitch]="item.status"
            >
              <div class="dz-image" [class.dz-image__loaded]="item.status === PhotoStatus.Success">
                <button (click)="onPreviewClick($event, item, i)">
                  <img *ngIf="!viewOnly && item.status === PhotoStatus.Success" [src]="item.thumbnail?.src" [alt]="getPreviewAlt(i)" />
                  <img *ngIf="viewOnly && item.status === PhotoStatus.Success" [src]="item.thumbnail?.src" [alt]="getPreviewAlt(i)" />
                </button>
                <div class="spinner-container" *ngSwitchCase="PhotoStatus.WaitingForThumbnail">
                  <svg class="spinner" viewBox="25 25 50 50">
                    <circle class="loader" cx="50" cy="50" r="20" fill="none" stroke="#5D5E60" stroke-width="4" />
                  </svg>
                </div>
              </div>

              <div
                *ngSwitchCase="PhotoStatus.Waiting"
                role="progressbar"
                class="dz-progress"
                [attr.aria-valuenow]="item.progress"
                aria-valuemin="0"
                aria-valuemax="100"
              >
                <span class="dz-upload" [style.width.px]="item.progress"></span>
              </div>

              <div class="dz-error-message" *ngSwitchCase="PhotoStatus.Error">
                <span> {{ item.errorMessageKey | i18n }} </span>
              </div>

              <button *ngIf="!viewOnly" class="dz-remove" (click)="removePhoto($event, item, i)">
                {{
                  (item.status === PhotoStatus.Success || item.status === PhotoStatus.Error
                    ? 'general.photoUpload.removePhoto'
                    : 'general.photoUpload.cancel'
                  ) | i18n
                }}
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </button>
</ng-template>

<ng-template #desktopUploader>
  <div
    class="photo-uploader"
    [id]="uploadContainer"
    (drop)="onFileDrop($event)"
    (dragover)="onDragLeaveAndOver($event)"
    (dragleave)="onDragLeaveAndOver($event)"
  >
    <ds-action-button *ngIf="!showPhotoUploadBox && !viewOnly" icon="icon-camera" action="add" (buttonClick)="showPhotoUploadBox = true">
      {{ 'photouploader.button.addphoto' | i18n }}
    </ds-action-button>
    <div class="photo-uploader-box" [ngClass]="{ view: viewOnly }" [hidden]="!showPhotoUploadBox">
      <div class="interaction">
        <div class="dropzone" role="region" [attr.aria-label]="'general.photoUpload.dropzone.ariaLabel' | i18n">
          <ds-icon *ngIf="!viewOnly" class="icon-dropzone" name="icon-camera" size="50"></ds-icon>
          <div class="info" [hidden]="viewOnly">
            <div class="message" *ngIf="canAcceptUploads">
              <h2 class="heading">{{ 'general.photoUpload.addPhotos' | i18n }}</h2>
              <p class="description" [innerHTML]="desktopInstructions | safeHtml"></p>
            </div>
            <div class="message" *ngIf="!canAcceptUploads">
              <p class="description blocked">{{ 'general.photoUpload.tooManyFiles' | i18n }}</p>
            </div>
          </div>
          <ul
            *ngIf="photoUploadService.photos.length > 0"
            class="previews"
            id="previewsContainer"
            [attr.aria-label]="'general.a11y.photoUpload.previews.ariaLabel' | i18n"
          >
            <li
              *ngFor="let item of photoUploadService.photos; let i = index"
              class="dz-preview dz-file-preview"
              [class.dz-error]="item.status === PhotoStatus.Error"
              [ngSwitch]="item.status"
            >
              <div class="dz-image" [class.dz-image__loaded]="item.status === PhotoStatus.Success">
                <button
                  (click)="onPreviewClick($event, item, i)"
                  [attr.aria-label]="'general.a11y.photoUpload.previewPhoto' | i18n : { index: i + 1 }"
                >
                  <img *ngIf="!viewOnly && item.status === PhotoStatus.Success" [src]="item.thumbnail?.src" [alt]="getPreviewAlt(i)" />
                  <img *ngIf="viewOnly && item.status === PhotoStatus.Success" [src]="item.thumbnail?.src" [alt]="getPreviewAlt(i)" />
                </button>
                <div class="spinner-container" *ngSwitchCase="PhotoStatus.WaitingForThumbnail">
                  <svg class="spinner" viewBox="25 25 50 50">
                    <circle class="loader" cx="50" cy="50" r="20" fill="none" stroke="#5D5E60" stroke-width="4" />
                  </svg>
                </div>
              </div>

              <div
                *ngSwitchCase="PhotoStatus.Waiting"
                role="progressbar"
                class="dz-progress"
                [attr.aria-valuenow]="item.progress"
                aria-valuemin="0"
                aria-valuemax="100"
              >
                <span class="dz-upload" [style.width.px]="item.progress"></span>
              </div>

              <div class="dz-error-message" *ngSwitchCase="PhotoStatus.Error">
                <span> {{ item.errorMessageKey | i18n }} </span>
              </div>

              <button
                *ngIf="!viewOnly"
                class="dz-remove"
                (click)="removePhoto($event, item, i)"
                [attr.aria-label]="
                  item.status === PhotoStatus.Success || item.status === PhotoStatus.Error
                    ? ('general.a11y.photoUpload.removePhoto' | i18n : { index: i + 1 })
                    : ('general.a11y.photoUpload.cancelUpload' | i18n : { index: i + 1 })
                "
              >
                {{
                  (item.status === PhotoStatus.Success || item.status === PhotoStatus.Error
                    ? 'general.photoUpload.removePhoto'
                    : 'general.photoUpload.cancel'
                  ) | i18n
                }}
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <p class="photo-uploader-description">
      {{ 'general.photoUpload.description' | i18n }}
    </p>
  </div>
</ng-template>

<input
  style="display: none"
  type="file"
  multiple
  #fileInput
  (change)="onFileSelected($event)"
  (cancel)="onInputCancel($event)"
  accept="image/jpeg,image/pjpeg"
  [disabled]="viewOnly"
/>
