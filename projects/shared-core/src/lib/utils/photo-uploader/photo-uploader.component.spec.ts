import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { PhotoUploaderComponent } from './photo-uploader.component';
import { PhotoUploadService, PhotoStatus, PhotoUploadItem } from './photo-upload.service';
import { PhotoUploadApiService } from './photo-upload-api.service';
import { PhotoPreviewService } from '../../form-engine/services/photo-preview.service';
import { ElementRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Subject, of } from 'rxjs';
import { SharedNativePictureSelectionService } from '../../abstract-services/shared-native-picture-selection.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MocksModule } from '@shared-core/testing';
import { PhotoGroup } from '../../generated/models/photo-group';
import { By } from '@angular/platform-browser';

describe('PhotoUploaderComponent', () => {
  let component: PhotoUploaderComponent;
  let fixture: ComponentFixture<PhotoUploaderComponent>;
  let photoUploadServiceMock: any;
  let photoUploadApiMock: any;
  let photoPreviewServiceMock: any;
  let nativePictureSelectionServiceMock: any;
  const photoAlt = 'Photo';
  const initializeSubject = new Subject<boolean>();

  beforeEach(() => {
    const mockPhotoGroups = [
      {
        id: '123',
        photoSets: [
          {
            originalId: 'photo1',
            extraLarge: {
              id: 'extraLarge1',
              height: 800,
              width: 600
            },
            thumbnail: {
              id: 'thumb1',
              height: 100,
              width: 75
            }
          }
        ]
      }
    ];

    photoUploadServiceMock = {
      photos: [],
      initialiseExistingPhotos: jest.fn().mockReturnValue(initializeSubject),
      emitCheckUploadAllow$: new Subject(),
      uploadedPhoto$: new Subject(),
      uploadInProgress: new Subject(),
      patientId: '',
      photoGroup: {},
      stopRetryInterval: jest.fn(),
      checkPhotoGroup: jest.fn().mockReturnValue(of(null)),
      setFiles: jest.fn()
    };

    photoUploadApiMock = {
      removePhoto: jest.fn().mockReturnValue(of({})),
      getPhotoGroup: jest.fn().mockReturnValue(of(mockPhotoGroups))
    };

    photoPreviewServiceMock = {
      openPreview: jest.fn()
    };

    nativePictureSelectionServiceMock = {
      getPicture: jest.fn().mockResolvedValue(null)
    };

    TestBed.configureTestingModule({
      imports: [MocksModule, HttpClientTestingModule],
      declarations: [PhotoUploaderComponent],
      providers: [
        {
          provide: PhotoUploadService,
          useFactory: () => photoUploadServiceMock
        },
        { provide: PhotoUploadApiService, useValue: photoUploadApiMock },
        { provide: PhotoPreviewService, useValue: photoPreviewServiceMock },
        { provide: ElementRef, useValue: { nativeElement: document.createElement('div') } },
        { provide: SharedNativePictureSelectionService, useValue: nativePictureSelectionServiceMock }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(PhotoUploaderComponent);
    component = fixture.componentInstance;
    component.altText = photoAlt;
    component.maximumFileCount = 5;

    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    component.fileInput = { nativeElement: fileInput } as ElementRef;

    (component as any).photoUploadService = photoUploadServiceMock;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('initialization', () => {
    it('should initialize with default values', () => {
      expect(component.canAcceptUploads).toBe(true);
      expect(component.containsPhotos).toBe(false);
      expect(component.viewOnly).toBeUndefined();
    });

    it('should initialize with provided photoGroup', () => {
      jest.spyOn(component, 'initialize');

      const photoGroup: PhotoGroup = {
        id: '123'
      };

      component.photoGroup = photoGroup;

      expect(component.initialize).toHaveBeenCalled();

      expect(component.uploaderPhotoGroup).toEqual(photoGroup);

      expect(photoUploadServiceMock.initialiseExistingPhotos).toHaveBeenCalledWith(component.onlySubmittedPhotos);
    });

    it('should update state when photos are initialized', () => {
      const photoGroup: PhotoGroup = {
        id: '123'
      };
      component.photoGroup = photoGroup;
      component.initialize();
      initializeSubject.next(true);

      expect(component.showPhotoUploadBox).toBe(true);
      expect(component.canAcceptUploads).toBe(true);
    });
  });

  describe('getPreviewAlt', () => {
    it('should return correct alt text based on number of photos', () => {
      const componentService = component['photoUploadService'];
      componentService.photos = [{ id: 'photo1', status: PhotoStatus.Success }];

      expect(component.getPreviewAlt(0)).toBe(photoAlt);

      componentService.photos = [
        { id: 'photo1', status: PhotoStatus.Success },
        { id: 'photo2', status: PhotoStatus.Success }
      ];

      expect(component.getPreviewAlt(0)).toBe(`${photoAlt} 1`);
      expect(component.getPreviewAlt(1)).toBe(`${photoAlt} 2`);
    });
  });

  describe('file selection', () => {
    it('should call checkPhotoGroup and setFiles when files are selected', () => {
      const files = [new File(['content'], 'test.jpg', { type: 'image/jpeg' })];
      const event = { target: { files, value: null } } as any;

      component.onFileSelected(event);

      expect(photoUploadServiceMock.checkPhotoGroup).toHaveBeenCalled();
      expect(photoUploadServiceMock.setFiles).toHaveBeenCalledWith(files);
    });

    it('should handle native service picture selection', fakeAsync(() => {
      const blob = new Blob(['test'], { type: 'image/jpeg' });
      nativePictureSelectionServiceMock.getPicture.mockResolvedValue(blob);
      photoUploadServiceMock.checkPhotoGroup.mockReturnValue(of({ id: 'new-group' }));
      jest.spyOn(component.photoGroupChange, 'emit');
      jest.spyOn(component.containsPhotosChange, 'emit');

      (component as any).selectFileViaNativeService();
      tick();

      expect(nativePictureSelectionServiceMock.getPicture).toHaveBeenCalled();
      expect(photoUploadServiceMock.checkPhotoGroup).toHaveBeenCalled();
      expect(component.photoGroupChange.emit).toHaveBeenCalledWith({ id: 'new-group' });
      expect(component.containsPhotosChange.emit).toHaveBeenCalledWith(true);
      expect(photoUploadServiceMock.setFiles).toHaveBeenCalledWith([blob]);
    }));

    it('should open file browser when not using native service', () => {
      jest.spyOn(component, 'openFileWithBrowser');
      nativePictureSelectionServiceMock.getPicture.mockRejectedValue('Only supported in native context!');

      (component as any).selectFileViaNativeService();

      setTimeout(() => {
        expect(component.openFileWithBrowser).toHaveBeenCalled();
      }, 0);
    });

    it('should limit number of files based on maximumFileCount', () => {
      component.maximumFileCount = 3;
      photoUploadServiceMock.photos = [
        { id: 'photo1', status: PhotoStatus.Success },
        { id: 'photo2', status: PhotoStatus.Success }
      ];

      const files = [
        new File(['content1'], 'test1.jpg', { type: 'image/jpeg' }),
        new File(['content2'], 'test2.jpg', { type: 'image/jpeg' }),
        new File(['content3'], 'test3.jpg', { type: 'image/jpeg' })
      ];

      (component as any).setFiles(files);

      expect(photoUploadServiceMock.setFiles).toHaveBeenCalledWith([files[0]]);
    });
  });

  describe('drag and drop', () => {
    it('should handle file drop when canAcceptUploads is true', () => {
      jest.spyOn(component as any, 'setFiles');
      component.canAcceptUploads = true;

      const file = new File(['content'], 'test.jpg', { type: 'image/jpeg' });
      const event = {
        dataTransfer: { files: [file] },
        preventDefault: jest.fn(),
        stopPropagation: jest.fn()
      } as any;

      component.onFileDrop(event);

      expect(component['setFiles']).toHaveBeenCalledWith([file]);
      expect(event.preventDefault).toHaveBeenCalled();
      expect(event.stopPropagation).toHaveBeenCalled();
    });

    it('should not process file drop when canAcceptUploads is false', () => {
      jest.spyOn(component as any, 'setFiles');
      component.canAcceptUploads = false;

      const event = {
        dataTransfer: { files: [new File(['content'], 'test.jpg', { type: 'image/jpeg' })] },
        preventDefault: jest.fn(),
        stopPropagation: jest.fn()
      } as any;

      component.onFileDrop(event);

      expect(component['setFiles']).not.toHaveBeenCalled();
    });

    it('should prevent default on drag over/leave events', () => {
      const event = {
        preventDefault: jest.fn(),
        stopPropagation: jest.fn()
      } as any;

      component.onDragLeaveAndOver(event);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(event.stopPropagation).toHaveBeenCalled();
    });
  });

  describe('photo preview', () => {
    it('should open preview when clicking on a photo', () => {
      const item = { id: 'photo1', preview: { src: 'preview-url', w: 100, h: 100 }, status: PhotoStatus.Success } as PhotoUploadItem;
      photoUploadServiceMock.photos = [item];

      jest.spyOn(component.openPreview, 'next');

      const event = { stopPropagation: jest.fn() } as any;
      component.onPreviewClick(event, item, 0);

      expect(event.stopPropagation).toHaveBeenCalled();
      expect(component.openPreview.next).toHaveBeenCalled();
      expect(photoPreviewServiceMock.openPreview).toHaveBeenCalled();
    });
  });

  describe('photo removal', () => {
    it('should handle photo removal correctly', () => {
      const photo: PhotoUploadItem = {
        id: 'photo1',
        status: PhotoStatus.Success,
        sub: { unsubscribe: jest.fn() }
      } as any;

      photoUploadServiceMock.photos = [photo];
      component.uploaderPhotoGroup = { id: 'group1', photosToRemove: [] };

      jest.spyOn(component.photoGroupChange, 'emit');
      jest.spyOn(component as any, 'updateContainsPhotosFlag');

      const event = {
        preventDefault: jest.fn(),
        stopPropagation: jest.fn()
      } as any;

      component.removePhoto(event, photo, 0);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(event.stopPropagation).toHaveBeenCalled();
      expect(photo.sub.unsubscribe).toHaveBeenCalled();
      expect(photoUploadApiMock.removePhoto).toHaveBeenCalledWith(photo);
      expect(component.photoGroupChange.emit).toHaveBeenCalledWith({
        id: 'group1',
        photosToRemove: ['photo1']
      });
    });
  });

  describe('client detection', () => {
    it('should detect mobile client correctly', () => {
      const originalUserAgent = navigator.userAgent;
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X)',
        configurable: true
      });

      (component as any).setClientIsMobileFlag();

      expect(component.clientIsMobile).toBe(true);

      Object.defineProperty(navigator, 'userAgent', {
        value: originalUserAgent,
        configurable: true
      });
    });
  });

  describe('state updates', () => {
    it('should update canAcceptUploads based on photo count', () => {
      component.maximumFileCount = 3;
      photoUploadServiceMock.photos = [
        { id: 'photo1', status: PhotoStatus.Success },
        { id: 'photo2', status: PhotoStatus.Success },
        { id: 'photo3', status: PhotoStatus.Success }
      ];

      (component as any).updateUploaderState();

      expect(component.canAcceptUploads).toBe(false);
      expect(component.containsPhotos).toBe(true);
    });

    it('should handle upload state updates from service', () => {
      jest.spyOn(component as any, 'updateUploaderState');

      photoUploadServiceMock.emitCheckUploadAllow$.next(null);

      expect(component['updateUploaderState']).toHaveBeenCalled();
    });

    it('should update photo status when uploadedPhoto$ emits', () => {
      const photo = { id: 'photo1', status: PhotoStatus.Waiting, thumbnail: null };
      photoUploadServiceMock.photos = [photo];

      photoUploadServiceMock.uploadedPhoto$.next({
        id: 'photo1',
        status: PhotoStatus.Success,
        thumbnail: 'thumbnail-url'
      });

      expect(photo.status).toBe(PhotoStatus.Success);
      expect(photo.thumbnail).toBe('thumbnail-url');
    });
  });

  describe('file upload errors', () => {
    const errorMessageKey = 'general.photoUpload.error.invalidFile';

    it('should render error message for failed upload in desktop view', () => {
      component.clientIsMobile = false;
      photoUploadServiceMock.photos = [
        {
          id: 'errorPhoto',
          status: PhotoStatus.Error,
          errorMessageKey: errorMessageKey
        }
      ];

      fixture.detectChanges();

      const errorPreview = fixture.debugElement.query(By.css('.dz-preview.dz-error'));

      expect(errorPreview).toBeTruthy();

      const errorMessage = fixture.debugElement.query(By.css('.dz-error-message span'));

      expect(errorMessage).toBeTruthy();
      expect(errorMessage.nativeElement.textContent.trim()).toBe(errorMessageKey);
    });

    it('should render error message for failed upload in mobile view', () => {
      component.clientIsMobile = true;
      fixture.detectChanges();

      photoUploadServiceMock.photos = [
        {
          id: 'errorPhoto',
          status: PhotoStatus.Error,
          errorMessageKey: errorMessageKey
        }
      ];

      fixture.detectChanges();

      const errorPreview = fixture.debugElement.query(By.css('.dz-preview.dz-error'));

      expect(errorPreview).toBeTruthy();

      const errorMessage = fixture.debugElement.query(By.css('.dz-error-message span'));

      expect(errorMessage).toBeTruthy();
      expect(errorMessage.nativeElement.textContent.trim()).toBe(errorMessageKey);
    });

    it('should add dz-error class to preview element for failed uploads', () => {
      photoUploadServiceMock.photos = [
        {
          id: 'errorPhoto',
          status: PhotoStatus.Error,
          errorMessageKey: errorMessageKey
        }
      ];

      fixture.detectChanges();

      const previewElement = fixture.debugElement.query(By.css('.dz-preview'));

      expect(previewElement.classes['dz-error']).toBe(true);
    });

    it('should show remove button for photos with error status', () => {
      photoUploadServiceMock.photos = [
        {
          id: 'errorPhoto',
          status: PhotoStatus.Error,
          errorMessageKey: errorMessageKey,
          sub: { unsubscribe: jest.fn() }
        }
      ];

      fixture.detectChanges();

      const removeButton = fixture.debugElement.query(By.css('.dz-remove'));

      expect(removeButton).toBeTruthy();
      expect(removeButton.nativeElement.textContent.trim()).toBe('general.photoUpload.removePhoto');

      const event = {
        preventDefault: jest.fn(),
        stopPropagation: jest.fn()
      };

      component.uploaderPhotoGroup = { id: 'group1', photosToRemove: [] };
      removeButton.triggerEventHandler('click', event);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(event.stopPropagation).toHaveBeenCalled();
      expect(photoUploadServiceMock.photos).toHaveLength(0);
    });

    it('should display the correct translated error message', () => {
      const customErrorKey = 'general.photoUpload.error.fileTooLarge';

      photoUploadServiceMock.photos = [
        {
          id: 'errorPhoto',
          status: PhotoStatus.Error,
          errorMessageKey: customErrorKey
        }
      ];

      fixture.detectChanges();

      const errorMessage = fixture.debugElement.query(By.css('.dz-error-message span'));

      expect(errorMessage.nativeElement.textContent.trim()).toBe(customErrorKey);
    });
  });

  describe('file selection dialog', () => {
    it('should not open file dialog when canAcceptUploads is false', () => {
      component.canAcceptUploads = false;
      jest.spyOn(component as any, 'selectFileViaNativeService');
      jest.spyOn(component, 'openFileWithBrowser');

      component.openFileSelectDialog();

      expect(component['selectFileViaNativeService']).not.toHaveBeenCalled();
      expect(component.openFileWithBrowser).not.toHaveBeenCalled();
    });

    it('should select file method based on client type', () => {
      component.canAcceptUploads = true;
      jest.spyOn(component as any, 'selectFileViaNativeService');
      jest.spyOn(component, 'openFileWithBrowser');

      component.clientIsMobile = true;
      component.openFileSelectDialog();

      expect(component['selectFileViaNativeService']).toHaveBeenCalled();

      jest.clearAllMocks();
      component.clientIsMobile = false;
      component.openFileSelectDialog();

      expect(component.openFileWithBrowser).toHaveBeenCalled();
    });

    it('should click the file input when openFileWithBrowser is called', () => {
      const clickSpy = jest.spyOn(component.fileInput.nativeElement, 'click');

      component.openFileWithBrowser();

      expect(clickSpy).toHaveBeenCalled();
    });
  });

  describe('ngAfterViewInit', () => {
    it('should add view class when viewOnly is true', () => {
      component.viewOnly = true;
      const mockElement = document.createElement('div');
      (component as any).$element = mockElement;

      const addClassSpy = jest.spyOn(mockElement.classList, 'add');

      component.ngAfterViewInit();

      expect(addClassSpy).toHaveBeenCalledWith('view');
    });

    it('should set up event listeners for browse link', () => {
      const browseLink = document.createElement('a');
      browseLink.id = 'browse-file-link';
      document.body.appendChild(browseLink);

      jest.spyOn(component, 'openFileSelectDialog');

      component.ngAfterViewInit();

      browseLink.click();

      expect(component.openFileSelectDialog).toHaveBeenCalled();

      jest.clearAllMocks();
      const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      browseLink.dispatchEvent(enterEvent);

      expect(component.openFileSelectDialog).toHaveBeenCalled();

      document.body.removeChild(browseLink);
    });
  });

  describe('ngOnDestroy', () => {
    it('should call stopRetryInterval and complete subjects', () => {
      jest.spyOn(component.destroy$, 'next');
      jest.spyOn(component.destroy$, 'complete');

      component.ngOnDestroy();

      expect(photoUploadServiceMock.stopRetryInterval).toHaveBeenCalled();
      expect(component.uploaderPhotoGroup).toBeUndefined();
      expect(component.destroy$.next).toHaveBeenCalled();
      expect(component.destroy$.complete).toHaveBeenCalled();
    });
  });
});
