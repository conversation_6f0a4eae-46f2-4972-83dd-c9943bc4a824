import { Injectable } from '@angular/core';
import { ConfigurationProviderService } from '../../abstract-services/configuration-provider.service';
import { SharedApi } from '../../abstract-services/shared-api.service';

import urlJoin from 'url-join';
import { PhotoScale } from '../../generated/models/photo-scale';
import { Observable } from 'rxjs/index';
import { PhotoMetadata } from '../../generated/models/photo-metadata';
import { PhotoUploadItem } from './photo-upload.service';
import { PhotoGroup } from '../../generated/models/photo-group';
import { PhotoGroupModel } from '../../generated/models/photo-group-model';
import { HttpClient } from '@angular/common/http';
@Injectable({
    providedIn: 'root',
})
export class PhotoUploadApiService {
    public targetUrl;

    constructor(
        private configService: ConfigurationProviderService,
        private noonaApi: SharedApi,
        private http: HttpClient
    ) {
        const base = this.configService.baseUrl();
        this.targetUrl = urlJoin(base, 'photo', this.configService.site());
    }

    getMetadata(item: PhotoUploadItem, photoGroup: PhotoGroup, scale: PhotoScale): Observable<PhotoMetadata> {
        return this.noonaApi.medicalApi.getPhotoMetadata1(item.id, photoGroup, scale);
    }

    createPhotoGroup(patientId): Observable<PhotoGroup> {
        return patientId
            ? this.noonaApi.medicalApi.createPhotoGroup(patientId)
            : this.noonaApi.medicalApi.createPhotoGroup();
    }

    removePhoto(item: PhotoUploadItem): Observable<boolean> {
        return this.noonaApi.medicalApi.removePhoto(item.id);
    }

    getPhotoGroup(onlySubmittedPhotos: boolean, photoGroup: PhotoGroup): Observable<PhotoGroupModel[]> {
        return this.noonaApi.medicalApi.getPhotoMetadata([photoGroup.id], onlySubmittedPhotos);
    }

    uploadPhoto(item: PhotoUploadItem, photoGroup: PhotoGroup): Observable<any> {
        const formData = new FormData();

        formData.append('photoGroup', photoGroup.id);
        formData.append('file', item.file);

        return this.http.post(this.targetUrl, formData, {
            reportProgress: true,
            observe: 'events',
        });
    }
}
