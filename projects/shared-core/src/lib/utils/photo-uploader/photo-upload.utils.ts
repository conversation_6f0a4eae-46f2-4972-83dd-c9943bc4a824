import { delayWhen, Observable, timer } from 'rxjs';
import { map, retryWhen } from 'rxjs/operators';

export const genericRetryStrategy = ({
    retries = 100,
    scalingDuration = 1000,
}: {
    retries?: number;
    scalingDuration?: number;
} = {}) => {
    return (source: Observable<any>) => {
        return source.pipe(
            map((res) => {
                if (retries-- <= 0 || !!res) {
                    return res;
                } else {
                    throw res;
                }
            }),
            retryWhen((errors) => {
                return errors.pipe(
                    delayWhen((val) => {
                        return timer(scalingDuration);
                    })
                );
            })
        );
    };
};
