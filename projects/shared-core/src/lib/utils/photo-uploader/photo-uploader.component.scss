@import '../../../styles/deprecated_mixins.scss';
@import '../../../styles/deprecated_variables.scss';
@import '../../form-engine/styles/sprite.scss';
@import '../../../styles/ds-variables.scss';

button.photo-uploader {
  width: 100%;
  height: 100%;
}

.photo-uploader {
  .spinner-container {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.2);
    cursor: not-allowed;
    pointer-events: none;
  }

  .spinner {
    position: absolute;
    top: 45px;
    left: 45px;
    width: 36px;
    height: 36px;
    animation: spin 1.5s linear 0s infinite;
  }

  .loader {
    stroke-dasharray: 150, 200;
    stroke-dashoffset: -10;
    stroke-linecap: round;
    animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;
  }

  .photo-uploader-box {
    .dz-message {
      display: none;
    }

    .interaction {
      height: 100%;

      .dropzone {
        background: var(--color-grey-lighten-5, $gray-very-light);
        border: 2px dashed var(--color-grey, $gray);
        border-radius: 5px;
        height: 100%;
        position: relative;
        padding: 0;

        @media screen and ($media-max-phone) {
          min-height: 200px;
        }

        @media screen and ($media-min-tablet) {
          min-height: 200px;
        }

        .icon-dropzone {
          display: flex;
          margin-top: $spacing-xl;
          margin-bottom: $spacing-xxs;
          fill: var(--color-grey-darken-2, $gray-mid-dark);
        }

        .previews {
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          background: rgba(0, 0, 0, 0.04);
          border-radius: 5px;
          margin: 0 $spacing-l $spacing-l;
          padding: $spacing-m $spacing-xs;

          .dz-preview {
            background: transparent;
            margin: $spacing-xs;

            &.loading {
              cursor: not-allowed;
              pointer-events: none;
            }

            .dz-image__loaded {
              background: transparent;
            }

            .dz-image {
              width: 130px;
              height: 130px;
              border-radius: 0;

              button {
                cursor: pointer;
                width: 100%;
                height: 100%;
              }

              img {
                width: 100%;
                height: 100%;
                max-width: 130px;
                max-height: 130px;
                object-fit: contain;
                background-color: var(--color-black);
              }
            }

            &:hover .dz-image img {
              filter: unset;
            }

            .dz-error-mark {
              .icon-error {
                height: 50px;
                width: 50px;
                @extend .svg-icon-cancel;
              }
            }

            .dz-remove {
              margin: 0 auto;
              cursor: pointer;
              pointer-events: all;
              color: var(--color-primary);
              font-weight: bold;
              text-decoration: underline;
            }
          }
        }

        .message {
          text-align: center;
          color: var(--color-grey-darken-2, $gray-mid-dark);

          .description {
            font-size: 18px;
            color: var(--color-grey, $gray);
            padding-bottom: $spacing-l;

            a {
              color: var(--color-primary);
              font-weight: bold;
              text-decoration: underline;
            }
          }

          &.contains-images {
            .heading {
              @media screen and ($media-min-tablet) {
                font-size: 18px;
              }
            }
          }
        }

        &.dz-drag-hover {
          background: $brand-n-very-light-turquoise;
          border-color: var(--color-primary);

          .message {
            .heading {
              font-size: 18px;
            }
          }
        }

        .dz-error {
          .dz-image {
            border: 2px solid $brand-n-pink;
            background: opacify($brand-n-pink, 0.5);
          }
        }

        .dz-error-message {
          opacity: 1;
          border-radius: 0;
          background: $brand-n-pink;
          @include font-light();
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 164px;

          &:after {
            display: none;
          }
        }

        .dz-error-mark {
          display: none;
        }
      }
    }

    &.blocked {
      cursor: not-allowed;

      .photo-uploader {
        pointer-events: none;

        .interaction {
          .dropzone {
            background: repeating-linear-gradient(45deg, $gray-light, $gray-light 10px, $gray-very-light 10px, $gray-very-light 20px);

            .previews {
              .dz-preview {
                pointer-events: all;
                cursor: pointer;

                &.loading {
                  cursor: not-allowed;
                  pointer-events: none;
                }
              }
            }

            .message {
              .description {
                span {
                  cursor: not-allowed;
                }

                &.blocked {
                  color: var(--color-grey-darken-2, $gray-mid-dark);
                }
              }
            }
          }
        }
      }
    }

    &.view {
      cursor: not-allowed;

      .photo-uploader {
        pointer-events: none;

        .interaction {
          .dropzone {
            background: white;
            border: 0;
            min-height: 0;

            .icon-dropzone {
              display: none;
            }

            .previews {
              margin: 0;

              .dz-preview {
                pointer-events: all;
                cursor: pointer;

                img {
                  min-width: 100%;
                  min-height: 100%;
                  max-width: none;
                  max-height: none;
                  width: auto;
                  height: auto;
                }
              }

              .dz-remove {
                display: none;
              }
            }
          }
        }
      }
    }
  }

  .photo-uploader-description {
    color: $body-text-color;
    font-size: var(--font-size-l);
    font-style: normal;
    font-weight: var(--font-weight-regular);
    line-height: var(--body-large-line-height);
    margin: var(--spacing-m) 0;
  }
}
