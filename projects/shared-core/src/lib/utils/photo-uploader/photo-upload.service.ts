import { HttpEvent, HttpEventType, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, EventEmitter, Injectable, OnDestroy } from '@angular/core';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { BehaviorSubject, Observable, of, Subject, Subscription } from 'rxjs';
import { catchError, concatMap, delayWhen, filter, map, mapTo, mergeMap, takeUntil, tap } from 'rxjs/operators';
import { PhotoGroup } from '../../generated/models/photo-group';
import { PhotoGroupModel } from '../../generated/models/photo-group-model';
import { PhotoMetadata } from '../../generated/models/photo-metadata';
import { PhotoScale } from '../../generated/models/photo-scale';
import { PhotoSet } from '../../generated/models/photo-set';
import { Status } from '../../generated/models/status';
import { BlobInformation } from '../services/blob-information';
import { BlobUrlService } from '../services/blob-url.service';
import { PhotoUploadApiService } from './photo-upload-api.service';

import { genericRetryStrategy } from './photo-upload.utils';

const MAX_FILE_SIZE = 6 * 1024 * 1024; // 6 MB
const MAX_RETRY_ATTEMPTS = 3;

export enum PhotoStatus {
    Waiting = 'Waiting',
    WaitingForThumbnail = 'WaitingForThumbnail',
    Success = 'Success',
    Error = 'Error',
}

export interface PhotoUploadItem {
    file?: File;
    id?: string;
    errorMessageKey?: string;
    thumbnail?: {
        src: SafeUrl;
        w: number;
        h: number;
    };
    preview?: PhotoUploadPreview;
    sub?: Subscription;
    status?: PhotoStatus;
    progress?: number; // between 0 and 100;
    retryAttempts?: number;
}

interface PhotoUploadPreview {
    src: string;
    w: number;
    h: number;
}

// service provided in component, not root injectable
@Injectable()
export class PhotoUploadService implements OnDestroy {
    private readonly targetUrl: string;
    public uploadInProgress = new EventEmitter<boolean>();

    public photos: PhotoUploadItem[] = []; // component template usage
    private retryIntervalId: number;

    public photoGroup: PhotoGroup;
    public patientId: string;

    private destroy$ = new Subject<void>();
    emitCheckUploadAllow$ = new BehaviorSubject(null);

    private isAndroid: boolean;

    private uploadedPhotoSub = new BehaviorSubject<PhotoUploadItem>(null);
    uploadedPhoto$ = this.uploadedPhotoSub.asObservable();

    constructor(
        private blobUrlService: BlobUrlService,
        private sanitizer: DomSanitizer,
        private api: PhotoUploadApiService,
        private changeDetectorRef: ChangeDetectorRef
    ) {
        this.targetUrl = api.targetUrl;
        this.isAndroid = this.detectAndroid();
    }

    private detectAndroid(): boolean {
        const userAgent = navigator.userAgent.toLowerCase();
        return /android/.test(userAgent);
    }

    private startRetryInterval(): void {
        if (!this.isAndroid) return;
        this.retryIntervalId = setInterval(() => {
            this.retryPendingUploads();
        }, 8000) as unknown as number;
    }

    public retryPendingUploads(): void {
        const pendingUploads = this.photos.filter((photo) => {
            return (
                (photo.status === PhotoStatus.Waiting || photo.status === PhotoStatus.WaitingForThumbnail) &&
                (photo.retryAttempts || 0) < MAX_RETRY_ATTEMPTS
            );
        });

        if (pendingUploads.length > 0) {
            pendingUploads.forEach((photo) => {
                const photoIndex = this.photos.findIndex((photoItem) => photoItem.id === photo.id);
                const retryAttempts = (photo.retryAttempts || 0) + 1;
                // we need to remove pending uploaded photo to avoid it being duplicated
                this.removePhoto(photo, photoIndex).subscribe(() => {
                    this.photos = [...this.photos, this.createAndUploadPhotoItem(photo.file, retryAttempts)];
                });
            });
        }

        if (this.shouldStopRetryInterval()) {
            this.stopRetryInterval();
            this.changeDetectorRef.detectChanges();
        }
    }

    private createAndUploadPhotoItem(file: File, retryAttempts = 0): PhotoUploadItem {
        const uploadItem: PhotoUploadItem = { file, status: PhotoStatus.Waiting, progress: 0, retryAttempts };
        const isFileTooBig = file.size > MAX_FILE_SIZE;
        if (isFileTooBig) {
            this.handleErrorFileTooBig(uploadItem);
        } else {
            uploadItem.sub = this.uploadPhoto(uploadItem).subscribe((data) => {
                if (uploadItem.retryAttempts >= MAX_RETRY_ATTEMPTS && uploadItem.status !== PhotoStatus.Success) {
                    this.setPhotoError(uploadItem);
                }
            });
        }
        return uploadItem;
    }

    public removePhoto(photo: PhotoUploadItem, photoIndex: number): Observable<boolean> {
        photo.sub?.unsubscribe();
        this.photos.splice(photoIndex, 1);
        return photo.id || photo.status !== PhotoStatus.Waiting ? this.api.removePhoto(photo) : of(false);
    }

    public stopRetryInterval(): void {
        if (this.retryIntervalId) {
            clearInterval(this.retryIntervalId);
            this.retryIntervalId = null;
        }
    }

    private setPhotoError(photo: PhotoUploadItem): void {
        photo.status = PhotoStatus.Error;
        photo.errorMessageKey = 'general.photoUpload.serverError';
        this.uploadedPhotoSub.next(photo);
        this.updateUploadStatus();
    }

    private shouldStopRetryInterval(): boolean {
        return (
            this.photos.length === 0 ||
            (this.photos.length > 0 &&
                this.photos.every((photo) => {
                    return photo.status === PhotoStatus.Success || (photo.retryAttempts || 0) >= MAX_RETRY_ATTEMPTS;
                }))
        );
    }

    public initialiseExistingPhotos(onlySubmittedPhotos: boolean): Observable<any> {
        const existingPhotosIds = this.photos.reduce((ids, photo) => photo.id ? [...ids, photo.id] : ids, []);

        if (existingPhotosIds && existingPhotosIds.length > 0) {
            this.photos = this.photos.filter((photo) => {
                return !this.photoGroup.photosToRemove?.includes(photo.id);
            });
        }

        return this.api.getPhotoGroup(onlySubmittedPhotos, this.photoGroup).pipe(
            mergeMap((photoGroups: PhotoGroupModel[]) => {
                const photoGroupModels = photoGroups && photoGroups.length > 0 ? photoGroups : [];
                const allPhotoSets = photoGroupModels.flatMap((group) => group.photoSets as unknown as PhotoSet[]);
                const { photosToRemove } = this.photoGroup;
                const nonRemovalPhotoSets = allPhotoSets.filter(
                    (photoSet) => !photosToRemove?.includes(photoSet.originalId)
                );
                return nonRemovalPhotoSets;
            }),
            concatMap((photoSet: PhotoSet) => {
                if (!photoSet) {
                    return of(null);
                }
                const uploadedPhoto: PhotoUploadItem = {
                    id: photoSet.originalId,
                    status: PhotoStatus.WaitingForThumbnail,
                    preview: this.setPreview(photoSet.extraLarge),
                };
                const existingPhotosIds = this.photos.map((photo) => photo.id);
                const isNewPhoto =
                    !existingPhotosIds.includes(uploadedPhoto.id) &&
                    !this.photos.find((photo) => photo.id === uploadedPhoto.id);
                if (isNewPhoto) {
                    this.photos.push(uploadedPhoto);
                }

                this.updateUploadStatus();
                this.emitCheckUploadAllow$.next(null);
                return this.setThumbnail(uploadedPhoto, photoSet.thumbnail);
            }),
            catchError((error) => {
                if (typeof error === 'string' && error.includes('NoonaApiError') && this.photos?.length > 0) {
                    this.photos.forEach((photoItem) => {
                        if (photoItem.status === PhotoStatus.Waiting) {
                            photoItem.status = PhotoStatus.Error;
                            photoItem.errorMessageKey = 'general.photoUpload.serverError';
                            this.uploadedPhotoSub.next(photoItem);
                        }
                    });
                }
                return of(error);
            })
        );
    }

    // Create photo group if not exists
    public checkPhotoGroup(): Observable<PhotoGroup> {
        return this.photoGroup
            ? of(this.photoGroup)
            : this.api.createPhotoGroup(this.patientId).pipe(
                  tap((group) => {
                      if (group) {
                          this.photoGroup = group;
                      }
                  })
              );
    }

    private getMetadata(item: PhotoUploadItem): Observable<PhotoMetadata> {
        return this.api.getMetadata(item, this.photoGroup, PhotoScale.EXTRA_LARGE).pipe(
            genericRetryStrategy({
                retries: 120,
            }),
            tap((metadata) => {
                return (item.preview = this.setPreview(metadata));
            }),
            takeUntil(this.destroy$)
        );
    }

    private setThumbnail(item: PhotoUploadItem, metadata: PhotoMetadata): Observable<BlobInformation | undefined> {
        if (!metadata) {
            return of();
        }
        return this.blobUrlService.getBlobUrl(`${this.targetUrl}?photoId=${metadata.id}`).pipe(
            tap((blob) => {
                item.thumbnail = {
                    src: this.sanitizer.bypassSecurityTrustUrl(blob.url),
                    h: metadata.height,
                    w: metadata.width,
                };
                item.status = PhotoStatus.Success;
                this.uploadedPhotoSub.next(item);
                this.changeDetectorRef.detectChanges();
                this.updateUploadStatus();
            })
        );
    }

    private getThumbnail(item: PhotoUploadItem): Observable<PhotoMetadata> {
        return this.api.getMetadata(item, this.photoGroup, PhotoScale.THUMBNAIL).pipe(
            genericRetryStrategy({
                retries: 10,
            }),
            delayWhen((metadata) => {
                return this.setThumbnail(item, metadata);
            }),
            takeUntil(this.destroy$)
        );
    }

    private uploadPhoto(item: PhotoUploadItem): Observable<void> {
        return this.api.uploadPhoto(item, this.photoGroup).pipe(
            tap((event) => {
                this.setProgress(event, item);
            }),
            filter((event) => {
                return event.type === HttpEventType.Response;
            }),
            map((response: HttpResponse<any>) => {
                return response.body;
            }),
            tap((response) => {
                item.id = response.photoId;
                item.status = PhotoStatus.WaitingForThumbnail;
                this.uploadedPhotoSub.next(item);
            }),
            mergeMap(() => {
                return this.getMetadata(item).pipe(
                    catchError(() => {
                        return of(null);
                    })
                );
            }),
            mergeMap((metadata) => {
                if (!metadata || metadata.status === Status.FAILED) {
                    this.handleServerError(item);
                    this.updateUploadStatus();
                    return this.api.removePhoto(item);
                } else {
                    return this.getThumbnail(item);
                }
            }),
            tap(() => {
                this.emitCheckUploadAllow$.next(null);
                if (item.status === PhotoStatus.Success && this.shouldStopRetryInterval()) {
                    this.stopRetryInterval();
                    this.uploadedPhotoSub.next(item);
                }
            }),
            mapTo(void 0)
        );
    }

    private setProgress(event: HttpEvent<any>, item: PhotoUploadItem) {
        if (event.type === HttpEventType.UploadProgress) {
            item.progress = Math.round(100 * (event.loaded / event.total));
        }
    }

    public setFiles(list: File[]): void {
        this.startRetryInterval();
        list.forEach((file) => {
            const uploadItem: PhotoUploadItem = {
                file,
                status: PhotoStatus.Waiting,
                progress: 0,
            };

            if (file.size > MAX_FILE_SIZE) {
                this.handleErrorFileTooBig(uploadItem);
            } else {
                uploadItem.sub = this.uploadPhoto(uploadItem).subscribe();
            }
            this.photos.push(uploadItem);
            this.updateUploadStatus();
            this.emitCheckUploadAllow$.next(null);
        });
    }

    private handleErrorFileTooBig(item: PhotoUploadItem): void {
        item.status = PhotoStatus.Error;
        item.errorMessageKey = 'general.photoUpload.fileTooBig';
        this.uploadedPhotoSub.next(item);
    }

    private handleServerError(item: PhotoUploadItem): void {
        item.status = PhotoStatus.Error;
        item.errorMessageKey = 'general.photoUpload.serverError';
        this.uploadedPhotoSub.next(item);
    }

    private setPreview(meta: PhotoMetadata): PhotoUploadPreview {
        return {
            src: `${this.targetUrl}?photoId=${meta?.id}`,
            h: meta?.height,
            w: meta?.width,
        };
    }

    private updateUploadStatus() {
        const isUploadInProgress = this.photos.some((item) => {
            return item.status === PhotoStatus.Waiting || item.status === PhotoStatus.WaitingForThumbnail;
        });
        this.uploadInProgress.emit(isUploadInProgress);
    }

    ngOnDestroy() {
        this.stopRetryInterval();
        this.destroy$.next();
        this.destroy$.complete();
        this.emitCheckUploadAllow$.complete();
        this.photos.forEach((item) => {
            return item.sub?.unsubscribe();
        });
    }
}
