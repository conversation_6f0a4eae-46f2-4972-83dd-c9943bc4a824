import { ChangeDetectionStrategy, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import lodashIsNaN from 'lodash/isNaN';
import isNil from 'lodash/isNil';
import { gradientSteps } from './wellbeing-gradient';

const defaultColor = '#EFEFEF';

/*
Component for displaying wellbeing numbers with correct color.
By default displays rounded circle with coloured bg.
Passing [invert]="true" shows only coloured text without circle.
*/
@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'wellbeing-indicator',
    styleUrls: ['./wellbeing-indicator.scss'],
    template: `
        <div class="wellbeing-indicator" [ngStyle]="style" [ngClass]="{ invert: invert }">
            {{ value !== undefined && value !== null ? value : '?' }}
        </div>
    `,
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WellbeingIndicatorComponent implements OnChanges {
    @Input() value: number;
    @Input() invert = false;
    @Input() invertDirection = false;
    @Input() roundDecimals = false;

    public color = defaultColor;

    ngOnChanges(changes: SimpleChanges): void {
        if (lodashIsNaN(this.value)) {
            this.color = defaultColor;
        }

        if (changes.value && !isNil(this.value)) {
            // Update color
            const index = this.invertDirection ? Math.floor(100 - this.value * 10) : Math.floor(this.value * 10);
            this.color = gradientSteps[index] || defaultColor;
            if (this.roundDecimals) {
                this.value = Math.round(changes.value.currentValue);
            }
        }
    }

    public get style() {
        const color = this.invert ? this.color : 'white';
        const backgroundColor = this.invert ? 'transparent' : this.color;

        return {
            color,
            backgroundColor,
        };
    }
}
