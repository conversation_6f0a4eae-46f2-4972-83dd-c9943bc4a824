@import '../../../styles/deprecated_mixins.scss';
@import '../../../styles/deprecated_variables.scss';
:root {
  display: inline-block;
}
.wellbeing-indicator {
  @include text-heading-lg();
  line-height: 1;
  color: white;
  &:not(.invert) {
    @include flex-layout(column, center, center);
    text-align: center;
    border-radius: 50%;
    font-weight: normal;
    overflow: hidden;
    text-decoration: none;
    width: $button-round-small-width;
    height: $button-round-small-width;
    user-select: none;
    .noona-icon {
      font-size: 30px;
    }
  }
}
.timeline-diary-entry-overall-wellness .wellbeing-indicator {
  text-shadow: rgba(0, 0, 0, 0.2) 0px 0px 2px;
}
