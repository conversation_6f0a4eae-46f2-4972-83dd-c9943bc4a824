import * as tinygradient_ from 'tinygradient';
const tinygradient = ((tinygradient_ as any).default || tinygradient_) as tinygradient_.Constructor;

const gradientColors = [
    '#fa7078', // red
    '#f4a84a',
    '#f4d94a',
    '#8cbf70',
    '#5fda6e', // green
];

const gradient = tinygradient(gradientColors);

// array[0..100] of gradient colors (from red to green)
export const gradientSteps = gradient
    .rgb(101)

    // map tinycolor objects to strings
    .map((color) => {
        return color.toRgbString();
    });
