<div *ngIf="errors" role="alert" class="error-indicator" [@enterAnimation]>
  <ng-container *ngIf="!errors.required && !customKey; else requiredFieldError">
    <div [class]="caretClass"></div>
    <p class="error-text" [attr.id]="id">{{ errorMessage() }}</p>
  </ng-container>
</div>

<ng-template #requiredFieldError>
  <ng-container *ngIf="!customKey; else customizedError">
    <nh-input-error-indicator></nh-input-error-indicator>
  </ng-container>
</ng-template>

<ng-template #customizedError>
  <div [class]="caretClass"></div>
  <p class="error-text" [attr.id]="id">{{ errorMessage() }}</p>
</ng-template>
