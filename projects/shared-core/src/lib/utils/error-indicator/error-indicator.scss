@import '../../../styles/deprecated_mixins.scss';
@import '../../../styles/deprecated_variables.scss';

.error-indicator {
  @include flex-layout(column);
  .caret-up {
    @include caret(large, up, danger);
    margin-left: $spacing-base;
  }
  .error-text {
    @include text-normal(left, white);
    @include font-regular();
    font-size: $font-size-medium;
    background-color: $color-danger;
    padding: $spacing-base;
  }
}
.modal {
  .error-indicator {
    .error-text {
      margin-bottom: $spacing-xsmall;

      @media screen and ($media-max-phone) {
        margin-left: -($padding-modal);
        margin-right: -($padding-modal);
      }
    }
  }
}
