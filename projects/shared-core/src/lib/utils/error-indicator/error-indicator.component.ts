import { animate, style, transition, trigger } from '@angular/animations';
import { Component, Input, OnInit } from '@angular/core';
import { ERROR_POSITION } from '../../constants';
import { I18NPipe } from '../../pipes/i18n.pipe';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'error-indicator',
    styleUrls: ['./error-indicator.scss'],
    templateUrl: './error-indicator.html',
    animations: [
        trigger('enterAnimation', [
            transition(':enter', [
                style({ height: '0', opacity: 0 }),
                animate('0.4s ease-in-out', style({ height: '60px', opacity: 1 })),
            ]),
            transition(':leave', [
                style({ height: '60px', opacity: 1 }),
                animate('0.4s ease-in-out', style({ height: '0', opacity: 0 })),
            ]),
        ]),
    ],
})
export class ErrorIndicatorComponent implements OnInit {
    @Input() errors;
    @Input() id: string;
    @Input() customKey: string;
    @Input() replacedContent = false;
    @Input() position = ERROR_POSITION.BOTTOM;

    public caretClass: string;

    constructor(private i18nPipe: I18NPipe) {}

    ngOnInit() {
        switch (this.position) {
            case ERROR_POSITION.TOP:
                this.caretClass = 'caret-down';
                break;
            case ERROR_POSITION.BOTTOM:
                this.caretClass = 'caret-up';
                break;
            default:
                this.caretClass = 'caret-up';
        }
    }

    public errorMessage(): string | undefined {
        if (this.customKey) {
            return this.i18nPipe.transform(this.customKey);
        }

        if (this.errors.required) {
            return this.i18nPipe.transform('errors.mandatory.message');
        }

        if (this.errors.isEmail || this.errors.email) {
            return this.i18nPipe.transform('errors.email.message');
        }

        if (this.errors.validEmail) {
            return this.i18nPipe.transform('errors.duplicateEmail.message');
        }

        if (this.errors.isBefore) {
            return this.i18nPipe.transform('errors.isBefore.message');
        }

        if (this.errors.yearOfBirth) {
            return this.i18nPipe.transform('errors.yearOfBirth');
        }

        if (this.errors.maxlength) {
            const value = this.i18nPipe.transform('errors.maxlength.message');
            if (value) {
                this.replacedContent = true;
                const error = this.errors.maxlength;
                const charactersOver = error.actualLength - error.requiredLength;
                return value.replace('{length}', charactersOver.toString(10));
            }
            return this.i18nPipe.transform('csvParser.patientInvitation.errors.maxLength');
        }

        if (this.errors.max) {
            this.replacedContent = true;
            const value = this.i18nPipe.transform('errors.max.message');
            return value.replace('{number}', this.errors.max.max);
        }

        if (this.errors.min) {
            this.replacedContent = true;
            const value = this.i18nPipe.transform('errors.min.message');
            return value.replace('{number}', this.errors.min.min);
        }
    }
}
