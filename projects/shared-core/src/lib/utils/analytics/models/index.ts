export enum NoonalyticCategories {
    ASK_SYMPTOM = 'askSymptom',
    ASK_NON_CLINICAL_TOPIC = 'askNonClinicalTopic',
    SYMPTOM = 'symptomInquiry',
    QUESTIONNAIRE = 'questionnairePatient',
    QUESTIONNAIRE_CLINIC = 'questionnaireClinic',
    QUESTIONNAIRE_CLINICIAN = 'questionnaireClinician',
    DIARY = 'diary',
    LIBRARY = 'library',
    PAGE_LOAD = 'Page Load',
    LOGIN_TRANSITION = 'Login Transition',
}

export enum NoonalyticQuestionnaireActions {
    CANCEL = 'cancel',
    SUBMIT = 'submit',
    ANSWERING = 'answering',
    VIEW = 'view',
}

export enum NoonalyticDiaryActions {
    CANCEL = 'cancelDiary',
    VIEW = 'viewDiary',
    WRITE = 'writeDiary',
}

export enum NoonalyticLibraryActions {
    CHECK = 'checkMedicalRecords',
    DOWNLOAD = 'downloadMedicalRecords',
    SEND = 'sendMedicalRecords',
    VIEW = 'viewMedicalRecords',
    VIEW_HISTORY = 'viewAccessHistory',
}

export interface TimedObject {
    action?: string;
    category?: string;
    label?: string;
    timing?: number; // milliseconds
    timedFn?: ReturnType<typeof setTimeout>;
}

export enum NoonalyticCustomMetrics {
    PAGE_LOAD = 'pageLoad',
}

export enum ClinicPageLabels {
    ANNOUNCEMENT = 'Announcement',
    AUTOMATIC_RULES = 'Automatic Rules',
    CARE_TEAMS = 'Care Teams',
    CLINIC_DATA_EXPORT = 'Clinic Data Export',
    CLINIC_INFORMATION = 'Clinic Information',
    CLINIC_LINKING_REQUESTS = 'Clinic Linking Requests',
    CLINIC_ORGANIZATION = 'Clinic Organization',
    CLINIC_SETTINGS = 'Clinic Settings',
    CONNECTION_REQUEST_LIST = 'Connection Request List',
    CREATE_PATIENT = 'Create Patient',
    CREATE_PATIENT_CASE = 'Create Patient Case',
    EDIT_PATIENT_CASE = 'Edit/Process Patient Case',
    HANDLE_PATIENT = 'Handle Patient',
    MANAGE_MESSAGE_TEMPLATES = 'Manage Message Templates',
    LOGIN_PAGE = 'Login',
    LOGIN_PAGE_PROXY = 'Nurse Login',
    NURSE_DIARY = 'Nurse Diary',
    NOONA_LOGS = 'Noona Logs',
    PAGE_LOAD = 'Page Load',
    PATIENT_DIAGNOSIS = 'Patient Diagnosis',
    PATIENT_GENERAL_INFO = 'Patient General Information',
    PATIENT_LIST = 'Patient List',
    PATIENT_INTEGRATION_LIST = 'Patient Integration List',
    PATIENT_INVITATION_LIST = 'Patient Invitation List',
    PROFILE = 'Profile',
    QUESTIONNAIRE_TEMPLATE_LIST = 'Questionnaire Template List',
    SEARCH_PATIENT = 'Search Patient',
    SEARCH_PATIENT_BLANK = 'Search Patient without query',
    SHOW_ANNOUNCEMENT = 'Show Announcement',
    TREATMENT_MODULES = 'Treatment Modules',
    TRIAGE_MANAGEMENT = 'Triage Management',
    USER_LISTING = 'User Listing',
    WORKQUEUE_CARD = 'Work queue card',
    WORKQUEUE_LIST = 'Work queue list',
}

/*
 * These are constansts for gtag
 * https://developers.google.com/gtagjs/reference/api
 */
export enum GTagCommands {
    CONFIG = 'config',
    EVENT = 'event',
    SET = 'set',
}

export enum NoonaAnalyticEvents {
    CLICK = 'click',
    SAVE = 'save',
    VIEW = 'view',
}

export type NoonalyticActions = NoonalyticDiaryActions | NoonalyticLibraryActions | NoonalyticQuestionnaireActions;
