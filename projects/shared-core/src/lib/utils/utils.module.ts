import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ScPipesModule } from '../pipes/sc-pipes.module';
import { ErrorIndicatorComponent } from './error-indicator/error-indicator.component';
import { PainPointerComponent } from './pain-pointer/pain-pointer.component';
import { PhotoUploaderComponent } from './photo-uploader/photo-uploader.component';
import { WellbeingIndicatorComponent } from './wellbeing-indicator/wellbeing-indicator.component';
import { DsCheckboxModule } from '../ds/components/checkbox/checkbox.module';
import { DsActionButtonModule } from '../ds/components/action-button/action-button.module';
import { DsIconModule } from '../ds/components/icon/icon.module';
import { InputErrorIndicatorComponent } from './input-error-indicator/input-error-indicator.component';

@NgModule({
    imports: [CommonModule, ScPipesModule, DsCheckboxModule, DsActionButtonModule, DsIconModule],
    declarations: [
        ErrorIndicatorComponent,
        WellbeingIndicatorComponent,
        PainPointerComponent,
        PhotoUploaderComponent,
        InputErrorIndicatorComponent,
    ],
    exports: [ErrorIndicatorComponent, WellbeingIndicatorComponent, PainPointerComponent, PhotoUploaderComponent],
})
export class UtilsModule {}
