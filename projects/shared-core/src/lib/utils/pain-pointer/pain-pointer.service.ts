import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Gender } from '../../generated/models/gender';
import { ASSETS_PATH } from '../../tokens/assets.token';
import { CapitalFirstLetterPipe } from '../../pipes/capital-first-letter.pipe';
import { FormFieldConfig } from '../../form-engine/models/form-field-config.interface';
import { FormItemType } from '../../generated/models/form-item-type';
import { AddAnswer } from '../../form-engine/store/actions/form.actions';
import { Store } from '@ngrx/store';
import { FieldService } from '../../form-engine/services/field.service';

@Injectable({
    providedIn: 'root',
})
export class PainPointerService {
    private maleDiagram: string;
    private femaleDiagram: string;
    private maleDiagramPath = `${this.assetsPath}images/pain-pointer/male-diagram-standing.svg`;
    private femaleDiagramPath = `${this.assetsPath}images/pain-pointer/female-diagram-standing.svg`;

    constructor(
        private http: HttpClient,
        @Inject(ASSETS_PATH) private assetsPath: string,
        private capital: CapitalFirstLetterPipe,
        private store: Store,
        private fieldService: FieldService
    ) {}

    getDiagram(gender: Gender): Observable<string> {
        const isGenderFemale = gender === Gender.FEMALE;
        const diagram = isGenderFemale ? this.femaleDiagram : this.maleDiagram;
        const headers = new HttpHeaders({ accept: 'image/svg+xml' });
        const responseType = 'text';
        const path = isGenderFemale ? this.femaleDiagramPath : this.maleDiagramPath;

        return diagram
            ? of(diagram)
            : this.http
                  .get(path, {
                      headers,
                      responseType,
                  })
                  .pipe(
                      tap((res) => {
                          if (isGenderFemale) {
                              this.femaleDiagram = res;
                          } else {
                              this.maleDiagram = res;
                          }
                      })
                  );
    }

    getOtherFieldKey(config: FormFieldConfig): string {
        return `other${this.capital.transform(config.key)}`;
    }

    getOtherConfig(config: FormFieldConfig): FormFieldConfig {
        return {
            type: FormItemType.TEXTAREA,
            formType: config.formType,
            key: this.getOtherFieldKey(config),
            maxLength: 500,
            minLength: 0,
            gender: config.gender,
            site: config.site,
        };
    }

    clearOtherConfigStateValues(config: FormFieldConfig) {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(config, null, false)));
    }
}
