import { TestBed } from '@angular/core/testing';
import { PainPointerService } from './pain-pointer.service';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { ASSETS_PATH } from '../../tokens/assets.token';
import { DEFAULT_ASSETS_PATH } from '../../constants';
import { Gender } from '../../generated/models/gender';
import { CapitalFirstLetterPipe } from '../../pipes/capital-first-letter.pipe';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { SharedSchemaService } from '../../abstract-services/shared-schema.service';
import { CamelCasePipe } from '../../pipes/camel-case.pipe';
import { HyphenedPipe } from '../../pipes/hyphened.pipe';
import { NoonaEncodePipe } from '../../pipes/noona-encode.pipe';
import { FormItemType } from '../../generated/models/form-item-type';
import { SymptomType } from '../../generated/models/symptom-type';
import { Application } from '../../models/application';

const mockFormConfig = {
  type: FormItemType.TEXTAREA,
  formType: SymptomType.SYMPTOM_ABDOMINAL,
  key: 'locations',
  maxLength: 500,
  minLength: 0,
  gender: Gender.MALE,
  site: Application.CLINIC
};
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('PainPointerService', () => {
  let service: PainPointerService;
  let httpTestingController: HttpTestingController;
  let store: MockStore;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        { provide: ASSETS_PATH, useValue: DEFAULT_ASSETS_PATH },
        CapitalFirstLetterPipe,
        provideMockStore(),
        SharedSchemaService,
        CamelCasePipe,
        HyphenedPipe,
        NoonaEncodePipe,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
      ]
    });
    service = TestBed.inject(PainPointerService);
    store = TestBed.inject(MockStore);
    httpTestingController = TestBed.inject(HttpTestingController);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('#getDiagram', () => {
    it.each([
      {
        gender: Gender.MALE,
        svg: 'test-diagram-male',
        url: 'assets/images/pain-pointer/male-diagram-standing.svg',
        serviceProperty: 'maleDiagram'
      },
      {
        gender: Gender.FEMALE,
        svg: 'test-diagram-female',
        url: 'assets/images/pain-pointer/female-diagram-standing.svg',
        serviceProperty: 'femaleDiagram'
      }
    ])('should get diagrams', ({ gender, svg, url, serviceProperty }) => {
      service.getDiagram(gender).subscribe(data => {
        expect(data).toEqual(svg);
      });

      const req = httpTestingController.expectOne(url);

      expect(req.request.method).toEqual('GET');

      req.flush(svg);
      httpTestingController.verify();

      expect(service[serviceProperty]).toEqual(svg);
    });
  });

  describe('#getOtherConfig', () => {
    it('returns config in correct format', () => {
      expect(service.getOtherConfig(mockFormConfig).formType).toBe(SymptomType.SYMPTOM_ABDOMINAL);
      expect(service.getOtherConfig(mockFormConfig).key).toBe('otherLocations');
      expect(service.getOtherConfig(mockFormConfig).gender).toBe(Gender.MALE);
      expect(service.getOtherConfig(mockFormConfig).site).toBe(Application.CLINIC);
    });
  });

  describe('#clearOtherConfigStateValues', () => {
    it('invokes store.dispatch with correct value', () => {
      const storeSpy = jest.spyOn(store, 'dispatch');
      service.clearOtherConfigStateValues(mockFormConfig);

      expect(storeSpy).toHaveBeenCalledWith({
        payload: { answer: null, field: 'locations', initialValue: false, required: false, type: 'abdominal' },
        type: '[Form] Add New Answer'
      });
    });
  });

  describe('#getOtherFieldKey', () => {
    it('returns key in correct format', () => {
      expect(service.getOtherFieldKey(mockFormConfig)).toBe('otherLocations');
    });
  });

  afterEach(() => {
    httpTestingController.verify();
  });
});
