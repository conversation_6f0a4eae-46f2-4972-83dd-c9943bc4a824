import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PainPointerComponent } from './pain-pointer.component';
import { PainPointerService } from './pain-pointer.service';
import { ElementRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of } from 'rxjs';
import { PainPointerSide } from '../../form-engine/models/pain-pointer-side.enum';
import { Gender } from '../../generated/models/gender';
import { By } from '@angular/platform-browser';
import { MocksModule, mockPainDiagram } from '@shared-core/testing';

describe('PainPointerComponent', () => {
  let component: PainPointerComponent;
  let fixture: ComponentFixture<PainPointerComponent>;
  let painPointerServiceMock: any;
  let elementRefMock: any;

  beforeEach(() => {
    painPointerServiceMock = {
      getDiagram: jest.fn().mockReturnValue(of(mockPainDiagram))
    };

    const mockDiv = document.createElement('div');
    mockDiv.className = 'diagram-container';
    elementRefMock = {
      nativeElement: mockDiv
    };

    TestBed.configureTestingModule({
      imports: [MocksModule],
      declarations: [PainPointerComponent],
      providers: [
        { provide: PainPointerService, useValue: painPointerServiceMock },
        { provide: ElementRef, useValue: elementRefMock }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(PainPointerComponent);
    component = fixture.componentInstance;

    component.gender = Gender.FEMALE;
    component.painLocations = ['leftHand', 'rightHand'];
    component.side = PainPointerSide.FRONT;
    component.painPointerWidth = 150;
    component.painPointerHeight = 300;
    component.view = false;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('initialization', () => {
    it('should initialize with provided inputs', () => {
      component.painPointerWidth = 150;
      component.painPointerHeight = 300;
      component.gender = Gender.MALE;
      component.painLocations = ['forehead'];
      component.side = PainPointerSide.FRONT;

      fixture.detectChanges();

      expect(component.gender).toBe(Gender.MALE);
      expect(component.painLocations).toEqual(['forehead']);
      expect(component.side).toBe(PainPointerSide.FRONT);
      expect(component.painPointerWidth).toBe(150);
      expect(component.painPointerHeight).toBe(300);
    });

    it('should call loadAndAddDiagram on ngOnChanges when all required inputs are set', () => {
      jest.spyOn(component as any, 'loadAndAddDiagram');

      component.ngOnChanges({
        gender: { currentValue: Gender.MALE, previousValue: undefined, firstChange: false, isFirstChange: () => false },
        side: { currentValue: PainPointerSide.FRONT, previousValue: undefined, firstChange: false, isFirstChange: () => false },
        painPointerWidth: { currentValue: 150, previousValue: undefined, firstChange: false, isFirstChange: () => false },
        painPointerHeight: { currentValue: 300, previousValue: undefined, firstChange: false, isFirstChange: () => false }
      });

      expect(component['loadAndAddDiagram']).toHaveBeenCalled();
    });
  });

  describe('diagram loading', () => {
    it('should get diagram from service with correct gender', () => {
      component.gender = Gender.MALE;

      component.ngOnChanges({});

      expect(painPointerServiceMock.getDiagram).toHaveBeenCalledWith(Gender.MALE);
    });

    it('should render body parts after diagram is loaded', () => {
      jest.spyOn(component as any, 'toggleListener');

      component.ngOnChanges({});

      expect(painPointerServiceMock.getDiagram).toHaveBeenCalledWith(Gender.FEMALE);
      expect(component['toggleListener']).toHaveBeenCalledWith(true);
    });

    it('should set locationIds from painLocations during initialization', () => {
      component.painLocations = ['leftHand', 'rightFoot'];

      component.ngOnChanges({});

      expect(component['locationIds']).toEqual(['left-hand', 'right-foot']);
    });

    it('should call updatePainPointer with correct parameters after diagram loads', () => {
      jest.spyOn(component, 'updatePainPointer');

      component.ngOnChanges({});

      expect(component.updatePainPointer).toHaveBeenCalledWith(component['diagram'], component.painLocations, component.side);
    });

    it('should not initialize if required inputs are missing', () => {
      jest.spyOn(component as any, 'loadAndAddDiagram');

      component.gender = undefined;
      component.ngOnChanges({});

      expect(component['loadAndAddDiagram']).not.toHaveBeenCalled();
      expect(component['initialized']).toBe(false);
    });
  });

  describe('removeOtherSide', () => {
    let mockBackElements: any;
    let mockFrontElements: any;
    let mockDiagram: any;

    beforeEach(() => {
      mockBackElements = {
        remove: jest.fn()
      };

      mockFrontElements = {
        remove: jest.fn()
      };

      mockDiagram = {
        find: jest.fn().mockImplementation(selector => {
          if (selector === '#body-back-bg, #body-back, #pain-spots-back, #pain-body-parts-back, #body-parts-back') {
            return mockBackElements;
          } else if (selector === '#body-front, #body-front_1_, #pain-spots-front, #pain-body-parts-front, #body-parts-front') {
            return mockFrontElements;
          }
          return { remove: jest.fn() };
        })
      };
    });

    it('should remove back elements when side is front', () => {
      component.removeOtherSide(mockDiagram, PainPointerSide.FRONT);

      expect(mockDiagram.find).toHaveBeenCalledWith('#body-back-bg, #body-back, #pain-spots-back, #pain-body-parts-back, #body-parts-back');
      expect(mockBackElements.remove).toHaveBeenCalled();
      expect(mockFrontElements.remove).not.toHaveBeenCalled();
    });

    it('should remove front elements when side is back', () => {
      component.removeOtherSide(mockDiagram, PainPointerSide.BACK);

      expect(mockDiagram.find).toHaveBeenCalledWith(
        '#body-front, #body-front_1_, #pain-spots-front, #pain-body-parts-front, #body-parts-front'
      );
      expect(mockFrontElements.remove).toHaveBeenCalled();
      expect(mockBackElements.remove).not.toHaveBeenCalled();
    });

    it('should use component diagram when no svg parameter provided', () => {
      component['diagram'] = mockDiagram;

      component.removeOtherSide(undefined, PainPointerSide.FRONT);

      expect(mockDiagram.find).toHaveBeenCalledWith('#body-back-bg, #body-back, #pain-spots-back, #pain-body-parts-back, #body-parts-back');
      expect(mockBackElements.remove).toHaveBeenCalled();
    });

    it('should remove front elements when side is not front', () => {
      component.removeOtherSide(mockDiagram, PainPointerSide.BACK);

      expect(mockDiagram.find).toHaveBeenCalledWith(
        '#body-front, #body-front_1_, #pain-spots-front, #pain-body-parts-front, #body-parts-front'
      );
      expect(mockFrontElements.remove).toHaveBeenCalled();
      expect(mockBackElements.remove).not.toHaveBeenCalled();
    });

    it('should handle undefined side parameter', () => {
      component.removeOtherSide(mockDiagram);

      expect(mockDiagram.find).toHaveBeenCalledWith(
        '#body-front, #body-front_1_, #pain-spots-front, #pain-body-parts-front, #body-parts-front'
      );
      expect(mockFrontElements.remove).toHaveBeenCalled();
      expect(mockBackElements.remove).not.toHaveBeenCalled();
    });
  });

  describe('body parts rendering', () => {
    beforeEach(async () => {
      component.enabledLocations = ['leftPalm', 'rightPalm', 'head', 'forehead', 'neck', 'leftShoulder'];

      component.gender = Gender.FEMALE;
      component.painLocations = ['leftPalm', 'rightPalm', 'head', 'forehead', 'neck', 'leftShoulder'];
      component.side = PainPointerSide.FRONT;
      component.painPointerWidth = 150;
      component.painPointerHeight = 300;
      component.view = false;
      component['locationIds'] = ['left-palm', 'right-palm', 'head', 'forehead', 'neck', 'left-shoulder'];
      component.selectedParts = [];

      component.ngOnChanges({});
      fixture.detectChanges();

      await new Promise(resolve => setTimeout(resolve, 500));
      fixture.detectChanges();
    });

    it('should render correct amount of body parts', () => {
      const bodyPartsWithClass = fixture.nativeElement.querySelectorAll('.body-part');

      expect(bodyPartsWithClass.length).toBeGreaterThan(0);
    });

    it('should render body parts in the correct order', () => {
      const bodyPartsWithClass: HTMLElement[] = Array.from(fixture.nativeElement.querySelectorAll('.body-part'));
      const orderedIds = bodyPartsWithClass.map(element => element.id);

      const expectedOrder = ['head', 'forehead', 'neck', 'left-shoulder', 'left-palm', 'right-palm'].filter(id =>
        component['locationIds'].includes(id)
      );

      expect(orderedIds).toEqual(expectedOrder);
    });

    it('should add body-part class only to matching locationIds', () => {
      const bodyPartsWithClass: HTMLElement[] = Array.from(fixture.nativeElement.querySelectorAll('.body-part'));

      bodyPartsWithClass.forEach(element => {
        expect(component['locationIds']).toContain(element.id);
      });
    });

    it('should set correct accessibility attributes on body parts', () => {
      const bodyPartsWithClass = fixture.nativeElement.querySelectorAll('.body-part');

      bodyPartsWithClass.forEach(element => {
        expect(element.getAttribute('tabindex')).toBe('0');
        expect(element.getAttribute('role')).toBe('checkbox');
        expect(element.getAttribute('disabled')).toBeFalsy();
        expect(element.getAttribute('aria-label')).toBeTruthy();
        expect(element.getAttribute('aria-checked')).toBe('false');
      });
    });

    it('should set aria-checked to true for selected parts', () => {
      const transformedName = component['hyphenedCapitalPipe'].transform('left-palm');
      component.selectedParts = [transformedName];
      component['toggleListener'](true);

      const leftPalm = fixture.nativeElement.querySelector('#left-palm');

      expect(leftPalm.getAttribute('aria-checked')).toBe('true');
    });

    it('should set disabled attribute correctly when enable is false', () => {
      component['toggleListener'](false);

      const bodyPartsWithClass = fixture.nativeElement.querySelectorAll('.body-part');
      bodyPartsWithClass.forEach(element => {
        expect(element.getAttribute('disabled')).toBeTruthy();
      });
    });

    it('should not process body parts if diagram is not loaded', () => {
      component['diagram'] = null;
      component['toggleListener'](true);

      const bodyPartsWithClass = fixture.nativeElement.querySelectorAll('.body-part');

      expect(bodyPartsWithClass).toHaveLength(0);
    });
  });

  describe('accessibility', () => {
    it('should set fieldset with correct aria attributes', () => {
      component.side = PainPointerSide.FRONT;
      fixture.detectChanges();

      component.ngOnChanges({});
      fixture.detectChanges();

      const fieldsetElem = fixture.debugElement.query(By.css('fieldset')).nativeElement;

      expect(fieldsetElem).toBeTruthy();
      expect(fieldsetElem.getAttribute('aria-label')).toBeTruthy();
    });

    it('should toggle listeners correctly when view changes', () => {
      jest.spyOn(component as any, 'toggleListener');

      component.ngOnChanges({});

      expect(component['toggleListener']).toHaveBeenCalledWith(true);
    });
  });

  describe('interaction', () => {
    it('should emit partToggled event when a body part is clicked', () => {
      jest.spyOn(component.partToggled, 'emit');

      component.ngOnChanges({});

      const mockEvent = {
        currentTarget: {
          id: 'left-palm'
        }
      };
      component['toggleSelectedPoint'](mockEvent as any);

      expect(component.partToggled.emit).toHaveBeenCalledWith('left-palm');
    });

    it('should toggle selection state when a body part is clicked', () => {
      jest.spyOn(component as any, 'setOpacity');
      component.selectedParts = [];

      component.ngOnChanges({});

      const mockEvent = {
        currentTarget: {
          id: 'left-palm',
          getAttribute: jest.fn().mockReturnValue('left-palm'),
          setAttribute: jest.fn()
        },
        attr: jest.fn().mockReturnValue('left-palm')
      };
      component['toggleSelectedPoint'](mockEvent as any);

      expect(component['setOpacity']).toHaveBeenCalledWith('left-palm', 0.5, 1, 0);
    });

    it('should call setOpacity with deselected values when part is already selected', () => {
      jest.spyOn(component as any, 'setOpacity');

      const transformedName = component['hyphenedCapitalPipe'].transform('left-palm');
      component.selectedParts = [transformedName];

      component.ngOnChanges({});

      const mockEvent = {
        currentTarget: {
          id: 'left-palm',
          getAttribute: jest.fn().mockReturnValue('left-palm'),
          setAttribute: jest.fn()
        },
        attr: jest.fn().mockReturnValue('left-palm')
      };
      component['toggleSelectedPoint'](mockEvent as any);

      expect(component['setOpacity']).toHaveBeenCalledWith('left-palm', 0, 0, 0.33);
    });

    it('should handle keyboard events', () => {
      jest.spyOn(component as any, 'toggleSelectedPoint');

      component.ngOnChanges({});

      const enterEvent = {
        key: 'Enter',
        preventDefault: jest.fn(),
        currentTarget: { id: 'left-palm' }
      };
      component['keyDown'](enterEvent as any);

      expect(enterEvent.preventDefault).toHaveBeenCalled();
      expect(component['toggleSelectedPoint']).toHaveBeenCalledWith(enterEvent);

      jest.clearAllMocks();
      const spaceEvent = {
        key: ' ',
        preventDefault: jest.fn(),
        currentTarget: { id: 'left-palm' }
      };
      component['keyDown'](spaceEvent as any);

      expect(spaceEvent.preventDefault).toHaveBeenCalled();
      expect(component['toggleSelectedPoint']).toHaveBeenCalledWith(spaceEvent);
    });
  });

  describe('mouse events', () => {
    it('should handle mouseOver event for body parts', () => {
      jest.spyOn(component as any, 'setOpacityWithoutLocation');
      jest.spyOn(component as any, 'setOpacityForPainArea');
      component.selectedParts = ['leftPalm'];

      component.ngOnChanges({});

      const mockEvent = {
        currentTarget: {
          id: 'left-palm'
        }
      };
      component['mouseOver'](mockEvent as any);

      expect(component['setOpacityWithoutLocation']).toHaveBeenCalledWith('left-palm', 1, 1);

      jest.clearAllMocks();
      const mockEvent2 = {
        currentTarget: {
          id: 'right-palm'
        }
      };
      component['mouseOver'](mockEvent2 as any);

      expect(component['setOpacityForPainArea']).toHaveBeenCalledWith('right-palm', 1);
    });

    it('should handle mouseLeave event for body parts', () => {
      jest.spyOn(component as any, 'setOpacity');
      component.selectedParts = ['leftPalm'];

      component.ngOnChanges({});

      const mockEvent = {
        currentTarget: {
          id: 'left-palm'
        }
      };
      component['mouseLeave'](mockEvent as any);

      expect(component['setOpacity']).toHaveBeenCalledWith('left-palm', 0.5, 1, 0);

      jest.clearAllMocks();
      const mockEvent2 = {
        currentTarget: {
          id: 'right-palm'
        }
      };
      component['mouseLeave'](mockEvent2 as any);

      expect(component['setOpacity']).toHaveBeenCalledWith('right-palm', 0, 0, 0.33);
    });

    it('should return early when mouseOver event is undefined', () => {
      jest.spyOn(component as any, 'setOpacityWithoutLocation');
      jest.spyOn(component as any, 'setOpacityForPainArea');

      component['mouseOver']();

      expect(component['setOpacityWithoutLocation']).not.toHaveBeenCalled();
      expect(component['setOpacityForPainArea']).not.toHaveBeenCalled();
    });

    it('should return early when mouseOver event has no currentTarget', () => {
      jest.spyOn(component as any, 'setOpacityWithoutLocation');
      jest.spyOn(component as any, 'setOpacityForPainArea');

      const mockEvent = {
        currentTarget: null
      };
      component['mouseOver'](mockEvent as any);

      expect(component['setOpacityWithoutLocation']).not.toHaveBeenCalled();
      expect(component['setOpacityForPainArea']).not.toHaveBeenCalled();
    });

    it('should return early when mouseLeave event is undefined', () => {
      jest.spyOn(component as any, 'setOpacity');

      component['mouseLeave']();

      expect(component['setOpacity']).not.toHaveBeenCalled();
    });

    it('should return early when mouseLeave event has no currentTarget', () => {
      jest.spyOn(component as any, 'setOpacity');

      const mockEvent = {
        currentTarget: null
      };
      component['mouseLeave'](mockEvent as any);

      expect(component['setOpacity']).not.toHaveBeenCalled();
    });
  });

  describe('cleanup', () => {
    it('should clean up listeners when component is destroyed', () => {
      jest.spyOn(component as any, 'toggleListener');

      component.ngOnChanges({});

      component.ngOnDestroy();

      expect(component['toggleListener']).toHaveBeenCalledWith(false);
    });
  });
});
