import { Pipe, PipeTransform } from '@angular/core';
import isString from 'lodash/isString';
import isNumber from 'lodash/isNumber';
import forEach from 'lodash/forEach';

@Pipe({ name: 'wordBreak' })
export class WordBreakPipe implements PipeTransform {
    transform(input: any, position: any): string {
        if (!input || !isString(input)) {
            return input;
        }
        if (!position || !isNumber(position) || position <= 0 || position >= 150) {
            position = 20;
        }
        const text = [];
        forEach(input.split(' '), (word) => {
            if (word.length > position) {
                const pattern = new RegExp('(.{' + position + '})', 'g');
                word = word.replace(pattern, '$1&#8203;');
            }
            text.push(word);
        });
        return text.join(' ');
    }
}
