import { Pipe, PipeTransform, SecurityContext } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';

@Pipe({
    name: 'regexReplaceInText',
})
export class RegexReplaceInTextPipe implements PipeTransform {
    constructor(private sanitizer: DomSanitizer) {}

    transform(value: string, regex, format): any {
        return this.sanitize(this.replace(value, regex, format));
    }

    replace(str, regex, format) {
        return str.replace(new RegExp(`(${regex})`, 'gi'), format);
    }

    sanitize(str) {
        return this.sanitizer.sanitize(SecurityContext.HTML, str);
    }
}
