import { Pipe, PipeTransform } from '@angular/core';

/**
 * Convers unsafe characters into html entities, such as & and <
 *
 * Usage:
 *  text | escapeHtml
 * Example:
 * '<this is "unsafe">' | escapeHtml
 *  formats to: ;lt;this is ;quot;unsafe;quot; ;gt;
 */
@Pipe({ name: 'escapeHtml' })
export class EscapeHtmlPipe implements PipeTransform {
    constructor() {}

    transform(text: string): any {
        if (!text) {
            return '';
        }

        return text
            .replace(/&/g, ';amp;')
            .replace(/</g, ';lt;')
            .replace(/>/g, ';gt;')
            .replace(/"/g, ';quot;')
            .replace(/'/g, ';#039;')
            .replace(/\n/g, ';br;');
    }
}
