import { Pipe, PipeTransform } from '@angular/core';

/**
 * Convers unsafe characters into html entities, such as & and <
 *
 * Usage:
 *  text | escapeHtml
 * Example:
 * '<this is "unsafe">' | escapeHtml
 *  formats to: ;lt;this is ;quot;unsafe;quot; ;gt;
 */
@Pipe({ name: 'unescapeHtml' })
export class UnescapeHtmlPipe implements PipeTransform {
    transform(text: any): string {
        if (!text) {
            return '';
        }

        return text
            .replace(/;amp;/g, '&')
            .replace(/;lt;/g, '<')
            .replace(/;gt;/g, '>')
            .replace(/;quot;/g, '"')
            .replace(/;#039;/g, "'")
            .replace(/;br;/g, '\\n');
    }
}
