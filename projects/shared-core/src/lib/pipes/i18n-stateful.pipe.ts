import { Pipe, PipeTransform } from '@angular/core';
import forEach from 'lodash/forEach';
import lodashKeys from 'lodash/keys';
import { NoonaLocaleService } from '../abstract-services/noona-locale.service';

/**
 * Tries to find the provided key from the current translation file and return the translated key.
 * If translation cannot be found returns the original key wrapped in questions marks
 * Usage:
 *  key | i18n
 * Example:
 * (in our translation files there is noona.example.test=This is test)
 *  {{ noona.example.test | i18n }}
 *  formats to: This is test
 */
@Pipe({
    name: 'i18nStateful',
    pure: false,
})
export class I18NStatefulPipe implements PipeTransform {
    constructor(private localeService: NoonaLocaleService) {}

    transform(key: string, args?: any): string {
        let text = this.localeService.getTranslation(key);
        if (text && args) {
            const keys = lodashKeys(args);
            forEach(keys, (k: string) => {
                text = text.replace('{' + k + '}', args[k]);
            });
        }
        return text;
    }
}
