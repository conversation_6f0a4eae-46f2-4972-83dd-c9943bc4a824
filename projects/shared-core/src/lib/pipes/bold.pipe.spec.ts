import { BoldPipe } from "./bold.pipe";

describe('bold', () => {
    // This pipe is a pure, stateless function so no need for BeforeEach
    const pipe = new BoldPipe();

    it('transforms "abc" to "\u00a0<b>abc</b>"', () => {
        expect(pipe.transform('abc')).toBe('\u00a0<b>abc</b>');
    });

    it('transforms "abc def" to "\u00a0<b>abc def</b>"', () => {
        expect(pipe.transform('abc def')).toBe('\u00a0<b>abc def</b>');
    });
});
