import { Pipe, PipeTransform } from '@angular/core';
import isUndefined from 'lodash/isUndefined';

@Pipe({ name: 'linkify' })
export class LinkifyPipe implements PipeTransform {
    readonly LINKY_URL_REGEXP = /((ftp|https?):\/\/|(www\.)|(mailto:)?[A-Za-z0-9._%+-]+@)[^\s<]*[^\s.;,(){}<>"”’]/i;
    readonly MAILTO_REGEXP = /^mailto:/i;

    transform(text: any, target: any): string {
        if (!text) {
            return text;
        }

        let raw = text;
        const html: string[] = [];
        let url = '';
        let i = -1;

        let match = raw.match(this.LINKY_URL_REGEXP);
        while (match) {
            // We can not end in these as they are sometimes found at the end of the sentence
            url = match[0];
            // if we did not match ftp/http/www/mailto then assume mailto
            if (!match[2] && !match[4]) {
                url = (match[3] ? 'http://' : 'mailto:') + url;
            }
            i = match.index;
            this.addText(raw.substr(0, i), html);
            this.addLink(url, match[0].replace(this.MAILTO_REGEXP, ''), target, html);
            raw = raw.substring(i + match[0].length);
            match = raw.match(this.LINKY_URL_REGEXP);
        }

        this.addText(raw, html);
        return html.join('');
    }

    private addText(text, html: string[]) {
        if (!text) {
            return;
        }
        html.push(text);
    }

    private addLink(url, text, target: any, html: string[]) {
        html.push('<a ');
        if (!isUndefined(target)) {
            html.push('target="', target, '" ');
        }
        html.push('href="', url.replace(/"/g, '&quot;').replace(/&#8203;/g, ''), '">');
        this.addText(text, html);
        html.push('</a>');
    }
}
