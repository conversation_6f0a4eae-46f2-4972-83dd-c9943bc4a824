import { Pipe, PipeTransform } from '@angular/core';
import isNil from 'lodash/isNil';
import isString from 'lodash/isString';

@Pipe({ name: 'lowercaseExceptGerman' })
export class LowercaseExceptGermanPipe implements PipeTransform {
    transform(s: any, args?: any): string {
        let locale = 'en-GB';
        if (!isNil(args)) {
            locale = args;
        }

        if (!isString(s) || s.length < 1) {
            return;
        }

        if (locale === 'de_DE') {
            return s;
        }

        // see for approved locale formats
        // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/toLocaleLowerCase
        return s.toLocaleLowerCase(locale.replace('_', '-'));
    }
}
