import { Pipe, PipeTransform } from '@angular/core';

/**
 * Converts escaped strings such as "\n"
 *
 * Usage:
 *  text | unescape
 * Example:
 * 'new\nline' | escapeHtml
 *  formats to:
 *    "new
 *    line"
 */
@Pipe({ name: 'unescape' })
export class UnescapePipe implements PipeTransform {
    constructor() {}

    transform(text: string): any {
        if (!text) {
            return '';
        }

        return text.replace(/\\n/g, '\n').replace(/\\t/g, '\t');
    }
}
