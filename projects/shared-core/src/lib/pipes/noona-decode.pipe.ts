import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'noonaDecode' })
export class NoonaDecodePipe implements PipeTransform {
    transform(input: any): string {
        if (!input) {
            return '';
        }
        return input
            .replace(/;amp;/g, '&amp;')
            .replace(/;lt;/g, '&lt;')
            .replace(/;gt;/g, '&gt;')
            .replace(/;quot;/g, '&quot;')
            .replace(/;#039;/g, '&#039;')
            .replace(/;br;/g, '<br>');
    }
}
