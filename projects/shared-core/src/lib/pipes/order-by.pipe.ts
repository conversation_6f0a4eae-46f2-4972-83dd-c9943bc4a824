import { Pipe, PipeTransform } from '@angular/core';

/**
 * Orders the given array based on the field provided.
 *
 * Default sort order is ascending if '-' (minus sign) is provided in front of the field then sorts in the
 * descending order.
 */
@Pipe({ name: 'orderBy' })
export class OrderByPipe implements PipeTransform {
    transform(values: any, orderField: string): any {
        if (!values || !orderField || typeof orderField !== 'string') {
            return values;
        }

        let orderDirection = 'ASC';
        if (orderField[0] === '-') {
            orderField = orderField.substr(1);
            orderDirection = 'DESC';
        }

        values.sort((a: any, b: any) => {
            if (orderDirection === 'ASC') {
                if (a[orderField] < b[orderField]) {
                    return -1;
                }
                if (a[orderField] > b[orderField]) {
                    return 1;
                }
                return 0;
            } else {
                if (a[orderField] < b[orderField]) {
                    return 1;
                }
                if (a[orderField] > b[orderField]) {
                    return -1;
                }
                return 0;
            }
        });

        return values;
    }
}
