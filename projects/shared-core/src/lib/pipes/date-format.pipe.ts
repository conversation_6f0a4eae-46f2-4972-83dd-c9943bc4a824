import { Pipe, PipeTransform } from '@angular/core';
import moment, { Moment } from 'moment';
import { ConfigurationProviderService } from '../abstract-services/configuration-provider.service';
import { DateFormattingPattern } from '../abstract-services/interfaces/date-formatting-pattern.interface';

/**
 * Formats the provided value with the given formatting pattern.
 *
 * Usage:
 *  key | nhDateFormat: formattingPattern
 * Example:
 *  (date is number representing the 1st of December 2017)
 *  {{ date | 'YYYY-MM-DD }}
 *  formats to: 2017-12-01
 */
@Pipe({
    name: 'nhDateFormat',
})
export class DateFormatPipe implements PipeTransform {
    private patterns: DateFormattingPattern;

    constructor(dfProviderService: ConfigurationProviderService) {
        dfProviderService.dateFormattingPattern().subscribe((dfp: DateFormattingPattern) => {
            this.patterns = dfp;
        });
    }

    transform(value: number | Moment | Date | string, formattingPattern: string): string {
        if (!value || !formattingPattern) {
            return '';
        }

        if (this.patterns && this.patterns[formattingPattern]) {
            formattingPattern = this.patterns[formattingPattern];
        }

        if (moment.isMoment(value)) {
            return value.format(formattingPattern);
        }
        return moment(new Date(value)).format(formattingPattern);
    }
}
