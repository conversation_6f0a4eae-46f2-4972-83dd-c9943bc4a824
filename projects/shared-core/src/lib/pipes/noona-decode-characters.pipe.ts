import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'noonaDecodeToCharacters' })
export class NoonaDecodeToCharactersPipe implements PipeTransform {
    transform(input: any): string {
        if (!input) {
            return '';
        }
        return input
            .replace(/;amp;/g, '&')
            .replace(/;lt;/g, '<')
            .replace(/;gt;/g, '>')
            .replace(/;quot;/g, '"')
            .replace(/;#039;/g, '&#039;')
            .replace(/;br;/g, '<br>');
    }
}
