import { Pipe, PipeTransform } from '@angular/core';

/**
 * Convers unsafe characters into html entities, such as & and <
 *
 * Usage:
 *  text | ellipsis:27
 *
 * Example:
 * 'this is long word' | ellipsis:5
 *      returns 'this ...'
 */
@Pipe({ name: 'ellipsis' })
export class EllipsisPipe implements PipeTransform {
    constructor() {}

    transform(text: string, maxLength: number, breakAtWord?: boolean): string {
        if (!text) {
            return '';
        }

        if (!maxLength || maxLength < 0 || text.length < maxLength) {
            return text;
        }

        if (breakAtWord === true) {
            let breakPos = Math.min(text.length, maxLength + 1);
            while (breakPos > maxLength) {
                breakPos = text.lastIndexOf(' ', breakPos - 1);
            }
            return text.substr(0, breakPos) + '...';
        }

        return text.substr(0, maxLength) + '...';
    }
}
