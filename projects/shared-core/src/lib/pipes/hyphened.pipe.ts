import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'hyphened',
})
export class HyphenedPipe implements PipeTransform {
    transform(value: any): string {
        if (!value) {
            return '';
        }

        if (value.length === 1) {
            return value;
        } else if (value.match(/_/)) {
            return value.replace(/_/g, '-');
        }

        return value.replace(/(([A-Z])|([0-9][0-9]*))/g, ($1) => {
            return '-' + $1.toLowerCase();
        });
    }
}
