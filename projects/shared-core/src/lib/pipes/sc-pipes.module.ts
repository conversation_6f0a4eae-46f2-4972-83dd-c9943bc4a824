import { ModuleWithProviders, NgModule } from '@angular/core';
import { AbbrPipe } from './abbr.pipe';
import { AmDateFormatPipe } from './am-date-format.pipe';
import { CamelCasePipe } from './camel-case.pipe';
import { CapitalFirstLetterPipe } from './capital-first-letter.pipe';
import { ConvertHyphenedToCapitalPipe } from './convert-hyphened-to-capital.pipe';
import { ConvertLineBreaksPipe } from './convert-line-breaks.pipe';
import { DateFormatPipe } from './date-format.pipe';
import { DecodePipe } from './decode.pipe';
import { EllipsisPipe } from './ellipsis.pipe';
import { EncodePipe } from './encode.pipe';
import { EscapeHtmlPipe } from './escape-html.pipe';
import { HyphenedPipe } from './hyphened.pipe';
import { I18NStatefulPipe } from './i18n-stateful.pipe';
import { I18NPipe } from './i18n.pipe';
import { InitialsPipe } from './initials.pipe';
import { LinkifyPipe } from './linkify.pipe';
import { LowercaseFirstPipe } from './lowercase-first-letter.pipe';
import { NoonaDecodeToCharactersPipe } from './noona-decode-characters.pipe';
import { NoonaDecodePipe } from './noona-decode.pipe';
import { NoonaEncodePipe } from './noona-encode.pipe';
import { OrderByPipe } from './order-by.pipe';
import { RemoveOtherPipe } from './remove-other.pipe';
import { ReplaceCharsPipe } from './replace-chars.pipe';
import { ReplacePipe } from './replace.pipe';
import { TrimPipe } from './trim.pipe';
import { TypeofPipe } from './typeof.pipe';
import { UnescapeHtmlPipe } from './unescape-html.pipe';
import { UnescapePipe } from './unescape.pipe';
import { UrlServerDecodePipe } from './urlserverdecode.pipe';
import { WordBreakPipe } from './word-break.pipe';
import { RegexReplaceInTextPipe } from './regex-replace-in-text.pipe';
import { SafeHtmlPipe } from './safe-html.pipe';
import { LowercaseExceptGermanPipe } from './lowercase-except-german.pipe';
import { BoldPipe } from './bold.pipe';

@NgModule({
    declarations: [
        AbbrPipe,
        HyphenedPipe,
        AmDateFormatPipe,
        ConvertHyphenedToCapitalPipe,
        WordBreakPipe,
        RemoveOtherPipe,
        ReplacePipe,
        ReplaceCharsPipe,
        TrimPipe,
        TypeofPipe,
        UnescapePipe,
        UnescapeHtmlPipe,
        UrlServerDecodePipe,
        CamelCasePipe,
        ConvertLineBreaksPipe,
        DecodePipe,
        EllipsisPipe,
        EncodePipe,
        EscapeHtmlPipe,
        InitialsPipe,
        LinkifyPipe,
        LowercaseFirstPipe,
        LowercaseExceptGermanPipe,
        NoonaDecodePipe,
        NoonaEncodePipe,
        OrderByPipe,
        CapitalFirstLetterPipe,
        I18NPipe,
        I18NStatefulPipe,
        DateFormatPipe,
        NoonaDecodeToCharactersPipe,
        RegexReplaceInTextPipe,
        SafeHtmlPipe,
    ],
    exports: [
        AbbrPipe,
        HyphenedPipe,
        AmDateFormatPipe,
        ConvertHyphenedToCapitalPipe,
        WordBreakPipe,
        RemoveOtherPipe,
        ReplacePipe,
        ReplaceCharsPipe,
        TrimPipe,
        TypeofPipe,
        UnescapePipe,
        UnescapeHtmlPipe,
        UrlServerDecodePipe,
        CamelCasePipe,
        ConvertLineBreaksPipe,
        DecodePipe,
        EllipsisPipe,
        EncodePipe,
        EscapeHtmlPipe,
        InitialsPipe,
        LinkifyPipe,
        LowercaseFirstPipe,
        LowercaseExceptGermanPipe,
        NoonaDecodePipe,
        NoonaEncodePipe,
        OrderByPipe,
        CapitalFirstLetterPipe,
        I18NPipe,
        I18NStatefulPipe,
        DateFormatPipe,
        NoonaDecodeToCharactersPipe,
        RegexReplaceInTextPipe,
        SafeHtmlPipe,
    ],
})
export class ScPipesModule {
    static forRoot(): ModuleWithProviders<ScPipesModule> {
        return {
            ngModule: ScPipesModule,
            providers: [
                AbbrPipe,
                HyphenedPipe,
                AmDateFormatPipe,
                ConvertHyphenedToCapitalPipe,
                WordBreakPipe,
                RemoveOtherPipe,
                ReplacePipe,
                ReplaceCharsPipe,
                TrimPipe,
                TypeofPipe,
                UnescapePipe,
                UnescapeHtmlPipe,
                UrlServerDecodePipe,
                CamelCasePipe,
                ConvertLineBreaksPipe,
                DecodePipe,
                EllipsisPipe,
                EncodePipe,
                EscapeHtmlPipe,
                InitialsPipe,
                LinkifyPipe,
                LowercaseFirstPipe,
                LowercaseExceptGermanPipe,
                NoonaDecodePipe,
                NoonaEncodePipe,
                OrderByPipe,
                CapitalFirstLetterPipe,
                I18NPipe,
                I18NStatefulPipe,
                DateFormatPipe,
                NoonaDecodeToCharactersPipe,
                RegexReplaceInTextPipe,
                SafeHtmlPipe,
                BoldPipe,
            ],
        };
    }
}
