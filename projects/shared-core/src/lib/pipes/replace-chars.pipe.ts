import { Pipe, PipeTransform } from '@angular/core';

/**
 * Replace characters with given regex
 *
 * usage example:
 *
 *      <template>{{ text | replaceChars:'\\s+':'-' }}
 *
 */
@Pipe({ name: 'replaceChars' })
export class ReplaceCharsPipe implements PipeTransform {
    transform(text: string, lookFor: string, replacement: string): any {
        if (!text) {
            return '';
        }
        const regExp = new RegExp(lookFor, 'g');
        return text.replace(regExp, replacement);
    }
}
