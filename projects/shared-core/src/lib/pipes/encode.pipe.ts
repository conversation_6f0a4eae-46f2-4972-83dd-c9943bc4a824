import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'encode' })
export class EncodePipe implements PipeTransform {
    transform(input: any): string {
        if (!input) {
            return '';
        }
        return input
            .replace(/&/g, '&amp;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;');
    }
}
