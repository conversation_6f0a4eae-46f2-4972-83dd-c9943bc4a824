import { ChangeDetectionStrategy, Component, EventEmitter, Inject, Input, isDevMode, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { take } from 'rxjs/operators';
import { MedicalInformation } from '../../../generated/models/medical-information';
import { Patient } from '../../../generated/models/patient';
import { SymptomType } from '../../../generated/models/symptom-type';
import { HyphenedPipe } from '../../../pipes/hyphened.pipe';
import { IllustrationService } from '../../services/illustration.service';
import { FormEngineState, getFormEngineState } from '../../store/reducers/state';
import { I18NPipe } from '../../../pipes/i18n.pipe';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-multiple-symptom-selection',
  styleUrls: ['./multiple-symptom-selection.component.scss'],
  templateUrl: './multiple-symptom-selection.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MultipleSymptomSelectionComponent implements OnInit {
  @Input() patient: Patient;
  @Input() symptomTypes: SymptomType[];

  @Output() typesChanged = new EventEmitter<SymptomType[]>();

  public medicalInformation$: Observable<MedicalInformation>;
  public selectedTypes: Set<SymptomType> = new Set<SymptomType>();
  public disabled = false;
  iconPath = '';

  constructor(
    private i18n: I18NPipe,
    private illustrations: IllustrationService,
    private hyphened: HyphenedPipe,
    private store: Store<FormEngineState>,
    @Inject('env') private env
  ) {}

  ngOnInit() {
    // NOONA-16582 quick fix for not shown icons.
    this.iconPath = this.env.assetPath + 'icons/';
    this.store
      .select(getFormEngineState)
      .pipe(take(1))
      .subscribe((state: FormEngineState) => {
        if (state.form && state.form.forms) {
          Object.keys(state.form.forms).forEach((type: SymptomType) => {
            this.selectedTypes.add(type);
          });

          this.typesChanged.emit(Array.from(this.selectedTypes));
        }
      });
  }

  getIllustrationClass(type: SymptomType): string {
    return this.illustrations.getIllustrationClass(type, this.patient.gender);
  }

  hyphenedIdentifier(type: SymptomType): string {
    return this.hyphened.transform(type);
  }

  toggleSelection(type: SymptomType) {
    if (this.selectedTypes.has(type)) {
      this.selectedTypes.delete(type);
    } else {
      this.selectedTypes.add(type);
    }

    this.typesChanged.emit(Array.from(this.selectedTypes));
  }

  isSelected(type: SymptomType): boolean {
    return this.selectedTypes.has(type);
  }

  isLongSymptomName(type: string): boolean {
    return this.i18n.transform('fe.formNames.' + type).length > 55;
  }
}
