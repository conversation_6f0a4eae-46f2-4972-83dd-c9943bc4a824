import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MultipleSymptomSelectionComponent } from './multiple-symptom-selection.component';
import { MocksModule } from '@shared-core/testing';

describe('MultipleSymptomSelectionComponent', () => {
    let component: MultipleSymptomSelectionComponent;
    let fixture: ComponentFixture<MultipleSymptomSelectionComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [MultipleSymptomSelectionComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(MultipleSymptomSelectionComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
