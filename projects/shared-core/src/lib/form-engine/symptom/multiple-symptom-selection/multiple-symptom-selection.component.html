<div class="symptom-type-select">
  <div class="symptom-select">
    <div class="wizard-section" *ngIf="symptomTypes">
      <div class="section-header">
        <h1 class="section-title">{{ 'case.symptomSelection.selectSymptoms.title' | i18n }}</h1>
        <p class="section-description">
          {{ 'case.symptomSelection.selectSymptoms.description' | i18n }}
        </p>
      </div>
      <ul class="symptom-grid clearfix">
        <li class="symptom-item" *ngFor="let type of symptomTypes" (click)="toggleSelection(type)">
          <div class="symptom-illustration-title">
            <div class="symptom-illustration-title-text" [ngClass]="{ 'long-text': isLongSymptomName(type) }">
              {{ 'fe.formNames.' + type | i18n }}
            </div>
          </div>
          <img class="symptom-illustration-image" [src]="iconPath + getIllustrationClass(type) + '.svg'" alt="" />
          <div class="yes-no-buttons clearfix new">
            <div class="checked-symptom selection-button">
              <input
                type="radio"
                role="button"
                id="{{ hyphenedIdentifier(type) }}-yes-selection"
                [checked]="isSelected(type)"
                [value]="true"
                name="{{ hyphenedIdentifier(type) }}-yes-no-selection"
                [disabled]="disabled"
                (click)="toggleSelection(type)"
              />
              <label for="{{ hyphenedIdentifier(type) }}-yes-selection">
                <img class="checkmark" [src]="iconPath + 'checkmark_white.svg'" alt="" />
              </label>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</div>
