<div id="wizard">
    <div class="symptom-inquiry" *ngIf="medicalInformation as info; else loading">
        <nh-multiple-symptom-selection
            [patient]="patient"
            (typesChanged)="typeSelectionChanged($event)"
            [symptomTypes]="info.symptomTypes"
        ></nh-multiple-symptom-selection>
        <div class="symptom-forms" *ngIf="showSymptomForms">
            <div class="symptom-form" *ngFor="let symptomForm of symptomForms; trackBy: symptomFormTrackBy">
                <nh-form-generator
                    *ngIf="isSymptomVisible(symptomForm.type)"
                    [formType]="symptomForm.type"
                    [gender]="patient.gender"
                    [hideButtons]="true"
                    [modules]="info.modules"
                ></nh-form-generator>
            </div>
        </div>
    </div>
    <div class="wizard-actions">
        <div class="wizard-button symptom-inquiry">
            <div class="errors" *ngIf="error">
                <span class="error">{{ error | i18n }}</span>
            </div>
        </div>
    </div>
    <ng-template #loading>
        <div class="symptom-inquiry">
            <div class="wizard-section loading">
                <div class="text-placeholder text-medium text-large animated-background"></div>
                <div class="block-placeholder col-md-12 animated-background" *ngFor="let i of [1...6]"></div>
            </div>
        </div>
    </ng-template>
</div>
