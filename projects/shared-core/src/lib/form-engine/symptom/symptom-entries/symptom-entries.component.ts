import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import { select, Store } from '@ngrx/store';
import forIn from 'lodash/forIn';
import indexOf from 'lodash/indexOf';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { SharedApi } from '../../../abstract-services/shared-api.service';
import { MedicalInformation } from '../../../generated/models/medical-information';
import { Patient } from '../../../generated/models/patient';
import { SymptomType } from '../../../generated/models/symptom-type';
import { CaseForm } from '../../interface/case-form.interface';
import { FormEntry } from '../../interface/form-entry.interface';
import { FormStatus } from '../../interface/form-status.interface';
import { FieldService } from '../../services/field.service';
import { FormState } from '../../store/reducers/form.reducer';
import { FormEngineState } from '../../store/reducers/state';
import { getFormsState } from '../../store/selectors/form.selectors';
import { getFormValidity } from '../../store/selectors/inquiry.selectors';

const FORM_UPDATE_DELAY_MS = 500;
const SELECT_AT_LEAST_ONE_SYMPTOM = 'wizard.multipleSymptoms.error.pleaseSelectAtLeastOneSymptom';

interface SymptomForm {
    type: SymptomType;
    symptomSelected: boolean;
    valid: boolean;
    formState: object; // represents the state of the symptom form inputs etc
}

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-symptom-entries',
    templateUrl: './symptom-entries.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SymptomEntriesComponent implements OnInit, OnDestroy, FormEntry {
    @Input() continue: Subject<void>;
    @Input() patient: Patient;

    @Output() status = new EventEmitter<CaseForm>();

    medicalInformation: MedicalInformation;

    @Input()
    editMode: false;

    error: string;
    uploading = false;
    showSymptomForms = false;
    summaryShown = false;
    symptomForms: SymptomForm[] = [];

    // represents the last shown symptom form
    private currentFormIndex = -1;

    private destroy$ = new Subject<boolean>();
    private initialTypeSelection = true;

    constructor(
        private api: SharedApi,
        private cd: ChangeDetectorRef,
        private store: Store<FormEngineState>,
        private fieldService: FieldService
    ) {}

    ngOnInit() {
        this.api.symptomApi.getRequiredMedicalInformation(this.patient.id).subscribe((medicalInfo) => {
            this.medicalInformation = medicalInfo;
            this.symptomForms = medicalInfo.symptomTypes.map((type) => {
                return {
                    type,
                    symptomSelected: false,
                    valid: false,
                    formState: {},
                };
            });
            this.addFormListeners();
            this.cd.markForCheck();
        });
        // subscribe to continue click
        this.continue.pipe(takeUntil(this.destroy$)).subscribe(this.continueClickHandler.bind(this));
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    isSymptomVisible(type: SymptomType): boolean {
        // show all the selected forms whose index is less than or equal the current index
        const symptomForm = this.symptomForms.find((sf, index) => {
            return sf.type === type && sf.symptomSelected && index <= this.currentFormIndex;
        });
        return symptomForm ? true : false;
    }

    symptomFormTrackBy(index: number, symptomForm: SymptomForm): any {
        return symptomForm.type;
    }

    isSymptomFormVisible(symptomForm: SymptomForm): boolean {
        return this.isSymptomVisible(symptomForm.type);
    }

    typeSelectionChanged(selectedTypes: SymptomType[]): void {
        if (selectedTypes.length > 0) {
            this.error = undefined;
        } else {
            this.error = SELECT_AT_LEAST_ONE_SYMPTOM;
            this.summaryShown = false;
            this.currentFormIndex = -1;
            this.status.emit({
                status: FormStatus.BACK,
            });
        }

        this.symptomForms = this.medicalInformation.symptomTypes.map((symptomType, index) => {
            if (this.initialTypeSelection && this.editMode) {
                this.summaryShown = true;
                if (selectedTypes.includes(symptomType) && this.currentFormIndex < index) {
                    this.currentFormIndex = index;
                }
            }

            if (selectedTypes.includes(symptomType) && this.currentFormIndex < index && this.summaryShown) {
                this.currentFormIndex = index;
            }

            return {
                type: symptomType,
                symptomSelected: selectedTypes.includes(symptomType),
                // validity && state preserves
                valid: this.symptomForms[index].valid,
                formState: this.symptomForms[index].formState,
            };
        });

        this.showSymptomForms = this.symptomForms.some((form) => {
            return form.symptomSelected;
        });
        this.initialTypeSelection = false;
    }

    /** handles the continue click */
    private continueClickHandler() {
        this.error = undefined;
        if (!this.showSymptomForms) {
            // show the first form
            this.handleSymptomSelectionStep();
        } else {
            // validate the current form and show the next form or proceed to summary
            this.handleSymptomFormStep(true);
        }
    }

    private handleSymptomFormStep(clicked: boolean) {
        if (
            this.symptomForms.every((form) => {
                return !form.symptomSelected;
            })
        ) {
            return;
        }

        const visibleFormsValid = this.symptomForms
            .filter((symptomForm) => {
                return this.isSymptomFormVisible(symptomForm);
            })
            .every((visibleForm) => {
                return visibleForm.valid;
            });

        if (!visibleFormsValid && clicked) {
            this.fieldService.validate();
        }

        if (visibleFormsValid) {
            const showSummary = this.nextStep();
            if (showSummary) {
                this.error = undefined;
                this.status.emit({
                    status: FormStatus.SUCCESS,
                    formData: {
                        symptoms: [
                            ...this.symptomForms
                                .filter((sF) => {
                                    return sF.symptomSelected;
                                })
                                .map((sF) => {
                                    return sF.formState;
                                }),
                        ],
                        patient: this.patient,
                    },
                });
                this.summaryShown = true;
                return;
            } else {
                this.status.emit({
                    status: FormStatus.INCOMPLETE,
                });
            }
        } else {
            if (clicked) {
                this.error = 'wizard.errors.submitFailed';
            }

            this.status.emit({
                status: FormStatus.ERROR,
                skipScroll: !clicked,
            });
        }

        this.cd.markForCheck();
    }

    private handleSymptomSelectionStep() {
        const selectedForms = this.symptomForms.filter((sF) => {
            return sF.symptomSelected;
        });
        if (selectedForms.length < 1) {
            this.error = SELECT_AT_LEAST_ONE_SYMPTOM;
            this.status.emit({
                status: FormStatus.ERROR,
            });
        } else {
            this.status.emit({
                status: FormStatus.INCOMPLETE,
            });
            this.showSymptomForms = true;
            this.nextStep();
        }
    }

    /**
     * Show the next symptom form.
     * returns true if all the symptom forms have been shown, otherwise false
     */
    private nextStep(): boolean {
        // find the next index of a form that is selected
        const nextIndex = indexOf(
            this.symptomForms.map((sF) => {
                return sF.symptomSelected;
            }),
            true,
            this.currentFormIndex + 1
        );
        if (nextIndex !== -1) {
            this.currentFormIndex = nextIndex;
            return false;
        }
        return true;
    }

    private addFormListeners() {
        this.store.pipe(select(getFormValidity), takeUntil(this.destroy$)).subscribe((validities) => {
            forIn(validities, (isValid, symptomType) => {
                const symptomForm = this.symptomForms.find((sF) => {
                    return sF.type === symptomType;
                });
                if (symptomForm) {
                    symptomForm.valid = isValid;
                }
            });

            if (
                Object.values(validities).every((valid) => {
                    return valid;
                })
            ) {
                this.error = undefined;
                this.cd.markForCheck();
            }
        });

        this.store
            .pipe(select(getFormsState), takeUntil(this.destroy$), debounceTime(FORM_UPDATE_DELAY_MS))
            .subscribe((formState) => {
                return this.parseForms(formState);
            });
    }

    parseForms(formState: FormState): void {
        const uploading: { [form: string]: boolean } = {};
        Object.entries(formState.forms).forEach(([symptomType, symptomFormValues]) => {
            const symptomFormState = { ...symptomFormValues };
            uploading[symptomType] = symptomFormState.uploading;

            this.fieldService.clearViewValues(symptomFormState);
            symptomFormState.classType = symptomFormState.type = symptomType;

            if (!symptomFormState.symptomDate) {
                symptomFormState.symptomDate = new Date();
            }

            /* preserve the state in the related symptom form object */
            this.symptomForms.find((sF) => {
                return sF.type === symptomType;
            }).formState = symptomFormState;
        });

        this.uploading = Object.values(uploading).some((val) => {
            return val;
        });

        if (this.summaryShown) {
            this.handleSymptomFormStep(false);
        }
    }
}
