import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SymptomSummariesComponent } from './symptom-summaries.component';
import { MocksModule } from '@shared-core/testing';

describe('SymptomSummariesComponent', () => {
    let component: SymptomSummariesComponent;
    let fixture: ComponentFixture<SymptomSummariesComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [SymptomSummariesComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(SymptomSummariesComponent);
        component = fixture.componentInstance;
        component.symptomCollection = {
            symptoms: [],
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
