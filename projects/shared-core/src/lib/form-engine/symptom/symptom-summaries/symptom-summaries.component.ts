import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Input,
    OnInit,
    Output,
} from '@angular/core';
import { NoonaLocaleService } from '../../../abstract-services/noona-locale.service';
import { Dictionary } from '../../../common-types';
import { Patient } from '../../../generated/models/patient';
import { SaveSymptomResult } from '../../../generated/models/save-symptom-result';
import { SymptomCollection } from '../../../generated/models/symptom-collection';
import { SymptomType } from '../../../generated/models/symptom-type';
import { TriageHomecareInstruction } from '../../../generated/models/triage-homecare-instruction';
import { TriageNoteTranslation } from '../../../generated/models/triage-note-translation';
import { TriageNoteType } from '../../../generated/models/triage-note-type';
import { ArrayUtils } from '../../../util/array.utils';
import { PreviewOption } from '../../../utils/photo-uploader/preview-option.interface';

export interface InstructionEvent {
    ruleId: string;
    translation: string;
}

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-symptom-summaries',
    styleUrls: ['./symptom-summaries.component.scss'],
    templateUrl: './symptom-summaries.component.html',
})
export class SymptomSummariesComponent implements OnInit {
    @Input() symptomCollection: SymptomCollection;
    @Input() patient: Patient;
    @Input() alerts: Dictionary<SymptomType, SaveSymptomResult[]>;
    @Input() previouslySelectedInstructions: string[];
    @Input() instructions: Dictionary<SymptomType, TriageHomecareInstruction[]>;

    @Output() instructionToggled = new EventEmitter<InstructionEvent>();
    @Output() openPhotoPreview = new EventEmitter<PreviewOption>();

    private locale: string;
    private clinicLocale: string;
    private selectedInstructions: string[] = [];

    constructor(private cd: ChangeDetectorRef, private localeService: NoonaLocaleService) {
        this.localeService.getClinicLanguage().then((lang) => {
            this.clinicLocale = lang;
        });
    }

    ngOnInit() {
        if (this.previouslySelectedInstructions && this.previouslySelectedInstructions.length > 0) {
            this.selectedInstructions = [...this.previouslySelectedInstructions];
        }
    }

    getAlerts(type: SymptomType): SaveSymptomResult[] {
        return this.alerts && this.alerts[type] ? this.alerts[type] : [];
    }

    getInstructions(type: SymptomType): TriageHomecareInstruction[] {
        return this.instructions && this.instructions[type] ? this.instructions[type] : [];
    }

    getInstructionTranslation(instruction: TriageHomecareInstruction): string {
        return this.getTranslation(instruction, TriageNoteType.CLINIC_USER);
    }

    isInstructionSelected(instruction: TriageHomecareInstruction): boolean {
        return this.selectedInstructions.includes(instruction.triageRule.id);
    }

    instructionClicked(instruction: TriageHomecareInstruction): void {
        const ruleId = instruction.triageRule.id;
        ArrayUtils.toggle(this.selectedInstructions, ruleId);
        this.instructionToggled.next({
            ruleId,
            translation: this.getTranslation(instruction, TriageNoteType.CASE_NOTE),
        });
        this.cd.markForCheck();
    }

    handleOpenPhotoPreview(previewOption: PreviewOption): void {
        this.openPhotoPreview.emit(previewOption);
    }

    private getTranslation(instruction: TriageHomecareInstruction, type: TriageNoteType): string {
        const translations: TriageNoteTranslation[] = instruction.triageNote
            .filter((note) => {
                return note.noteType === type;
            })
            .flatMap((note) => {
                return note.triageNoteTranslations;
            });

        const userTranslation = translations.find((translation) => {
            return translation.language === this.locale;
        });
        if (userTranslation) {
            return userTranslation.translation;
        }

        const clinicTranslation = translations.find((translation) => {
            return translation.language === this.clinicLocale;
        });
        return clinicTranslation ? clinicTranslation.translation : '';
    }
}
