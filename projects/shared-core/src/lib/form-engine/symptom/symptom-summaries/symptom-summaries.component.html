<div id="wizard">
    <div class="symptom-card">
        <div class="symptom-entries-summary-content summary-step">
            <ng-container *ngFor="let symptom of symptomCollection.symptoms">
                <div class="symptom-header">
                    <div class="symptom-title">{{ 'fe.formNames.' + symptom.classType | i18n }}</div>
                </div>
                <div class="line"></div>
                <div class="symptom-container">
                    <symptom-summary-content
                        [theSymptom]="symptom"
                        [gender]="patient.gender"
                        [onlySubmittedPhotos]="false"
                        (openPreview)="handleOpenPhotoPreview($event)"
                    ></symptom-summary-content>
                </div>
                <div class="symptom-alerts" *ngIf="alerts">
                    <nh-symptom-alert-summary
                        *ngFor="let alert of getAlerts(symptom.type)"
                        [saveSymptomResult]="alert"
                    ></nh-symptom-alert-summary>
                </div>
                <div class="instructions" *ngIf="instructions">
                    <div class="heading-label" *ngIf="getInstructions(symptom.type).length > 0">
                        {{ 'nurse.handlePatient.newCase.homeCare.instructions.title' | i18n }}
                    </div>
                    <div
                        class="input-wrapper"
                        *ngFor="let instruction of getInstructions(symptom.type); let index = index"
                    >
                        <div class="checkbox">
                            <input
                                type="checkbox"
                                [id]="'instruction-' + index"
                                [name]="'instruction-' + index"
                                [checked]="isInstructionSelected(instruction)"
                            />
                            <label
                                (click)="instructionClicked(instruction)"
                                [innerHTML]="
                                    getInstructionTranslation(instruction)
                                        | encode
                                        | convertLineBreaks
                                        | noonaDecode
                                        | wordBreak: 35
                                "
                            ></label>
                        </div>
                    </div>
                </div>
            </ng-container>
        </div>
    </div>
</div>
