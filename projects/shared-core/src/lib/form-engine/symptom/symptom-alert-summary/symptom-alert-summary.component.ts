import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { SaveSymptomResult } from '../../../generated/models/save-symptom-result';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-symptom-alert-summary',
    styleUrls: ['./symptom-alert-summary.component.scss'],
    templateUrl: './symptom-alert-summary.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SymptomAlertSummaryComponent implements OnInit {
    @Input()
    saveSymptomResult: SaveSymptomResult;

    descriptions: string[];
    instruction: string;

    constructor() {}

    ngOnInit() {
        this.descriptions = this.saveSymptomResult.symptomPrioritisationResult.advices.map((advice) => {
            return advice.description;
        });

        this.instruction = this.saveSymptomResult.symptomPrioritisationResult.modalContent;
    }

    getRuleName(): string {
        const advices = this.saveSymptomResult.symptomPrioritisationResult.advices;
        return advices ? advices[0].matchingRuleId : '';
    }
}
