import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SymptomAlertSummaryComponent } from './symptom-alert-summary.component';
import { MocksModule } from '@shared-core/testing';
import { AutoSubmitStatus } from '../../../generated/models/auto-submit-status';

describe('SymptomAlertSummaryComponent', () => {
    let component: SymptomAlertSummaryComponent;
    let fixture: ComponentFixture<SymptomAlertSummaryComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [SymptomAlertSummaryComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(SymptomAlertSummaryComponent);
        component = fixture.componentInstance;
        component.saveSymptomResult = {
            autoSubmitStatus: AutoSubmitStatus.NONE,
            symptomPrioritisationResult: {
                advices: [
                    {
                        description: 'test',
                    },
                ],
            },
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
