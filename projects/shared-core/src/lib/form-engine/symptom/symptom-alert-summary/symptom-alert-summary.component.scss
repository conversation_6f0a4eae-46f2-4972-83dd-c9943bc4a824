@import '../../../../styles/deprecated_mixins.scss';
@import '../../../../styles/deprecated_variables.scss';
@import '../../../form-engine/styles/sprite.scss';
.symptom-alert-container {
  display: flex;
  flex-direction: column;
  .rule-name {
    margin: 10px 0;
    word-break: break-word;
  }
  .icon {
    display: flex;
    align-items: center;
    .icon-alert-container {
      margin-right: 15px;
      display: inline-flex;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: rgba(255, 133, 133, 0.3);
      justify-content: center;
      align-items: center;
      .icon-alert {
        width: 20px;
        height: 20px;
        margin-bottom: 3px;

        @extend .svg-icon_alert_red;
      }
    }
  }
  .patient-instructions-title {
    margin: 10px 0;
    color: $dark;
    @include font-bold();
  }
  .patient-instructions-content,
  .rule-description {
    @include font-light();
    color: $gray-dark;
  }
}
