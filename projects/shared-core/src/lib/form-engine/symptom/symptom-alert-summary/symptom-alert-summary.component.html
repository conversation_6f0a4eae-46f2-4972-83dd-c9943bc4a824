<div class="symptom-alert-container">
    <div class="name-container">
        <div class="icon">
            <div class="icon-alert-container">
                <div class="icon-alert"></div>
            </div>
            <div class="icon-info">{{ 'symptom.noonaTriageAlert' | i18n }}</div>
        </div>
        <h1 class="rule-name">{{ getRuleName() }}</h1>
        <div class="rule-description" *ngFor="let description of descriptions">{{ description }}</div>
    </div>
    <div class="line"></div>
    <div class="instruction-container" *ngIf="!!instruction">
        <div class="patient-instructions-title">{{ 'symptom.alert.patientInstructions' | i18n }}:</div>
        <div class="patient-instructions-content">{{ instruction }}</div>
    </div>
</div>
