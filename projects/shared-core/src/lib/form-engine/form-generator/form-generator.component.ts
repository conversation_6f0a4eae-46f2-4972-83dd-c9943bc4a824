import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output
} from '@angular/core';
import { select, Store } from '@ngrx/store';
import moment, { Moment } from 'moment';
import { take, takeUntil } from 'rxjs/operators';
import { SharedSchemaService } from '../../abstract-services/shared-schema.service';
import { FormType } from '../../constants';
import { FormItemType } from '../../generated/models/form-item-type';
import { Gender } from '../../generated/models/gender';
import { SymptomType } from '../../generated/models/symptom-type';
import { TreatmentModule } from '../../generated/models/treatment-module';
import { CrfFormInformationViewModel } from '../models/crf/crf-form-information-view-model';
import { FormFieldConfig } from '../models/form-field-config.interface';
import { QuestionnaireInquiryInformationViewModel } from '../models/questionnaire-inquiry-information-view-model';
import { ScrollBehavior } from '../models/scroll-behavior.enum';
import { SymptomInquiryInformationViewModel } from '../models/symptom-inquiry-information-view-model';
import { StartNewForm } from '../store/actions/form.actions';
import { FormEngineState } from '../store/reducers/state';
import { selectForm } from '../store/selectors/form.selectors';
import { InquiryType } from '../../generated/models/inquiry-type';
import { PreviewOption } from '../../utils/photo-uploader/preview-option.interface';
import { PhotoPreviewService } from '../services/photo-preview.service';
import { Subject } from 'rxjs';

const BASELINE_OR_FOLLOW_UP = [InquiryType.BASELINE, InquiryType.FOLLOWUP];

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-form-generator',
  templateUrl: './form-generator.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FormGeneratorComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() formType: FormType;
  @Input() gender: Gender;
  @Input() info?: SymptomInquiryInformationViewModel | QuestionnaireInquiryInformationViewModel | CrfFormInformationViewModel;
  @Input() hideButtons = false;
  @Input() answerSince?: Moment;
  @Input() modules: TreatmentModule[];
  @Input() currentState: string;
  @Input() secondaryButtonTranslationKey: string;

  @Output() next = new EventEmitter<void>();
  @Output() previous = new EventEmitter<void>();
  @Output() canceled = new EventEmitter<void>();
  @Output() openPhotoPreview = new EventEmitter<PreviewOption>();

  public fields: FormFieldConfig[];

  private formKey: string;
  private layout: any;
  private destroy$ = new Subject<void>();

  constructor(
    private schema: SharedSchemaService,
    private elRef: ElementRef,
    private store: Store<FormEngineState>,
    private photoPreviewService: PhotoPreviewService
  ) {}

  ngOnInit() {
    this.formKey = `${this.formType}Form`;
    this.layout = this.schema.getSchema().formEngineForms[this.formKey];

    let earlierSymptom = false;

    if (!!this.info) {
      if (!(this.info instanceof QuestionnaireInquiryInformationViewModel || this.info instanceof CrfFormInformationViewModel)) {
        earlierSymptom = this.info.symptomsInDiary && this.info.symptomsInDiary.includes(this.formType as SymptomType);
      }

      if (!this.gender && !(this.info instanceof CrfFormInformationViewModel)) {
        this.gender = this.info.gender;
      }
    }

    const fields = this.parseForm(this.layout.fields, earlierSymptom);
    const sections = fields.filter(field => {
      return field.type === FormItemType.WIZARD_SECTION;
    });
    fields.forEach((section, index) => {
      section.sectionNumber = index + 1;
      section.sectionCount = sections.length;
    });

    this.store.pipe(select(selectForm(this.formType)), take(1)).subscribe(state => {
      if (!state) {
        this.store.dispatch(new StartNewForm({ type: this.formType, totalSections: sections.length }));
      }
    });

    this.fields = fields;

    this.photoPreviewService.openPreview$.pipe(takeUntil(this.destroy$)).subscribe(previewOption => {
      this.openPhotoPreview.emit(previewOption);
    });
  }

  ngAfterViewInit() {
    if (this.elRef.nativeElement.querySelector('.section-header')) {
      this.elRef.nativeElement.querySelector('.section-header').scrollIntoView({
        behavior: ScrollBehavior.SMOOTH,
        block: 'start'
      });
    } else {
      this.elRef.nativeElement.scrollIntoView({
        behavior: ScrollBehavior.SMOOTH,
        block: 'start'
      });
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  public triggerNext() {
    this.next.emit();
  }

  public triggerPrevious() {
    this.previous.emit();
  }

  public triggerCancel() {
    this.canceled.emit();
  }

  private parseForm(fields: any[], earlierSymptom: boolean): FormFieldConfig[] {
    return fields.reduce((items: FormFieldConfig[], current) => {
      if (current.type === FormItemType.ROW) {
        if (current.columns.length > 1) {
          throw new Error('More than 1 column in row item');
        }

        return items.concat(this.parseForm(current.columns[0].formItems, false));
      }

      return [...items, this.parseConfig(current, earlierSymptom)];
    }, []);
  }

  private parseConfig(field: any, earlierSymptom: boolean): FormFieldConfig {
    const config: FormFieldConfig = { ...field };
    config.formKey = this.formKey;
    config.formType = this.formType;
    config.gender = this.gender;

    if (!!this.info) {
      if (this.info instanceof QuestionnaireInquiryInformationViewModel) {
        config.treatmentModuleTypes = [this.info.treatmentModuleType];
      } else if (this.info instanceof CrfFormInformationViewModel) {
        config.treatmentModuleTypes = [];
      } else {
        this.setSymptomReportConfigs(config, this.info);
      }
    } else {
      config.treatmentModuleTypes = this.modules.map(module => {
        return module.treatmentModuleType;
      });
    }

    if (field.formItems) {
      config.children = this.parseForm(field.formItems, false);
      delete (config as any).formItems;
    }

    if (config.type === FormItemType.WIZARD_SECTION && earlierSymptom) {
      config.type = FormItemType.EARLIER_SYMPTOM;
      config.key = 'earlierSymptom';
      config.descriptionKey = 'patient.symptomInquiry.previouslyAddedSymptom.description';
    }

    return config;
  }

  private setSymptomReportConfigs(config: FormFieldConfig, info: SymptomInquiryInformationViewModel): void {
    config.since = this.isBaselineOrFollowUp(info) ? moment().subtract(1, 'month').toDate() : info.lastAeq;
    config.fromInquiry = !!info.symptomInquiry;

    config.subInquiry = config.fromInquiry && info.symptomInquiry && info.symptomInquiry.subInquiry;

    if (config.fromInquiry) {
      config.treatmentModuleTypes = [info.symptomInquiry.patientTreatmentModule.treatmentModule.treatmentModuleType];
    } else {
      throw new Error('Diary entry not supported yet');
    }
  }

  private isBaselineOrFollowUp(info: SymptomInquiryInformationViewModel): boolean {
    return !!info.symptomInquiry && BASELINE_OR_FOLLOW_UP.includes(info.symptomInquiry.type);
  }

  private infoIsSymptomInquiryInformationViewModel(
    info: SymptomInquiryInformationViewModel | QuestionnaireInquiryInformationViewModel | CrfFormInformationViewModel
  ): info is SymptomInquiryInformationViewModel {
    return (info as SymptomInquiryInformationViewModel).symptomInquiry !== undefined;
  }

  public hideFieldForStatusCheckQuestionnaire(
    info: SymptomInquiryInformationViewModel | QuestionnaireInquiryInformationViewModel | CrfFormInformationViewModel
  ) {
    if (info !== undefined && this.infoIsSymptomInquiryInformationViewModel(info)) {
      return info.symptomInquiry.type === InquiryType.TO_POST_TREATMENT;
    } else {
      return false;
    }
  }
}
