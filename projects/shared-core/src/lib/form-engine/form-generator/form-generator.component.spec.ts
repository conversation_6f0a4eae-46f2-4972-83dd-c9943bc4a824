import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { FormGeneratorComponent } from './form-generator.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../generated/models/questionary-type';

describe('FormGeneratorComponent', () => {
    let component: FormGeneratorComponent;
    let fixture: ComponentFixture<FormGeneratorComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [FormGeneratorComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(FormGeneratorComponent);
        component = fixture.componentInstance;
        component.formType = QuestionaryType.BOUNCE_HADS;
        fixture.elementRef.nativeElement.scrollIntoView = jest.fn();
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
