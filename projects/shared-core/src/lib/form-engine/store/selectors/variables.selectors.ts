import { createSelector } from '@ngrx/store';
import { FormEngineState, getFormEngineState } from '../reducers/state';
import * as fromVariables from '../reducers/variables.reducer';

export const selectFormVariablesState = createSelector(getFormEngineState, (state: FormEngineState) => {
    return state && state.variables;
});

export const getAllFormVariables = createSelector(selectFormVariablesState, fromVariables.getAllFormVariables);

export const getFormVariables = createSelector(selectFormVariablesState, fromVariables.getFormVariables);

export const selectFormVariable = (key: string) => {
    return createSelector(getFormVariables, (entities) => {
        return entities && entities[key];
    });
};

export const selectFormVariableValue = (key: string) => {
    return createSelector(getFormVariables, (entities) => {
        if (key) {
            const value = entities && entities[key] && entities[key].value;
            return value && value instanceof Array ? value[0] : value;
        } else {
            return null;
        }
    });
};

export const selectFormVariableArrayValue = (key: string) => {
    return createSelector(getFormVariables, (entities) => {
        if (key) {
            const value = entities && entities[key] && entities[key].value;
            return value && value instanceof Array ? value : [value];
        } else {
            return null;
        }
    });
};
