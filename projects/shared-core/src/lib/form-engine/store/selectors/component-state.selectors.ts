import { Dictionary } from '@ngrx/entity';
import { createSelector } from '@ngrx/store';
import values from 'lodash/values';
import * as fromComponentState from '../reducers/component-state.reducer';
import { FormEngineState, getFormEngineState } from '../reducers/state';
import { ViewComponentState } from '../../models/view-component-state';

export const selectComponentStates = createSelector(getFormEngineState, (state: FormEngineState) => {
    return state && state.componentState;
});

export const getComponentStates = createSelector(selectComponentStates, fromComponentState.getComponentStates);

export const selectAllComponentStates = createSelector(
    getComponentStates,
    (entities: Dictionary<ViewComponentState>) => {
        return entities && values(entities);
    }
);

export const selectComponentState = (key: string) => {
    return createSelector(getComponentStates, (entities: Dictionary<ViewComponentState>) => {
        if (entities) {
            return entities[key];
        } else {
            return null;
        }
    });
};
