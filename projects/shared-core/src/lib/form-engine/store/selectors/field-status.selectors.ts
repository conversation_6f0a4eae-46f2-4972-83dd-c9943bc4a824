import { Dictionary } from '@ngrx/entity';
import { createSelector } from '@ngrx/store';
import lodashValues from 'lodash/values';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import * as fromFieldStatus from '../reducers/field-status.reducer';
import { FormEngineState, getFormEngineState } from '../reducers/state';

export const selectFieldStatusState = createSelector(getFormEngineState, (state: FormEngineState) => {
    return state && state.fieldStatuses;
});

export const getAllFieldStatuses = createSelector(selectFieldStatusState, fromFieldStatus.getAllFieldStatuses);

export const getFieldStatuses = createSelector(selectFieldStatusState, fromFieldStatus.getFieldStatuses);

export const selectFieldStatuses = (
    field?: string,
    fsType?: FieldStatusType,
    translationKey?: string,
    includeChildKeys?: boolean
) => {
    return createSelector(getFieldStatuses, (entities: Dictionary<FieldStatus>) => {
        if (entities) {
            const values = lodashValues(entities);
            return values.filter((fs) => {
                return (
                    !field ||
                    ((fs.fieldKey === field || (includeChildKeys && fs.fieldKey.startsWith(field))) &&
                        (!fsType || fs.statusType === fsType) &&
                        (!translationKey || fs.translationKey === translationKey))
                );
            });
        } else {
            return null;
        }
    });
};
