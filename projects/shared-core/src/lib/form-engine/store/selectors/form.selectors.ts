import { createSelector } from '@ngrx/store';
import { FormType } from '../../../constants';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { FormEngineState, getFormEngineState } from '../reducers/state';
import { getDestructuredSubArrayFields, isSubArrayField } from '../../../util/sub-array-field.utils';

export const getFormsState = createSelector(getFormEngineState, (state: FormEngineState) => {
    return state.form;
});

export const selectForm = (type: FormType) => {
    return createSelector(getFormEngineState, (state: FormEngineState) => {
        return state && state.form && state.form.forms && state.form.forms[type];
    });
};

export const selectField = (type: FormType, field: string) => {
    if (isSubArrayField(field)) {
        const { fieldKey, parentKey, id } = getDestructuredSubArrayFields(field);
        return createSelector(selectForm(type), (form: any) => {
            const index =
                form[parentKey] &&
                form[parentKey].findIndex((answer) => {
                    return answer.index === id;
                });
            return index >= 0 && form[parentKey][index][fieldKey];
        });
    }
    return createSelector(getFormEngineState, (state: FormEngineState) => {
        return state && state.form && state.form.forms && state.form.forms[type] && state.form.forms[type][field];
    });
};

export const selectCurrentSection = (type: FormType) => {
    return createSelector(getFormEngineState, (state: FormEngineState) => {
        return (
            state && state.form && state.form.forms && state.form.forms[type] && state.form.forms[type].currentSection
        );
    });
};

export const selectIsFormDirty = createSelector(getFormsState, (state) => {
    return Object.values(state.forms).some((form) => {
        return form.isDirty;
    });
});
