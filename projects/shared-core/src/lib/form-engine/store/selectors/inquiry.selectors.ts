import { createSelector } from '@ngrx/store';
import { FormType } from '../../../constants';
import { SymptomType } from '../../../generated/models/symptom-type';
import { FormEngineState, getFormEngineState } from '../reducers/state';

export const getInquiryState = createSelector(getFormEngineState, (state: FormEngineState) => {
    return state.inquiry;
});

export const getSymptomTypesState = createSelector(getFormEngineState, (state: FormEngineState) => {
    return state.inquiry.selectedSymptomTypes;
});

export const selectType = (type: SymptomType) => {
    return createSelector(getFormEngineState, (state: FormEngineState) => {
        return state.inquiry.selectedSymptomTypes[type];
    });
};

export const getRequiredTypesState = createSelector(getFormEngineState, (state: FormEngineState) => {
    return state.inquiry.requiredTypeSelections;
});

export const getAllSymptomTypes = createSelector(getFormEngineState, (state: FormEngineState) => {
    return state.inquiry.symptomTypes;
});

export const getUnselectedSymptoms = createSelector(getSymptomTypesState, (symptomTypesState) => {
    return Object.entries(symptomTypesState)
        .filter(([key, value]) => {
            return !value;
        })
        .map(([key, value]) => {
            return key;
        });
});

export const getFormValidity = createSelector(getFormEngineState, (state: FormEngineState) => {
    return state.inquiry.formValidity;
});

export const selectAllInvalidForms = createSelector(getFormValidity, (formValidity) => {
    return Object.entries(formValidity)
        .filter(([formKey, isValid]) => {
            return !isValid;
        })
        .map((entry) => {
            return entry[0];
        }) as FormType[];
});

export const selectAreAllFormsValid = createSelector(getFormValidity, (formValidity) => {
    return Object.values(formValidity).every((valid) => {
        return valid;
    });
});

export const getAdditionalQuestions = createSelector(getFormEngineState, (state: FormEngineState) => {
    return state.inquiry.additionalQuestions;
});

export const selectSymptomInformation = (type: FormType) => {
    return createSelector(getFormEngineState, (state: FormEngineState) => {
        return state.inquiry.symptomInformations[type];
    });
};
