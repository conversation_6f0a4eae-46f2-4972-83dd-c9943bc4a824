import cloneDeep from 'lodash/cloneDeep';
import { getDestructuredSubArrayFields, isSubArrayField } from '../../../util/sub-array-field.utils';
import { Answer } from '../../models/answer.interface';
import * as fromCrf from '../actions/crf.action';
import * as fromForm from '../actions/form.actions';

export interface FormState {
    forms: {
        [type: string]: {
            [field: string]: any;
            validFields: {
                [field: string]: boolean;
            };
            formValid: boolean;
            uploading: boolean;
            currentSection: number;
            totalSections: number;
            isDirty: boolean;
            visibilities: { field: string; visibility: boolean }[];
        };
    };
}

export const initialState = {
    forms: {},
};

export function reducer(state: FormState, action: fromForm.FormAction | fromCrf.CrfFormAction): FormState {
    switch (action.type) {
        case fromForm.RESET_FORM:
            return {
                ...state,
                ...initialState,
            };

        case fromForm.START_NEW_FORM:
            return {
                ...state,
                forms: {
                    ...state.forms,
                    [action.payload.type]: {
                        validFields: {},
                        formValid: false,
                        uploading: false,
                        currentSection: 1,
                        totalSections: action.payload.totalSections || 1,
                        isDirty: false,
                        visibilities: [],
                    },
                },
            };

        case fromForm.CONTINUE_FORM:
            const symptom = action.payload;
            return {
                ...state,
                forms: {
                    ...state.forms,
                    [symptom.type]: {
                        ...symptom,
                        validFields: {},
                        formValid: true,
                        uploading: false,
                        currentSection: 1000,
                        totalSections: 1,
                        isDirty: false,
                        visibilities: [],
                    },
                },
            };

        case fromForm.ADD_ANSWER: {
            const answer = action.payload;
            let newForm: any;
            // e.g. parentKey.0.fieldKey
            if (isSubArrayField(answer.field)) {
                return addSubArrayAnswer(state, action.payload);
            } else {
                // previousAnswer can be undefined either if its value is set to 'undefined' or if the state form doesn't have
                // the field, which is why we also check for hasOwnProperty
                const previousAnswer = state.forms[answer.type][answer.field];
                const keepPreviousAnswer = answer.initialValue && state.forms[answer.type].hasOwnProperty(answer.field);
                newForm = {
                    ...state.forms[answer.type],
                    [answer.field]: keepPreviousAnswer ? previousAnswer : answer.answer,
                    isDirty: state.forms[answer.type].isDirty || !answer.initialValue,
                };
            }

            if (!newForm.validFields) {
                newForm.validFields = {};
            }

            return {
                ...state,
                forms: {
                    ...state.forms,
                    [answer.type]: newForm,
                },
            };
        }

        case fromForm.ADD_ANSWERS: {
            const answers = action.payload;
            const newForms = answers.reduce((acc, answer) => {
                if (isSubArrayField(answer.field)) {
                    acc = {
                        ...acc,
                        ...addSubArrayAnswer(state, answer).forms,
                    };
                } else {
                    const previousAnswer = acc[answer.type][answer.field];
                    const keepPreviousAnswer = answer.initialValue && acc[answer.type].hasOwnProperty(answer.field);
                    acc = {
                        ...acc,
                        [answer.type]: {
                            ...acc[answer.type],
                            [answer.field]: answer.answer,
                        },
                        [answer.field]: keepPreviousAnswer ? previousAnswer : answer.answer,
                        isDirty: acc[answer.type].isDirty || !answer.initialValue,
                    };
                }
                return acc;
            }, state.forms);

            return {
                ...state,
                forms: {
                    ...state.forms,
                    ...newForms,
                },
            };
        }

        case fromForm.NEXT_SECTION: {
            const form = { ...state.forms[action.payload] };
            form.currentSection = form.currentSection + 1;
            return {
                ...state,
                forms: {
                    ...state.forms,
                    [action.payload]: form,
                },
            };
        }

        case fromForm.PREVIOUS_SECTION: {
            const form = { ...state.forms[action.payload] };
            form.currentSection = form.currentSection - 1;
            return {
                ...state,
                forms: {
                    ...state.forms,
                    [action.payload]: form,
                },
            };
        }

        case fromForm.SET_FIELD_VALIDITY: {
            const payload = action.payload;
            const form = { ...state.forms[payload.formType] };

            if (!form.validFields) {
                form.validFields = {};
            }

            const fieldValidity = { [payload.field]: payload.valid };
            form.validFields = { ...form.validFields, ...fieldValidity };
            form.formValid = Object.values(form.validFields).every((valid) => {
                return valid;
            });

            return {
                ...state,
                forms: {
                    ...state.forms,
                    [payload.formType]: form,
                },
            };
        }

        case fromForm.SET_FIELD_VISIBILITY: {
            const payload = action.payload;
            const form = { ...state.forms[payload.formType] };

            const visibilityInfo = { field: payload.field, visibility: payload.visibility };
            form.visibilities = [...form.visibilities, visibilityInfo];

            return {
                ...state,
                forms: {
                    ...state.forms,
                    [payload.formType]: form,
                },
            };
        }

        case fromForm.SET_UPLOADING: {
            const payload = action.payload;
            const form = {
                ...state.forms[payload.form],
                uploading: payload.uploading,
            };

            return {
                ...state,
                forms: {
                    ...state.forms,
                    [payload.form]: form,
                },
            };
        }

        case fromCrf.LOAD_CRF_FORM_SUCCESS:
            return {
                ...state,
                forms: {
                    ...state.forms,
                    [action.payload.baseInformation.crfFormType]: {
                        ...action.payload.formSpecific,
                        validFields: {},
                        formValid: false,
                        uploading: false,
                        currentSection: 1,
                        totalSections: 1,
                        isDirty: false,
                        visibilities: [],
                    },
                },
            };
        default:
            return state;
    }
}

function addSubArrayAnswer(state: FormState, payload: Answer) {
    const { parentKey, fieldKey, id } = getDestructuredSubArrayFields(payload.field);
    // Start with an empty array if parentField is null, which can happen e.g. when FormInputField sets the visibility
    const newAnswer = state.forms[payload.type][parentKey] ? cloneDeep(state.forms[payload.type][parentKey]) : [];
    const index = newAnswer.findIndex((answer) => {
        return answer.index === id;
    });
    if (index === -1) {
        // eslint-disable-next-line no-console
        console.warn(`Could not find array answer with 'index' field equal to ${id}`);
        return state;
    }
    const keepPreviousAnswer = payload.initialValue && newAnswer[index] && newAnswer[index].hasOwnProperty(fieldKey);
    if (keepPreviousAnswer) {
        return state;
    }

    newAnswer[index][fieldKey] = payload.answer;
    const newForm = {
        ...state.forms[payload.type],
        [parentKey]: newAnswer,
        isDirty: state.forms[payload.type].isDirty || !payload.initialValue,
    };

    if (!newForm.validFields) {
        newForm.validFields = {};
    }

    return {
        ...state,
        forms: {
            ...state.forms,
            [payload.type]: newForm,
        },
    };
}
