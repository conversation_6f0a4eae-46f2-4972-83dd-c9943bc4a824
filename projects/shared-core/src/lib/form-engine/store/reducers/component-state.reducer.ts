import { createEntityAdapter, EntityAdapter, EntityState } from '@ngrx/entity';
import * as fromComponentState from '../actions/component-state.action';
import { ViewComponentState } from '../../models/view-component-state';

export type ComponentState = EntityState<ViewComponentState>;

const adapter: EntityAdapter<ViewComponentState> = createEntityAdapter<ViewComponentState>();

export const initialState: ComponentState = adapter.getInitialState();

export function reducer(state: ComponentState, action: fromComponentState.ComponentStateAction): ComponentState {
    switch (action.type) {
        case fromComponentState.RESET_COMPONENT_STATES: {
            return adapter.removeAll(state);
        }

        case fromComponentState.UPDATE_COMPONENT_STATE: {
            return adapter.upsertOne(action.payload, state);
        }

        default:
            return state;
    }
}

const { selectEntities, selectAll } = adapter.getSelectors();

export const getComponentStates = selectEntities;
export const getAllComponentStates = selectAll;
