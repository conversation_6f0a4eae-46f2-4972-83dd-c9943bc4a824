import { createFeatureSelector } from '@ngrx/store';
import * as fromCrf from './crf.reducer';
import * as fromFieldStatus from './field-status.reducer';
import * as fromRender from './form-render.reducer';
import * as fromForm from './form.reducer';
import * as fromInquiry from './inquiry.reducer';
import * as fromPatient from './patient.reducer';
import * as fromQuestionnaire from './questionnaire.reducer';
import * as fromVariables from './variables.reducer';
import * as fromComponentState from './component-state.reducer';

export const FORM_ENGINE_MODULE_NAME = 'formEngine';

export interface FormEngineState {
    inquiry: fromInquiry.InquiryState;
    patient: fromPatient.PatientState;
    form: fromForm.FormState;
    questionnaire: fromQuestionnaire.QuestionnaireState;
    forms: fromRender.FormRenderState;
    crf: fromCrf.CrfFormState;
    variables: fromVariables.FormVariablesState;
    fieldStatuses: fromFieldStatus.FieldStatusState;
    componentState: fromComponentState.ComponentState;
}

export function getInitialFormEngineState(): FormEngineState {
    return {
        inquiry: fromInquiry.initialState,
        patient: fromPatient.initialState,
        form: fromForm.initialState,
        questionnaire: fromQuestionnaire.initialState,
        forms: fromRender.initialState,
        crf: fromCrf.initialState,
        variables: fromVariables.initialState,
        fieldStatuses: fromFieldStatus.initialState,
        componentState: fromComponentState.initialState,
    };
}

export const getFormEngineState = createFeatureSelector(FORM_ENGINE_MODULE_NAME);
