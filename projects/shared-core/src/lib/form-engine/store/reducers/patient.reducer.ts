import { PatientContactInformation } from '../../../generated/models/patient-contact-information';
import * as fromPatient from '../actions/patient.action';

export interface PatientState {
    patient?: PatientContactInformation;
}

export const initialState = {
    patient: undefined,
};

export function reducer(state: PatientState, action: fromPatient.PatientAction): PatientState {
    switch (action.type) {
        case fromPatient.SET_PATIENT_FOR_FORM_ENGINE:
            return {
                ...state,
                patient: action.payload,
            };
        case fromPatient.CLEAR_PATIENT_FOR_FORM_ENGINE:
            return {
                ...state,
                patient: undefined,
            };
        default:
            return state;
    }
}

export const getPatient = (state: PatientState) => {
    return state.patient;
};
