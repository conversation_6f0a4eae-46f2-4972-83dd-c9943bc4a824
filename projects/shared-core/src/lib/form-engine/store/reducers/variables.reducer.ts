import { createE<PERSON>ty<PERSON>dapter, Entity<PERSON>dapter, EntityState } from '@ngrx/entity';
import { FormVariableValue } from '../../models/form-var-value';
import * as fromVariables from '../actions/variables.action';

export interface FormVariablesState extends EntityState<FormVariableValue> {
    loaded: boolean;
    loading: boolean;
}

const adapter: EntityAdapter<FormVariableValue> = createEntityAdapter<FormVariableValue>({
    selectId: (v) => {
        return v.key;
    },
});

export const initialState: FormVariablesState = adapter.getInitialState({
    loaded: false,
    loading: false,
});

export function reducer(state: FormVariablesState, action: fromVariables.FormVariablesAction): FormVariablesState {
    switch (action.type) {
        /*
         * Form must init all available variables upfront!
         * No upsert supported!
         */
        case fromVariables.UPDATE_FORM_VARIABLE: {
            // Allow transient variables to be added adhoc
            if (!state.entities[action.payload.id] && action.payload.changes.transient) {
                // A bit hacky but we assume that transient variables will always provide a needed infos as part of an update / add
                const newTransientVariable: any = {
                    ...action.payload.changes,
                    key: action.payload.id.toString(),
                };
                return adapter.upsertOne(newTransientVariable, { ...state, loaded: true, loading: false });
            } else {
                return adapter.updateOne(action.payload, state);
            }
        }

        case fromVariables.RESET_FORM_VARIABLES: {
            const emptyState = adapter.removeAll(state);
            return adapter.upsertMany(action.payload, { ...emptyState, loaded: true, loading: false });
        }

        default:
            return state;
    }
}

const { selectEntities, selectAll } = adapter.getSelectors();

export const getFormVariables = selectEntities;
export const getAllFormVariables = selectAll;
