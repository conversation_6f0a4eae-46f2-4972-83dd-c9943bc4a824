import { QuestionnaireInquiryInformationViewModel } from '../../models/questionnaire-inquiry-information-view-model';
import * as fromQuestionnaire from '../actions/questionnaire.action';

export interface QuestionnaireState {
    loaded: boolean;
    loading: boolean;
    info: QuestionnaireInquiryInformationViewModel;
    inquiryId: string;
    nextOfKinAnswer: boolean;
}

export const initialState: QuestionnaireState = {
    loaded: false,
    loading: false,
    info: new QuestionnaireInquiryInformationViewModel(),
    inquiryId: '',
    nextOfKinAnswer: false,
};

export function reducer(state: QuestionnaireState, action: fromQuestionnaire.QuestionnaireAction): QuestionnaireState {
    switch (action.type) {
        case fromQuestionnaire.LOAD_QUESTIONNAIRE:
            return {
                ...initialState,
                loading: true,
                inquiryId: action.payload,
            };

        case fromQuestionnaire.LOAD_QUESTIONNAIRE_SUCCESS:
            const info = new QuestionnaireInquiryInformationViewModel();
            info.copyFields(action.payload);
            return {
                ...state,
                loading: false,
                loaded: true,
                info,
            };

        case fromQuestionnaire.RESET_QUESTIONNAIRE_STATE:
            return {
                ...initialState,
            };
        case fromQuestionnaire.QUESTIONNAIRE_SET_NEXT_OF_KIN_ANSWER:
            return {
                ...state,
                nextOfKinAnswer: action.payload,
            };
    }
    return state;
}
