import { createEntity<PERSON>dapter, Entity<PERSON>dapter, EntityState } from '@ngrx/entity';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import * as fromFieldStatus from '../actions/field-status.actions';
import { CrfFormAction } from '../actions/crf.action';

export interface FieldStatusState extends EntityState<FieldStatus> {
    loaded: boolean;
    loading: boolean;
}

const adapter: EntityAdapter<FieldStatus> = createEntityAdapter<FieldStatus>({
    selectId: (v) => {
        return fieldStatusEntityId(v);
    },
});

export const fieldStatusEntityId = (v: FieldStatus) => {
    return v.formType + v.fieldKey + v.translationKey + v.statusType;
};

export const initialState: FieldStatusState = adapter.getInitialState({
    loaded: false,
    loading: false,
});

const componentFieldStatuses = [
    FieldStatusType.HARD_ERROR,
    FieldStatusType.MISSING_VALUE,
    FieldStatusType.NOT_READY,
    FieldStatusType.SOFT_ERROR,
    FieldStatusType.TERMINATION_ERROR,
];

export function reducer(
    state: FieldStatusState,
    action: fromFieldStatus.FieldStatusAction | CrfFormAction
): FieldStatusState {
    switch (action.type) {
        case fromFieldStatus.ADD_OR_UPDATE_FIELD_STATUS: {
            return adapter.upsertOne(action.payload, state);
        }

        case fromFieldStatus.RESET_FIELD_STATUSES: {
            const clearState = adapter.removeAll(state);
            if (!action.fieldStatuses || action.fieldStatuses.length === 0) {
                return clearState;
            } else {
                return adapter.upsertMany(action.fieldStatuses, { ...clearState, loaded: true, loading: false });
            }
        }

        case fromFieldStatus.REMOVE_FIELD_STATUS: {
            return adapter.removeOne(fieldStatusEntityId(action.payload), state);
        }

        case fromFieldStatus.REMOVE_ALL_COMPONENT_FIELD_STATUSES_BY_FORMTYPE_AND_FIELDKEY: {
            const ids: string[] = [];
            for (const objectId of state.ids) {
                const object = state.entities[objectId];
                if (
                    object.formType === action.payload.formType &&
                    object.fieldKey.startsWith(action.payload.fieldKey) &&
                    componentFieldStatuses.some((status) => {
                        return status === object.statusType;
                    })
                ) {
                    ids.push(fieldStatusEntityId(object));
                }
            }
            return adapter.removeMany(ids, state);
        }

        case fromFieldStatus.REMOVE_ALL_FIELD_STATUSES_WITH_KEY_INCLUDING: {
            return removeAllFieldStatusesWithKeyIncluding(action.payload, state);
        }

        default:
            return state;
    }
}

function removeAllFieldStatusesWithKeyIncluding(key: string, state: FieldStatusState): FieldStatusState {
    const ids = state.ids as string[];
    const idsToRemove = ids.filter((id) => {
        return id.includes(key);
    });
    return adapter.removeMany(idsToRemove, state);
}

const { selectEntities, selectAll } = adapter.getSelectors();

export const getFieldStatuses = selectEntities;
export const getAllFieldStatuses = selectAll;
