import { InputGroupViewModel } from '../../models/input-group-view-model.interface';
import * as formRenderAction from '../actions/form-render.action';

export interface FormRenderState {
    questionnaires: QuestionnairesState;
}

export interface QuestionnairesState {
    [formName: string]: QuestionnaireState;
}

export interface QuestionnaireState {
    inputGroups: InputGroupViewModel[];
}

export const initialState: FormRenderState = {
    questionnaires: {},
};

export function reducer(state: FormRenderState, action: formRenderAction.All): FormRenderState {
    switch (action.type) {
        case formRenderAction.ADD_QUESTIONNAIRE_INPUT_GROUP:
            return addInputGroup(state, action);
        case formRenderAction.CLEAR_QUESTIONNAIRE_STATE:
            if (state.questionnaires[action.payload]) {
                const newState: FormRenderState = $.extend(true, {}, state);
                delete newState.questionnaires[action.payload];
                return newState;
            }
            return state;
        default:
            return state;
    }
}

function addInputGroup(state: FormRenderState, action: formRenderAction.AddQuestionnaireInputGroup): FormRenderState {
    if (!action.payload.inputGroup) {
        return state;
    }

    const newState: FormRenderState = $.extend(true, {}, state);
    const inputGroup: InputGroupViewModel = $.extend(true, {}, action.payload.inputGroup);
    const { form } = action.payload;
    if (newState.questionnaires[form]) {
        const questionnaire = newState.questionnaires[form];
        const existingInputGroup = questionnaire.inputGroups.find((group) => {
            return group.name === inputGroup.name;
        });
        if (existingInputGroup) {
            const { values } = existingInputGroup;
            if (equalValues(values, action.payload.inputGroup.values)) {
                return state;
            }

            existingInputGroup.values = inputGroup.values;
        } else {
            questionnaire.inputGroups = [...questionnaire.inputGroups, $.extend(true, {}, action.payload.inputGroup)];
        }
    } else {
        newState.questionnaires[form] = {
            inputGroups: [inputGroup],
        };
    }

    return newState;
}

function equalValues(oldValues: string[], currentValues: string[]): boolean {
    return (
        oldValues.length === currentValues.length &&
        oldValues.every((value, index) => {
            return value === currentValues[index];
        })
    );
}
