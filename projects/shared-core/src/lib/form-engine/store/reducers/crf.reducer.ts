import { CrfFormBaseInformation } from '../../models/crf/crf-form-base-information';
import { CrfFormInformationViewModel } from '../../models/crf/crf-form-information-view-model';
import * as fromCrf from '../actions/crf.action';

export interface CrfFormState {
    loaded: boolean;
    loading: boolean;
    info: CrfFormBaseInformation;
    crfFormId: string;
}

export const initialState: CrfFormState = {
    loaded: false,
    loading: false,
    info: {},
    crfFormId: '',
};

export function reducer(state: CrfFormState, action: fromCrf.CrfFormAction): CrfFormState {
    switch (action.type) {
        case fromCrf.LOAD_CRF_FORM:
            return {
                ...initialState,
                crfFormId: action.payload,
                loading: true,
            };

        case fromCrf.LOAD_CRF_FORM_SUCCESS:
            return {
                ...state,
                loading: false,
                loaded: true,
                info: action.payload.baseInformation,
            };

        case fromCrf.RESET_CRF_FORM_STATE:
            return initialState;

        default:
            return state;
    }
}
