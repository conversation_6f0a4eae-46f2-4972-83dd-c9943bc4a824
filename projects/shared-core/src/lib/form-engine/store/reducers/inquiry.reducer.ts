import { FormType, wellnessSymptomTypes } from '../../../constants';
import { InquiryType } from '../../../generated/models/inquiry-type';
import { SymptomInformation } from '../../../generated/models/symptom-information';
import { SymptomType } from '../../../generated/models/symptom-type';
import { SymptomInquiryInformationViewModel } from '../../models/symptom-inquiry-information-view-model';
import * as fromInquiry from '../actions/inquiry.action';
import get from 'lodash/get';

export interface InquiryState {
    loaded: boolean;
    loading: boolean;
    error: string;
    info: SymptomInquiryInformationViewModel;
    inquiryId: string;
    selectedSymptomTypes: {
        [key: string]: boolean;
    };
    requiredTypeSelections: FormType[];
    symptomTypes: FormType[];
    formVisibility: {
        [key: string]: boolean;
    };
    symptomOrder: {
        [key: string]: number;
    };
    currentStep: number;
    showSummary: boolean;
    formValidity: {
        [type: string]: boolean;
    };
    additionalQuestions: string;
    nextOfKinAnswer: boolean;
    symptomInformations: { [type: string]: SymptomInformation };
}

export const initialState: InquiryState = {
    loaded: false,
    loading: false,
    error: '',
    inquiryId: '',
    info: {},
    selectedSymptomTypes: {},
    requiredTypeSelections: [],
    symptomTypes: [],
    formVisibility: {},
    symptomOrder: {},
    currentStep: -1,
    showSummary: false,
    formValidity: {},
    additionalQuestions: '',
    nextOfKinAnswer: false,
    symptomInformations: {},
};

export function reducer(state: InquiryState, action: fromInquiry.InquiryAction): InquiryState {
    switch (action.type) {
        case fromInquiry.LOAD_SYMPTOM_INQUIRY:
            return {
                ...state,
                loading: true,
                loaded: false,
                error: '',
                info: {},
                inquiryId: action.payload,
            };

        case fromInquiry.LOAD_SYMPTOM_INQUIRY_FAIL:
            let error = '';
            if (action.payload) {
                error = action.payload;
            }
            return {
                ...initialState,
                error,
            };

        case fromInquiry.LOAD_SYMPTOM_INQUIRY_SUCCESS:
            return {
                ...state,
                info: action.payload as SymptomInquiryInformationViewModel,
                loaded: true,
                loading: false,
                requiredTypeSelections: [],
                selectedSymptomTypes: {},
            };

        case fromInquiry.RESET_INQUIRY_STATE:
            return {
                ...initialState,
            };

        case fromInquiry.SELECT_SYMPTOM_TYPE: {
            const symptomIndex = state.symptomOrder[action.payload];
            const newState = {
                ...state,
                selectedSymptomTypes: {
                    ...state.selectedSymptomTypes,
                    [action.payload]: true,
                },
            };

            if (state.currentStep >= symptomIndex) {
                newState.formVisibility = {
                    ...state.formVisibility,
                    [action.payload]: true,
                };
            }
            return newState;
        }

        case fromInquiry.UNSELECT_SYMPTOM_TYPE:
            return {
                ...state,
                selectedSymptomTypes: {
                    ...state.selectedSymptomTypes,
                    [action.payload]: false,
                },
                formVisibility: {
                    ...state.formVisibility,
                    [action.payload]: false,
                },
            };

        case fromInquiry.SET_REQUIRED_SYMPTOM_TYPES:
            return {
                ...state,
                requiredTypeSelections: action.payload,
            };

        case fromInquiry.REMOVE_REQUIRED_TYPE:
            const types = state.requiredTypeSelections.filter((item) => {
                return item !== action.payload;
            });
            return {
                ...state,
                requiredTypeSelections: types,
            };

        case fromInquiry.SET_SYMPTOM_TYPES:
            return {
                ...state,
                symptomTypes: action.payload,
                formVisibility: action.payload.reduce((fields: { [type: string]: boolean }, type: SymptomType) => {
                    return {
                        ...fields,
                        [type]: false,
                    };
                }, {}),
                symptomOrder: action.payload.reduce(
                    (symptomOrder: { [type: string]: number }, type: SymptomType, index: number) => {
                        return {
                            ...symptomOrder,
                            [type]: index,
                        };
                    },
                    {}
                ),
            };

        case fromInquiry.SHOW_SYMPTOM_FORM:
            return {
                ...state,
                formVisibility: {
                    ...state.formVisibility,
                    [action.payload]: true,
                },
            };

        case fromInquiry.HIDE_SYMPTOM_FORM:
            return {
                ...state,
                formVisibility: {
                    ...state.formVisibility,
                    [action.payload]: false,
                },
            };

        case fromInquiry.SHOW_NEXT_FORM: {
            let currentStep = state.currentStep;
            let showSummary = false;
            let showType: string;

            while (true) {
                currentStep = currentStep + 1;
                if (currentStep < state.symptomTypes.length) {
                    const type = state.symptomTypes[currentStep];
                    const selected = state.selectedSymptomTypes[type];

                    // skip generalCondtion, weight and destress for TO_POST_TREATMENT (status check questionnaire)
                    const questionnaireType = get(state, ['info', 'symptomInquiry', 'type']);
                    if (questionnaireType === InquiryType.TO_POST_TREATMENT && wellnessSymptomTypes.includes(type)) {
                        continue;
                    }

                    if (selected || wellnessSymptomTypes.includes(type)) {
                        showType = type;
                        break;
                    }
                } else {
                    showSummary = true;
                    break;
                }
            }

            const newState = {
                ...state,
                currentStep,
                showSummary,
            };

            if (showType) {
                newState.formVisibility = { ...newState.formVisibility, ...{ [showType]: true } };
            }

            return newState;
        }

        case fromInquiry.FORM_VALID:
            return setFormValidity(state, action.payload, true);

        case fromInquiry.FORM_INVALID:
            return setFormValidity(state, action.payload, false);

        case fromInquiry.SET_ADDITIONAL_QUESTIONS:
            return {
                ...state,
                additionalQuestions: action.payload,
            };

        case fromInquiry.SET_NEXT_OF_KIN_ANSWER:
            return {
                ...state,
                nextOfKinAnswer: action.payload,
            };

        case fromInquiry.SET_SYMPTOM_INFORMATIONS:
            return {
                ...state,
                symptomInformations: action.payload,
            };

        default:
            return state;
    }
}

function setFormValidity(state: InquiryState, type: FormType, valid: boolean) {
    return {
        ...state,
        formValidity: {
            ...state.formValidity,
            [type]: valid,
        },
    };
}
