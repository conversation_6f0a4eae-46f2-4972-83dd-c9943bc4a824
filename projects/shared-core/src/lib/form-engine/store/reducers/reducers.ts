import { ActionReducerMap } from '@ngrx/store';
import * as fromCrf from './crf.reducer';
import * as fromFieldStatus from './field-status.reducer';
import * as fromRender from './form-render.reducer';
import * as fromForm from './form.reducer';
import * as fromInquiry from './inquiry.reducer';
import * as fromPatient from './patient.reducer';
import * as fromQuestionnaire from './questionnaire.reducer';
import { FormEngineState } from './state';
import * as fromVariables from './variables.reducer';
import * as fromComponentState from './component-state.reducer';

export const reducers: ActionReducerMap<FormEngineState> = {
    inquiry: fromInquiry.reducer,
    patient: fromPatient.reducer,
    form: fromForm.reducer,
    questionnaire: fromQuestionnaire.reducer,
    forms: fromRender.reducer,
    crf: fromCrf.reducer,
    variables: fromVariables.reducer,
    fieldStatuses: fromFieldStatus.reducer,
    componentState: fromComponentState.reducer,
};
