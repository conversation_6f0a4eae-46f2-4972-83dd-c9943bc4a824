/* eslint-disable max-classes-per-file */

import { Update } from '@ngrx/entity';
import { Action } from '@ngrx/store';
import { FieldStatus } from '../../models/field-status.interface';
import { FormVariableValue } from '../../models/form-var-value';

export const ADD_OR_UPDATE_FIELD_STATUS = '[Form] Add OR Update Field Status';
export const REMOVE_FIELD_STATUS = '[Form] Remove Field Status';
export const RESET_FIELD_STATUSES = '[Form] Reset Field Status';
export const REMOVE_ALL_COMPONENT_FIELD_STATUSES_BY_FORMTYPE_AND_FIELDKEY =
    '[Form] Remove All Component Field Statuses By Form Type And Field Key';
export const REMOVE_ALL_FIELD_STATUSES_WITH_KEY_INCLUDING = '[Form] Remove All Field Statuses With Key Including';

export class AddOrUpdateFieldStatus implements Action {
    readonly type = ADD_OR_UPDATE_FIELD_STATUS;
    constructor(public payload: FieldStatus) {}
}

export class ResetFieldStatuses implements Action {
    readonly type = RESET_FIELD_STATUSES;
    constructor(public fieldStatuses: FieldStatus[]) {}
}

export class RemoveFieldStatus implements Action {
    readonly type = REMOVE_FIELD_STATUS;
    constructor(public payload: FieldStatus) {}
}

export class RemoveAllComponentFieldStatusesFormTypeAndFieldKey implements Action {
    readonly type = REMOVE_ALL_COMPONENT_FIELD_STATUSES_BY_FORMTYPE_AND_FIELDKEY;
    constructor(public payload: FieldStatus) {}
}

export class RemoveAllFieldStatusesWithKeyIncluding implements Action {
    readonly type = REMOVE_ALL_FIELD_STATUSES_WITH_KEY_INCLUDING;
    constructor(public payload: string) {}
}

export type FieldStatusAction =
    | AddOrUpdateFieldStatus
    | ResetFieldStatuses
    | RemoveFieldStatus
    | RemoveAllComponentFieldStatusesFormTypeAndFieldKey
    | RemoveAllFieldStatusesWithKeyIncluding;
