/* eslint-disable max-classes-per-file */
import { Action } from '@ngrx/store';
import { FormType } from '../../../constants';
import { SymptomInformation } from '../../../generated/models/symptom-information';
import { SymptomInquiryInformation } from '../../../generated/models/symptom-inquiry-information';

export const LOAD_SYMPTOM_INQUIRY = '[Inquiry] Load Symptom Inquiry';
export const LOAD_SYMPTOM_INQUIRY_FAIL = '[Inquiry] Load Symptom Inquiry Fail';
export const LOAD_SYMPTOM_INQUIRY_SUCCESS = '[Inquiry] Load Symptom Inquiry Success';
export const RESET_INQUIRY_STATE = '[Inquiry] Reset Symptom Inquiry State';

export const SELECT_SYMPTOM_TYPE = '[Inquiry] Select Symptom Type';
export const UNSELECT_SYMPTOM_TYPE = '[Inquiry] Unselect Symptom Type';

export const SET_SYMPTOM_TYPES = '[Inquiry] Set Symptom Types';
export const SET_REQUIRED_SYMPTOM_TYPES = '[Inquiry] Set Required Symptom Types';
export const REMOVE_REQUIRED_TYPE = '[Inquiry] Remove Required Type';

export const SHOW_SYMPTOM_FORM = '[Inquiry] Show Symptom';
export const HIDE_SYMPTOM_FORM = '[Inquiry] Hide Symptom';
export const SHOW_NEXT_FORM = '[Inquiry] Show Next Form';

export const FORM_VALID = '[Inquiry] Set Form Valid';
export const FORM_INVALID = '[Inquiry] Set Form Invalid';

export const SET_ADDITIONAL_QUESTIONS = '[Inquiry] Set Additional Questions';
export const SET_NEXT_OF_KIN_ANSWER = '[Inquiry] Set Next Of Kin Answer';

export const SET_SYMPTOM_INFORMATIONS = '[Inquiry] Set Symptom Informations';

export class LoadSymptomInquiry implements Action {
    readonly type = LOAD_SYMPTOM_INQUIRY;
    constructor(public payload: string) {}
}

export class LoadSymptomInquiryFail implements Action {
    readonly type = LOAD_SYMPTOM_INQUIRY_FAIL;
    constructor(public payload: string) {}
}

export class LoadSymptomInquirySuccess implements Action {
    readonly type = LOAD_SYMPTOM_INQUIRY_SUCCESS;
    constructor(public payload: SymptomInquiryInformation) {}
}

export class ResetInquiryState implements Action {
    readonly type = RESET_INQUIRY_STATE;
}

export class SelectSymptomType implements Action {
    readonly type = SELECT_SYMPTOM_TYPE;
    constructor(public payload: FormType) {}
}

export class UnselectSymptomType implements Action {
    readonly type = UNSELECT_SYMPTOM_TYPE;
    constructor(public payload: FormType) {}
}

export class SetSymptomTypes implements Action {
    readonly type = SET_SYMPTOM_TYPES;
    constructor(public payload: FormType[]) {}
}

export class SetRequiredSymptomTypes implements Action {
    readonly type = SET_REQUIRED_SYMPTOM_TYPES;
    constructor(public payload: FormType[]) {}
}

export class RemoveRequiredType implements Action {
    readonly type = REMOVE_REQUIRED_TYPE;
    constructor(public payload: FormType) {}
}

export class ShowSymptomForm implements Action {
    readonly type = SHOW_SYMPTOM_FORM;
    constructor(public payload: FormType) {}
}

export class HideSymptomForm implements Action {
    readonly type = HIDE_SYMPTOM_FORM;
    constructor(public payload: FormType) {}
}

export class ShowNextForm implements Action {
    readonly type = SHOW_NEXT_FORM;
}

export class SetFormValid implements Action {
    readonly type = FORM_VALID;
    constructor(public payload: FormType) {}
}

export class SetFormInvalid implements Action {
    readonly type = FORM_INVALID;
    constructor(public payload: FormType) {}
}

export class SetAdditionalQuestions implements Action {
    readonly type = SET_ADDITIONAL_QUESTIONS;
    constructor(public payload: string) {}
}

export class SetNextOfKinAnswer implements Action {
    readonly type = SET_NEXT_OF_KIN_ANSWER;
    constructor(public payload: boolean) {}
}

export class SetSymptomInformations implements Action {
    readonly type = SET_SYMPTOM_INFORMATIONS;
    constructor(public payload: { [type: string]: SymptomInformation }) {}
}

export type InquiryAction =
    | HideSymptomForm
    | LoadSymptomInquiry
    | LoadSymptomInquiryFail
    | LoadSymptomInquirySuccess
    | RemoveRequiredType
    | SelectSymptomType
    | SetAdditionalQuestions
    | SetFormInvalid
    | SetFormValid
    | SetNextOfKinAnswer
    | SetRequiredSymptomTypes
    | SetSymptomTypes
    | ShowNextForm
    | ShowSymptomForm
    | UnselectSymptomType
    | SetSymptomInformations
    | ResetInquiryState;
