/* eslint-disable max-classes-per-file */
import { Action } from '@ngrx/store';
import { FormType } from '../../../constants';
import { Answer } from '../../models/answer.interface';
import { FieldValidity } from '../../models/field-validity.interface';
import { SymptomVm } from '../../models/symptom-view-model.interface';
import { FieldVisibility } from '../../models/field-visibility.interface';

export const START_NEW_FORM = '[Form] Start New';
export const CONTINUE_FORM = '[Form] Continue';
export const ADD_ANSWER = '[Form] Add New Answer';
export const ADD_ANSWERS = '[Form] Add New Answers';
export const NEXT_SECTION = '[Form] Next Section';
export const PREVIOUS_SECTION = '[Form] Previous Section';

export const SET_FIELD_VALIDITY = '[Form] Set Field Validity';
export const SET_FIELD_VISIBILITY = '[Form] Set Field Visibility';
export const SET_UPLOADING = '[Form] Set Form Uploading';

export const RESET_FORM = '[Form] Reset Form State';

export class StartNewForm implements Action {
    readonly type = START_NEW_FORM;

    constructor(public payload: { type: FormType; totalSections?: number }) {}
}

export class ContinueForm implements Action {
    readonly type = CONTINUE_FORM;

    constructor(public payload: SymptomVm) {}
}

export class AddAnswer implements Action {
    readonly type = ADD_ANSWER;
    constructor(public payload: Answer) {}
}

export class AddAnswers implements Action {
    readonly type = ADD_ANSWERS;
    constructor(public payload: Answer[]) {}
}

export class NextSection implements Action {
    readonly type = NEXT_SECTION;
    constructor(public payload: FormType) {}
}

export class PreviousSection implements Action {
    readonly type = PREVIOUS_SECTION;
    constructor(public payload: FormType) {}
}

export class SetFieldValidity implements Action {
    readonly type = SET_FIELD_VALIDITY;
    constructor(public payload: FieldValidity) {}
}

export class SetFieldVisibility implements Action {
    readonly type = SET_FIELD_VISIBILITY;
    constructor(public payload: FieldVisibility) {}
}

export class SetUploading implements Action {
    readonly type = SET_UPLOADING;
    constructor(public payload: { form: FormType; uploading: boolean }) {}
}

export class ResetFormState implements Action {
    readonly type = RESET_FORM;
}

export type FormAction =
    | StartNewForm
    | AddAnswer
    | AddAnswers
    | ContinueForm
    | SetFieldValidity
    | SetUploading
    | NextSection
    | ResetFormState
    | PreviousSection
    | SetFieldVisibility;
