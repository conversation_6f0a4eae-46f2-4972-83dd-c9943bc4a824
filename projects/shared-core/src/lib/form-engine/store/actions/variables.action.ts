/* eslint-disable max-classes-per-file */

import { Update } from '@ngrx/entity';
import { Action } from '@ngrx/store';
import { FormVariableValue } from '../../models/form-var-value';

export const RESET_FORM_VARIABLES = '[Form Variables] Reset Form Variable';

export const UPDATE_FORM_VARIABLE = '[Form Variables] Update Form Variable';

export class ResetFormVariables implements Action {
    readonly type = RESET_FORM_VARIABLES;
    constructor(public payload: FormVariableValue[]) {}
}

export class UpdateFormVariable implements Action {
    readonly type = UPDATE_FORM_VARIABLE;

    constructor(public payload: Update<FormVariableValue>) {}
}

export type FormVariablesAction = ResetFormVariables | UpdateFormVariable;
