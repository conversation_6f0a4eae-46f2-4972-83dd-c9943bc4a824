/* eslint-disable max-classes-per-file */
import { Action } from '@ngrx/store';
import { PatientContactInformation } from '../../../generated/models/patient-contact-information';

export const SET_PATIENT_FOR_FORM_ENGINE = '[Form Engine] Set Patient';
export const CLEAR_PATIENT_FOR_FORM_ENGINE = '[Form Engine] Clear Patient';

export class SetPatientForFormEngine implements Action {
    readonly type = SET_PATIENT_FOR_FORM_ENGINE;
    constructor(public payload: PatientContactInformation) {}
}

export class ClearPatientForFormEngine implements Action {
    readonly type = CLEAR_PATIENT_FOR_FORM_ENGINE;
}

export type PatientAction = SetPatientForFormEngine | ClearPatientForFormEngine;
