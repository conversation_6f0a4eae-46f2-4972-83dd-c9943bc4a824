/* eslint-disable max-classes-per-file */
import { Action } from '@ngrx/store';
import { QuestionnaireInquiryInformation } from '../../../generated/models/questionnaire-inquiry-information';

export const LOAD_QUESTIONNAIRE = '[Questionnaire] Load Questionnaire';
export const LOAD_QUESTIONNAIRE_SUCCESS = '[Questionnaire] Load Questionnaire Success';
export const RESET_QUESTIONNAIRE_STATE = '[Questionnaire] Reset Questionnaire State';
export const QUESTIONNAIRE_SET_NEXT_OF_KIN_ANSWER = '[Questionnaire] Set Next of Kin Answer';

export class LoadQuestionnaire implements Action {
    readonly type = LOAD_QUESTIONNAIRE;
    constructor(public payload: string) {}
}

export class ResetQuestionnaireState implements Action {
    readonly type = RESET_QUESTIONNAIRE_STATE;
}

export class LoadQuestionnaireSucces implements Action {
    readonly type = LOAD_QUESTIONNAIRE_SUCCESS;
    constructor(public payload: QuestionnaireInquiryInformation) {}
}

export class QuestionnaireSetNextOfKinAnswer implements Action {
    readonly type = QUESTIONNAIRE_SET_NEXT_OF_KIN_ANSWER;
    constructor(public payload: boolean) {}
}

export type QuestionnaireAction =
    | LoadQuestionnaire
    | ResetQuestionnaireState
    | LoadQuestionnaireSucces
    | QuestionnaireSetNextOfKinAnswer;
