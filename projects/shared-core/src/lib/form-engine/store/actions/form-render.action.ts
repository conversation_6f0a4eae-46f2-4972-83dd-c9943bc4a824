import { Action } from '@ngrx/store';
import { InputGroupViewModel } from '../../models/input-group-view-model.interface';

/* eslint-disable max-classes-per-file */

export const ADD_QUESTIONNAIRE_INPUT_GROUP = '[Form render questionnaire] add input group';
export const CLEAR_QUESTIONNAIRE_STATE = '[Form render questionnaire] clear state';

export interface QuestionnaireInputGroupInformation {
    form: string;
    inputGroup: InputGroupViewModel;
}

export class AddQuestionnaireInputGroup implements Action {
    readonly type = ADD_QUESTIONNAIRE_INPUT_GROUP;

    constructor(public payload: QuestionnaireInputGroupInformation) {}
}

export class ClearQuestionnaireState implements Action {
    readonly type = CLEAR_QUESTIONNAIRE_STATE;

    constructor(public payload: string) {}
}

export type All = AddQuestionnaireInputGroup | ClearQuestionnaireState;
