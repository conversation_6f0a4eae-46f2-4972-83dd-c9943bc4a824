/* eslint-disable max-classes-per-file */
import { Action } from '@ngrx/store';
import { CrfFormInformation } from '../../models/crf/crf-form-information';

export const LOAD_CRF_FORM = '[CRF Form] Load CRF Form';
export const LOAD_CRF_FORM_SUCCESS = '[CRF Form] Load CRF Form Success';
export const RESET_CRF_FORM_STATE = '[CRF Form] Reset CRF Form State';

export class LoadCrfForm implements Action {
    readonly type = LOAD_CRF_FORM;

    constructor(public payload: string) {}
}

export class ResetCrfFormState implements Action {
    readonly type = RESET_CRF_FORM_STATE;
}

export class LoadCrfFormSuccess implements Action {
    readonly type = LOAD_CRF_FORM_SUCCESS;

    constructor(public payload: CrfFormInformation) {}
}

export type CrfFormAction = LoadCrfForm | ResetCrfFormState | LoadCrfFormSuccess;
