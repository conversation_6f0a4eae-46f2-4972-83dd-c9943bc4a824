/* eslint-disable max-classes-per-file */

import { Update } from '@ngrx/entity';
import { Action } from '@ngrx/store';
import { FormVariableValue } from '../../models/form-var-value';
import { ComponentState } from '../reducers/component-state.reducer';
import { ViewComponentState } from '../../models/view-component-state';

export const RESET_COMPONENT_STATES = '[Component State] Reset Component States';

export const UPDATE_COMPONENT_STATE = '[Component State] Update Component State';

export class ResetComponentStates implements Action {
    readonly type = RESET_COMPONENT_STATES;
}

export class UpdateComponentState implements Action {
    readonly type = UPDATE_COMPONENT_STATE;

    constructor(public payload: ViewComponentState) {}
}

export type ComponentStateAction = ResetComponentStates | UpdateComponentState;
