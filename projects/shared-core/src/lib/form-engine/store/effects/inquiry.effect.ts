import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';

import { Store } from '@ngrx/store';
import { of, EMPTY } from 'rxjs';
import { catchError, map, switchMap, withLatestFrom } from 'rxjs/operators';
import { SymptomInquiryInformation } from '../../../generated/models/symptom-inquiry-information';
import { SymptomInquiryService } from '../../services/symptom-inquiry.service';
import * as inquiryActions from '../actions/inquiry.action';
import * as patientActions from '../actions/patient.action';
import { FormEngineState, getFormEngineState } from '../reducers/state';

@Injectable()
export class InquiryEffects {
  constructor(private actions$: Actions, private inquiryService: SymptomInquiryService, private store: Store<FormEngineState>) {}

  loadInquiry$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(inquiryActions.LOAD_SYMPTOM_INQUIRY),
      switchMap((action: inquiryActions.LoadSymptomInquiry) => {
        return this.inquiryService.loadInquiryData(action.payload).pipe(
          catchError(error => {
            if (error && error.name && error.name === 'NoonaApiError') {
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              return of({ error: 'server' } as SymptomInquiryInformation);
            } else {
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              return of({ error: 'internal' } as SymptomInquiryInformation);
            }
          }),
          map(information => {
            return new inquiryActions.LoadSymptomInquirySuccess(information);
          })
        );
      })
    );
  });

  removeRequiredType$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(inquiryActions.SELECT_SYMPTOM_TYPE, inquiryActions.UNSELECT_SYMPTOM_TYPE),
      switchMap((action: inquiryActions.SelectSymptomType | inquiryActions.UnselectSymptomType) => {
        return of(new inquiryActions.RemoveRequiredType(action.payload));
      })
    );
  });

  hideSymptomForm$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(inquiryActions.UNSELECT_SYMPTOM_TYPE),
      switchMap((action: inquiryActions.UnselectSymptomType) => {
        return of(new inquiryActions.HideSymptomForm(action.payload));
      })
    );
  });

  hideCurrentForm$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(inquiryActions.UNSELECT_SYMPTOM_TYPE),
      withLatestFrom(this.store.select(getFormEngineState)),
      switchMap(([action, state]) => {
        const s = state as FormEngineState;
        const a = action as inquiryActions.UnselectSymptomType;

        const currentStep = s.inquiry.currentStep;
        const symptomIndex = s.inquiry.symptomOrder[a.payload];

        if (currentStep === symptomIndex) {
          return of(new inquiryActions.ShowNextForm());
        }
        return EMPTY;
      })
    );
  });
}
