import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { CrfFormInformation } from '../../models/crf/crf-form-information';
import { CrfFormService } from '../../services/crf-form.service';
import * as crfActions from '../actions/crf.action';

@Injectable()
export class CrfFormEffects {
    constructor(private actions$: Actions, private crfFormService: CrfFormService) {}

    loadCrfForm$ = createEffect(() => {
        return this.actions$.pipe(
            ofType(crfActions.LOAD_CRF_FORM),
            switchMap((action: crfActions.LoadCrfForm) => {
                return this.crfFormService.loadCrfFormInformation(action.payload).pipe(
                    catchError((_error) => {
                        return of({
                            answerable: false,
                        });
                    }),
                    map((info: CrfFormInformation) => {
                        return new crfActions.LoadCrfFormSuccess(info);
                    })
                );
            })
        );
    });
}
