import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';

import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { QuestionnaireService } from '../../services/questionnaire.service';
import * as questionnaireActions from '../actions/questionnaire.action';

@Injectable()
export class QuestionnaireEffects {
    constructor(private actions$: Actions, private questionnaireService: QuestionnaireService) {}

    loadQuestionnaire$ = createEffect(() => {
        return this.actions$.pipe(
            ofType(questionnaireActions.LOAD_QUESTIONNAIRE),
            switchMap((action: questionnaireActions.LoadQuestionnaire) => {
                return this.questionnaireService.loadQuestionnaireInformation(action.payload).pipe(
                    catchError((_error) => {
                        return of({
                            answerable: false,
                        });
                    }),
                    map((info) => {
                        return new questionnaireActions.LoadQuestionnaireSucces(info);
                    })
                );
            })
        );
    });
}
