import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';

import { Action, Store } from '@ngrx/store';

import { Observable, of } from 'rxjs';
import { debounceTime, switchMap, withLatestFrom } from 'rxjs/operators';
import { SharedApi } from '../../../abstract-services/shared-api.service';
import * as formActions from '../actions/form.actions';
import * as inquiryActions from '../actions/inquiry.action';
import { FormEngineState } from '../reducers/state';
import { getFormsState } from '../selectors/form.selectors';

const BACKEND_REFRESH_DEBOUNCE = 1000;

@Injectable()
export class FormEffects {
    constructor(private actions$: Actions, private store: Store<FormEngineState>, private api: SharedApi) {}

    validateForm$ = createEffect(() => {
        return this.actions$.pipe(
            ofType(formActions.SET_FIELD_VALIDITY),
            withLatestFrom(this.store.select(getFormsState)),
            switchMap(([a, state]) => {
                const action = a as formActions.SetFieldValidity;

                const form = state.forms[action.payload.formType];
                if (form && form.formValid) {
                    return of(new inquiryActions.SetFormValid(action.payload.formType)) as Observable<Action>;
                } else {
                    return of(new inquiryActions.SetFormInvalid(action.payload.formType)) as Observable<Action>;
                }
            })
        );
    });

    refreshBackend$ = createEffect(
        () => {
            return this.actions$.pipe(
                ofType(formActions.ADD_ANSWER),
                debounceTime(BACKEND_REFRESH_DEBOUNCE),
                switchMap(() => {
                    if (this.api.medicalApi) {
                        this.api.medicalApi.refreshUserSession().subscribe();
                    }
                    return of();
                })
            );
        },
        { dispatch: false }
    );
}
