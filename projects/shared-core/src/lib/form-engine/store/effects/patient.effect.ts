import { Actions, createEffect, ofType } from '@ngrx/effects';
import { ClearPatientForFormEngine, SetPatientForFormEngine } from '../actions/patient.action';
import { LOAD_QUESTIONNAIRE_SUCCESS, LoadQuestionnaireSucces } from '../actions/questionnaire.action';
import {
    LOAD_SYMPTOM_INQUIRY_FAIL,
    LOAD_SYMPTOM_INQUIRY_SUCCESS,
    LoadSymptomInquirySuccess,
} from '../actions/inquiry.action';
import { catchError, map, switchMap } from 'rxjs/operators';

import { Injectable } from '@angular/core';
import { PatientWrapperService } from '../../services/patient-wrapper.service';
import { of } from 'rxjs';

@Injectable()
export class PatientEffects {
    constructor(private actions$: Actions, private patientService: PatientWrapperService) {}

    clearPatient$ = createEffect(() => {
        return this.actions$.pipe(
            ofType(LOAD_SYMPTOM_INQUIRY_FAIL),
            switchMap(() => {
                return of(new ClearPatientForFormEngine());
            })
        );
    });

    loadPatient$ = createEffect(() => {
        return this.actions$.pipe(
            ofType(LOAD_QUESTIONNAIRE_SUCCESS, LOAD_SYMPTOM_INQUIRY_SUCCESS),
            switchMap((action: LoadQuestionnaireSucces | LoadSymptomInquirySuccess) => {
                return this.patientService.getPatientInformation(action.payload.patientId).pipe(
                    map((patientInfo) => {
                        return new SetPatientForFormEngine(patientInfo);
                    }),
                    catchError((_error) => {
                        return of(new ClearPatientForFormEngine());
                    })
                );
            })
        );
    });
}
