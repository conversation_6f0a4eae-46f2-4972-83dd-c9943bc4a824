<div class="step questionary-summary-step">
  <div class="form-element-container">
    <h1 class="section-title">{{ title | i18n }}</h1>
    <p class="section-description" [innerText]="description | i18n"></p>
  </div>
  <div class="actions mb-l">
    <ds-button *ngIf="viewMode" (buttonClick)="close()">
      {{ 'general.close' | i18n }}
    </ds-button>
    <ds-button *ngIf="!viewMode" variation="secondary" (buttonClick)="canceled.next()">
      {{ 'general.cancel' | i18n }}
    </ds-button>
    <ds-button *ngIf="!viewMode" class="submit-button" type="submit" (buttonClick)="submit()">
      {{ 'general.save' | i18n }}
    </ds-button>
  </div>
  <div class="wizard-actions upper-wizard-actions">
    <div class="next-of-kin-section horizontally-centered" *ngIf="askNextOfKin">
      <div class="checkbox">
        <input type="checkbox" id="next-of-kin" name="next-of-kin" [checked]="nextOfKinAnswered" (change)="toggleNextOfKin()" />
        <label for="next-of-kin">{{ 'symptomInquiry.summary.nextOfKin.checkboxLabel' | i18n }}</label>
      </div>
    </div>
  </div>
  <div class="form-element-container">
    <div class="questionnaire-summary-content">
      <h2 class="questionnaire-title">{{ 'fe.formNames.' + info.questionnaireType | i18n }}</h2>

      <div class="symptom" *ngIf="questionnaire">
        <symptom-summary-content
          [theSymptom]="questionnaire"
          [gender]="info.gender"
          [onlySubmittedPhotos]="false"
        ></symptom-summary-content>
      </div>
    </div>
  </div>
  <div class="actions form-element-container">
    <ds-button *ngIf="viewMode" (buttonClick)="close()">
      {{ 'general.close' | i18n }}
    </ds-button>
    <ds-button *ngIf="!viewMode" variation="secondary" (buttonClick)="canceled.next()">
      {{ 'general.cancel' | i18n }}
    </ds-button>
    <ds-button *ngIf="!viewMode" class="submit-button" type="submit" (buttonClick)="submit()">
      {{ 'general.save' | i18n }}
    </ds-button>
  </div>
</div>
