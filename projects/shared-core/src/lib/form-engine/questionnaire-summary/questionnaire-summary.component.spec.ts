import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockModule } from 'ng-mocks';
import { QuestionnaireSummaryComponent } from './questionnaire-summary.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../generated/models/questionary-type';
import { NgModule, NO_ERRORS_SCHEMA } from '@angular/core';
import { SymptomSummaryContentComponent } from '../summary/symptom-summary-modal/symptom-summary-content.component';

@NgModule({
    declarations: [SymptomSummaryContentComponent],
    exports: [SymptomSummaryContentComponent],
})
class SymptomSummaryContentComponentModule {}

describe('QuestionnaireSummaryComponent', () => {
    let component: QuestionnaireSummaryComponent;
    let fixture: ComponentFixture<QuestionnaireSummaryComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule, MockModule(SymptomSummaryContentComponentModule)],
            declarations: [QuestionnaireSummaryComponent],
            schemas: [NO_ERRORS_SCHEMA],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(QuestionnaireSummaryComponent);
        component = fixture.componentInstance;
        component.info = {
            questionnaireType: QuestionaryType.BOUNCE_HADS,
            symptomsInDiary: [],
            copyFields: () => {},
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
