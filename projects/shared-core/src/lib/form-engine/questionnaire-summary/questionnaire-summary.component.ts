import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output
} from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { debounceTime, take, takeUntil } from 'rxjs/operators';
import { I18NPipe } from '../../pipes/i18n.pipe';
import { QuestionnaireInquirySubmit } from '../interface/questionnaire-inquiry-submit.interface';
import { QuestionnaireInquiryInformationViewModel } from '../models/questionnaire-inquiry-information-view-model';
import { FieldService } from '../services/field.service';
import { QuestionnaireSetNextOfKinAnswer } from '../store/actions/questionnaire.action';
import { FormState } from '../store/reducers/form.reducer';
import { FormEngineState } from '../store/reducers/state';
import { getFormsState } from '../store/selectors/form.selectors';

const DELAY = 500;

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-questionnaire-summary',
  templateUrl: './questionnaire-summary.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class QuestionnaireSummaryComponent implements OnInit, OnDestroy {
  // To be implemented later when patient side is completed
  public viewMode = false;

  @Input() info: QuestionnaireInquiryInformationViewModel;
  @Input() isValid: boolean;
  @Input() askNextOfKin: boolean;

  @Output() canceled = new EventEmitter<void>();
  @Output() save = new EventEmitter<QuestionnaireInquirySubmit>();

  public questionnaire: any;
  public nextOfKinAnswered = false;
  public title = '';
  public description = '';
  public disclaimer = '';

  private destroy$ = new Subject<boolean>();
  private submitting = false;

  constructor(
    private elRef: ElementRef,
    private store: Store<FormEngineState>,
    private cd: ChangeDetectorRef,
    private i18n: I18NPipe,
    private fieldService: FieldService
  ) {}

  ngOnInit() {
    this.listenForFormChanges();
    this.title = 'patient.wizard.summary.title';
    this.description = 'patient.wizard.summary.description';
  }

  ngOnDestroy() {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  public close() {
    // To be implemented later when patient side is completed
  }

  public toggleNextOfKin() {
    this.nextOfKinAnswered = !this.nextOfKinAnswered;
    this.store.dispatch(new QuestionnaireSetNextOfKinAnswer(this.nextOfKinAnswered));
    this.questionnaire = {
      ...this.questionnaire,
      nextOfKinAnswer: this.nextOfKinAnswered
    };
    this.cd.markForCheck();
  }

  public submit() {
    if (!this.isValid) {
      this.fieldService.validate();
      return;
    }

    if (!this.submitting) {
      this.submitting = true;

      const message = {
        title: this.i18n.transform(`fe.formNames.${this.info.questionnaireType}`),
        content: this.i18n.transform('patient.questionary.submit.message.content')
      };

      const questionnaire = { ...this.questionnaire };
      this.fieldService.clearViewValues(questionnaire);

      this.save.next({ questionnaire, message, messageId: this.info.messageId, patientId: this.info.patientId });
    }
  }

  private listenForFormChanges() {
    this.store
      .select(getFormsState)
      .pipe(take(1))
      .subscribe(initialState => {
        this.parseForm(initialState);

        this.store
          .select(getFormsState)
          .pipe(takeUntil(this.destroy$), debounceTime(DELAY))
          .subscribe(state => {
            return this.parseForm(state);
          });
      });
  }

  private parseForm(state: FormState) {
    const f = state.forms[this.info.questionnaireType] ? state.forms[this.info.questionnaireType] : Object.values(state.forms)[0];
    this.questionnaire = { ...f };
    this.questionnaire.questionaryDate = new Date();
    this.questionnaire.classType = this.questionnaire.type = this.info.questionnaireType;

    this.cd.markForCheck();
  }
}
