@import '../../../styles/ds-variables.scss';

$footer-height: 90px;
:host,
.crf-form {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-height: 0;
}
.crf-form {
  &-footer {
    height: $footer-height;
    background-color: $color-white;
  }
  &-content {
    margin: auto;
    width: 728px;
  }
  &-content-scroll-container {
    flex: 1;
    overflow-y: auto;
  }
}
.general-error-footer {
  &:hover {
    cursor: pointer;
  }
  &--type-termination {
    background-color: #f0e9f6;
    color: $color-brand-1-darken-2;
  }
  &--type-soft {
    background-color: #e3f6fc;
    color: $color-brand-2-darken-2;
  }
  &--type-hard {
    background-color: #ffe6e6;
    color: $color-brand-3-darken-2;
  }
}
