import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { CrfFormEntryComponent } from './crf-form-entry.component';
import { MocksModule } from '@shared-core/testing';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';

describe('CrfFormEntryComponent', () => {
    let component: CrfFormEntryComponent;
    let fixture: ComponentFixture<CrfFormEntryComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [CrfFormEntryComponent],
            providers: [
                {
                    provide: ActivatedRoute,
                    useValue: {
                        queryParams: of({ submittedForm: 'false' }),
                    },
                },
            ],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(CrfFormEntryComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
