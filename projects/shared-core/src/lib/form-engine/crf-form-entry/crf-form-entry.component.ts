import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { select, Store } from '@ngrx/store';
import { combineLatest, Subject } from 'rxjs';
import { debounceTime, take, takeUntil, withLatestFrom } from 'rxjs/operators';
import { FormSpecificHandlerFactory } from '../../abstract-services/form-specific-handler-factory.service';
import { FormSpecificHandler } from '../../abstract-services/form-specific-handler.service';
import { SharedSchemaService } from '../../abstract-services/shared-schema.service';
import { CrfFormBaseInformation } from '../models/crf/crf-form-base-information';
import { CrfFormInformationViewModel } from '../models/crf/crf-form-information-view-model';
import { CrfFormSubmit } from '../models/crf/crf-form-submit.interface';
import { CRFVariableValue } from '../models/crf/crf-var-value';
import { FieldStatus, FieldStatusType } from '../models/field-status.interface';
import { FormVariableValue } from '../models/form-var-value';
import { FieldService } from '../services/field.service';
import { FormVariablesService } from '../services/form-variables.service';
import { ResetComponentStates } from '../store/actions/component-state.action';
import { LoadCrfForm, ResetCrfFormState } from '../store/actions/crf.action';
import { ResetFieldStatuses } from '../store/actions/field-status.actions';
import { ResetFormState } from '../store/actions/form.actions';
import { ResetFormVariables } from '../store/actions/variables.action';
import { FormState } from '../store/reducers/form.reducer';
import { FormEngineState } from '../store/reducers/state';
import { selectCrfState } from '../store/selectors/crf.selectors';
import { selectFieldStatuses } from '../store/selectors/field-status.selectors';
import { getFormsState } from '../store/selectors/form.selectors';
import { getFormValidity } from '../store/selectors/inquiry.selectors';
import { getAllFormVariables } from '../store/selectors/variables.selectors';

const DELAY_MS = 500;

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-crf-form-entry',
    templateUrl: './crf-form-entry.component.html',
    styleUrls: ['./crf-form-entry.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CrfFormEntryComponent implements OnInit, OnDestroy {
    @Input() crfFormId: string;
    @Input() primaryButtonTranslationKey: string;
    @Input() secondaryButtonTranslationKey: string;
    @Input() cancelButtonTranslationKey: string;

    @Output() handleCancel = new EventEmitter<void>();
    @Output() goBack = new EventEmitter<void>();
    @Output() save = new EventEmitter<CrfFormSubmit>();
    @Output() saveDraft = new EventEmitter<CrfFormSubmit>();

    public info: CrfFormInformationViewModel;
    public submissionDisabled = true;
    public isInitialized = false;
    public finishLaterAllowed = true;

    private isSubmitting = false;
    private isValid = false;

    private crfForm: any;
    private fieldStatuses: FieldStatus[] = [];
    private formSpecificHandler: FormSpecificHandler;

    private destroy$ = new Subject<boolean>();

    constructor(
        private store: Store<FormEngineState>,
        private fieldService: FieldService,
        private cd: ChangeDetectorRef,
        private fshf: FormSpecificHandlerFactory,
        private fvSrv: FormVariablesService,
        private schemaService: SharedSchemaService,
        private route: ActivatedRoute
    ) {}

    ngOnInit() {
        // Make sure CRF form is loaded cleanly
        this.store.dispatch(new ResetCrfFormState());
        this.store.dispatch(new LoadCrfForm(this.crfFormId));

        this.route.queryParams.subscribe((params) => {
            if (params && params.submittedForm) {
                const alreadySubmittedForm: boolean = JSON.parse(params.submittedForm);
                this.finishLaterAllowed = !alreadySubmittedForm;
            }
        });

        this.store
            .pipe(
                select(selectCrfState),
                withLatestFrom(this.store.select(getFormsState), (crfState, formState) => {
                    return { crfState, formState };
                }),
                takeUntil(this.destroy$)
            )
            .subscribe(({ crfState, formState }) => {
                if (crfState.loaded && !crfState.info.answerable) {
                    this.goBack.next();
                    return;
                }

                if (crfState.loaded && !this.isInitialized) {
                    this.info = new CrfFormInformationViewModel();
                    this.info.copyFields(crfState.info);

                    if (crfState.info.schema) {
                        this.setSchemaForForm(crfState.info);
                    }

                    this.parseForm(formState);

                    this.initFormVariableHandling(crfState.info);
                    this.initFormSpecificHandler(crfState.info);
                    this.initFormFieldStatusHandling(crfState.info);
                    this.listenForFormChanges();

                    this.isInitialized = true;
                    this.cd.markForCheck();
                }
            });

        this.store
            .select(getFormValidity)
            .pipe(takeUntil(this.destroy$))
            .subscribe((valids) => {
                this.isValid = Object.values(valids).every((valid) => {
                    return valid;
                });
                this.cd.markForCheck();
            });
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
        this.store.dispatch(new ResetComponentStates());
        this.store.dispatch(new ResetCrfFormState());
        this.store.dispatch(new ResetFormState());
        this.store.dispatch(new ResetFormVariables([]));
    }

    public cancel() {
        this.handleCancel.emit();
    }

    public submit() {
        if (!this.isValid) {
            this.fieldService.validate();
            return;
        }

        if (!this.isSubmitting) {
            this.isSubmitting = true;

            // Get the latest form state since this.crfForm could have stale data due to debounced update
            // Use debounce since form specific handlers could still have pending validations
            combineLatest([this.store.select(getFormsState), this.store.select(selectFieldStatuses())])
                .pipe(debounceTime(10), take(1))
                .subscribe(([state, fieldStatuses]) => {
                    this.parseForm(state);

                    const crfForm = { ...this.crfForm };
                    const crfFormVariables = this.fetchCrfFormVariablesForBackendPost(this.info);
                    const submitContent: CrfFormSubmit = {
                        crfForm,
                        crfFormVariables,
                        fieldStatuses,
                    };
                    const formIsValid = this.formSpecificHandler.checkFormValidity(submitContent);

                    if (formIsValid) {
                        this.fieldService.clearViewValues(crfForm);
                        this.save.emit(submitContent);
                    } else {
                        setTimeout(this.scrollToFirstError, 500);
                    }

                    setTimeout(() => {
                        this.isSubmitting = false;
                    }, 500);
                });
        }
    }

    public scrollToFirstError() {
        const elements = document.getElementsByClassName('field-status-message-container-unaccepted');
        const firstError = elements.item(0);
        if (firstError) {
            firstError.scrollIntoView({ behavior: 'auto', block: 'center', inline: 'center' });
        }
    }

    public finishLater() {
        const crfForm = { ...this.crfForm };
        const crfFormVariables = this.fetchCrfFormVariablesForBackendPost(this.info);
        let fieldStatuses = this.fieldStatuses.map((fs) => {
            return { ...fs };
        });
        fieldStatuses = fieldStatuses.filter((status) => {
            return status.statusType !== FieldStatusType.MISSING_VALUE;
        });

        this.fieldService.clearViewValues(crfForm);

        const saveDraftContent: CrfFormSubmit = {
            crfForm,
            crfFormVariables,
            fieldStatuses,
        };

        this.saveDraft.emit(saveDraftContent);
    }

    private listenForFormChanges() {
        this.store.pipe(select(getFormsState), takeUntil(this.destroy$), debounceTime(DELAY_MS)).subscribe((state) => {
            return this.parseForm(state);
        });
    }

    private setSchemaForForm(infos: CrfFormBaseInformation) {
        this.schemaService.overwriteFormSchema(infos.crfFormType + 'Form', infos.schema);
    }

    private parseForm(state: FormState) {
        if (!this.info || !state) {
            return;
        }
        const f = state.forms[this.info.crfFormType]
            ? state.forms[this.info.crfFormType]
            : Object.values(state.forms)[0];
        this.crfForm = { ...f };
        this.crfForm.crfFormDate = new Date();
        this.crfForm.classType = this.crfForm.type = this.info.crfFormType;

        this.cd.markForCheck();
    }

    private initFormSpecificHandler(info: CrfFormBaseInformation) {
        this.formSpecificHandler = this.fshf.getHandler(info.crfFormType, info.study.type);
        if (this.formSpecificHandler) {
            this.formSpecificHandler.init(this.store, this.destroy$, info);
        }
    }

    private initFormFieldStatusHandling(info: CrfFormBaseInformation) {
        this.store.dispatch(new ResetFieldStatuses(info.formFieldStatuses));
        this.store
            .select(selectFieldStatuses())
            .pipe(debounceTime(200), takeUntil(this.destroy$))
            .subscribe((statuses: FieldStatus[]) => {
                this.fieldStatuses = statuses;
                if (statuses) {
                    const unAcceptedStatues =
                        statuses.filter((status) => {
                            return !status.accepted;
                        }).length > 0;
                    const acceptedTerminationErrors =
                        statuses.filter((status) => {
                            return status.statusType === FieldStatusType.TERMINATION_ERROR && status.accepted === true;
                        }).length > 0;
                    this.submissionDisabled = unAcceptedStatues && !acceptedTerminationErrors;
                } else {
                    this.submissionDisabled = false;
                }
                this.cd.markForCheck();
            });
    }

    private initFormVariableHandling(info: CrfFormBaseInformation) {
        // CRF Form variables are stored as string.
        // Need to transform the string value to the correct runtime value (e.g. Date)
        // before usage and transform back before save.
        const formVariables =
            info.variableValuesWithUpdates &&
            info.variableValuesWithUpdates
                .filter((v) => {
                    return !!v;
                })
                .map((v) => {
                    return {
                        ...v,
                        value: this.fvSrv.getValueForUsage(v),
                    };
                });
        this.store.dispatch(new ResetFormVariables(formVariables));
    }

    private fetchCrfFormVariablesForBackendPost(info: CrfFormBaseInformation): CRFVariableValue[] {
        let crfFormVariables: CRFVariableValue[] = [];
        this.store.pipe(select(getAllFormVariables), take(1)).subscribe((variables: FormVariableValue[]) => {
            crfFormVariables =
                variables &&
                variables.map((v) => {
                    const crfV = {
                        ...v,
                        value: this.fvSrv.getValueForSave(v),
                        studyId: info.study.id,
                        siteId: info.site.id,
                    };
                    return crfV;
                });
        });
        return crfFormVariables.filter((fv) => {
            return !fv.transient;
        });
    }

    public footerErrorMessageDisplayed(): boolean {
        const unconfirmedFieldStatuses = this.fieldStatuses.filter((status) => {
            return !status.accepted && status.statusType !== FieldStatusType.NOT_READY;
        });
        return unconfirmedFieldStatuses.length > 0;
    }

    // TODO Add localisation, which probably needs rethinking of error messages
    public getGeneralErrorMessages(): string[] {
        const errorCountByType = this.getErrorCountByType();
        const hardErrorCount = errorCountByType[FieldStatusType.HARD_ERROR];
        const missingValueCount = errorCountByType[FieldStatusType.MISSING_VALUE];
        const terminationErrorCount = errorCountByType[FieldStatusType.TERMINATION_ERROR];
        const softErrorCount = errorCountByType[FieldStatusType.SOFT_ERROR];
        const messages = [];
        let message = '';

        if (hardErrorCount + missingValueCount > 0) {
            message = 'Please fix';
            if (hardErrorCount + missingValueCount >= 2) {
                message += ' all';
            }
            if (hardErrorCount > 0) {
                message += ` ${hardErrorCount} ${hardErrorCount > 1 ? 'errors' : 'error'}`;
            }
            if (hardErrorCount > 0 && missingValueCount > 0) {
                message += ' and';
            }
            if (missingValueCount > 0) {
                message += ` ${missingValueCount} missing ${missingValueCount > 1 ? 'values' : 'value'}`;
            }
            message += ' before submitting the form.';
            messages.push(message);
        }
        if (terminationErrorCount + softErrorCount > 0) {
            message = 'Please confirm';
            if (terminationErrorCount + softErrorCount >= 2) {
                message += ' all';
            }
            if (softErrorCount > 0) {
                message += ` ${softErrorCount} ${softErrorCount > 1 ? 'warnings' : 'warning'}`;
            }
            if (terminationErrorCount > 0 && softErrorCount > 0) {
                message += ' and';
            }
            if (terminationErrorCount > 0) {
                message += ` ${terminationErrorCount} failed eligibility criteria`;
            }
            message += '. Revise your answers if necessary.';
            messages.push(message);
        }

        return messages;
    }

    getGeneralErrorStyle() {
        if (this.hasOnlyWarnings()) {
            return 'warning';
        }
        if (this.hasOnlyTerminationErrors()) {
            return 'termination';
        }
        return 'hard';
    }

    private getErrorCountByType() {
        const softErrorCount = this.fieldStatuses.filter((status) => {
            return status.statusType === FieldStatusType.SOFT_ERROR && !status.accepted;
        }).length;
        const terminationErrorCount = this.fieldStatuses.filter((status) => {
            return status.statusType === FieldStatusType.TERMINATION_ERROR && !status.accepted;
        }).length;
        const hardErrorCount = this.fieldStatuses.filter((status) => {
            return status.statusType === FieldStatusType.HARD_ERROR;
        }).length;
        const missingErrorCount = this.fieldStatuses.filter((status) => {
            return status.statusType === FieldStatusType.MISSING_VALUE;
        }).length;

        return {
            [FieldStatusType.HARD_ERROR]: hardErrorCount,
            [FieldStatusType.MISSING_VALUE]: missingErrorCount,
            [FieldStatusType.SOFT_ERROR]: softErrorCount,
            [FieldStatusType.TERMINATION_ERROR]: terminationErrorCount,
        };
    }

    private hasOnlyWarnings(): boolean {
        const errorCountByType = this.getErrorCountByType();

        return (
            errorCountByType[FieldStatusType.SOFT_ERROR] > 0 &&
            errorCountByType[FieldStatusType.TERMINATION_ERROR] === 0 &&
            errorCountByType[FieldStatusType.HARD_ERROR] === 0 &&
            errorCountByType[FieldStatusType.MISSING_VALUE] === 0
        );
    }

    private hasOnlyTerminationErrors(): boolean {
        const errorCountByType = this.getErrorCountByType();

        return (
            errorCountByType[FieldStatusType.TERMINATION_ERROR] > 0 &&
            errorCountByType[FieldStatusType.SOFT_ERROR] === 0 &&
            errorCountByType[FieldStatusType.HARD_ERROR] === 0 &&
            errorCountByType[FieldStatusType.MISSING_VALUE] === 0
        );
    }
}
