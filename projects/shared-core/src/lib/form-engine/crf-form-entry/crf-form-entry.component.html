<div *ngIf="isInitialized" class="crf-form">
    <div class="crf-form-content-scroll-container">
        <div class="crf-form-content pt-l pb-l">
            <nh-form-generator
                *ngIf="info"
                (cancel)="cancel()"
                [formType]="info.crfFormType"
                [hideButtons]="true"
                [info]="info"
            >
            </nh-form-generator>
        </div>
    </div>
    <div
        *ngIf="footerErrorMessageDisplayed()"
        (click)="scrollToFirstError()"
        [className]="
            'general-error-footer flex flex--column flex--justify-content-center flex--align-items-center sticky pl-xl pr-xl pt-m pb-m border--top general-error-footer--type-' +
            getGeneralErrorStyle()
        "
    >
        <span *ngFor="let message of getGeneralErrorMessages()">{{ message }}</span>
    </div>
    <footer
        class="crf-form-footer flex flex--justify-content-space-between flex--align-items-center sticky pl-xl pr-xl border--top"
    >
        <ds-button (buttonClick)="cancel()" variation="link">{{ cancelButtonTranslationKey | i18n }}</ds-button>
        <div class="right-aligned">
            <ds-button *ngIf="finishLaterAllowed" (buttonClick)="finishLater()" class="mr-m" variation="secondary">{{
                secondaryButtonTranslationKey | i18n
            }}</ds-button>
            <ds-button (buttonClick)="submit()" [isDisabled]="submissionDisabled">{{
                primaryButtonTranslationKey | i18n
            }}</ds-button>
        </div>
    </footer>
</div>
