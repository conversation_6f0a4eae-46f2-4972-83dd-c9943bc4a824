@import '../../../styles/deprecated_mixins.scss';
@import '../../../styles/deprecated_variables.scss';
.questionnaire-summary-content {
  .symptom-title {
    margin-top: $spacing-base;
    @include text-heading-sm();
    margin-bottom: $spacing-xsmall;
  }
  .symptom-summary-content {
    background-color: $color-primary-background;
    padding: $spacing-base;
    .sent-to-clinic-info {
      display: none;
    }
  }
  .additional-questions {
    .title {
      margin-top: $spacing-base 0 $spacing-small 0;
      @include text-heading-sm();
      padding: $spacing-base 0 $spacing-xsmall 0;
    }
    .content {
      @include text-normal();
      background-color: $color-primary-background;
      padding: $spacing-base;
    }
  }
}
