import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Gender } from '../../generated/models/gender';
import { Symptom } from '../../generated/models/symptom';
import { SymptomType } from '../../generated/models/symptom-type';
import { PreviewOption } from '../../utils/photo-uploader/preview-option.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'questionnaire-summary-content',
    styleUrls: ['./questionnaire-summary-content.scss'],
    templateUrl: './questionnaire-summary-content.html',
})
export class QuestionnaireSummaryContentComponent implements OnInit {
    // --------------------------------------------------------------------
    // ------------------ Attributes --------------------------------------
    // --------------------------------------------------------------------
    public painPointerLocations = null;
    public symptoms: Symptom[];
    public questionary: any;

    @Input() onlySubmittedPhotos = true;
    @Input() gender: Gender;
    @Input()
    public set theQuestionary(questionary: any) {
        if (questionary) {
            this.questionary = questionary;
            this.symptoms = questionary.symptoms ? questionary.symptoms : [questionary];
            this.symptoms.sort((s1, s2) => {
                if (s1.type === SymptomType.SYMPTOM_GENERAL_CONDITION) {
                    return -1;
                }
                if (s2.type === SymptomType.SYMPTOM_GENERAL_CONDITION) {
                    return 1;
                }
                return 0;
            });
        }
    }
    @Input() noSymptoms: string[];

    @Output() openPhotoPreview = new EventEmitter<PreviewOption>();

    // --------------------------------------------------------------------
    // ------------------ Life Cycle --------------------------------------
    // --------------------------------------------------------------------

    ngOnInit() {
        if (!this.gender) {
            throw Error('Gender has to be specified.');
        }
    }

    handleOpenPhotoPreview(previewOption: PreviewOption) {
        this.openPhotoPreview.emit(previewOption);
    }
}
