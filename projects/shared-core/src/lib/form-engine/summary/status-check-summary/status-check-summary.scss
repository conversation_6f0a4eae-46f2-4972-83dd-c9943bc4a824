@import '../../../../styles/deprecated_mixins.scss';
@import '../../../../styles/deprecated_variables.scss';
.status-check-summary__container {
  .symptom-title {
    margin-top: $spacing-base;
    @include text-heading-sm();
    margin-bottom: $spacing-xsmall;
  }
}
.status-check-summary__content {
  background-color: $color-primary-background;
  border-radius: 10px;
  padding: 15px;
  .symptom-attribute {
    margin-bottom: $spacing-base;
  }
  .symptom-attribute-title {
    @include font-semibold2();
  }
  .symptom-attribute-value {
    width: 100%;
  }
}
