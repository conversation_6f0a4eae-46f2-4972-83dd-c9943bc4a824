import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Symptom } from '../../../generated/models/symptom';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'status-check-summary',
    styleUrls: ['./status-check-summary.scss'],
    templateUrl: './status-check-summary.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StatusCheckSummaryComponent {
    @Input() statusCheck: Symptom;
}
