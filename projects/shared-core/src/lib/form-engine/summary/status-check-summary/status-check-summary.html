<div class="status-check-summary__container">
    <div class="symptom-title">{{ 'fe.formNames.statusCheck' | i18n }}</div>
    <div class="status-check-summary__content">
        <div class="symptom-attribute" *ngIf="statusCheck.fellingIntensity != undefined">
            <div class="symptom-attribute-title">{{'fe.statusCheck.fellingIntensity.title' | i18n }}</div>
            <div class="symptom-attribute-value">{{statusCheck.fellingIntensity}} / 10</div>
        </div>
        <div class="symptom-attribute" *ngIf="statusCheck.anySymptom != undefined">
            <div class="symptom-attribute-title">{{'fe.statusCheck.anySymption.title' | i18n }}</div>
            <div class="symptom-attribute-value">
                {{'fe.statusCheck.anySymption.' + statusCheck.anySymptom + '.label' | i18n }}
            </div>
        </div>
        <div class="symptom-attribute" *ngIf="statusCheck.topics && statusCheck.topics.length > 0">
            <div class="symptom-attribute-title">{{'fe.statusCheck.topics.title' | i18n }}</div>
            <div class="symptom-attribute-value">
                <div *ngFor="let topic of statusCheck.topics">
                    <ng-container *ngIf="topic !== 'other'">
                        {{'fe.statusCheck.topics.' + topic + '.label' | i18n}}
                    </ng-container>
                </div>
                <ng-container *ngIf="statusCheck.otherTopics">{{statusCheck.otherTopics}}</ng-container>
            </div>
        </div>
        <div class="symptom-attribute" *ngIf="statusCheck.medicationQuestion">
            <div class="symptom-attribute-title">{{ 'fe.statusCheck.medicationQuestion.title' | i18n }}</div>
            <div class="symptom-attribute-value">{{ statusCheck.medicationQuestion }}</div>
        </div>
        <div class="symptom-attribute" *ngIf="statusCheck.appointmentQuestion">
            <div class="symptom-attribute-title">{{ 'fe.statusCheck.appointmentQuestion.title' | i18n }}</div>
            <div class="symptom-attribute-value">{{ statusCheck.appointmentQuestion }}</div>
        </div>
        <div class="symptom-attribute" *ngIf="statusCheck.insureOrFinancQuestion">
            <div class="symptom-attribute-title">{{ 'fe.statusCheck.insureOrFinancQuestion.title' | i18n }}</div>
            <div class="symptom-attribute-value">{{ statusCheck.insureOrFinancQuestion }}</div>
        </div>
        <div class="symptom-attribute" *ngIf="statusCheck.anyContact != undefined">
            <div class="symptom-attribute-title">{{'fe.statusCheck.anyContact.title' | i18n }}</div>
            <div class="symptom-attribute-value">
                {{'fe.statusCheck.anyContact.' + statusCheck.anyContact + '.label' | i18n }}
            </div>
        </div>
        <div class="symptom-attribute" *ngIf="statusCheck.contactMethods">
            <div class="symptom-attribute-title">{{'fe.statusCheck.contactMethods.title' | i18n }}</div>
            <div class="symptom-attribute-value">
                {{ 'fe.statusCheck.contactMethods.' + statusCheck.contactMethods + '.label' | i18n }}
            </div>
        </div>
        <div class="symptom-attribute" *ngIf="statusCheck.contactTime">
            <div class="symptom-attribute-title">{{'fe.statusCheck.contactTime.title' | i18n }}</div>
            <div class="symptom-attribute-value">{{ statusCheck.contactTime}}</div>
        </div>
    </div>
</div>
