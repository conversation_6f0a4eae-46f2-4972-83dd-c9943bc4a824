import {
    ChangeDetectionStrategy,
    ChangeDetector<PERSON>ef,
    Component,
    EventEmitter,
    HostBinding,
    Input,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import moment, { Moment } from 'moment';

import { AddQuestionnaireInputGroup } from '../../store/actions/form-render.action';
import { Application } from '../../../models/application';
import { ArrayUtils } from '../../../util/array.utils';
import { ConfigurationProviderService } from '../../../abstract-services/configuration-provider.service';
import { DatepickerService } from '../../services/datepicker.service';
import { FormEngineState } from '../../store/reducers/state';
import { Gender } from '../../../generated/models/gender';
import { HyphenedPipe } from '../../../pipes/hyphened.pipe';
import { NoonaLocaleService } from '../../../abstract-services/noona-locale.service';
import { PainPointerSide } from '../../models/pain-pointer-side.enum';
import { PreviewOption } from '../../../utils/photo-uploader/preview-option.interface';
import { QuestionaryType } from '../../../generated/models/questionary-type';
import { ReportOrigin } from '../../models/symptom-report-origin.enum';
import { SharedSchemaService } from '../../../abstract-services/shared-schema.service';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { SymptomService } from '../../services/symptom.service';
import { TDateRangeIntervals } from '../../interface/t-date-range-intervals.interface';
import { TRangeData } from '../../interface/t-range-data.interface';
import { TRangePosition } from '../../interface/t-range-position.interface';
import cloneDeep from 'lodash/cloneDeep';
import filter from 'lodash/filter';
import isDate from 'lodash/isDate';
import isNumber from 'lodash/isNumber';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'symptom-summary-content',
    styleUrls: ['./symptom-summary-content.scss'],
    templateUrl: './symptom-summary-content.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SymptomSummaryContentComponent implements OnDestroy, OnInit {
    @HostBinding('class') get hostClasses() {
        return [
            'nh-symptom-summary-content',
            this.hasBeenSendToClinic() ? 'nh-symptom-summary-content--sent-to-clinic' : '',
            this.class,
        ].join(' ');
    }
    constructor(
        private datePickerService: DatepickerService,
        private schema: SharedSchemaService,
        private hyphenedPipe: HyphenedPipe,
        private store: Store<FormEngineState>,
        private cd: ChangeDetectorRef,
        private configService: ConfigurationProviderService,
        private symptomService: SymptomService,
        private localeService: NoonaLocaleService
    ) {}

    // --------------------------------------------------------------------
    // ------------------ Attributes --------------------------------------
    // --------------------------------------------------------------------

    @Input() gender: Gender;
    @Input() class: string;
    @Input() onlySubmittedPhotos = true;
    @Input()
    public set theSymptom(symptom: any) {
        if (symptom) {
            this.symptom = symptom;

            if (symptom.photo) {
                this.symptom.photo = cloneDeep(symptom.photo);
            }

            this.selectPainPointerLocations();
            this.cd.markForCheck();
        }
    }

    @Output() openPreview = new EventEmitter<PreviewOption>();

    public painPointerLocations = null;
    public containsPhotos = true;
    public symptom: any;
    public viewInputGroups = {};
    public isScoringEnabled = true;
    public ReportOrigin = ReportOrigin;
    locale: string;

    readonly PainPointerSide = PainPointerSide;

    private dateAttributeRangeData: TRangeData = {};
    private longFormatPattern = 'dd.MM.yyyy';
    private destroy$: Subject<boolean> = new Subject<boolean>();
    private inputGroups: { [key in string]: { [field in string]: string } };

    // --------------------------------------------------------------------
    // ------------------ Life Cycle --------------------------------------
    // --------------------------------------------------------------------

    public ngOnInit() {
        this.configService.dateFormattingPattern().subscribe((pattern) => {
            this.longFormatPattern = pattern.longDatePattern;
        });

        this.inputGroups = this.schema.getSchema().inputGroups;
        this.setIsScoringEnabled();
        this.locale = this.localeService.getLocale();
    }

    public ngOnDestroy() {
        if (this.destroy$ && !this.destroy$.closed) {
            this.destroy$.next(true);
            this.destroy$.complete();
        }
    }

    // --------------------------------------------------------------------
    // ------------------ Generic Summary ---------------------------------
    // --------------------------------------------------------------------

    public getMaxScore(q): string {
        if (q.type === QuestionaryType.BOUNCE_HADS) {
            return '/42';
        } else if (
            q.type === QuestionaryType.TENONC_DISTRESS_AFTERBASELINE ||
            q.type === QuestionaryType.TENONC_DISTRESS_BASELINE
        ) {
            return '/6';
        } else if (q.type === QuestionaryType.QOL15_D) {
            return '/10';
        }

        return '';
    }

    public itemAttributeList() {
        const allAttributes = this.symptomService.getSymptomAttributes(this.symptom);
        return filter(allAttributes, (a: string) => {
            return !this.shouldSkip(a);
        });
    }

    public shouldSkip = function (attr: string) {
        if (this.inputGroups && this.inputGroups[this.symptom.type]) {
            if (this.inputGroups[this.symptom.type][attr]) {
                this.addAttributeToInputGroup(attr);
                return true;
            }
        }

        let value = this.symptom[attr];

        if (attr === 'description') {
            return true;
        }

        if (attr === 'locations' && ((Array.isArray(value) && value.length === 0) || !value)) {
            value = this.symptom.otherLocations;
        }

        if (attr === 'score' && value < 0) {
            return true;
        }

        return value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0);
    };

    public showDescription(): boolean {
        return this.symptom.description && this.symptom.description.length > 0;
    }

    public getContainerClass(attribute: string) {
        return this.hyphenedPipe.transform('attribute-container-' + attribute);
    }

    public isPerformanceStatus() {
        const symptomsWithoutDateRanges = [
            'generalCondition',
            'hairChanges',
            'libidoAndErection',
            'weightChanges',
            'distress',
            'weight',
        ];
        return symptomsWithoutDateRanges.indexOf(this.symptom.type) >= 0;
    }

    public showReportingDate() {
        const reportingDate = this.getReportingDate();
        return !this.symptom.peakDate || !reportingDate.isSame(this.symptom.peakDate, 'day');
    }

    public getReportingDate(): Moment {
        return this.symptom.created ? moment(this.symptom.created) : moment();
    }

    public hasDateRangeAndPeakDate() {
        return this.symptom.dateRange && this.symptom.dateRange.length > 0 && this.symptom.peakDate;
    }

    public getDateString(arrayValue: Moment, rangePosition: TRangePosition, last: boolean) {
        const dateString = this.toLongDateFormat(arrayValue);
        const consecutiveDays = rangePosition.isStart && !rangePosition.isEnd;
        const singleDayInRange = !last && rangePosition.isEnd;
        if (!singleDayInRange && !consecutiveDays && !last) {
            return;
        } else if (consecutiveDays) {
            return dateString + '&nbsp;-&nbsp;';
        } else if (singleDayInRange) {
            return dateString + ', ';
        } else if (last) {
            return dateString;
        }
    }

    public toLongDateFormat(date: Moment | Date): string {
        return moment(date).format(this.longFormatPattern);
    }

    public getTranslationKey(value: string): string {
        return this.symptomService.getItemsTranslations(this.symptom)[value];
    }

    public getUnit(attr): string {
        if (this.symptom[attr + 'Unit']) {
            return this.symptom[attr + 'Unit'];
        }
        return '';
    }

    public getRangeIntervalsIfIsDateArray(allDates): TDateRangeIntervals {
        if (allDates instanceof Array && allDates.length > 0) {
            // eslint-disable-next-line @typescript-eslint/prefer-for-of
            for (let idx = 0; idx < allDates.length; idx++) {
                if (!this.isDate(allDates[idx])) {
                    return null;
                }
            }
            return this.datePickerService.calculateDateRangeStartsAndEnds(allDates, this.longFormatPattern);
        }
        return null;
    }

    public getDatePositionInRangeIntervals(date, dateRangeInterval: TDateRangeIntervals): TRangePosition {
        if (!date || !dateRangeInterval) {
            return { isStart: true, isEnd: true };
        }
        date = moment(date);
        const dateISO = date.format(this.longFormatPattern);
        const isStart = dateRangeInterval.rangeStartsISO.indexOf(dateISO) >= 0;
        const isEnd = dateRangeInterval.rangeEndsISO.indexOf(dateISO) >= 0;
        return { isStart, isEnd };
    }

    public containsPhotosChange(doesContain) {
        this.containsPhotos = doesContain;
        this.cd.detectChanges();
    }

    public getRangePosition(arrayValue, attribute): TRangePosition {
        this.dateAttributeRangeData[attribute] = {
            rangeIntervals: null,
            rangePositions: null,
        };
        const rangeData: any = this.dateAttributeRangeData[attribute];
        if (!rangeData.rangeIntervals) {
            rangeData.rangeIntervals = this.getRangeIntervalsIfIsDateArray(this.symptom[attribute]);
        }
        if (!rangeData.rangePositions) {
            rangeData.rangePositions = {};
        }
        if (!rangeData.rangePositions[arrayValue]) {
            rangeData.rangePositions[arrayValue] = this.getDatePositionInRangeIntervals(
                arrayValue,
                rangeData.rangeIntervals
            );
        }
        return rangeData.rangePositions[arrayValue];
    }

    public isDate(item) {
        return (
            isDate(item) ||
            moment.isMoment(item) ||
            (isNumber(item) && item > 10000 && moment(item).isValid) ||
            (isNumber(item) && item < -10000 && moment(item).isValid)
        );
    }

    public isFirstItem(attr) {
        return (
            !Array.isArray(this.symptom[attr]) ||
            this.symptom[attr].length === 0 ||
            (this.symptom[attr].length === 1 && this.symptom[attr][0] === 'other')
        );
    }

    public getHeight(attribute) {
        if (this.symptom.heightUnit === 'cm') {
            return this.symptom[attribute] + ' ' + this.symptom.heightUnit;
        }
        if (this.symptom.heightUnit === 'ftAndIn') {
            const feet = Math.floor(this.symptom[attribute] / 12);
            const inches = Math.floor(this.symptom[attribute] % 12);

            return feet + ' ft ' + inches + ' in';
        }
        return '';
    }

    public hasBeenSendToClinic() {
        return this.symptom.submitted || this.symptom.hasSymptomReport;
    }

    public get titleIsClinicNotifiedAboutSevere(): boolean {
        if (!this.symptom?.reportOrigin) {
            return false;
        }
        return (
            this.symptom?.reportOrigin === ReportOrigin.DIARY ||
            this.symptom?.reportOrigin === ReportOrigin.QUESTIONNAIRE
        );
    }

    private selectPainPointerLocations() {
        const painPointers = this.symptomService.getPainPointers();
        this.painPointerLocations =
            this.symptom &&
            painPointers[this.symptom.type] &&
            this.symptom[painPointers[this.symptom.type]] &&
            this.symptom[painPointers[this.symptom.type]].length > 0
                ? this.symptom[painPointers[this.symptom.type]]
                : null;
    }

    private addAttributeToInputGroup(attr: string): void {
        const value = this.symptom[attr];
        if (value === undefined || value === null) {
            return;
        }

        const inputGroupName: string = this.inputGroups[this.symptom.type][attr];
        if (!this.viewInputGroups[inputGroupName]) {
            this.viewInputGroups[inputGroupName] = {
                name: inputGroupName,
                values: [],
                formName: this.symptom.type,
            };
        }

        const values: string[] = this.viewInputGroups[inputGroupName].values;
        const form = this.symptom.id ? this.symptom.id : this.symptom.type;
        const inputGroup = this.viewInputGroups[inputGroupName];
        if (value) {
            if (!values.includes(attr)) {
                values.push(attr);
                this.store.dispatch(new AddQuestionnaireInputGroup({ form, inputGroup }));
            }
        } else {
            if (values.includes(attr)) {
                ArrayUtils.remove(values, attr);
                this.store.dispatch(new AddQuestionnaireInputGroup({ form, inputGroup }));
            }
        }
    }

    private isSymptom(item: any): boolean {
        return item && item.symptomDate && !item.questionaryDate;
    }

    private setIsScoringEnabled(): void {
        if (this.isSymptom(this.symptom)) {
            this.isScoringEnabled = true;
        } else {
            const questionarySettings = this.configService.questionarySettings().find((q) => {
                return q.questionaryType === this.symptom.classType;
            });
            this.isScoringEnabled =
                this.configService.site() === Application.CLINIC ||
                (questionarySettings && questionarySettings.scoreVisible);
        }
    }
}
