<div class="symptom-summary-content">
    <div *ngIf="hasBeenSendToClinic()" class="sent-to-clinic-info">
        <span>
            {{ titleIsClinicNotifiedAboutSevere ? ('patient.symptomCard.automatedAnswer' | i18n) : ('patient.symptomCard.submittedToClinic'  | i18n) }}
        </span>
    </div>
    <div *ngIf="symptom.nextOfKinAnswer" class="next-of-kin-info">
        <span>{{ 'patient.symptomCard.symptomContent.nextOfKin.answeredStatus' | i18n }}</span>
    </div>
    <div class="symptom-summary-details">
        <div
            *ngFor="let attribute of itemAttributeList()"
            class="symptom-attribute"
            [ngClass]="'attribute-value-type-' + attribute"
        >
            <div class="symptom-attribute-title" [ngSwitch]="attribute">
                <!-- Special cases-->
                <ng-container *ngSwitchCase="'symptomDate'">
                    <ng-container *ngIf="!isPerformanceStatus() && !showReportingDate()">
                        {{ 'symptom.common.date.diaryTitle' | i18n }}
                    </ng-container>
                    <ng-container *ngIf="!isPerformanceStatus() && showReportingDate()">
                        {{ 'symptom.common.date.diaryTitle' | i18n }} ({{ 'symptom.common.date.reportingDate.diaryTitle'
                        | i18n }})
                    </ng-container>
                    <ng-container *ngIf="isPerformanceStatus()">
                        {{ 'symptom.common.date.reportingDate.diaryTitle' | i18n }}
                    </ng-container>
                </ng-container>
                <ng-container *ngSwitchCase="'questionaryDate'">
                    {{ 'questionary.common.date.diaryTitle' | i18n }}
                </ng-container>
                <ng-container *ngSwitchCase="'score'">
                    <ng-container *ngIf="isScoringEnabled">
                        {{ 'questionary.common.score.diaryTitle' | i18n }}
                    </ng-container>
                </ng-container>
                <ng-container *ngSwitchCase="'adverseEffectStatus'">
                    {{ 'symptom.common.adverseEffectStatus.diaryTitle' | i18n }}
                </ng-container>
                <!-- Default-->
                <ng-container *ngSwitchDefault> {{ getTranslationKey(attribute + 'Summary') | i18n }} </ng-container>
            </div>
            <div *ngIf="attribute === 'questionaryDate'" class="symptom-attribute-value">
                {{ toLongDateFormat(symptom[ attribute ]) }}
            </div>
            <div
                *ngIf="attribute === 'symptomDate' && hasDateRangeAndPeakDate() && !isPerformanceStatus() && showReportingDate()"
                class="symptom-attribute-value"
            >
                {{ toLongDateFormat(symptom[ attribute ]) }} ({{ toLongDateFormat(getReportingDate()) }})
            </div>
            <div
                *ngIf="attribute === 'symptomDate' && hasDateRangeAndPeakDate() && !isPerformanceStatus() && !showReportingDate()"
                class="symptom-attribute-value"
            >
                {{ toLongDateFormat(symptom[ attribute ]) }}
            </div>
            <div *ngIf="attribute === 'symptomDate' && isPerformanceStatus()" class="symptom-attribute-value">
                {{ toLongDateFormat(symptom[ attribute ]) }}
            </div>
            <div
                *ngIf="attribute === 'symptomDate' && !hasDateRangeAndPeakDate() && !isPerformanceStatus()"
                class="symptom-attribute-value"
            >
                {{ 'symptom.common.isConstant' | i18n }} ({{ toLongDateFormat(getReportingDate()) }})
            </div>
            <ng-container *ngIf="attribute !== 'symptomDate' && attribute !== 'questionaryDate'">
                <ng-container [ngSwitch]="symptom[attribute] | typeof">
                    <!-- Arrays-->
                    <div *ngSwitchCase="'object'" class="symptom-attribute-value">
                        <span *ngFor=" let arrayValue of symptom[attribute] | removeOther; let index = index; let last = last">
                            <span *ngIf="arrayValue !== 'other' && !isDate(arrayValue)">
                                <span>{{ getTranslationKey(attribute) + '.' + arrayValue + '.label' | i18n }}</span>
                                <span>{{ last ? '' : ', ' }}</span>
                            </span>
                            <span *ngIf="isDate(arrayValue)">
                                <span [innerHTML]="getDateString(arrayValue, getRangePosition(arrayValue, attribute), last)"></span>
                            </span>
                        </span>
                        <span *ngIf="symptom['other' + (attribute | capitalFirstLetter)]">{{ isFirstItem(attribute) ? '' : ', '}}
                            <span [innerHtml]="symptom['other' + (attribute | capitalFirstLetter)] | encode | noonaDecode | wordBreak: 30">
                            </span>
                        </span>
                        <div *ngIf="isDate(symptom[attribute])">
                            <span>{{ toLongDateFormat(symptom[attribute]) }}</span>
                        </div>
                    </div>
                    <!-- Strings-->
                    <div *ngSwitchCase="'string'" class="symptom-attribute-value">
                        <ng-container *ngIf="attribute === 'adverseEffectStatus'">
                            <span>{{ 'symptom.common.adverseEffectStatus.' + symptom[ attribute ] | i18n }}</span>
                        </ng-container>
                        <ng-container *ngIf="attribute !== 'adverseEffectStatus'">
                            <span *ngIf="!getTranslationKey(attribute) && attribute === 'score' && isScoringEnabled">
                                {{ symptom[ attribute ] + (symptom.questionaryDate ? getMaxScore(symptom) : '') }} {{
                                getUnit(attribute) }}
                            </span>
                            <span
                                *ngIf="!getTranslationKey(attribute) && attribute !== 'score' && isScoringEnabled"
                                [innerHtml]="symptom[attribute] + ' ' + getUnit(attribute) | encode | convertLineBreaks | noonaDecode | wordBreak: 35"
                            ></span>
                            <span *ngIf="getTranslationKey(attribute) && (attribute !== 'score' || isScoringEnabled)">
                                {{ getTranslationKey(attribute) + '.' + symptom[ attribute ] + '.label' | i18n }}
                            </span>
                            <!-- FORM TEXT FIELDS-->
                            <span *ngIf="!getTranslationKey(attribute) && (attribute !== 'score' && !isScoringEnabled)"
                                [innerHtml]="symptom[attribute] + ' ' + getUnit(attribute) | encode | convertLineBreaks | noonaDecode | wordBreak: 35">
                            </span>
                        </ng-container>
                    </div>
                    <!-- Numbers-->
                    <div *ngSwitchCase="'number'" class="symptom-attribute-value">
                        <span>
                            <div *ngIf="isDate(symptom[attribute])">
                                <span>{{ toLongDateFormat(symptom[attribute]) }}</span>
                            </div>
                            <span *ngIf="getTranslationKey(attribute)">
                                {{ getTranslationKey(attribute) + '.' + symptom[ attribute ] + '.label' | i18n |
                                capitalFirstLetter}}
                            </span>
                            <span *ngIf="!getTranslationKey(attribute) && !isDate(symptom[attribute]) && attribute !== 'height'">
                                {{ symptom[ attribute ] }} {{ getUnit(attribute) }}
                            </span>
                            <span *ngIf="!getTranslationKey(attribute) && attribute === 'height'">
                                {{ getHeight(attribute)}}
                            </span>
                        </span>
                    </div>
                    <!-- Booleans-->
                    <div *ngSwitchCase="'boolean'" class="symptom-attribute-value">
                        {{ getTranslationKey(attribute) + '.' + symptom[ attribute ] + '.label' | i18n }}
                    </div>
                </ng-container>
            </ng-container>
            <div
                *ngIf="(attribute === 'locations' || attribute === 'locationsBody') && painPointerLocations && gender"
                class="symptom-locations"
            >
                <pain-pointer
                    [painLocations]="painPointerLocations"
                    [gender]="gender"
                    [enabledLocations]="painPointerLocations"
                    [side]="PainPointerSide.FRONT"
                ></pain-pointer>
                <pain-pointer
                    [painLocations]="painPointerLocations"
                    [gender]="gender"
                    [enabledLocations]="painPointerLocations"
                    [side]="PainPointerSide.BACK"
                ></pain-pointer>
            </div>
        </div>
        <div *ngIf="showDescription()" class="symptom-attribute attribute-value-type-description">
            <div class="symptom-attribute-title">{{ 'symptom.description' | i18n }}</div>
            <div
                class="symptom-attribute-value"
                [innerHtml]="symptom.description | encode | noonaDecode | convertLineBreaks | wordBreak: 20"
            ></div>
        </div>
        <input-group-render-container
            [formName]="symptom?.id ? symptom?.id : symptom?.type"
        ></input-group-render-container>
        <div *ngIf="symptom.photo" class="symptom-attribute" [style.display]="containsPhotos? 'flex': 'none'">
            <div class="symptom-attribute-title">{{ 'patient.symptomsummary.photos' | i18n }}</div>
            <div class="symptom-attribute-value">
                <div class="symptom-photos">
                    <photo-uploader
                        [(photoGroup)]="symptom.photo"
                        [viewOnly]="true"
                        [onlySubmittedPhotos]="onlySubmittedPhotos"
                        (containsPhotosChange)="containsPhotosChange($event)"
                        (openPreview)="openPreview.next($event)"
                    ></photo-uploader>
                </div>
                <div *ngIf="symptom.photo.description" class="symptom-photos-description">
                    <span>{{ symptom.photo.description }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
