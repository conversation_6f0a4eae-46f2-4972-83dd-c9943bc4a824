@import '../../../../styles/deprecated_mixins.scss';
@import '../../../../styles/deprecated_variables.scss';
:root {
  display: block;
}
.sent-to-clinic-info,
.next-of-kin-info {
  color: var(--color-primary, $color-primary);
  margin-bottom: $spacing-base;
}
.symptom-summary-details {
  .symptom-attribute {
    @include flex-layout(column, flex-start, flex-start);
    margin-bottom: $spacing-base;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
  .symptom-attribute-title {
    @include font-semibold2();
  }
  .symptom-attribute-value {
    width: 100%;
    .symptom-photos-description {
      span {
        word-break: break-word;
      }
    }
  }
  .symptom-locations {
    display: flex;
    margin-top: $spacing-small;
  }
  .photo-uploader {
    .previews {
      background-color: $color-primary-background;
      padding-left: 10px;
      padding-top: 10px;
      display: inline-block;
    }
  }
}

#symptom-summary-modal-modal {
  .symptom-summary-content {
    .photo-uploader {
      .previews {
        background-color: $white;
      }
    }
  }
}
.case-details {
  .symptom-summary-content {
    .symptom-summary-details {
      padding: 20px;
      font-size: 15px;
      font-family: $font-family-base;
      .symptom-attribute-title {
        @include font-bold();
        color: $dark;
      }
    }
  }
}
