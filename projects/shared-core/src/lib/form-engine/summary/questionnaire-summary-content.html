<div class="questionnaire-summary-content">
    <ng-container *ngFor="let symptom of symptoms">
        <ng-container *ngIf="symptom?.classType === 'statusCheck' && symptom?.anySymptom != undefined">
            <status-check-summary [statusCheck]="symptom"></status-check-summary>
        </ng-container>
        <ng-container *ngIf="symptom?.classType !== 'statusCheck'">
            <div class="symptom-title">{{ 'fe.formNames.' + symptom.classType | i18n }}</div>
            <symptom-summary-content
                [theSymptom]="symptom"
                [gender]="gender"
                [onlySubmittedPhotos]="onlySubmittedPhotos"
                (openPreview)="handleOpenPhotoPreview($event)"
            ></symptom-summary-content>
        </ng-container>
    </ng-container>

    <div class="additional-questions" *ngIf="questionary && questionary.additionalQuestions">
        <div class="title">{{ 'symptomReport.additionalQuestions.title' | i18n }}</div>
        <div class="content">{{ questionary.additionalQuestions }}</div>
    </div>

    <div class="no-symptoms" *ngIf="noSymptoms && noSymptoms.length > 0">
        <h3 class="no-symptoms__title">{{ 'symptomsGroups.noSymptoms' | i18n }}</h3>
        <ul class="no-symptoms-list">
            <li class="no-symptoms-list__item" *ngFor="let symptom of noSymptoms">
                {{ 'fe.formNames.' + symptom | i18n }}
            </li>
        </ul>
    </div>
</div>
