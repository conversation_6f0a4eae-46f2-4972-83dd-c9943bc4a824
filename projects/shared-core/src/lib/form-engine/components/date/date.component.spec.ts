import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ChangeDetectorRef, NO_ERRORS_SCHEMA } from '@angular/core';
import { MocksModule } from '@shared-core/testing';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { FieldStatus } from '../../models/field-status.interface';
import { FieldService } from '../../services/field.service';
import { DateComponent } from './date.component';
import { SimpleDateInputValue } from '../simple-date-input/simple-date-input.component';
import moment from 'moment';
import { of } from 'rxjs';

const dateValue = new Date(2020, 0, 1);

function createEvent(
    validDate: boolean,
    isUnknownDayPattern: boolean,
    storeValue: Date,
    momentValue: moment.Moment,
    stringValue: string,
    isFuture: boolean,
    isAfterMaxDate: boolean
): SimpleDateInputValue {
    return {
        validDate,
        isUnknownDayPattern,
        storeValue,
        momentValue,
        stringValue,
        isFuture,
        isAfterMaxDate,
    };
}

describe('DateComponent', () => {
    let component: DateComponent;
    let fixture: ComponentFixture<DateComponent>;
    let fieldService: FieldService;
    let store: MockStore;
    let changeDetectorRef: ChangeDetectorRef;
    let dispatchSpy: jest.SpyInstance;
    let fieldServiceGetAnswerSpy: jest.SpyInstance;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [DateComponent],
            providers: [provideMockStore(), FieldService, ChangeDetectorRef],
            schemas: [NO_ERRORS_SCHEMA],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(DateComponent);
        component = fixture.componentInstance;
        store = TestBed.inject(MockStore);
        fieldService = TestBed.inject(FieldService);
        changeDetectorRef = TestBed.inject(ChangeDetectorRef);

        component.config = {
            allowFutureDate: false,
            key: 'testKey',
            labelKey: 'testLabelKey',
            subLabelKey: 'testSubLabelKey',
            formType: 'testFormType',
        };
        const subHelper = require('../../services/subscription-helper');
        jest.spyOn(subHelper, 'safeSelectFieldStatus').mockReturnValue(of([] as FieldStatus[]));
        jest.spyOn(subHelper, 'safeSelectField').mockReturnValue(of(dateValue));
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize properties on ngOnInit', () => {
        const getClinicDateFormatPatternSpy = jest.spyOn(component as any, 'getClinicDateFormatPattern');
        component.ngOnInit();
        expect(component.fieldKey).toBe(component.config.key);
        expect(component.titleTranslationKey).toBe(component.config.labelKey);
        expect(component.descriptionTranslationKey).toBe(component.config.subLabelKey);
        expect(getClinicDateFormatPatternSpy).toHaveBeenCalled();
    });

    it('should call next and complete on destroy$', () => {
        const nextSpy = jest.spyOn((component as any).destroy$, 'next');
        const completeSpy = jest.spyOn((component as any).destroy$, 'complete');
        component.ngOnDestroy();
        expect(nextSpy).toHaveBeenCalledWith(true);
        expect(completeSpy).toHaveBeenCalled();
    });

    describe('onDateChanged', () => {
        it('invokes dispatch and fieldService.getAnswer', () => {
            const dispatchSpy = jest.spyOn(store, 'dispatch');
            const fieldServiceGetAnswerSpy = jest.spyOn(fieldService, 'getAnswer');
            let event: SimpleDateInputValue;
            event = createEvent(true, false, dateValue, moment(dateValue), '01.01.2020', false, false);
            component.onDateChanged(event);
            expect(fieldServiceGetAnswerSpy).toHaveBeenCalledWith(component.config, dateValue, false);
            expect(component.dayMissing).toBe(false);
            expect(component.validDate).toBe(true);
            expect(dispatchSpy).toHaveBeenCalled();
        });
    });

    describe('simpleDateFocusLost', () => {
        it('should get error messages for invalid date', () => {
            const spy = jest.spyOn(component, 'setErrorMessageForInvalidDateValue');
            let event: SimpleDateInputValue;
            event = createEvent(null, false, null, null, '0101201', null, null);
            component.simpleDateFocusLost(event);
            expect(spy).toHaveBeenCalled();
        });

        it('should get error message for missin day value', () => {
            const spy = jest.spyOn(component, 'setErrorMessageForMissingDay');
            let event: SimpleDateInputValue;
            event = createEvent(true, true, dateValue, moment(dateValue), '01.2020', false, false )
            component.simpleDateFocusLost(event);
            expect(spy).toHaveBeenCalled();
        });
    });
});
