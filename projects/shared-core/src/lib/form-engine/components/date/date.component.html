<div class="form-element-container" *ngIf="visible" [nsFormElementContainer]="hasErrors">
    <nh-form-error-indicator *ngIf="!dateValue" [config]="config" (isFormValid)="handleFormErrors($event)"></nh-form-error-indicator>
    <nh-input-header [config]="config"></nh-input-header>
        <nh-simple-date-input
            [inputId]="config.key + 'DateInput'"
            [dateFormat]="dateFormat"
            [dateValue]="dateValue"
            (valueUpdate)="onDateChanged($event)"
            (focusLost)="simpleDateFocusLost($event)"
            [maxDate]="maxDate"
            [configKey]="fieldKey"
            [unknownDayDefault]="config.unknownDayDefault"
            [allowUnknownDate]="config.allowUnknownDate"
        ></nh-simple-date-input>
    <label class="date-input-hint" *ngIf="!validDate && errorInvalidDate" [innerHTML]="errorInvalidDate"></label>
    <label class="date-input-hint" *ngIf="dayMissing && errorDayMissing" [innerHTML]="errorDayMissing"></label>
</div>
