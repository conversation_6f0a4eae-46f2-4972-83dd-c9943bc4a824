import {
    ChangeDetectionStrategy,
    ChangeDetector<PERSON>ef,
    Component,
    HostBinding,
    Input,
    OnDestroy,
    OnInit,
} from '@angular/core';
import { Store } from '@ngrx/store';
import moment from 'moment';
import { Subject } from 'rxjs';
import { ConfigurationProviderService } from '../../../abstract-services/configuration-provider.service';
import { FormItemType } from '../../../generated/models/form-item-type';
import { DecodePipe } from '../../../pipes/decode.pipe';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { FieldService } from '../../services/field.service';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';
import { SimpleDateInputValue } from '../simple-date-input/simple-date-input.component';
import { safeSelectField, safeSelectFieldStatus } from '../../services/subscription-helper';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import type { IsFormValidEvent } from '../form-error-indicator/form-error-indicator.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-date-component',
    templateUrl: './date.component.html',
    styleUrls: ['./date.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DateComponent extends FormInputField implements OnInit, FormField, OnDestroy {
    @HostBinding('class') get hostClasses() {
        return 'datecomponent';
    }

    constructor(
        protected store: Store<FormEngineState>,
        private fieldService: FieldService,
        cd: ChangeDetectorRef,
        private decode: DecodePipe,
        private configService: ConfigurationProviderService,
        private i18nPipe: I18NPipe
    ) {
        super(cd);
    }

    static readonly FORM_ITEM_TYPE = FormItemType.DATE;

    public value: string;

    public fieldKey: string = null;

    public titleTranslationKey: string = null;

    public descriptionTranslationKey: string = null;

    public dateFormat: string;

    public monthYearFormat: string;

    @Input()
    public config: FormFieldConfig;

    @Input()
    public visible: boolean;

    public enabled = true;

    public maxDate: Date;

    private destroy$ = new Subject<boolean>();

    public dateValue: Date;

    public validDate: boolean;

    public dayMissing: boolean;

    public errorInvalidDate: string;

    public errorDayMissing: string;

    hasErrors = false;

    public ngOnInit() {
        if (!this.config.allowFutureDate) {
            this.maxDate = new Date();
        }

        this.fieldKey = this.config.key;
        this.titleTranslationKey = this.config.labelKey;
        this.descriptionTranslationKey = this.config.subLabelKey;

        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));
        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((dv: Date) => {
            this.dateValue = dv;
        });
        safeSelectFieldStatus(this.store, this.destroy$, this.config.key, FieldStatusType.NOT_READY).subscribe(
            (fieldStatuses: FieldStatus[]) => {
                this.enabled = !fieldStatuses || fieldStatuses.length === 0;
                this.cd.markForCheck();
            }
        );
        this.getClinicDateFormatPattern();
        this.setMonthYearFormat();
    }

    public ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    private getClinicDateFormatPattern() {
        this.configService.dateFormattingPattern().subscribe((pattern) => {
            this.dateFormat = pattern.longDatePattern;
        });
    }

    public onDateChanged(event: SimpleDateInputValue) {
        this.clearStatusForGivenKey(this.config.key);

        if (!event.validDate) {
            this.validDate = false;
            this.dayMissing = false;
            this.cd.markForCheck();
        } else if (event.isUnknownDayPattern) {
            this.dayMissing = true;
            this.validDate = true;
            this.cd.markForCheck();
        } else {
            this.validDate = true;
            this.dayMissing = false;
            this.clearErrorMessages();
        }
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, event.storeValue, false)));
    }
    public clearErrorMessages() {
        this.errorInvalidDate = '';
        this.errorDayMissing = '';
        this.cd.markForCheck();
    }

    public simpleDateFocusLost(event: SimpleDateInputValue) {
        this.onDateChanged(event);
        if (!event.validDate) {
            this.setErrorMessageForInvalidDateValue();
        } else if (event.isUnknownDayPattern) {
            this.setErrorMessageForMissingDay();
        }
        this.cd.markForCheck();
    }

    public getDateFormatDelimiter(dateFormat: string): string {
        if (dateFormat.includes('.')) {
            return '.';
        }
        if (dateFormat.includes('/')) {
            return '/';
        }
        if (dateFormat.includes('-')) {
            return '-';
        }
        return '/';
    }

    public setMonthYearFormat() {
        const separator = this.getDateFormatDelimiter(this.dateFormat);

        const isYearFirst = this.dateFormat.startsWith('YYYY');
        this.monthYearFormat = isYearFirst ? `YYYY${separator}MM` : `MM${separator}YYYY`;
    }

    public setErrorMessageForInvalidDateValue() {
        const messageString = this.i18nPipe.transform('errors.invalidDate.messageStart', {
            clinicDateFormat: this.dateFormat,
            clinicMonthYearFormat: this.monthYearFormat,
        });
        this.errorInvalidDate = messageString;
    }

    public setErrorMessageForMissingDay() {
        const messagestring = this.i18nPipe.transform('errors.dayMissingFromDate');
        this.errorDayMissing = messagestring;
    }

    handleFormErrors($event: IsFormValidEvent): void {
        this.hasErrors = !$event.valid && $event.isDirty;
        this.cd.markForCheck();
    }
}
