import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { LabelLocation } from '../../models/label-location.enum';
import { FormVariablesService } from '../../services/form-variables.service';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputFieldWithVarSupport } from '../form-input-field-variable-support.component';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-text-input-header',
    templateUrl: './text-input-header.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TextInputHeaderComponent extends FormInputFieldWithVarSupport implements Form<PERSON>ield {
    @Input()
    public config: FormFieldConfig;

    public labelLocation = LabelLocation;

    constructor(protected store: Store<FormEngineState>, cd: ChangeDetectorRef, protected fvs: FormVariablesService) {
        super(cd, fvs);
    }

    set visibility(visibility: boolean) {}
}
