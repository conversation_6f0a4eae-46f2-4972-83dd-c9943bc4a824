import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { TextInputHeaderComponent } from './text-input-header.component';
import { MocksModule } from '@shared-core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { QuestionaryType } from '../../../generated/models/questionary-type';
import { LabelLocation } from '../../models/label-location.enum';

describe('TextInputHeaderComponent', () => {
    let component: TextInputHeaderComponent;
    let fixture: ComponentFixture<TextInputHeaderComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [TextInputHeaderComponent],
            schemas: [NO_ERRORS_SCHEMA],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(TextInputHeaderComponent);
        component = fixture.componentInstance;
        component.config = {
            key: 'test',
            labelKey: 'test.label',
            formType: QuestionaryType.BOUNCE_HADS,
            labelLocation: LabelLocation.NONE,
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
