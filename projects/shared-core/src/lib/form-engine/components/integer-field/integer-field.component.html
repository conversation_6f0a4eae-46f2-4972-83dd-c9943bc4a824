<div class="form-element-container" *ngIf="visible" [nsFormElementContainer]="hasErrors">
  <nh-form-error-indicator [config]="config" (isFormValid)="handleFormErrors($event)"></nh-form-error-indicator>
  <nh-input-header [config]="config" [id]="config.key + '-header'"></nh-input-header>
  <div
    class="input-group integer-field-input-group"
    *ngIf="config.unitType !== unitType.HEIGHT || config.heightUnit !== heightUnit.FEET_AND_INCH"
  >
    <input
      class="form-control fe-integer-field"
      type="number"
      [ngModel]="currentValue"
      [id]="config.key | hyphened"
      [name]="config.key | hyphened"
      [min]="config.min"
      [max]="config.max"
      (keydown)="checkAndpreventCharacters($event)"
      (ngModelChange)="onChange(changeType.FULL, $event)"
      [placeholder]="errorOutOfRange ? '' : defaultError"
      [attr.aria-invalid]="!isValidValue ? 'true' : null"
      [attr.aria-labelledby]="config.key + '-header'"
      [attr.aria-describedby]="!isValidValue ? config.key + '-error' : null"
    />
    <div class="input-group-addon" *ngIf="!!config.unit">{{ config.unit | i18n }}</div>
    <div class="input-group-addon" *ngIf="!!config.unitType">{{ measurementSystem }}</div>
  </div>
  <fieldset class="input-group col-md-6" *ngIf="config.unitType === unitType.HEIGHT && config.heightUnit === heightUnit.FEET_AND_INCH">
    <nh-form-error-indicator [config]="config" (isFormValid)="handleFormErrors($event)"></nh-form-error-indicator>
    <input
      class="form-control fe-integer-field"
      id="height-ft"
      type="number"
      name="height-ft"
      [ngModel]="valueFeet"
      [min]="config.min"
      [max]="config.max"
      [attr.aria-labelledby]="config.key + '-header height-ft-unit'"
      [attr.aria-describedby]="!isValidValue ? config.key + '-error' : null"
      [attr.aria-invalid]="!isValidValue ? 'true' : null"
      (keydown)="checkAndpreventCharacters($event)"
      (ngModelChange)="onChange(changeType.FT, $event)"
    />
    <div class="input-group-addon" id="height-ft-unit">{{ config.heightUnit | slice : 0 : 2 }}</div>
    <input
      class="form-control fe-integer-field"
      id="height-in"
      type="number"
      name="height-in"
      [ngModel]="valueInch"
      [min]="config.min"
      [max]="config.max"
      [attr.aria-labelledby]="config.key + '-header height-in-unit'"
      [attr.aria-describedby]="!isValidValue ? config.key + '-error' : null"
      [attr.aria-invalid]="!isValidValue ? 'true' : null"
      (keydown)="checkAndpreventCharacters($event)"
      (ngModelChange)="onChange(changeType.IN, $event)"
    />
    <div id="height-in-unit" class="input-group-addon">{{ config.heightUnit | slice : 5 : 7 | lowercase }}</div>
  </fieldset>

  <label *ngIf="!isValidValue" class="input-range-hint" [id]="config.key + '-error'" role="alert" [innerHTML]="errorOutOfRange"> </label>
</div>
