import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { IntegerFieldComponent } from './integer-field.component';
import { MocksModule } from '@shared-core/testing';
import { UnitType } from '../../models/unit-type.enum';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('IntegerFieldComponent', () => {
    let component: IntegerFieldComponent;
    let fixture: ComponentFixture<IntegerFieldComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [IntegerFieldComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(IntegerFieldComponent);
        component = fixture.componentInstance;
        component.config = {
            key: 'phq',
            labelKey: 'test.label',
            formType: QuestionaryType.BOUNCE_HADS,
            unitType: UnitType.HEIGHT,
            min: 0,
            max: 1,
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
