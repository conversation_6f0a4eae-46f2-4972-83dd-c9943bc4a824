import { ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { take } from 'rxjs/operators';
import { ConfigurationProviderService } from '../../../abstract-services/configuration-provider.service';
import { HeightUnit } from '../../../generated/models/height-unit';
import { LowercaseFirstPipe } from '../../../pipes/lowercase-first-letter.pipe';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { UnitType } from '../../models/unit-type.enum';
import { FieldService } from '../../services/field.service';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';
import { safeSelectField } from '../../services/subscription-helper';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { BoldPipe } from '../../../pipes/bold.pipe';
import type { IsFormValidEvent } from '../form-error-indicator/form-error-indicator.interface';

export enum IntegerChangeType {
    FULL,
    FT,
    IN,
}

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-integer-field',
    templateUrl: './integer-field.component.html',
    styleUrls: ['./integer-field.component.scss'],
})
export class IntegerFieldComponent extends FormInputField implements OnInit, OnDestroy, FormField {
    @Input()
    public config: FormFieldConfig;

    @Input()
    public visible: boolean;

    public heightUnit = HeightUnit;
    public unitType = UnitType;
    public measurementSystem: any;
    public changeType = IntegerChangeType;
    hasErrors = false;

    public valueInch: number;
    public valueFeet: number;
    public currentValue: number;
    public isValidValue = true;
    public errorOutOfRange: string;
    public defaultError: string;

    private destroy$ = new Subject<boolean>();
    constructor(
        protected store: Store<FormEngineState>,
        private lowercaseFirst: LowercaseFirstPipe,
        private fieldService: FieldService,
        private configService: ConfigurationProviderService,
        private i18nPipe: I18NPipe,
        private boldPipe: BoldPipe,
        cd: ChangeDetectorRef
    ) {
        super(cd);
    }

    ngOnInit() {
        if (this.config.unitType) {
            this.configService
                .measurementSystem()
                .pipe(take(1))
                .subscribe((measurementSystem) => {
                    if (measurementSystem) {
                        this.measurementSystem = measurementSystem[this.lowercaseFirst.transform(this.config.unitType)];
                    }
                });
        }

        this.getErrorMessageForValuesOutOfRange();
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));

        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((value) => {
            if (this.config.unitType === UnitType.HEIGHT && this.config.heightUnit === HeightUnit.FEET_AND_INCH) {
                this.valueFeet = Math.floor(value / 12);
                this.valueInch = Math.floor(value % 12);
            } else {
                this.currentValue = value;
            }
            if (this.currentValue < this.config.min || this.currentValue > this.config.max) {
                this.isValidValue = false;
            } else {
                this.isValidValue = true;
            }
        });

        if (this.config.unitType !== undefined || this.config.unitType !== null) {
            const unitConfig = { ...this.config };
            unitConfig.key = `${this.config.key}Unit`;

            this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(unitConfig, this.measurementSystem, false)));
        }
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    onChange(changeType: IntegerChangeType, value: number) {
        let payload = this.fieldService.getAnswer(this.config, undefined, false);
        switch (changeType) {
            case IntegerChangeType.FULL: {
                payload = this.fieldService.getAnswer(this.config, value, false);
                break;
            }

            case IntegerChangeType.FT: {
                const inch = this.valueInch;
                if (inch !== undefined && inch >= 0) {
                    const height = value * 12 + inch;
                    if (!isNaN(height) && height >= 0) {
                        payload = this.fieldService.getAnswer(this.config, height, false);
                    }
                }
                break;
            }

            case IntegerChangeType.IN: {
                const feet = this.valueFeet;
                if (feet !== undefined && feet >= 0) {
                    const height = feet * 12 + value;
                    if (!isNaN(height) && height >= 0) {
                        payload = this.fieldService.getAnswer(this.config, height, false);
                    }
                }
                break;
            }
        }

        this.store.dispatch(new AddAnswer(payload));
    }

    getErrorMessageForValuesOutOfRange() {
        const minAndMax = {
            numberMin: this.config.min.toString(),
            numberMax: this.config.max.toString(),
        };
        const startValue = this.i18nPipe.transform('errors.valueOutOfRange.messageStart');
        const endValue = this.i18nPipe.transform('errors.valueOutOfRange.messageEnd', minAndMax);

        this.defaultError = startValue + ' ' + endValue;

        this.errorOutOfRange = startValue + this.boldPipe.transform(endValue);
    }

    checkAndpreventCharacters(keyDownEvent) {
        // prevent: "e"
        if ([69].includes(keyDownEvent.keyCode)) {
            keyDownEvent.preventDefault();
        }
    }

    handleFormErrors($event: IsFormValidEvent): void {
        this.hasErrors = !$event.valid && $event.isDirty;
        this.cd.markForCheck();
    }
}
