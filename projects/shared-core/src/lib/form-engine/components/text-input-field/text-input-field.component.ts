import { FormInputField } from '../form-input-field.component';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { safeSelectField } from '../../services/subscription-helper';
import { FormEngineState } from '../../store/reducers/state';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { AddAnswer } from '../../store/actions/form.actions';
import { FieldService } from '../../services/field.service';
import { UntypedFormControl, Validators } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { IsFormValidEvent } from '../form-error-indicator/form-error-indicator.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-text-input-field',
    templateUrl: './text-input-field.component.html',
    styleUrls: ['./text-input-field.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TextInputFieldComponent extends FormInputField implements OnInit, OnDestroy {
    @Input() config: FormFieldConfig;
    @Input() visible: boolean;
    input: UntypedFormControl;

    public titleTranslationKey: string = null;
    public descriptionTranslationKey: string = null;
    private destroy$ = new Subject<boolean>();

    public hasErrors: boolean;
    public inputValue: string;
    public hasInput = false;

    constructor(
        protected cd: ChangeDetectorRef,
        protected store: Store<FormEngineState>,
        private fieldService: FieldService
    ) {
        super(cd);
    }

    ngOnInit(): void {
        this.titleTranslationKey = this.config.labelKey;
        this.descriptionTranslationKey = this.config.subLabelKey;
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));
        this.input = new UntypedFormControl('', {
            validators: [Validators.required],
        });
        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((value) => {
            this.input.setValue(value, { emitEvent: false });
        });
    }

    ngOnDestroy(): void {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public onChange(value: string) {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, value, false)));

        if (this.inputValue === '') {
            this.hasInput = false;
        } else {
            this.hasInput = value?.length > 0;
        }
    }

    handleFormErrors(event: IsFormValidEvent): void {
        this.hasErrors = !event.valid && event.isDirty && !this.hasInput;
        this.cd.markForCheck();
    }
}
