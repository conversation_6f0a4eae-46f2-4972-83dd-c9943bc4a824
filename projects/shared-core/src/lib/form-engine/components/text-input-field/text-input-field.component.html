<div class="form-element-container" *ngIf="visible" [nsFormElementContainer]="hasErrors">
    <nh-form-error-indicator *ngIf="!hasInput" [config]="config" (isFormValid)="handleFormErrors($event)"></nh-form-error-indicator>
    <nh-input-header [config]="config"></nh-input-header>
    <div class="input-field flex flex--column">
        <input
            type="text"
            required
            [formControl]="input"
            (ngModelChange)="onChange($event)"
        />
    </div>
</div>
