<div class="form-element-container" *ngIf="visible">
  <nh-checkbox-list-header [config]="config"></nh-checkbox-list-header>
  <div class="photos form-element-container">
    <photo-uploader
      [(containsPhotos)]="containsPhotos"
      [maximumFileCount]="config.maxLength"
      [onlySubmittedPhotos]="onlySubmittedPhotos"
      [patientId]="patientId"
      [photoGroup]="photoGroup"
      [photoId]="previewContainerId"
      [viewOnly]="config.disabled"
      (photoGroupChange)="photoGroupChanged($event)"
      (uploadInProgress)="uploadInProgress($event)"
    ></photo-uploader>
    <nh-form-error-indicator [config]="config"></nh-form-error-indicator>
  </div>
  <div class="form-element-container" *ngIf="containsPhotos">
    <h4 id="photo-description-label">{{ 'patient.photoUpload.additionalInformation' | i18n }}</h4>
    <div class="input-wrapper">
      <textarea
        class="form-control fe-text-area-field"
        rows="4"
        type="text"
        [ngModel]="photoDescription"
        name="photo-description"
        aria-labelledby="photo-description-label"
        (ngModelChange)="photoDescriptionChanged($event)"
      ></textarea>
    </div>
    <nh-form-error-indicator [config]="photoDescriptionConfig"></nh-form-error-indicator>
  </div>
</div>
