import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { photoGroupDescriptionForm<PERSON>ey } from '../../../constants';
import { FormItemType } from '../../../generated/models/form-item-type';
import { PhotoGroup } from '../../../generated/models/photo-group';
import { HyphenedPipe } from '../../../pipes/hyphened.pipe';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { FieldService } from '../../services/field.service';
import { AddAnswer, SetUploading } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { selectPatient } from '../../store/selectors/patient.selectors';
import { FormInputField } from '../form-input-field.component';
import { safeSelectField } from '../../services/subscription-helper';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-photo-item',
    templateUrl: './photo-item.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PhotoItemComponent extends FormInputField implements OnInit, OnDestroy, FormField {
    @Input() config: FormFieldConfig;
    @Input() photoGroup: PhotoGroup;
    @Input() visible: boolean;

    public patientId: string;
    public previewContainerId: string;
    public photoDescription: string;
    public photoDescriptionConfig: FormFieldConfig;
    public onlySubmittedPhotos = true;
    public containsPhotos = false;

    private destroy$ = new Subject<boolean>();

    constructor(
        protected store: Store<FormEngineState>,
        private fieldService: FieldService,
        private hyphened: HyphenedPipe,
        cd: ChangeDetectorRef
    ) {
        super(cd);
    }

    ngOnInit() {
        this.store
            .select(selectPatient)
            .pipe(take(1))
            .subscribe((patient) => {
                if (patient) {
                    this.patientId = patient.id;
                }
            });

        this.photoDescriptionConfig = {
            type: FormItemType.TEXTAREA,
            formType: this.config.formType,
            key: photoGroupDescriptionFormKey,
            maxLength: 500,
            minLength: 0,
            gender: this.config.gender,
            site: this.config.site,
        };

        this.previewContainerId = `${this.config.key}-${this.hyphened.transform(this.config.formKey)}`;
        if (!this.photoGroup) {
            this.onlySubmittedPhotos = false;
            safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType)
                .pipe(takeUntil(this.destroy$))
                .subscribe((photoGroup) => {
                    return this.onPhotoGroupChange(photoGroup);
                });
        }
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public onPhotoGroupChange(photoGroup: PhotoGroup) {
        if (photoGroup) {
            this.photoGroup = { ...photoGroup };
            this.photoDescription = photoGroup.description;

            this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, photoGroup, false)));
            this.store.dispatch(
                new AddAnswer(this.fieldService.getAnswer(this.photoDescriptionConfig, this.photoDescription, false))
            );
        }
    }

    public photoGroupChanged(photoGroup: PhotoGroup) {
        this.photoGroup = { ...photoGroup };
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, photoGroup, false)));
    }

    public photoDescriptionChanged(description: string) {
        if (this.photoGroup) {
            this.photoDescription = description;
            this.photoGroup.description = description;

            this.photoGroupChanged(this.photoGroup);
            this.store.dispatch(
                new AddAnswer(this.fieldService.getAnswer(this.photoDescriptionConfig, this.photoDescription, false))
            );
        }
    }

    public uploadInProgress(uploading: boolean) {
        this.store.dispatch(
            new SetUploading({
                form: this.config.formType,
                uploading,
            })
        );
    }
}
