import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PhotoItemComponent } from './photo-item.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('PhotoItemComponent', () => {
    let component: PhotoItemComponent;
    let fixture: ComponentFixture<PhotoItemComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [PhotoItemComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(PhotoItemComponent);
        component = fixture.componentInstance;
        component.config = { key: 'photo', labelKey: 'test.label', formType: QuestionaryType.BOUNCE_HADS };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
