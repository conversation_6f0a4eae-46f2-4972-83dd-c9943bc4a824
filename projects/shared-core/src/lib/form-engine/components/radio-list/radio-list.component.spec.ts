import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { RadioListComponent } from './radio-list.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('RadioListComponent', () => {
    let component: RadioListComponent;
    let fixture: ComponentFixture<RadioListComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [RadioListComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(RadioListComponent);
        component = fixture.componentInstance;
        component.config = {
            key: 'test',
            labelKey: 'test.label',
            formType: QuestionaryType.BOUNCE_HADS,
            enableFieldStatus: true,
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
