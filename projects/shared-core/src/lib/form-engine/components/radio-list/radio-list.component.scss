@import '../../../../styles/deprecated_variables.scss';
@import '../../../../styles/ds-variables.scss';

$border: thin solid lightgrey;
:root {
  display: block;
}
:host.horizontal-list {
  border: $border;
  border-bottom: none;
  &:first-of-type {
    border-top-left-radius: $border-radius-md;
    border-top-right-radius: $border-radius-md;
  }
  &:last-of-type {
    border-bottom-right-radius: $border-radius-md;
    border-bottom-left-radius: $border-radius-md;
    border-bottom: $border;
  }
}
.horizontal {
  .input-wrapper {
    background-color: white;
  }
  .question {
    font-weight: $font-weight-bold;
  }
  .question ::ng-deep .neg-mt-l {
    margin-top: $spacing-xxs;
  }
  .radio {
    padding: 0;
    margin: 0;
    border-radius: 0;
    min-height: 0;
    box-shadow: none;
  }
}
.spacing ::ng-deep .neg-mt-l {
  margin-top: $spacing-xxs;
}
