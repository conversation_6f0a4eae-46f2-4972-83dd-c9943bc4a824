import {
    ChangeDetectionStrategy,
    ChangeDetector<PERSON>ef,
    Component,
    HostBinding,
    Input,
    OnDestroy,
    OnInit,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { Dictionary } from '../../../common-types';
import { ListEntry } from '../../../generated/models/list-entry';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { ModelValue } from '../../models/model-value.interface';
import { FieldService } from '../../services/field.service';
import { FormVariablesService } from '../../services/form-variables.service';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';
import { safeSelectField, safeSelectFieldStatus } from '../../services/subscription-helper';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import isNil from 'lodash/isNil';
import has from 'lodash/has';
import difference from 'lodash/difference';
import type { IsFormValidEvent } from '../form-error-indicator/form-error-indicator.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-radio-list',
    templateUrl: './radio-list.component.html',
    styleUrls: ['./radio-list.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RadioListComponent extends FormInputField implements OnInit, FormField, OnDestroy {
    @Input()
    public config: FormFieldConfig;

    @Input()
    public visible: boolean;

    @HostBinding('class') get hostClasses() {
        if (this.config.horizontal && this.visible) {
            return 'horizontal-list';
        }
        return '';
    }

    public values: ModelValue[];
    public selectedValue: string | null | undefined;
    public fieldStatusControlledVisibilty = true;
    public enabled = true;
    hasErrors = false;

    private destroy$ = new Subject<boolean>();

    i18nPlaceholders: Dictionary<string, string> = {};

    constructor(
        private fieldService: FieldService,
        protected store: Store<FormEngineState>,
        cd: ChangeDetectorRef,
        private fvs: FormVariablesService,
        private i18NPipe: I18NPipe
    ) {
        super(cd);
    }

    ngOnInit() {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));
        if (this.config.enableFieldStatus) {
            if (this.config.questionGroupId) {
                safeSelectFieldStatus(
                    this.store,
                    this.destroy$,
                    this.config.questionGroupId,
                    FieldStatusType.NOT_READY
                ).subscribe((fieldStatuses: FieldStatus[]) => {
                    this.fieldStatusControlledVisibilty = !fieldStatuses || fieldStatuses.length === 0;
                    this.cd.markForCheck();
                });
            } else {
                safeSelectFieldStatus(this.store, this.destroy$, this.config.key, FieldStatusType.NOT_READY).subscribe(
                    (fieldStatuses: FieldStatus[]) => {
                        this.enabled = !fieldStatuses || fieldStatuses.length === 0;
                        this.cd.markForCheck();
                    }
                );
            }
        }

        if (this.config.optionsProviderFieldKeys && this.config.optionsProviderFieldKeys.length > 0) {
            const keysOptionsMap = new Map();
            this.config.optionsProviderFieldKeys.forEach((key) => {
                safeSelectField(this.store, this.destroy$, key, this.config.formType).subscribe((keyOptions: any[]) => {
                    const optionsToAdd = keyOptions && keyOptions.length > 0 ? keyOptions : [];
                    keysOptionsMap.set(key, optionsToAdd);
                    let combinedOptions = [];
                    keysOptionsMap.forEach((options: string[], optionskey: string) => {
                        combinedOptions = [...combinedOptions, ...options];
                    });
                    this.updateWithProviderOptions(combinedOptions);
                });
            });
        } else {
            this.values = this.fieldService.getListValues(this.config);
        }

        safeSelectField(
            this.store,
            this.destroy$,
            this.config.key, // E.g. bmsS1StudyEligibilityMortalityStatus
            this.config.formType
        ).subscribe((value) => {
            this.selectedValue = isNil(value) ? value : String(value);
            this.cd.markForCheck();
        });

        this.fvs.getTranslations(this.store, this.destroy$).subscribe((t) => {
            this.i18nPlaceholders = t;
            this.cd.markForCheck();
        });
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    isChecked(value: any): boolean {
        if (typeof value !== 'string') {
            return String(value) === this.selectedValue;
        }
        return value === this.selectedValue;
    }

    private updateWithProviderOptions(options: any[]) {
        const visible = !(!options || options.length === 0);
        this.visibility = visible;

        // Radio list works with ModelValue but gets ListEntry from the Schema
        // Work with ListEntry to keep it consistent with the logic in FieldService
        const currentKeys = this.values
            ? this.values.map((v) => {
                  return v.value;
              })
            : [];
        const newKeys = options
            ? options.map((v) => {
                  return has(v, 'key') ? v.key : v;
              })
            : [];
        const sameKeys =
            currentKeys.length > 0 &&
            newKeys.length > 0 &&
            currentKeys.length === newKeys.length &&
            difference(newKeys, currentKeys).length === 0;

        if (!this.visible) {
            this.resetSelectedValuesAndOptions([]);
        } else if (!sameKeys) {
            // Here we assume that the option is a simple type if it doesn't have a 'key' field
            const listEntryOptions: ListEntry[] = options.map((option) => {
                if (has(option, 'key')) {
                    return option;
                }
                return {
                    key: option,
                };
            });
            this.resetSelectedValuesAndOptions(listEntryOptions);
        }

        this.cd.markForCheck();
    }

    private resetSelectedValuesAndOptions(options: ListEntry[]) {
        this.values = this.fieldService.getListValues(this.config, options);
        if (
            this.selectedValue &&
            !options
                .map((o) => {
                    return o.key;
                })
                .includes(this.selectedValue)
        ) {
            this.selectedValue = null;
            this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, false)));
        }
    }

    public onChange(value: ModelValue) {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, value.value, false)));
        this.clearValueMissingHardErrorStatusForGivenKey(this.config.key);
    }

    public componentVisible(): boolean {
        return this.visible && this.fieldStatusControlledVisibilty;
    }

    public getOptionTranslation(option: ModelValue): string {
        const optionTranslation = this.i18NPipe.transform(option.translationKey);
        return optionTranslation === option.translationKey ? option.value : optionTranslation;
    }

    handleFormErrors($event: IsFormValidEvent): void {
        this.hasErrors = !$event.valid && $event.isDirty;
        this.cd.markForCheck();
    }
}
