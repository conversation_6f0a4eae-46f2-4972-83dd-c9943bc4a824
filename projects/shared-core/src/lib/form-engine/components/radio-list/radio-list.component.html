<div class="form-element-container" *ngIf="componentVisible()" [nsFormElementContainer]="hasErrors">
  <div class="vertical" *ngIf="!config.horizontal">
    <nh-form-error-indicator *ngIf="!selectedValue" [config]="config" (isFormValid)="handleFormErrors($event)"></nh-form-error-indicator>
    <nh-input-header [config]="config"></nh-input-header>
    <div class="spacing" *ngIf="config.enableFieldStatus">
      <nh-field-status-indicator [config]="config"></nh-field-status-indicator>
    </div>
    <div *ngIf="enabled" class="input-wrapper">
      <div class="radio" *ngFor="let value of values">
        <input
          class="form-control"
          type="radio"
          [id]="value.id"
          [name]="value.id"
          [value]="value.value"
          [checked]="isChecked(value.value)"
          (change)="onChange(value)"
        />
        <label [for]="value.id" [innerHTML]="getOptionTranslation(value) | safeHtml"></label>
      </div>
    </div>
  </div>

  <div class="horizontal" *ngIf="config.horizontal">
    <div class="input-wrapper flex pt-l pr-l pb-l pl-l">
      <div class="question flex-item--1 type--font-weight-semibold mr-xl">
        <nh-form-error-indicator
          *ngIf="!selectedValue"
          [config]="config"
          (isFormValid)="handleFormErrors($event)"
        ></nh-form-error-indicator>
        <span [innerHTML]="config.labelKey | i18n : i18nPlaceholders | safeHtml"></span>
        <div *ngIf="config.enableFieldStatus">
          <nh-field-status-indicator [config]="config"></nh-field-status-indicator>
        </div>
      </div>
      <div class="flex-item--1" *ngFor="let value of values">
        <div class="radio" *ngIf="enabled">
          <input
            class="form-control"
            type="radio"
            [id]="value.id"
            [name]="value.id"
            [value]="value.value"
            [checked]="value.value === selectedValue"
            (change)="onChange(value)"
          />
          <label [for]="value.id" [innerHTML]="getOptionTranslation(value) | safeHtml"></label>
        </div>
      </div>
    </div>
  </div>
</div>
