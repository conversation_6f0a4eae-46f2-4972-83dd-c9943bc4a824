import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SymptomInformation } from '../../../generated/models/symptom-information';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { RequiredType } from '../../models/required-type.enum';
import { FieldService } from '../../services/field.service';
import { AddAnswer, ContinueForm, StartNewForm } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { selectForm } from '../../store/selectors/form.selectors';
import { selectSymptomInformation } from '../../store/selectors/inquiry.selectors';

enum EarlierSymptomAction {
  SEND = 'send',
  CREATE = 'create'
}

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-earlier-symptom',
  templateUrl: './earlier-symptom.component.html',
  styleUrls: ['./earlier-symptom.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class EarlierSymptomComponent implements OnInit, OnDestroy, FormField {
  @Input()
  public config: FormFieldConfig;

  @Input()
  public fieldHideCondition: boolean;

  @Input()
  secondaryButtonTranslationKey: string;

  @Output()
  public next = new EventEmitter<void>();

  @Output()
  public previous = new EventEmitter<void>();

  @Output()
  public canceled = new EventEmitter<void>();

  public info: SymptomInformation;
  public valid: boolean;
  public error: string;

  public actions = Object.values(EarlierSymptomAction);
  public action = EarlierSymptomAction;
  public selectedAction: EarlierSymptomAction;

  private initialized = false;
  private destroy$ = new Subject<boolean>();

  constructor(private store: Store<FormEngineState>, private fieldService: FieldService, private cd: ChangeDetectorRef) {}

  ngOnInit() {
    this.config.required = {
      conditionType: RequiredType.BOOLEAN_TRUE
    };

    this.store
      .select(selectSymptomInformation(this.config.formType))
      .pipe(takeUntil(this.destroy$))
      .subscribe(info => {
        this.info = info;
      });

    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));

    this.store
      .select(selectForm(this.config.formType))
      .pipe(takeUntil(this.destroy$))
      .subscribe(form => {
        if (form) {
          this.valid = form.formValid;
          if (this.valid) {
            this.error = undefined;
          } else {
            if (this.initialized) {
              this.error = 'wizard.errors.submitFailed';
            }
          }
          if (this.error === undefined && this.initialized) {
            this.fieldService.clearFormFieldStyle();
            this.cd.detectChanges();
          }
          this.cd.markForCheck();
        }
      });
  }

  ngOnDestroy() {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  public onChange(action: EarlierSymptomAction) {
    const isFirstSelection = this.selectedAction === null;
    this.selectedAction = action;
    if (action === EarlierSymptomAction.SEND) {
      this.store.dispatch(
        new ContinueForm({
          ...this.info.mostSevere,
          earlierSymptom: action
        })
      );
      // Changing the answer has the side effect of answers being set to null because the visibility changes
      // Therefore, a duplicate ContinueForm action is needed to undo them after the visibility change
      // See FormInputFieldComponent#set visibility
      if (!isFirstSelection) {
        this.store.dispatch(
          new ContinueForm({
            ...this.info.mostSevere,
            earlierSymptom: action
          })
        );
      }
    } else {
      this.store.dispatch(new StartNewForm({ type: this.config.formType, totalSections: 1 }));
      this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, action, false)));
    }
  }

  public nextSection() {
    this.initialized = true;
    this.next.emit();
  }

  public previousSection() {
    this.initialized = true;
    this.previous.emit();
  }
}
