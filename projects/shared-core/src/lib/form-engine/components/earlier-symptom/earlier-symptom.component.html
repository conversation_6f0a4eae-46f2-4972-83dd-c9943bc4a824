<div class="wizard-section fe-wizard-section">
  <div class="section-header">
    <div class="form-element-container">
      <h1 class="section-title" [innerHTML]="config.headerKey | i18n | safeHtml"></h1>
      <p class="section-description" [innerHTML]="config.descriptionKey | i18n | safeHtml"></p>
    </div>
  </div>
  <div class="section-content">
    <div class="earlier-symptom-actions-container">
      <div class="symptom">
        <div class="questionnaire-summary-content mb-l">
          <symptom-summary-content [theSymptom]="info.mostSevere" [gender]="config.gender"></symptom-summary-content>
        </div>
        <div class="form-element-container">
          <div class="header">
            <nh-form-error-indicator [config]="config"></nh-form-error-indicator>
            <h4>{{ 'patient.contactClinic.previouslyAddedSymptom.selection.title' | i18n }} <span *ngIf="config.required">*</span></h4>
          </div>
          <div class="input-wrapper">
            <div class="radio" *ngFor="let action of actions">
              <input
                type="radio"
                [id]="'earlier-' + config.formKey + '-symptom-' + action"
                [checked]="selectedAction === action"
                (change)="onChange(action)"
              />
              <label [for]="'earlier-' + config.formKey + '-symptom-' + action">
                {{ 'patient.symptomInquiry.earlierSymptomActions.actions.' + action | i18n }}
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="selectedAction === action.CREATE">
      <ng-container
        class="no-padding"
        *ngFor="let field of config.children"
        [fieldHideCondition]="fieldHideCondition"
        nhDynamicFormField
        [config]="field"
      ></ng-container>
    </div>
  </div>
</div>
<div class="wizard-button">
  <div class="errors" *ngIf="error">
    <span class="error">{{ error | i18n }}</span>
  </div>
  <div class="step-buttons center">
    <ds-button variation="secondary" class="button-cancel" (buttonClick)="canceled.emit()">
      {{ secondaryButtonTranslationKey | i18n }}
    </ds-button>
    <ds-button class="button-next" (buttonClick)="nextSection()">
      {{ 'general.next' | i18n }}
    </ds-button>
  </div>
</div>
