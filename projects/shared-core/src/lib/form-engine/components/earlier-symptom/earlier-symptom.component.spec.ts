import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { EarlierSymptomComponent } from './earlier-symptom.component';
import { MocksModule } from '@shared-core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('EarlierSymptomComponent', () => {
    let component: EarlierSymptomComponent;
    let fixture: ComponentFixture<EarlierSymptomComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            schemas: [NO_ERRORS_SCHEMA],
            declarations: [EarlierSymptomComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(EarlierSymptomComponent);
        component = fixture.componentInstance;
        component.config = { key: 'test', formType: QuestionaryType.BOUNCE_HADS };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
