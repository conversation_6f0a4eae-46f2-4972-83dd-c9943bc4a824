import { Component, Input } from '@angular/core';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';

@Component({
    selector: 'ns-text-block',
    templateUrl: './text-block.component.html',
    styleUrls: ['./text-block.component.scss'],
})
export class TextBlockComponent implements FormField {
    @Input()
    public config: FormFieldConfig;

    constructor() {}
}
