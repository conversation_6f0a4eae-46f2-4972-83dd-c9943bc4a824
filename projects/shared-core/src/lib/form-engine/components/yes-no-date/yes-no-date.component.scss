@import '../../../../styles/deprecated_variables.scss';
@import '../../../../styles/ds-variables.scss';

$border: thin solid lightgrey;
:root {
  display: block;
}
:host.visible {
  border: $border;
  border-bottom: none;
  &:first-of-type {
    border-top-left-radius: $border-radius-md;
    border-top-right-radius: $border-radius-md;
  }
  &:last-of-type {
    border-bottom-right-radius: $border-radius-md;
    border-bottom-left-radius: $border-radius-md;
    border-bottom: $border;
    margin-bottom: $spacing-l;
  }
}
.input-wrapper {
  background-color: white;
  min-height: 126px;
}
.question {
  font-weight: $font-weight-bold;
}
.question ::ng-deep .neg-mt-l {
  margin-top: $spacing-xxs;
}
