<div class="form-element-container" *ngIf="componentVisible()">
  <div class="input-wrapper flex pt-l pr-l pb-l pl-l">
    <div class="question flex-item--1 type--font-weight-semibold mr-xl">
      {{ questionTranslationKey | i18n }}
      <ng-container class="spacing">
        <nh-field-status-indicator [config]="config"></nh-field-status-indicator>
      </ng-container>
    </div>
    <ng-container *ngIf="enabled">
      <div class="flex-item--1 mr-l">
        <div class="radio flex flex--column">
          <div class="mb-xs">
            <input
              [checked]="booleanAttributeValue === true"
              [id]="booleanAttributeRadioIdYes"
              [name]="booleanAttributeRadioIdYes"
              [value]="true"
              class="form-control mr-s"
              type="radio"
              (change)="onBooleanChanged(true)"
            />
            <label [for]="booleanAttributeRadioIdYes">{{ booleanAttributeTranslationKeyYes | i18n }}</label>
          </div>
          <nh-simple-date-input
            #simpleDateInput
            [inputId]="dateAttributeConfig.key + 'DateInput'"
            [dateFormat]="dateFormat"
            [dateValue]="dateValue"
            [maxDate]="maxDate"
            (valueUpdate)="onDateChanged($event)"
            (focusLost)="simpleDateFocusLost($event)"
            (clicked)="simpleDateInputClicked()"
            [configKey]="config.key"
            [unknownDayDefault]="config.unknownDayDefault"
            [allowUnknownDate]="config.allowUnknownDate"
          ></nh-simple-date-input>
          <div class="pl-xl">
            <nh-field-status-indicator [config]="this.dateAttributeConfig"></nh-field-status-indicator>
          </div>
        </div>
      </div>

      <div *ngIf="enabled" class="flex-item--1">
        <div class="radio">
          <input
            class="form-control"
            type="radio"
            [id]="booleanAttributeRadioIdNo"
            [name]="booleanAttributeRadioIdNo"
            [value]="false"
            [checked]="booleanAttributeValue === false"
            (change)="onBooleanChanged(false)"
          />
          <label [for]="booleanAttributeRadioIdNo">{{ booleanAttributeTranslationKeyNo | i18n }}</label>
        </div>
      </div>
    </ng-container>
  </div>
</div>
