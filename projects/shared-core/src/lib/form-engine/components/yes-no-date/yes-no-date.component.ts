import { ChangeDetector<PERSON><PERSON>, Component, HostBinding, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { ConfigurationProviderService } from '../../../abstract-services/configuration-provider.service';
import { FormItemType } from '../../../generated/models/form-item-type';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { FieldService } from '../../services/field.service';
import { safeSelectField, safeSelectFieldStatus } from '../../services/subscription-helper';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';
import { SimpleDateInputComponent, SimpleDateInputValue } from '../simple-date-input/simple-date-input.component';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-yes-no-date',
    templateUrl: './yes-no-date.component.html',
    styleUrls: ['./yes-no-date.component.scss'],
})
export class YesNoDateComponent extends FormInputField implements OnInit, FormField, OnDestroy {
    @HostBinding('class') get hostClasses() {
        const modifier = this.visible ? ' visible' : ' invisible';
        return 'yesnodate' + modifier;
    }

    constructor(
        protected store: Store<FormEngineState>,
        private fieldService: FieldService,
        cd: ChangeDetectorRef,
        private configService: ConfigurationProviderService
    ) {
        super(cd);
        this.initDatePickerConfig();
    }

    static readonly FORM_ITEM_TYPE = FormItemType.YES_NO_DATE;

    static readonly BOOLEAN_ATTRIBUTE_POSTFIX = 'Selected';
    static readonly BOOLEAN_ATTRIBUTE_POSTFIX_YES = 'Yes';
    static readonly BOOLEAN_ATTRIBUTE_POSTFIX_NO = 'No';

    static readonly DATE_ATTRIBUTE_POSTFIX = 'Date';

    @ViewChild('simpleDateInput') simpleDateInput: SimpleDateInputComponent;

    private booleanAttributeConfig: FormFieldConfig = null;
    public booleanAttributeRadioIdYes: string = null;
    public booleanAttributeRadioIdNo: string = null;
    public booleanAttributeTranslationKeyYes: string = null;
    public booleanAttributeTranslationKeyNo: string = null;
    public booleanAttributeValue: boolean = null;

    public dateAttributeConfig: FormFieldConfig = null;
    public dateAttributeId: string = null;
    public dateAttributeValue: string = null;
    public maxDate: Date;

    public questionTranslationKey: string = null;

    public dateFormat: string;

    @Input()
    public config: FormFieldConfig;

    @Input()
    public visible: boolean;

    public fieldStatusControlledVisibilty = true;

    public enabled = true;

    private destroy$ = new Subject<boolean>();

    private lastSimpleDateInputValue: SimpleDateInputValue;

    dateValue: Date;

    ngOnInit() {
        if (this.config.enableFieldStatus) {
            if (this.config.questionGroupId) {
                safeSelectFieldStatus(
                    this.store,
                    this.destroy$,
                    this.config.questionGroupId,
                    FieldStatusType.NOT_READY
                ).subscribe((fieldStatuses: FieldStatus[]) => {
                    this.fieldStatusControlledVisibilty = !fieldStatuses || fieldStatuses.length === 0;
                    this.cd.markForCheck();
                });
            } else {
                safeSelectFieldStatus(this.store, this.destroy$, this.config.key, FieldStatusType.NOT_READY).subscribe(
                    (fieldStatuses: FieldStatus[]) => {
                        this.enabled = !fieldStatuses || fieldStatuses.length === 0;
                        this.cd.markForCheck();
                    }
                );
            }
        }
        if (!this.config.allowFutureDate) {
            this.maxDate = new Date();
        }
        const baseKey = this.config.key;
        const booleanAttributeKey = baseKey + YesNoDateComponent.BOOLEAN_ATTRIBUTE_POSTFIX;
        const dateAttributeKey = baseKey + YesNoDateComponent.DATE_ATTRIBUTE_POSTFIX;

        this.questionTranslationKey = this.config.labelKey;
        const baseTranslationKey = this.config.secondaryTranslationPrefix || this.config.labelKey;

        this.booleanAttributeTranslationKeyYes =
            baseTranslationKey +
            YesNoDateComponent.BOOLEAN_ATTRIBUTE_POSTFIX +
            YesNoDateComponent.BOOLEAN_ATTRIBUTE_POSTFIX_YES;
        this.booleanAttributeTranslationKeyNo =
            baseTranslationKey +
            YesNoDateComponent.BOOLEAN_ATTRIBUTE_POSTFIX +
            YesNoDateComponent.BOOLEAN_ATTRIBUTE_POSTFIX_NO;

        this.booleanAttributeRadioIdYes = booleanAttributeKey + YesNoDateComponent.BOOLEAN_ATTRIBUTE_POSTFIX_YES;
        this.booleanAttributeRadioIdNo = booleanAttributeKey + YesNoDateComponent.BOOLEAN_ATTRIBUTE_POSTFIX_NO;

        this.dateAttributeId = dateAttributeKey;

        this.booleanAttributeConfig = {
            formKey: this.config.formKey,
            formType: this.config.formType,
            site: this.config.site,
            key: booleanAttributeKey,
            type: this.config.type,
        };

        this.dateAttributeConfig = {
            formKey: this.config.formKey,
            formType: this.config.formType,
            site: this.config.site,
            key: dateAttributeKey,
            type: this.config.type,
        };

        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.booleanAttributeConfig, null, true)));
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.dateAttributeConfig, null, true)));

        safeSelectField(
            this.store,
            this.destroy$,
            this.booleanAttributeConfig.key,
            this.booleanAttributeConfig.formType
        ).subscribe((value: boolean) => {
            this.booleanAttributeValue = value;
            this.cd.markForCheck();
        });

        safeSelectField(
            this.store,
            this.destroy$,
            this.dateAttributeConfig.key,
            this.dateAttributeConfig.formType
        ).subscribe((dv: Date) => {
            this.dateValue = dv;
        });
    }

    getDateAttributeValue() {
        return this.dateAttributeValue;
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public simpleDateFocusLost(event: SimpleDateInputValue) {
        this.onDateChanged(event, false);
        this.publishDateAttribute(event.storeValue);
        if (this.booleanAttributeValue === true) {
            if (!this.lastSimpleDateInputValue || !this.lastSimpleDateInputValue.momentValue) {
                this.addValueMissingHardErrorStatusForGivenKey(this.config.key);
            }
            if (this.lastSimpleDateInputValue.isUnknownDayPattern) {
                this.addConfirmUnkownDaySoftErrorStatusForGivenKey(this.config.key);
            }
            if (this.lastSimpleDateInputValue.isAfterMaxDate) {
                this.addFutureDateHardErrorStatusForGivenKey(this.config.key);
            }
            if (!this.lastSimpleDateInputValue.validDate) {
                this.addInvalidValueHardErrorStatusForGivenKey(this.config.key);
            }
        }
    }

    public simpleDateInputClicked() {
        this.onBooleanChanged(true);
    }

    public onBooleanChanged(value: boolean, focusInput = true) {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.booleanAttributeConfig, value, null)));
        this.clearStatusForGivenKey(this.config.key);
        if (!value) {
            this.lastSimpleDateInputValue = null;
            this.publishDateAttribute(null);
            this.cd.markForCheck();
        } else if (focusInput) {
            setTimeout(() => {
                this.simpleDateInput.focus();
            }, 250);
        }
    }

    public onDateChanged(event: SimpleDateInputValue, focusInput = true) {
        this.lastSimpleDateInputValue = event;
        this.onBooleanChanged(true, focusInput);
        this.clearStatusForGivenKey(this.config.key);
        this.clearStatusForGivenKey(this.dateAttributeConfig.key);
    }

    private initDatePickerConfig() {
        this.configService.dateFormattingPattern().subscribe((pattern) => {
            this.dateFormat = pattern.longDatePattern.toUpperCase();
        });
    }

    private publishDateAttribute(toPublish) {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.dateAttributeConfig, toPublish, null)));
    }

    public componentVisible(): boolean {
        return this.visible && this.fieldStatusControlledVisibilty;
    }
}
