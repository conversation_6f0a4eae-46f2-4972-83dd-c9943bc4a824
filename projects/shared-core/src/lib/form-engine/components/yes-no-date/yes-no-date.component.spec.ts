import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { YesNoDateComponent } from './yes-no-date.component';
import { MocksModule } from '@shared-core/testing';

describe('YesNoDate', () => {
    let component: YesNoDateComponent;
    let fixture: ComponentFixture<YesNoDateComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            schemas: [NO_ERRORS_SCHEMA],
            declarations: [YesNoDateComponent],
            imports: [MocksModule],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(YesNoDateComponent);
        component = fixture.componentInstance;
        component.config = { key: 'test' };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
