<div [nsFormElementContainer]="hasErrors" id="pain-pointer-directive">
  <div class="pain-pointer-container" *ngIf="visible" [onDestroyPainDirective]="config">
    <nh-form-error-indicator [config]="config" [other]="true" (isFormValid)="handleFormErrors($event)"></nh-form-error-indicator>
    <nh-input-header [config]="config"></nh-input-header>
    <div class="pain-pointer-row">
      <div class="col-xs-12 col-sm-12 col-md-12">
        <div class="row">
          <div class="col-xs-6">
            <pain-pointer
              [painLocations]="enabledLocations"
              [gender]="config.gender"
              [side]="side.FRONT"
              [view]="false"
              (partToggled)="partToggled($event)"
              [selectedParts]="selectedLocations"
              [painPointerWidth]="width"
              [painPointerHeight]="height"
              [enabledLocations]="enabledLocations"
            ></pain-pointer>
          </div>
          <div class="col-xs-6">
            <pain-pointer
              [painLocations]="enabledLocations"
              [gender]="config.gender"
              [side]="side.BACK"
              [view]="false"
              (partToggled)="partToggled($event)"
              [selectedParts]="selectedLocations"
              [painPointerWidth]="width"
              [painPointerHeight]="height"
              [enabledLocations]="enabledLocations"
            ></pain-pointer>
          </div>
        </div>
      </div>
    </div>
    <div class="pain-locations">
      <ng-container *ngFor="let location of selectedLocations; let first = first; let last = last">
        <span *ngIf="first">{{ 'patient.painPointer.selectedLocations' | i18n }}:&nbsp;</span>
        <span>{{ config.translationPrefix + '.' + location + '.label' | i18n | lowercaseExceptGerman : locale }}</span>
        <span *ngIf="!last">,&nbsp;</span>
      </ng-container>
    </div>
    <div class="other-details">
      <label class="text-area-paragraph" for="other-locations">
        {{
          (selectedLocations?.length
            ? 'patient.newSymptom.painSymptom.step1.locationSpecification'
            : 'patient.newSymptom.painSymptom.step1.otherLocations'
          ) | i18n
        }}
      </label>
      <textarea
        class="form-control fe-pain-pointer-textarea-field"
        id="other-locations"
        type="text"
        [ngModel]="otherText"
        name="other-locations"
        (ngModelChange)="onOtherChange($event)"
      ></textarea>
    </div>
  </div>
</div>
