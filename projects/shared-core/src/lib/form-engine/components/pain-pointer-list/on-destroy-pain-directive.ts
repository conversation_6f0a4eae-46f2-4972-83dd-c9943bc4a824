import { Directive, Input, OnDestroy } from '@angular/core';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { PainPointerService } from '../../../utils/pain-pointer/pain-pointer.service';
import { getStyle } from '../../directives/form-element-container/form-element-container.utils';
import { formElementContainerDefaultStyles } from '../../directives/form-element-container/form-element-container.const';

/**
 * Directive for the pain pointer element to handle a corner case where the element is used conditionally in a case
 * which was and is not supported by the general logics of the form engine and its associated error handling.
 */
@Directive({ selector: '[onDestroyPainDirective]' })
export class OnDestroyPainDirective implements OnDestroy {
    @Input('onDestroyPainDirective') config: FormFieldConfig;

    constructor(private painPointerService: PainPointerService) {}
    ngOnDestroy(): void {
        this.painPointerService.clearOtherConfigStateValues(this.painPointerService.getOtherConfig(this.config));
        const painBodyElement: HTMLElement = document.getElementById('pain-pointer-directive');
        painBodyElement?.setAttribute('style', getStyle(formElementContainerDefaultStyles));
    }
}
