import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { ConvertHyphenedToCapitalPipe } from '../../../pipes/convert-hyphened-to-capital.pipe';
import { DecodePipe } from '../../../pipes/decode.pipe';
import { ArrayUtils } from '../../../util/array.utils';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { PainPointerSide } from '../../models/pain-pointer-side.enum';
import { FieldService } from '../../services/field.service';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';
import { safeSelectField } from '../../services/subscription-helper';
import { NoonaLocaleService } from '../../../abstract-services/noona-locale.service';
import { IsFormValidEvent } from '../form-error-indicator/form-error-indicator.interface';
import { PainPointerService } from '../../../utils/pain-pointer/pain-pointer.service';

const HEIGHT = 520;
const WIDTH = 195;

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-pain-pointer-list',
  templateUrl: './pain-pointer-list.component.html',
  styleUrls: ['./pain-pointer-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PainPointerListComponent extends FormInputField implements OnInit, OnDestroy, FormField {
  @Input()
  public config: FormFieldConfig;

  @Input()
  public visible: boolean;

  public otherConfig: FormFieldConfig;

  public width = WIDTH;
  public height = HEIGHT;

  public enabledLocations;
  public selectedLocations: string[];

  public side = PainPointerSide;
  public otherText: string;

  locale: string;
  hasErrors = false;

  private otherFieldKey: string;
  private destroy$ = new Subject<boolean>();
  currentValue: string[] = [];
  private initialized = false;

  constructor(
    protected store: Store<FormEngineState>,
    private fieldService: FieldService,
    private toCamel: ConvertHyphenedToCapitalPipe,
    cd: ChangeDetectorRef,
    private decode: DecodePipe,
    private localeService: NoonaLocaleService,
    private painPointerService: PainPointerService
  ) {
    super(cd);
  }

  ngOnInit() {
    this.locale = this.localeService.getLocale();
    this.otherFieldKey = this.painPointerService.getOtherFieldKey(this.config);
    this.otherConfig = this.painPointerService.getOtherConfig(this.config);

    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));
    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.otherConfig, null, true)));
    this.enabledLocations = this.fieldService.getListValues(this.config).map(location => {
      return location.value;
    });

    safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe(values => {
      this.selectedLocations = values ? [...values] : [];

      if (!this.initialized) {
        this.currentValue = this.selectedLocations;
        this.initialized = true;
      }
      this.currentValue = this.visible ? this.currentValue : [];
    });

    safeSelectField(this.store, this.destroy$, this.otherConfig.key, this.otherConfig.formType).subscribe(value => {
      this.otherText = this.decode.transform(value);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  public partToggled(location: string) {
    ArrayUtils.toggle(this.currentValue, this.toCamel.transform(location));
    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, [...this.currentValue], false)));
  }

  public onOtherChange(content: string) {
    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.otherConfig, content, false)));
  }

  handleFormErrors($event: IsFormValidEvent): void {
    this.hasErrors = !$event.valid && $event.isDirty;
    this.cd.markForCheck();
  }
}
