import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PainPointerListComponent } from './pain-pointer-list.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';
import { FieldService } from '../../services/field.service';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { PainPointerService } from '../../../utils/pain-pointer/pain-pointer.service';
import { FormItemType } from '../../../generated/models/form-item-type';
import { Gender } from '../../../generated/models/gender';
import { Application } from '../../../models/application';
import { SymptomType } from '../../../generated/models/symptom-type';
import { MockStore } from '@ngrx/store/testing';
import { AddAnswer } from '../../store/actions/form.actions';

const mockGetAnswer = {
  field: 'pain',
  answer: 'shoulder',
  required: false,
  initialValue: false,
  type: QuestionaryType.BOUNCE_HADS
};

const fieldServiceMock = {
  getListValues: () => {
    return [
      {
        id: 'pain',
        selected: false,
        translationKey: 'test.label',
        value: 'shoulder',
        isAdhoc: false
      },
      {
        id: 'pain',
        selected: false,
        translationKey: 'test.label',
        value: 'neck',
        isAdhoc: false
      }
    ];
  },
  getAnswer: () => {
    return {
      answer: 'shoulder',
      field: 'pain',
      required: false,
      type: QuestionaryType.BOUNCE_HADS,
      initialValue: false
    };
  }
};

const painPointerServiceMock = {
  getOtherFieldKey: () => {
    return 'otherLocations';
  },
  getOtherConfig: () => {
    return {
      type: FormItemType.TEXTAREA,
      formType: SymptomType.SYMPTOM_ABDOMINAL,
      key: 'otherLocations',
      maxLength: 500,
      minLength: 0,
      gender: Gender.MALE,
      site: Application.CLINIC
    };
  }
};

describe('PainPointerListComponent', () => {
  let component: PainPointerListComponent;
  let fixture: ComponentFixture<PainPointerListComponent>;
  let store: MockStore;
  let fieldService: FieldService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MocksModule],
      declarations: [PainPointerListComponent],
      providers: [
        { provide: FieldService, useValue: fieldServiceMock },
        { provide: PainPointerService, useValue: painPointerServiceMock }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PainPointerListComponent);
    component = fixture.componentInstance;
    component.config = { key: 'pain', labelKey: 'test.label', formType: QuestionaryType.BOUNCE_HADS };
    fieldService = TestBed.inject(FieldService);
    store = TestBed.inject(MockStore);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component with correct properties', () => {
    component.ngOnInit();
    expect(component.locale).toBeDefined();
    const fieldServiceGetListValuesSpy = jest.spyOn(fieldService, 'getListValues');
    fieldServiceGetListValuesSpy.mockReturnValueOnce(fieldServiceMock.getListValues());
    expect(component.enabledLocations).toEqual(['shoulder', 'neck']);
  });

  it('should toggle part selection and dispatch AddAnswer action', () => {
    const dispatchSpy = jest.spyOn(store, 'dispatch');
    component.currentValue = [];
    const location = 'shoulder';
    component.partToggled(location);
    expect(component.currentValue).toContain(location);
    expect(dispatchSpy).toHaveBeenCalledWith(new AddAnswer(mockGetAnswer));
  });

  it('should dispatch AddAnswer action on other text change', () => {
    const dispatchSpy = jest.spyOn(store, 'dispatch');
    const fieldServiceGetAnswerSpy = jest.spyOn(fieldService, 'getAnswer');
    component.onOtherChange('text sample');
    expect(fieldServiceGetAnswerSpy).toHaveBeenCalledWith(component.otherConfig, 'text sample', false);
    expect(dispatchSpy).toHaveBeenCalledTimes(1);
  });

  it('should call next and complete on destroy$', () => {
    const nextSpy = jest.spyOn(component['destroy$'], 'next');
    const completeSpy = jest.spyOn(component['destroy$'], 'complete');
    component.ngOnDestroy();
    expect(nextSpy).toHaveBeenCalledWith(true);
    expect(completeSpy).toHaveBeenCalled();
  });
});
