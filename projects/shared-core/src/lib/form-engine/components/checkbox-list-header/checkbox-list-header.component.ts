import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-checkbox-list-header',
    templateUrl: './checkbox-list-header.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CheckboxListHeaderComponent implements FormField {
    @Input() config: FormFieldConfig;
}
