import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { CheckboxListHeaderComponent } from './checkbox-list-header.component';
import { MocksModule } from '@shared-core/testing';

describe('CheckboxListHeaderComponent', () => {
    let component: CheckboxListHeaderComponent;
    let fixture: ComponentFixture<CheckboxListHeaderComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [CheckboxListHeaderComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(CheckboxListHeaderComponent);
        component = fixture.componentInstance;
        component.config = { key: 'test' };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
