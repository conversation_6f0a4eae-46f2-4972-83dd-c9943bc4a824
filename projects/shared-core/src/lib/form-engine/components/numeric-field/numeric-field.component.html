<div *ngIf="visible" class="numeric-field-container flex--column">
    <nh-form-question
        *ngIf="hasTitle()"
        [titleTranslationKey]="titleTranslationKey"
        [descriptionTranslationKey]="descriptionTranslationKey"
    ></nh-form-question>
    <div class="numeric-field flex flex--column">
        <input
            #numericInput
            type="number"
            class="numeric-field__input"
            step="any"
            required
            [id]="inputId"
            [formControl]="input"
            [min]="config.min"
            [max]="config.max"
            (blur)="updateFieldStatuses()"
            (keyup.enter)="updateFieldStatuses()"
        />
        <span class="numeric-field__unit type--color-grey">{{ config.unit | i18n }}</span>
    </div>
    <nh-field-status-indicator [config]="config"></nh-field-status-indicator>
</div>
