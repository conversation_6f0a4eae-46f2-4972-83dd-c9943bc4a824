@import '../../../../styles/ds-variables.scss';

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.numeric-field-container {
  margin-bottom: var(--spacing-l);
}
.numeric-field {
  position: relative;
}
.numeric-field__input {
  padding-right: $unit-max-width;
}
.numeric-field__unit {
  max-width: $unit-max-width;
  position: absolute;
  right: var(--spacing-m);
  top: var(--spacing-s);
  user-select: none;
  text-align: center;
}
