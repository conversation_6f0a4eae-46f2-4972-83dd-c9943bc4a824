import { FormInputField } from '../form-input-field.component';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { safeSelectField } from '../../services/subscription-helper';
import { FormEngineState } from '../../store/reducers/state';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { AddAnswer } from '../../store/actions/form.actions';
import { FieldService } from '../../services/field.service';
import { UntypedFormControl, Validators } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { FieldStatusType } from '../../models/field-status.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-numeric-field',
    templateUrl: './numeric-field.component.html',
    styleUrls: ['./numeric-field.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NumericFieldComponent extends FormInputField implements OnInit, OnDestroy {
    @Input() config: FormFieldConfig;
    @Input() visible: boolean;
    @Input()
    public inputId = '';
    input: UntypedFormControl;
    @ViewChild('numericInput') numericInputField: ElementRef;

    public titleTranslationKey: string = null;
    public descriptionTranslationKey: string = null;

    private readonly MIN_VIOLATION_ERROR_TRANSLATION_KEY = 'fe.numericField.minViolation';
    private readonly MAX_VIOLATION_ERROR_TRANSLATION_KEY = 'fe.numericField.maxViolation';
    private readonly OUT_OF_RANGE_ERROR_TRANSLATION_KEY = 'fe.numericField.outOfRange';
    private outOfRangeTranslationKey: string;
    private destroy$ = new Subject<boolean>();

    constructor(
        protected cd: ChangeDetectorRef,
        protected store: Store<FormEngineState>,
        private fieldService: FieldService
    ) {
        super(cd);
    }

    ngOnInit(): void {
        this.titleTranslationKey = this.config.labelKey;
        this.descriptionTranslationKey = this.config.subLabelKey;
        this.outOfRangeTranslationKey = this.getCorrectOutOfRangeTranslationKey();
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));
        this.input = new UntypedFormControl('', {
            validators: [Validators.required, Validators.min(this.config.min), Validators.max(this.config.max)],
        });
        this.input.valueChanges.pipe(takeUntil(this.destroy$)).subscribe((value) => {
            this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, value, false)));
        });
        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((value) => {
            if (value !== undefined) {
                this.input.setValue(value, { emitEvent: false });
            }
        });
        this.updateFieldStatuses();
    }

    ngOnDestroy(): void {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    updateFieldStatuses(): void {
        this.clearStatusForGivenKey(this.config.key);
        const { errors } = this.input;
        if (!errors) {
            return;
        }
        if (errors.required && this.input.dirty) {
            this.addValueMissingHardErrorStatusForGivenKey(this.config.key);
        }
        if (errors.min || errors.max) {
            this.addOutOfRangeFieldStatus();
        }
    }

    hasTitle(): boolean {
        return this.titleTranslationKey && this.titleTranslationKey.length > 0;
    }

    private addOutOfRangeFieldStatus(): void {
        this.addFieldError(
            this.config.key,
            this.outOfRangeTranslationKey,
            FieldStatusType.HARD_ERROR,
            `min:${this.config.min},max:${this.config.max}`
        );
    }

    private getCorrectOutOfRangeTranslationKey(): string {
        if (this.config.min != null && this.config.max != null) {
            return this.OUT_OF_RANGE_ERROR_TRANSLATION_KEY;
        }
        if (this.config.min != null) {
            return this.MIN_VIOLATION_ERROR_TRANSLATION_KEY;
        }
        if (this.config.max != null) {
            return this.MAX_VIOLATION_ERROR_TRANSLATION_KEY;
        }
    }

    focus() {
        this.numericInputField.nativeElement.focus();
    }
}
