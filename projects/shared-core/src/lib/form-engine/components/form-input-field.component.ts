import { ChangeDetectorRef } from '@angular/core';
import { Store } from '@ngrx/store';
import { FormFieldConfig } from '../models/form-field-config.interface';
import { AddAnswer } from '../store/actions/form.actions';
import { FormEngineState } from '../store/reducers/state';
import {
    AddOrUpdateFieldStatus,
    RemoveAllComponentFieldStatusesFormTypeAndFieldKey,
    RemoveFieldStatus,
} from '../store/actions/field-status.actions';
import { FieldStatusType } from '../models/field-status.interface';
import { SimpleDateInputComponent } from './simple-date-input/simple-date-input.component';
import { UpdateComponentState } from '../store/actions/component-state.action';

export abstract class FormInputField {
    static readonly MISSING_FIELD_TRANSLATION_KEY = 'fe.error.generic.missingFieldOrQuestion';
    static readonly INVALID_FIELD_TRANSLATION_KEY = 'fe.error.generic.invalidFieldOrQuestion';
    static readonly FUTURE_DATE_TRANSLATION_KEY = 'fe.error.generic.futureDate';

    public visible;
    public config: FormFieldConfig;
    protected store: Store<FormEngineState>;

    protected constructor(protected cd: ChangeDetectorRef) {}

    set visibility(visibility: boolean) {
        this.visible = visibility;

        this.store.dispatch(
            new UpdateComponentState({
                id: this.config.key,
                visible: visibility,
            })
        );

        if (!this.visible && this.config.key) {
            this.store.dispatch(
                new AddAnswer({
                    type: this.config.formType,
                    field: this.config.key,
                    answer: null,
                    required: false,
                    initialValue: false,
                })
            );

            this.clearStatusForGivenKey(this.config.key);
        }
        this.cd.markForCheck();
    }

    clearStatusForGivenKey(itemKey: string) {
        this.store.dispatch(
            new RemoveAllComponentFieldStatusesFormTypeAndFieldKey({
                formType: this.config.formType,
                fieldKey: itemKey,
                translationKey: '',
            })
        );
    }

    clearInvalidValueHardErrorStatusForGivenKey(itemKey: string) {
        this.store.dispatch(
            new RemoveFieldStatus({
                formType: this.config.formType,
                fieldKey: itemKey,
                translationKey: FormInputField.INVALID_FIELD_TRANSLATION_KEY,
                statusType: FieldStatusType.HARD_ERROR,
            })
        );
    }

    addInvalidValueHardErrorStatusForGivenKey(itemKey: string) {
        this.store.dispatch(
            new AddOrUpdateFieldStatus({
                formType: this.config.formType,
                fieldKey: itemKey,
                translationKey: FormInputField.INVALID_FIELD_TRANSLATION_KEY,
                statusType: FieldStatusType.HARD_ERROR,
            })
        );
    }

    clearFutureDateHardErrorStatusForGivenKey(itemKey: string) {
        this.store.dispatch(
            new RemoveFieldStatus({
                formType: this.config.formType,
                fieldKey: itemKey,
                translationKey: FormInputField.FUTURE_DATE_TRANSLATION_KEY,
                statusType: FieldStatusType.HARD_ERROR,
            })
        );
    }

    addFutureDateHardErrorStatusForGivenKey(itemKey: string) {
        this.store.dispatch(
            new AddOrUpdateFieldStatus({
                formType: this.config.formType,
                fieldKey: itemKey,
                translationKey: FormInputField.FUTURE_DATE_TRANSLATION_KEY,
                statusType: FieldStatusType.HARD_ERROR,
            })
        );
    }

    clearValueMissingHardErrorStatusForGivenKey(itemKey: string) {
        this.store.dispatch(
            new RemoveFieldStatus({
                formType: this.config.formType,
                fieldKey: itemKey,
                translationKey: FormInputField.MISSING_FIELD_TRANSLATION_KEY,
                statusType: FieldStatusType.MISSING_VALUE,
            })
        );
    }

    addValueMissingHardErrorStatusForGivenKey(itemKey: string) {
        this.store.dispatch(
            new AddOrUpdateFieldStatus({
                formType: this.config.formType,
                fieldKey: itemKey,
                translationKey: FormInputField.MISSING_FIELD_TRANSLATION_KEY,
                statusType: FieldStatusType.MISSING_VALUE,
            })
        );
    }

    addFieldError(erroFieldKey: string, errorTranslationKey: string, errorType: FieldStatusType, meta: string) {
        this.store.dispatch(
            new AddOrUpdateFieldStatus({
                formType: this.config.formType,
                fieldKey: erroFieldKey,
                translationKey: errorTranslationKey,
                statusType: errorType,
                meta,
            })
        );
    }

    clearFieldError(erroFieldKey: string, errorTranslationKey: string, errorType: FieldStatusType) {
        this.store.dispatch(
            new RemoveFieldStatus({
                formType: this.config.formType,
                fieldKey: erroFieldKey,
                translationKey: errorTranslationKey,
                statusType: errorType,
            })
        );
    }

    addConfirmUnkownDaySoftErrorStatusForGivenKey(itemKey: string) {
        this.store.dispatch(
            new AddOrUpdateFieldStatus({
                formType: this.config.formType,
                fieldKey: itemKey,
                translationKey: SimpleDateInputComponent.UNKNOWN_DAY_SOFT_ERROR_TRANSLATION_KEY,
                statusType: FieldStatusType.SOFT_ERROR,
            })
        );
    }

    clearConfirmUnkownDaySoftErrorStatusForGivenKey(itemKey: string) {
        this.store.dispatch(
            new RemoveFieldStatus({
                formType: this.config.formType,
                fieldKey: itemKey,
                translationKey: SimpleDateInputComponent.UNKNOWN_DAY_SOFT_ERROR_TRANSLATION_KEY,
                statusType: FieldStatusType.SOFT_ERROR,
            })
        );
    }
}
