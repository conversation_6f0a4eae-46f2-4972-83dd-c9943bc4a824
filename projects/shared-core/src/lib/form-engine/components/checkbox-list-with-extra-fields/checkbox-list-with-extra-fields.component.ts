import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import remove from 'lodash/remove';
import { combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ArrayUtils } from '../../../util/array.utils';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { ModelValue } from '../../models/model-value.interface';
import { FieldService } from '../../services/field.service';
import { safeSelectField, safeSelectFieldStatus } from '../../services/subscription-helper';
import { UpdateComponentState } from '../../store/actions/component-state.action';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-checkbox-list-with-extra-fields',
    templateUrl: './checkbox-list-with-extra-fields.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CheckboxListWithExtraFieldsComponent extends FormInputField implements OnInit, FormField, OnDestroy {
    public static readonly RADIO_FIELD_POSTFIX = 'Radio';
    public static readonly TEXT_FIELD_POSTFIX = 'Text';
    public static readonly SELECTED_RADIO_OPTION_FIELD_POSTFIX = 'RadioOption';

    public static readonly DESCRIPTION_POSTFIX = 'description';

    @Input() public config: FormFieldConfig;
    @Input() public visible: boolean;

    public titleTranslationKey: string;
    public descriptionTranslationKey: string;
    public checkboxOptions: ModelValue[];
    public selectedOptions: string[];
    public radioOptions: ModelValue[] = [];
    public radioSelected: boolean;
    public selectedRadioOption: string | null;
    public textOption: ModelValue;
    public textInput: string;
    public fieldStatusControlledVisibilty = true;
    public enabled = true;
    public textFieldConfig: FormFieldConfig;

    private radioFieldConfig: FormFieldConfig;
    private selectedRadioOptionFieldConfig: FormFieldConfig;
    private destroy$ = new Subject<boolean>();

    constructor(
        protected store: Store<FormEngineState>,
        protected cd: ChangeDetectorRef,
        private fieldService: FieldService
    ) {
        super(cd);
    }

    ngOnInit() {
        this.initFieldConfigs();
        this.initTranslationKeys();
        if (this.config.enableFieldStatus) {
            if (this.config.questionGroupId) {
                safeSelectFieldStatus(
                    this.store,
                    this.destroy$,
                    this.config.questionGroupId,
                    FieldStatusType.NOT_READY
                ).subscribe((fieldStatuses: FieldStatus[]) => {
                    this.fieldStatusControlledVisibilty = !fieldStatuses || fieldStatuses.length === 0;
                    this.cd.markForCheck();
                });
            } else {
                safeSelectFieldStatus(this.store, this.destroy$, this.config.key, FieldStatusType.NOT_READY).subscribe(
                    (fieldStatuses: FieldStatus[]) => {
                        this.enabled = !fieldStatuses || fieldStatuses.length === 0;
                        this.cd.markForCheck();
                    }
                );
            }
        }
        this.checkboxOptions = this.fieldService.getListValues(this.config);
        if (this.config.radioOption) {
            const radioOptions = this.config.radioOption.split(',');
            this.radioOptions = remove(this.checkboxOptions, (option) => {
                return radioOptions.includes(option.value);
            });
        }
        if (this.hasExtraTextField()) {
            this.textOption = remove(this.checkboxOptions, (option) => {
                return option.value === this.config.optionsWithExtraField[0].key;
            })[0];
        }
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, [], true)));
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.radioFieldConfig, null, true)));
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.textFieldConfig, null, true)));
        this.store.dispatch(
            new AddAnswer(this.fieldService.getAnswer(this.selectedRadioOptionFieldConfig, null, true))
        );

        // Clear missing value errors on any change
        combineLatest([
            safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType),
            safeSelectField(this.store, this.destroy$, this.radioFieldConfig.key, this.radioFieldConfig.formType),
            safeSelectField(this.store, this.destroy$, this.textFieldConfig.key, this.textFieldConfig.formType),
            safeSelectField(
                this.store,
                this.destroy$,
                this.selectedRadioOptionFieldConfig.key,
                this.selectedRadioOptionFieldConfig.formType
            ),
        ])
            .pipe(takeUntil(this.destroy$))
            .subscribe(([selectedOptions, radioValue, textInput, selectedRadioOption]) => {
                this.selectedOptions = selectedOptions ? selectedOptions : [];
                this.radioSelected = radioValue;
                this.textInput = textInput;
                this.selectedRadioOption = selectedRadioOption;
                this.clearValueMissingHardErrorStatusForGivenKey(this.config.key);
                this.clearValueMissingHardErrorStatusForGivenKey(this.textFieldConfig.key);
            });
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public toggleOption(option: ModelValue) {
        this.setRadioSelected(null);

        const selectedOptions = [...this.selectedOptions];
        if (option === this.textOption && selectedOptions && selectedOptions.includes(option.value)) {
            this.handleInputChange('');
        }
        ArrayUtils.toggle(selectedOptions, option.value);

        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, selectedOptions, false)));
    }

    public setRadioSelected(value: string | null) {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.radioFieldConfig, value != null, false)));
        this.store.dispatch(
            new AddAnswer(this.fieldService.getAnswer(this.selectedRadioOptionFieldConfig, value, false))
        );
        if (value) {
            this.resetSelectedOptions();
        }
    }

    public handleInputChange(input: string) {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.textFieldConfig, input, false)));
        if (this.selectedOptions && !this.selectedOptions.includes(this.textOption.value) && !!input) {
            this.toggleOption(this.textOption);
        }
    }

    private resetSelectedOptions() {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, [], false)));
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.textFieldConfig, '', false)));
    }

    private initFieldConfigs() {
        this.radioFieldConfig = {
            ...this.config,
            key: this.config.key + CheckboxListWithExtraFieldsComponent.RADIO_FIELD_POSTFIX,
        };
        this.textFieldConfig = {
            ...this.config,
            key: this.config.key + CheckboxListWithExtraFieldsComponent.TEXT_FIELD_POSTFIX,
        };
        this.selectedRadioOptionFieldConfig = {
            ...this.config,
            key: this.config.key + CheckboxListWithExtraFieldsComponent.SELECTED_RADIO_OPTION_FIELD_POSTFIX,
        };
    }

    private initTranslationKeys() {
        const baseLabelKey = this.config.labelKey.replace('.label', '.');

        this.titleTranslationKey = this.config.labelKey;
        this.descriptionTranslationKey =
            baseLabelKey + CheckboxListWithExtraFieldsComponent.DESCRIPTION_POSTFIX + '.label';
    }

    public checkMissingValueError(focused: boolean) {
        if (!focused) {
            if (!this.radioSelected) {
                if (!this.selectedOptions || this.selectedOptions.length === 0) {
                    this.addValueMissingHardErrorStatusForGivenKey(this.config.key);
                }
                if (
                    this.textOption &&
                    this.selectedOptions &&
                    this.selectedOptions.includes(this.textOption.value) &&
                    !this.textInput
                ) {
                    this.addValueMissingHardErrorStatusForGivenKey(this.textFieldConfig.key);
                }
            }
        }
    }

    public componentVisible(): boolean {
        return this.visible && this.fieldStatusControlledVisibilty;
    }

    private hasExtraTextField(): boolean {
        return (
            Array.isArray(this.config.optionsWithExtraField) &&
            !!this.config.optionsWithExtraField.find((option) => {
                return option.type === 'TEXT';
            })
        );
    }

    set visibility(visibility: boolean) {
        this.visible = visibility;

        this.store.dispatch(
            new UpdateComponentState({
                id: this.config.key,
                visible: visibility,
            })
        );
        if (!this.visible && this.config.key) {
            this.clearAllAnswersAndStatuses();
        }
        this.cd.markForCheck();
    }

    private clearAllAnswersAndStatuses() {
        this.clearAnswerForFieldKey(this.config.key);
        this.clearStatusForGivenKey(this.config.key);
        if (this.radioFieldConfig) {
            this.clearAnswerForFieldKey(this.radioFieldConfig.key);
            this.clearStatusForGivenKey(this.radioFieldConfig.key);
            this.clearAnswerForFieldKey(this.selectedRadioOptionFieldConfig.key);
        }
        if (this.textFieldConfig) {
            this.clearAnswerForFieldKey(this.textFieldConfig.key);
            this.clearStatusForGivenKey(this.textFieldConfig.key);
        }
    }

    private clearAnswerForFieldKey(fieldKey: string) {
        this.store.dispatch(
            new AddAnswer({
                type: this.config.formType,
                field: fieldKey,
                answer: null,
                required: false,
                initialValue: false,
            })
        );
    }
}
