<div *ngIf="componentVisible()" class="form-element-container mb-l">
  <nh-form-question [descriptionTranslationKey]="descriptionTranslationKey" [titleTranslationKey]="titleTranslationKey"></nh-form-question>
  <nh-field-status-indicator [config]="config"></nh-field-status-indicator>

  <div *ngIf="enabled">
    <div *ngFor="let option of checkboxOptions" class="mt-s">
      <ds-checkbox
        (checkboxChange)="toggleOption(option)"
        [checked]="selectedOptions && selectedOptions.includes(option.value)"
        (focusChange)="checkMissingValueError($event)"
      >
        {{ option.translationKey | i18n }}
      </ds-checkbox>
    </div>

    <div *ngIf="textOption" class="mt-s">
      <ds-checkbox
        (checkboxChange)="toggleOption(textOption)"
        [checked]="selectedOptions && selectedOptions.includes(textOption.value)"
        (focusChange)="checkMissingValueError($event)"
      >
        {{ textOption.translationKey | i18n }}
      </ds-checkbox>
      <input type="text" (ngModelChange)="handleInputChange($event)" [ngModel]="textInput" (blur)="checkMissingValueError(false)" />

      <nh-field-status-indicator [config]="textFieldConfig"></nh-field-status-indicator>
    </div>

    <div *ngFor="let radioOption of radioOptions" class="radio mt-s">
      <input
        [id]="radioOption.id"
        [checked]="radioOption.value === selectedRadioOption"
        class="form-control"
        type="radio"
        (change)="setRadioSelected(radioOption.value)"
      />
      <label [for]="radioOption.id">{{ radioOption.translationKey | i18n }}</label>
    </div>
  </div>
</div>
