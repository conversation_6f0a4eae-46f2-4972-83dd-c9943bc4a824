import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { CheckboxListWithExtraFieldsComponent } from './checkbox-list-with-extra-fields.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('CheckboxListWithExtraFieldsComponent', () => {
    let component: CheckboxListWithExtraFieldsComponent;
    let fixture: ComponentFixture<CheckboxListWithExtraFieldsComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [CheckboxListWithExtraFieldsComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(CheckboxListWithExtraFieldsComponent);
        component = fixture.componentInstance;
        component.config = { key: 'phq', labelKey: 'test.label', formType: QuestionaryType.BOUNCE_HADS };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
