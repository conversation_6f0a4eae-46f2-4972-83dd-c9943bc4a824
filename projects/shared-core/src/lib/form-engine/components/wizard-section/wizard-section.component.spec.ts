import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { WizardSectionComponent } from './wizard-section.component';
import { QuestionaryType } from '../../../generated/models/questionary-type';
import { MocksModule } from '@shared-core/testing';

describe('WizardSectionComponent', () => {
    let component: WizardSectionComponent;
    let fixture: ComponentFixture<WizardSectionComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [WizardSectionComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(WizardSectionComponent);
        component = fixture.componentInstance;
        component.config = {
            key: 'test',
            labelKey: 'test.label',
            formType: QuestionaryType.BOUNCE_HADS,
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
