<div class="wizard-section fe-wizard-section" *ngIf="visible && currentSection >= config.sectionNumber">
  <div class="section-header">
    <div class="form-element-container">
      <h1
        *ngIf="hasSectionHeader"
        class="section-title word-break"
        [innerHTML]="config.headerKey | i18n : i18nPlaceholders | safeHtml"
      ></h1>
      <p *ngIf="hasSectionDescription" class="section-description" [innerHTML]="sectionDescription | safeHtml"></p>
    </div>
  </div>
  <div class="section-content">
    <div class="arrow-down"></div>
    <ng-container
      class="no-padding"
      *ngFor="let field of config.children"
      nhDynamicFormField
      [fieldHideCondition]="fieldHideCondition"
      [config]="field"
    ></ng-container>
    <div class="wizard-button" *ngIf="showButtons">
      <div class="errors" *ngIf="error">
        <span class="error">{{ error | i18n }}</span>
      </div>
      <p *ngIf="config.footerKey" class="footer-text center" [innerHTML]="config.footerKey | i18n | safeHtml"></p>
      <div class="step-buttons center">
        <ds-button variation="secondary" *ngIf="previousEnabled('cancel')" class="button-cancel" (buttonClick)="canceled.emit()">
          {{ getSecondaryKeyTranslationKey() | i18n }}
        </ds-button>
        <ds-button variation="secondary" class="button-cancel" *ngIf="previousEnabled('previous')" (buttonClick)="previousSection()">
          {{ 'general.previous' | i18n }}
        </ds-button>
        <ds-button class="button-next" (buttonClick)="nextSection()">
          {{ 'general.next' | i18n }}
        </ds-button>
      </div>
    </div>
  </div>
</div>
