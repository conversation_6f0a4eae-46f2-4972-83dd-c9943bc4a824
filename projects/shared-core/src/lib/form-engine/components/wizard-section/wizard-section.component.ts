import { ChangeDetector<PERSON><PERSON>, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ElementRef } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FormItemType } from '../../../generated/models/form-item-type';
import { Application } from '../../../models/application';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { FieldService } from '../../services/field.service';
import { FormEngineState } from '../../store/reducers/state';
import { selectForm } from '../../store/selectors/form.selectors';
import { FormInputField } from '../form-input-field.component';
import { FormVariablesService } from '../../services/form-variables.service';
import { <PERSON><PERSON><PERSON>ehavior } from '../../models/scroll-behavior.enum';
import { Dictionary } from '../../../common-types';
import get from 'lodash/get';

const QUESTIONNAIRE_KEY = 'questionnaire';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-wizard-section',
  templateUrl: './wizard-section.component.html',
  styleUrls: ['./wizard-section.component.scss']
})
export class WizardSectionComponent extends FormInputField implements OnInit, OnDestroy, FormField {
  @Input()
  public config: FormFieldConfig;

  @Input()
  public fieldHideCondition: boolean;

  @Output()
  public next = new EventEmitter<void>();

  @Output()
  public previous = new EventEmitter<void>();

  @Input()
  public showButtons = true;

  @Input()
  secondaryButtonTranslationKey: string;

  @Output()
  public canceled = new EventEmitter<void>();

  public error: string;
  public currentSection: number;
  public sectionDescription = '';
  public hasSectionDescription: boolean;
  public sectionDescriptionRequiredMark = '';
  public hasSectionHeader: boolean;
  public i18nPlaceholders: Dictionary<string, string>;

  private destroy$ = new Subject<boolean>();
  private valid = true;
  private initialized = false;
  private scrolledIntoView = false;

  constructor(
    private fieldService: FieldService,
    protected store: Store<FormEngineState>,
    cd: ChangeDetectorRef,
    private elRef: ElementRef,
    private i18n: I18NPipe,
    protected fvs: FormVariablesService
  ) {
    super(cd);
  }

  ngOnInit() {
    if (
      this.config.type === FormItemType.WIZARD_SECTION &&
      this.config.site === Application.CLINIC &&
      this.config.key !== QUESTIONNAIRE_KEY
    ) {
      this.config.descriptionKey = 'case.symptomDescription.nurse.askFromPatient';
    }

    this.fieldService
      .listenForValidate()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.error = this.valid ? undefined : 'wizard.errors.submitFailed';
      });

    this.store
      .select(selectForm(this.config.formType))
      .pipe(takeUntil(this.destroy$))
      .subscribe(form => {
        if (form) {
          this.valid = form.formValid;
          if (this.valid) {
            this.error = undefined;
          } else {
            if (this.initialized && this.invalidInThisSection(form)) {
              this.error = 'wizard.errors.submitFailed';
            } else {
              this.error = undefined;
            }
          }

          this.currentSection = form.currentSection;

          if (get(this.config, 'previous')) {
            if (this.currentSection === this.config.sectionNumber && this.visible) {
              setTimeout(() => {
                this.scrollIntoView();
              }, 10);
            }
          } else {
            if (this.currentSection >= this.config.sectionNumber && this.visible && !this.scrolledIntoView) {
              setTimeout(() => {
                this.scrollIntoView();
              }, 10);
            }
          }

          if (this.error === undefined && this.initialized) {
            this.fieldService.clearFormFieldStyle();
            this.cd.detectChanges();
          }

          this.cd.markForCheck();
        }
      });

    this.sectionDescription = this.getSectionDescription();
    this.sectionDescriptionRequiredMark = this.i18n.transform('wizard.steps.required.field');
    this.hasSectionDescription = this.isKeyTranslated(this.config.descriptionKey);
    this.hasSectionHeader = this.i18n.transform(this.config.headerKey) !== 'NULL';

    // Change detection did not work with the FormInputFieldWithVar support.
    this.fvs.getTranslations(this.store, this.destroy$).subscribe(t => {
      this.i18nPlaceholders = t;
      this.sectionDescription = this.getSectionDescription(t);
      this.cd.detectChanges();
    });

    this.fieldService
      .getNextFormSectionAsObservable()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.fieldService.updateFormFieldStyle();
        this.nextSection();
        this.cd.markForCheck();
      });
  }

  private scrollIntoView() {
    this.scrolledIntoView = true;

    if (this.elRef.nativeElement.querySelector('.section-header')) {
      this.elRef.nativeElement.querySelector('.section-header').scrollIntoView({
        behavior: ScrollBehavior.SMOOTH,
        block: 'start'
      });
    } else {
      this.elRef.nativeElement.scrollIntoView({
        behavior: ScrollBehavior.SMOOTH,
        block: 'start'
      });
    }
  }

  previousEnabled(type) {
    if (get(this.config, 'previous') && this.config.sectionNumber > 1) {
      return type === 'previous';
    } else {
      return type === 'cancel';
    }
  }

  private invalidInThisSection(form) {
    const fieldStatus = form.validFields;
    // eslint-disable-next-line guard-for-in
    for (const key in fieldStatus) {
      const valid = fieldStatus[key];
      if (!valid && this.hasFieldConfiguration(key)) {
        this.fieldService.updateFormFieldStyle();
        this.cd.detectChanges();
        return true;
      }
    }
    return false;
  }

  ngOnDestroy() {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  private hasFieldConfiguration(key) {
    return this.config.children.some(config => {
      return config.key === key;
    });
  }

  private getSectionDescription(i18nPlaceholders = {}): string {
    return this.i18n.transform(this.config.descriptionKey, i18nPlaceholders);
  }

  public nextSection() {
    this.initialized = true;
    this.next.emit();
  }

  public previousSection() {
    if (this.elRef.nativeElement.parentElement.children) {
      this.elRef.nativeElement.parentElement.children[this.currentSection - 2].querySelector('.section-header').scrollIntoView({
        behavior: ScrollBehavior.SMOOTH,
        block: 'center'
      });
    }
    if (this.currentSection - 2 > 0) {
      setTimeout(() => {
        this.previous.emit();
      }, 400);
    } else {
      setTimeout(() => {
        this.previous.emit();
      }, 10);
    }
  }

  public isKeyTranslated(key: string) {
    return key ? this.i18n.transform(key).indexOf(key) < 0 : false;
  }

  public getSecondaryKeyTranslationKey(): string {
    return this.secondaryButtonTranslationKey ? this.secondaryButtonTranslationKey : 'general.cancel';
  }
}
