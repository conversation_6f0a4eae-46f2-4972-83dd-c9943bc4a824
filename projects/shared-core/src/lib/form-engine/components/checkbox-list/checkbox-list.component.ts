import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BehaviorSubject, Subject } from 'rxjs';
import { ERROR_POSITION } from '../../../constants';
import { FormItemType } from '../../../generated/models/form-item-type';
import { CapitalFirstLetterPipe } from '../../../pipes/capital-first-letter.pipe';
import { DecodePipe } from '../../../pipes/decode.pipe';
import { ArrayUtils } from '../../../util/array.utils';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { ModelValue } from '../../models/model-value.interface';
import { FieldService } from '../../services/field.service';
import { safeSelectField } from '../../services/subscription-helper';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';
import type { IsFormValidEvent } from '../form-error-indicator/form-error-indicator.interface';

const NONE_OF_THE_ABOVE = 'noneOfTheAbove';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-checkbox-list',
    templateUrl: './checkbox-list.component.html',
    styleUrls: ['./checkbox-list.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CheckboxListComponent extends FormInputField implements OnInit, OnDestroy, FormField {
    @Input()
    public config: FormFieldConfig;

    @Input()
    public visible: boolean;

    public otherConfig: FormFieldConfig;
    public otherFieldKey: string;
    public options: ModelValue[];
    public otherValue: string;
    public containsOther = false;
    public showOther$ = new BehaviorSubject(false);
    public position = ERROR_POSITION;
    public optionSelected = false;
    public otherQuestionAnswered = false;
    hasErrors = false;

    private currentValues: any[];
    private destroy$ = new Subject<boolean>();

    constructor(
        protected store: Store<FormEngineState>,
        private fieldService: FieldService,
        private capital: CapitalFirstLetterPipe,
        cd: ChangeDetectorRef,
        private decode: DecodePipe
    ) {
        super(cd);
    }

    ngOnInit() {
        this.options = this.fieldService.getListValues(this.config);
        this.currentValues = this.options.map((option) => {
            return option.value;
        });
        this.containsOther = this.options.some((option) => {
            return option.value === 'other';
        });

        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));

        if (this.containsOther) {
            this.otherFieldKey = `other${this.capital.transform(this.config.key)}`;
            this.otherConfig = {
                type: FormItemType.TEXTAREA,
                formType: this.config.formType,
                key: this.otherFieldKey,
                maxLength: 500,
                minLength: 0,
                gender: this.config.gender,
                required: this.config.required,
                site: this.config.site,
            };

            this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.otherConfig, null, true)));

            safeSelectField(this.store, this.destroy$, this.otherFieldKey, this.otherConfig.formType).subscribe(
                (value) => {
                    this.otherValue = this.decode.transform(value);
                }
            );
        }

        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((values: any[]) => {
            this.currentValues = values ? values : [];
            this.options.forEach((option) => {
                option.selected = this.currentValues.includes(option.value);
            });

            this.isOtherQuestionSelected(this.currentValues);
            this.cd.markForCheck();
        });
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public isOptionDisabled(option: ModelValue) {
        if (option.value !== NONE_OF_THE_ABOVE) {
            return this.currentValues.length > 0 && this.currentValues.includes(NONE_OF_THE_ABOVE);
        } else {
            return this.currentValues.length > 0 && !this.currentValues.includes(NONE_OF_THE_ABOVE);
        }
    }

    public toggleOption(option: ModelValue) {
        if (this.isOptionDisabled(option)) {
            return;
        }

        const values = [...this.currentValues];
        ArrayUtils.toggle(values, option.value);

        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, values, false)));

        this.showOther$.next(values.includes('other'));
        this.isOtherQuestionSelected(values);

        if (!values.includes('other') && this.containsOther) {
            this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.otherConfig, null, false)));
        }
    }

    public onOtherChange(value: string) {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.otherConfig, value, false)));
        this.isOtherQuestionAnswered(value);
    }

    private isOtherQuestionSelected(vaules: any[]) {
        // If other field selected, then check if the other question text field is answered
        if (vaules.includes('other') && this.otherValue === '') {
            this.optionSelected = false;
        } else {
            this.optionSelected = vaules.length >= 1;
        }
    }

    private isOtherQuestionAnswered(value: string) {
        if (this.otherValue === '') {
            this.otherQuestionAnswered = false;
        } else {
            this.otherQuestionAnswered = value?.length > 0;
        }
    }

    handleFormErrors($event: IsFormValidEvent): void {
        this.hasErrors = !$event.valid && $event.isDirty;
        this.cd.markForCheck();
    }
}
