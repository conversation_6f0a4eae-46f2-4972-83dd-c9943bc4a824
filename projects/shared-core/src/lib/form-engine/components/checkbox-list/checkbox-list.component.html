<div class="form-element-container" [nsFormElementContainer]="hasErrors" *ngIf="visible">
  <nh-form-error-indicator
    *ngIf="!optionSelected"
    [config]="config"
    [position]="position.BOTTOM"
    (isFormValid)="handleFormErrors($event)"
  ></nh-form-error-indicator>
  <nh-checkbox-list-header [config]="config"></nh-checkbox-list-header>
  <p class="form-element-container-hint" *ngIf="config.hint" [innerHTML]="config.hint | i18n | safeHtml"></p>
  <div class="nh-checkbox-container input-wrapper">
    <div class="checkbox" *ngFor="let option of options" [ngClass]="{ disabled: config.disabled || isOptionDisabled(option) }">
      <input
        type="checkbox"
        [id]="option.id"
        [name]="option.id"
        [disabled]="config.disabled || isOptionDisabled(option)"
        [checked]="option.selected"
        (change)="toggleOption(option)"
      />
      <label [for]="option.id" [innerHTML]="option.translationKey | i18n | safeHtml"></label>
    </div>
    <div *ngIf="showOther$ | async">
      <h4 id="free-text-area">{{ 'general.otherInputLabel' | i18n }}</h4>
      <nh-form-error-indicator
        *ngIf="!otherQuestionAnswered"
        [config]="otherConfig"
        (isFormValid)="handleFormErrors($event)"
      ></nh-form-error-indicator>
      <textarea
        class="form-control"
        [id]="otherFieldKey"
        [name]="otherFieldKey"
        rows="4"
        [ngModel]="otherValue"
        [disabled]="config.disabled"
        aria-labelledby="free-text-area"
        (ngModelChange)="onOtherChange($event)"
      ></textarea>
    </div>
  </div>
</div>
