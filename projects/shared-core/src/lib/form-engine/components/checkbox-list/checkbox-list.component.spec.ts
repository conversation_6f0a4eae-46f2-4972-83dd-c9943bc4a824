import {ComponentFixture, TestBed, waitForAsync} from '@angular/core/testing';
import {NO_ERRORS_SCHEMA} from '@angular/core';
import {CheckboxListComponent} from './checkbox-list.component';
import {MocksModule} from '@shared-core/testing';
import {FieldService} from '../../services/field.service';
import {capitalize} from "lodash";
import {FormItemType} from "../../../generated/models/form-item-type";
import {MockStore, provideMockStore} from "@ngrx/store/testing";
import {AddAnswer} from '../../store/actions/form.actions';
import {ModelValue} from "../../models/model-value.interface";
import {ArrayUtils} from '../../../util/array.utils';

const mockOptionsOther = [
    {
        id: "testId",
        translationKey: "translationKey",
        selected: false,
        value: "other",
        isAdhoc: false
    }
];

const mockOptionsNone: ModelValue = {
    id: "testId",
    translationKey: "translationKey",
    selected: false,
    value: "noneOfTheAbove",
    isAdhoc: false
};

const mockGetAnswer = {
    type: "configType",
    field: "configKey",
    answer: "answer",
    required: true,
    initialValue: true
}

const subHelper = require('../../services/subscription-helper');

describe('CheckboxListComponent', () => {
    let component: CheckboxListComponent;
    let fixture: ComponentFixture<CheckboxListComponent>;
    let fieldService: FieldService;
    let store: MockStore;
    let configKey = {key: 'test'};

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            schemas: [NO_ERRORS_SCHEMA],
            imports: [MocksModule],
            providers: [provideMockStore()],
            declarations: [CheckboxListComponent],
        }).compileComponents();
        fixture = TestBed.createComponent(CheckboxListComponent);
        component = fixture.componentInstance;
        component.config = configKey;
        fieldService = TestBed.inject(FieldService);
        store = TestBed.inject(MockStore);
        fixture.detectChanges();
    }));

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    describe('ngOnInit', () => {
        beforeEach(waitForAsync(() => {
            const fieldServiceGetAnswerSpy = jest.spyOn(fieldService, 'getAnswer');
            fieldServiceGetAnswerSpy.mockReturnValue(mockGetAnswer);
        }));

        it('sets options', () => {
            const fieldServiceGetListValuesSpy = jest.spyOn(fieldService, 'getListValues');
            fieldServiceGetListValuesSpy.mockReturnValueOnce(mockOptionsOther);
            component.ngOnInit();
            expect(component.options).toBeTruthy();
            expect(component.options[0].id).toBe('testId');
        });

        it('sets containsOther to true and sets attributes and invokes methods', () => {
            const fieldServiceGetListValuesSpy = jest.spyOn(fieldService, 'getListValues');
            fieldServiceGetListValuesSpy.mockReturnValueOnce(mockOptionsOther);
            const dispatchSpy = jest.spyOn(store, 'dispatch');
            component.ngOnInit();
            expect(component.containsOther).toBe(true);
            expect(dispatchSpy).toHaveBeenCalledWith(new AddAnswer(mockGetAnswer));
            testOtherContainsSetsAttributesAndInvokesMethods();
        });

        it('sets containsOther to false', () => {
            component.ngOnInit();
            expect(component.containsOther).toBe(false);
        });

        it('invokes safeSelectField twice if containsOther', () => {
            const safeSelectFieldSpy = jest.spyOn(subHelper, 'safeSelectField');
            safeSelectFieldSpy.getMockImplementation()
            const fieldServiceGetListValuesSpy = jest.spyOn(fieldService, 'getListValues');
            fieldServiceGetListValuesSpy.mockReturnValueOnce(mockOptionsOther);
            component.ngOnInit();
            expect(safeSelectFieldSpy).toHaveBeenCalledTimes(2);
            expect(safeSelectFieldSpy).toHaveBeenNthCalledWith(1, component['store'], component['destroy$'], component.otherFieldKey, component.otherConfig.formType);
            expect(safeSelectFieldSpy).toHaveBeenNthCalledWith(2, component['store'], component['destroy$'], component.config.key, component.config.formType);
        });
    });

    describe('ngOnDestroy', () => {
        it('should call next and complete', () => {
            component['destroy$'].next = jest.fn();
            component['destroy$'].complete = jest.fn();
            component.ngOnDestroy();

            expect(component['destroy$'].next).toHaveBeenCalledTimes(1);
            expect(component['destroy$'].complete).toHaveBeenCalledTimes(1);
        });
    });

    describe('isOptionDisabled', () => {
        it('returns true if option is not noneOfTheAbove and currentValues does include it', () => {
            component["currentValues"] = ["noneOfTheAbove"];
            expect(component.isOptionDisabled(mockOptionsOther[0])).toBe(true);
        });

        it('returns false if option is not noneOfTheAbove and currentValues does not include it', () => {
            component["currentValues"] = ["value1"];
            expect(component.isOptionDisabled(mockOptionsOther[0])).toBe(false);
        });

        it('returns false if option is not noneOfTheAbove and currentValues.length is 0', () => {
            component["currentValues"] = [];
            expect(component.isOptionDisabled(mockOptionsOther[0])).toBe(false);
        });

        it('returns true if option is noneOfTheAbove and currentValues does not include it', () => {
            component["currentValues"] = ["value1"];
            expect(component.isOptionDisabled(mockOptionsNone)).toBe(true);
        });

        it('returns false if option is noneOfTheAbove and currentValues does include it', () => {
            component["currentValues"] = ["noneOfTheAbove"];
            expect(component.isOptionDisabled(mockOptionsNone)).toBe(false);
        });

        it('returns false if option is noneOfTheAbove and currentValues.length is 0', () => {
            component["currentValues"] = [];
            expect(component.isOptionDisabled(mockOptionsNone)).toBe(false);
        });
    });

    describe('toggleOption', () => {
        it('exits if option is disabled', () => {
            const dispatchSpy = jest.spyOn(store, 'dispatch');
            component["currentValues"] = ["value1"];
            component.toggleOption(mockOptionsNone);
            expect(dispatchSpy).toHaveBeenCalledTimes(0);
        });

        it('toggles and dispatches new addAnswer if option is enabled', () => {
            const toggleSpy = jest.spyOn(ArrayUtils, 'toggle');
            const dispatchSpy = jest.spyOn(store, 'dispatch');
            const fieldServiceGetAnswerSpy = jest.spyOn(fieldService, 'getAnswer');
            fieldServiceGetAnswerSpy.mockReturnValueOnce(mockGetAnswer);
            component["currentValues"] = ['noneOfTheAbove'];
            component.toggleOption(mockOptionsNone);
            expect(toggleSpy).toHaveBeenCalledTimes(1);
            expect(dispatchSpy).toHaveBeenCalledWith(new AddAnswer(mockGetAnswer));
        });

        it('$showOther.next is invoked', () => {
            component["currentValues"] = ['noneOfTheAbove'];
            component['showOther$'].next = jest.fn();
            component.toggleOption(mockOptionsNone);
            expect(component['showOther$'].next).toHaveBeenCalledTimes(1);
        });

        it('isOtherQuestionSelected is invoked and sets optionSelected', () => {
            component["currentValues"] = ['noneOfTheAbove', 'other'];
            component.toggleOption(mockOptionsNone);
            expect(component.optionSelected).toBe(true);
            component.otherValue = '';
            component.toggleOption(mockOptionsNone);
            expect(component.optionSelected).toBe(false);
        });

        it('empties other text field when values dont include other', () => {
            const dispatchSpy = jest.spyOn(store, 'dispatch');
            const fieldServiceGetAnswerSpy = jest.spyOn(fieldService, 'getAnswer');
            component["currentValues"] = ['noneOfTheAbove'];
            component.containsOther = true;
            component.toggleOption(mockOptionsNone);
            expect(fieldServiceGetAnswerSpy).toHaveBeenCalledWith(component.otherConfig, null, false);
            expect(dispatchSpy).toHaveBeenCalledTimes(2);
        });

        it('dispatch is not invoked when values include other', () => {
            const dispatchSpy = jest.spyOn(store, 'dispatch');
            component["currentValues"] = ['noneOfTheAbove', 'other'];
            component.toggleOption(mockOptionsNone);
            expect(dispatchSpy).toHaveBeenCalledTimes(1);
        });
    });

    describe('onOtherChange', () => {
        it('invokes dispatch and fieldService.getAnswer', () => {
            const dispatchSpy = jest.spyOn(store, 'dispatch');
            const fieldServiceGetAnswerSpy = jest.spyOn(fieldService, 'getAnswer');
            component.onOtherChange("value");
            expect(fieldServiceGetAnswerSpy).toHaveBeenCalledWith(component.otherConfig, "value", false);
            expect(dispatchSpy).toHaveBeenCalledTimes(1);
        });

        it('sets otherQuestionAnswered', () => {
            component.onOtherChange("value");
            expect(component.otherQuestionAnswered).toBe(true);
            component.onOtherChange("");
            expect(component.otherQuestionAnswered).toBe(false);
            expect(component.otherValue).toBe(undefined);
            component.otherValue = '';
            expect(component.otherQuestionAnswered).toBe(false);
        });
    });

    function testOtherContainsSetsAttributesAndInvokesMethods() {
        // field key and configs are set when containsOther is true and sets otherValue
        expect(component.otherFieldKey).toBe(`other${capitalize(configKey.key)}`);
        expect(component.otherConfig.type).toBe(FormItemType.TEXTAREA);
        expect(component.otherConfig.formType).toBe(component.config.formType);
        expect(component.otherConfig.key).toBe(component.otherFieldKey);
        expect(component.otherConfig.maxLength).toBe(500);
        expect(component.otherConfig.minLength).toBe(0);
        expect(component.otherConfig.gender).toBe(component.config.gender);
        expect(component.otherConfig.required).toBe(component.config.required);
        expect(component.otherConfig.site).toBe(component.config.site);
    }
});

