@import '../../../../styles/deprecated_mixins.scss';
@import '../../../../styles/deprecated_variables.scss';
.nh-dropdown {
  width: 100%;
  position: relative;
  .nh-dropdown-input {
    display: none;
  }
  .button-select {
    width: 100%;
    border: 1px solid #e6e6e6;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.09);
    height: 60px;
    display: flex;
    justify-content: space-between;
    &:hover {
      background-color: $color-white-background-hint-hover;
      .caret {
        opacity: 0.7;
      }
    }
    &:hover {
      background-color: $color-white-background-hint-active;
    }
    &:disabled {
      background-color: $btn-link-disabled-color;
    }
    .button-text {
      padding: 20px 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 95%;
      @include text-normal-list(left);
      font-size: 16px;
    }
    .caret {
      margin-top: 6px;
      border-top: 6px dashed;
      border-right: 6px solid transparent;
      border-left: 6px solid transparent;
      opacity: 0.5;
      transition: transform 0.25s;
      position: relative;
      top: 18px;
      right: 10px;
      transform: rotate(-90deg);
    }
    &.open {
      .caret {
        transform: rotate(0deg);
      }
    }
    &:focus {
      outline: 0;
    }
  }
  .dropdown-menu {
    padding: 0;
    position: absolute;
    width: 100%;
    margin-top: -1px;
    z-index: 10;
    display: none;
    &.open {
      display: block;
      .dropdown-menu-item {
        height: 42px;
        padding: 12px 17px;
        @include text-normal-list(left);
        font-size: 16px;
        cursor: pointer;
        &:hover {
          background-color: rgba(37, 164, 204, 0.08);
        }
      }
    }
  }
}
