import { ChangeDetectorRef, Component, ElementRef, Input, ViewChild, OnDestroy, ViewRef } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { DropDownEntry } from '../../models/dropdown-entry.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-dropdown',
    styleUrls: ['./nh-dropdown.scss'],
    templateUrl: './nh-dropdown.html',
})
export class NhDropdownComponent implements OnDestroy {
    @Input() translationString: string;
    @Input() disabled: boolean;
    @Input() placeholder: string;
    @Input() model: any;
    @Input() items: DropDownEntry[];
    @Input() control: UntypedFormControl;
    @Input() onSelect: (item: any) => void;
    @Input() displayKey: string;
    @Input() identifier: string;

    @ViewChild('nhDropdownSelector', { static: true }) el: ElementRef;

    public isDropdownOpen = false;

    @Input() domIdentifier: string;

    constructor(private chRef: ChangeDetectorRef, private i18n: I18NPipe) {}

    ngOnDestroy() {
        this.chRef.detach();
    }

    toggleClick() {
        if (!this.isDropdownOpen) {
            this.bindDocumentClickHandlers();
        } else {
            this.unbindDocumentClickHandlers();
        }
        this.isDropdownOpen = !this.isDropdownOpen;
    }

    bindDocumentClickHandlers() {
        $(document).on('click', (event) => {
            return this.documentClickHandler(event);
        });
        $(document).on('touchend', (event) => {
            return this.documentClickHandler(event);
        });
    }

    unbindDocumentClickHandlers() {
        $(document).off('click', (event) => {
            return this.documentClickHandler(event);
        });
        $(document).off('touchend', (event) => {
            return this.documentClickHandler(event);
        });
    }

    documentClickHandler(event: any) {
        const isClickedElementChildOfDropdown = this.el.nativeElement.contains(event.target);
        if (isClickedElementChildOfDropdown) {
            return;
        } else {
            this.isDropdownOpen = false;
            this.unbindDocumentClickHandlers();
            if (!(this.chRef as ViewRef).destroyed) {
                this.chRef.detectChanges();
            }
        }
    }

    onSelectItem(item: any) {
        if (this.control) {
            if (!this.onSelect) {
                // in case no onSelect handler, patching in the value thru FormController
                this.control.patchValue(item[this.identifier ? this.identifier : 'id']);
            }
            this.control.markAsDirty();
        }
        if (this.onSelect) {
            this.onSelect(item);
        }
        this.toggleClick();
    }

    getDisplayLabel() {
        if (this.identifier) {
            const item = this.items.find((itm) => {
                return itm[this.identifier] === this.model;
            });
            if (item) {
                return this.displayKey ? item[this.displayKey] : item[this.identifier];
            }
            return '';
        }
        if (this.translationString) {
            return this.i18n.transform(this.translationString + this.model);
        }
    }

    getOptionsLabel(item: DropDownEntry) {
        if (this.translationString) {
            return this.i18n.transform(this.translationString + item.key);
        }

        if (this.displayKey) {
            return item[this.displayKey];
        }

        return item;
    }
}
