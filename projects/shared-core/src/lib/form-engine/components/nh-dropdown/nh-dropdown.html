<div class="nh-dropdown" #nhDropdownSelector>
    <div class="nh-dropdown-input" *ngIf="control">
        <input type="control" />
    </div>
    <button
        [disabled]="disabled"
        (click)="toggleClick()"
        [attr.id]="domIdentifier"
        [ngClass]="{ open: isDropdownOpen }"
        class="button-select"
    >
        <div class="button-text" *ngIf="!model">{{ placeholder | i18n }}</div>
        <div class="button-text" *ngIf="model">{{ getDisplayLabel() }}</div>
        <b class="caret"></b>
    </button>
    <ul class="dropdown-menu" *ngIf="isDropdownOpen" [ngClass]="{ open: isDropdownOpen }">
        <li
            id="{{'nh-dropdown-list-item-' + item.id }}"
            class="dropdown-menu-item"
            *ngFor="let item of items"
            (click)="onSelectItem(item)"
        >
            {{ getOptionsLabel(item) }}
        </li>
    </ul>
</div>
