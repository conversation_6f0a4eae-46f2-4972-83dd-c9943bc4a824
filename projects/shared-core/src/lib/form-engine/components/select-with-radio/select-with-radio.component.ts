import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import remove from 'lodash/remove';
import { Subject } from 'rxjs';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { ModelValue } from '../../models/model-value.interface';
import { FieldService } from '../../services/field.service';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';
import { safeSelectField, safeSelectFieldStatus } from '../../services/subscription-helper';

interface SelectOption {
    id?: string;
    label: string;
}

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-select-with-radio',
    templateUrl: './select-with-radio.component.html',
    styleUrls: ['./select-with-radio.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectWithRadioComponent extends FormInputField implements OnInit, FormField, OnDestroy {
    public static readonly RADIO_ATTRIBUTE_POSTFIX = 'Radio';

    public static readonly DESCRIPTION_POSTFIX = 'description';
    public static readonly PLACEHOLDER_POSTFIX = 'placeholder';

    @Input() public config: FormFieldConfig;
    @Input() public visible: boolean;

    public titleTranslationKey: string;
    public descriptionTranslationKey: string;
    public placeholderTranslationKey: string;
    public options: SelectOption[];
    public radioOption: ModelValue;
    public radioSelected: boolean;
    public selectedOption: SelectOption;
    public fieldStatusControlledVisibilty = true;
    public enabled = true;

    private radioAttributeConfig: FormFieldConfig;
    private destroy$ = new Subject<boolean>();

    constructor(
        protected store: Store<FormEngineState>,
        protected cd: ChangeDetectorRef,
        private fieldService: FieldService,
        private i18NPipe: I18NPipe
    ) {
        super(cd);
    }

    ngOnInit() {
        this.initAttributeConfigs();
        this.initTranslationKeys();
        if (this.config.enableFieldStatus) {
            if (this.config.questionGroupId) {
                safeSelectFieldStatus(
                    this.store,
                    this.destroy$,
                    this.config.questionGroupId,
                    FieldStatusType.NOT_READY
                ).subscribe((fieldStatuses: FieldStatus[]) => {
                    this.fieldStatusControlledVisibilty = !fieldStatuses || fieldStatuses.length === 0;
                    this.cd.markForCheck();
                });
            } else {
                safeSelectFieldStatus(this.store, this.destroy$, this.config.key, FieldStatusType.NOT_READY).subscribe(
                    (fieldStatuses: FieldStatus[]) => {
                        this.enabled = !fieldStatuses || fieldStatuses.length === 0;
                        this.cd.markForCheck();
                    }
                );
            }
        }
        const allOptions = this.fieldService.getListValues(this.config);
        if (this.config.radioOption) {
            this.radioOption = remove(allOptions, (option) => {
                return option.value === this.config.radioOption;
            })[0];
        }
        this.options = allOptions.map((option) => {
            return {
                id: option.value,
                label: this.i18NPipe.transform(option.translationKey),
            };
        });

        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.radioAttributeConfig, null, true)));

        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe(
            (selectedOption) => {
                return (this.selectedOption = this.options.filter((option) => {
                    return option.id === selectedOption;
                })[0]);
            }
        );

        safeSelectField(
            this.store,
            this.destroy$,
            this.radioAttributeConfig.key,
            this.radioAttributeConfig.formType
        ).subscribe((value) => {
            return (this.radioSelected = value);
        });
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public handleChange(value: SelectOption) {
        this.setRadioSelected(false);
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, value.id, false)));
        this.clearValueMissingHardErrorStatusForGivenKey(this.config.key);
    }

    public setRadioSelected(value: boolean) {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.radioAttributeConfig, value, false)));
        if (value) {
            this.resetSelectedOptions();
            this.clearValueMissingHardErrorStatusForGivenKey(this.config.key);
        }
    }

    private resetSelectedOptions() {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, false)));
    }

    private initAttributeConfigs() {
        this.radioAttributeConfig = {
            ...this.config,
            key: this.config.key + SelectWithRadioComponent.RADIO_ATTRIBUTE_POSTFIX,
        };
    }

    private initTranslationKeys() {
        const baseLabelKey = this.config.labelKey.replace('.label', '.');

        this.titleTranslationKey = this.config.labelKey;
        this.descriptionTranslationKey = baseLabelKey + SelectWithRadioComponent.DESCRIPTION_POSTFIX + '.label';
        this.placeholderTranslationKey = baseLabelKey + SelectWithRadioComponent.PLACEHOLDER_POSTFIX + '.label';
    }

    public checkMissingValueError() {
        if (!this.radioSelected && !this.selectedOption) {
            this.addValueMissingHardErrorStatusForGivenKey(this.config.key);
        }
    }

    public componentVisible(): boolean {
        return this.visible && this.fieldStatusControlledVisibilty;
    }
}
