import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { SelectWithRadioComponent } from './select-with-radio.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('SelectWithRadio', () => {
    let component: SelectWithRadioComponent;
    let fixture: ComponentFixture<SelectWithRadioComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [SelectWithRadioComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(SelectWithRadioComponent);
        component = fixture.componentInstance;
        component.config = {
            key: 'test',
            labelKey: 'test.label',
            formType: QuestionaryType.BOUNCE_HADS,
            enableFieldStatus: true,
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
