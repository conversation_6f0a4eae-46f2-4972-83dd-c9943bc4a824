<div *ngIf="componentVisible()" class="form-element-container mb-l">
  <nh-form-question [descriptionTranslationKey]="descriptionTranslationKey" [titleTranslationKey]="titleTranslationKey"></nh-form-question>
  <nh-field-status-indicator [config]="config"></nh-field-status-indicator>

  <div *ngIf="enabled">
    <div class="mt-l">
      <ng-select
        (change)="handleChange($event)"
        [ngModel]="selectedOption"
        [clearable]="false"
        [items]="options"
        [placeholder]="placeholderTranslationKey | i18n"
        class="ds-dropdown"
        (blur)="checkMissingValueError()"
      ></ng-select>
    </div>

    <div class="radio mt-l">
      <input [checked]="radioSelected" class="form-control" type="radio" (change)="setRadioSelected(true)" />
      <label [for]="radioOption.id">{{ radioOption.translationKey | i18n }}</label>
    </div>
  </div>
</div>
