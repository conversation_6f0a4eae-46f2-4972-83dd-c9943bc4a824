@import '../../../../styles/ds-variables.scss';

$border: thin solid lightgrey;
.datatable {
  --datatable-fixed-row-height: 60px;
}
.datatable--no-rounded-corners-last-row ::ng-deep .datatable__row:last-of-type {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.ds-button.add-more-button {
  background-color: var(--datatable-row-background-color);
  border: var(--datatable-border-width) solid var(--datatable-border-color);
  border-top: none;
  border-radius: 0;
  border-bottom-right-radius: var(--datatable-border-radius);
  border-bottom-left-radius: var(--datatable-border-radius);
  height: 60px;
  width: 100%;
  &.ds-button__active {
    border-color: var(--datatable-border-color);
  }
  &:hover {
    border-color: var(--datatable-border-color);
  }
  ::ng-deep .ds-button__content-wrapper {
    justify-content: flex-start;
  }
}
.hidden-column {
  display: none;
}
.datatable ::ng-deep {
  & .neg-mt-l {
    margin-top: $spacing-xxs;
  }
  & .action-column {
    padding: 0;
  }
  & .datatable__expanded-content-wrapper {
    z-index: 0;
  }
}
.measurement__dropdown ::ng-deep {
  & .actions__dropdown--open {
    margin: 0;
    padding: 0;
  }
  & .option {
    padding: 0.8rem !important;
  }
}
.form-element-container ::ng-deep .ng-select-container {
  min-height: 0;
  height: var(--input-height);
}
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.numeric-field {
  position: relative;
}
.numeric-field__input {
  padding-right: $unit-max-width;
}
.numeric-field__unit {
  max-width: $unit-max-width;
  position: absolute;
  right: var(--spacing-m);
  top: var(--spacing-s);
  user-select: none;
  text-align: center;
}
