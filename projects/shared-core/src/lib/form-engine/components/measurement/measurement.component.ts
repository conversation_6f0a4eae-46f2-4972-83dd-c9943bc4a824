import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { select, Store } from '@ngrx/store';
import throttle from 'lodash/throttle';
import moment from 'moment';
import { combineLatest } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { v4 as uuid } from 'uuid';
import { ConfigurationProviderService } from '../../../abstract-services/configuration-provider.service';
import { Dictionary } from '../../../common-types';
import { DateFormatPipe } from '../../../pipes/date-format.pipe';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { DateRangeConfig, FormFieldConfig } from '../../models/form-field-config.interface';
import { INVALID_DATE } from '../../models/invalid-date';
import { isAfterDate, isBeforeDate } from '../../services/datecheck-helper';
import { FieldService } from '../../services/field.service';
import { FormVariablesService } from '../../services/form-variables.service';
import { safeSelectField, safeSelectFieldStatus, safeSelectVariable } from '../../services/subscription-helper';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputFieldWithVarSupport } from '../form-input-field-variable-support.component';
import { SimpleDateInputValue } from '../simple-date-input/simple-date-input.component';
import { selectFieldStatuses } from '../../store/selectors/field-status.selectors';

export interface MeasurementRow {
    rowId: number;
    date: Date;
    value: any;
    metric: any;
}

interface SelectOption {
    id: string;
    label: string;
}

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-measurement',
    templateUrl: './measurement.component.html',
    styleUrls: ['./measurement.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MeasurementComponent extends FormInputFieldWithVarSupport implements OnInit {
    constructor(
        protected store: Store<FormEngineState>,
        protected cd: ChangeDetectorRef,
        private fieldService: FieldService,
        private i18NPipe: I18NPipe,
        private configService: ConfigurationProviderService,
        protected formVarSrv: FormVariablesService,
        private dfp: DateFormatPipe
    ) {
        super(cd, formVarSrv);
    }

    get addedRows() {
        return this._addedRows;
    }

    set addedRows(rows: MeasurementRow[]) {
        const answersToSave: MeasurementRow[] = rows.map((row) => {
            const value = this.editableMeasurmentValue ? row.value : row.value?.id;
            return {
                ...row,
                [MeasurementComponent.METRIC_KEY]: this.getMetricValueToPublish(row),
                [MeasurementComponent.VALUE_KEY]: value ? value : null,
            };
        });
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, answersToSave, false)));
        this._addedRows = rows;
    }

    get datatableClasses() {
        return this.multiRow ? 'datatable datatable--no-rounded-corners-last-row' : 'datatable';
    }
    public static MEASUREMENT_FIELD_POSTFIX = 'Measurement';
    public static RADIO_FIELD_POSTFIX = 'Radio';

    public static readonly MEASUREMENT_VALUES_POSTFIX = 'Measurement';

    public static readonly DESCRIPTION_POSTFIX = 'description';
    public static readonly METRIC_HEADER_POSTFIX = 'metricHeader';
    public static readonly METRIC_PLACEHOLDER_POSTFIX = 'metricPlaceholder';
    public static readonly DATE_HEADER_POSTFIX = 'dateHeader';
    public static readonly MEASUREMENT_HEADER_POSTFIX = 'measurementHeader';
    public static readonly MEASUREMENT_PLACEHOLDER_POSTFIX = 'measurementPlaceholder';
    public static readonly ADD_MORE_POSTFIX = 'addMore';
    public static readonly RADIO_YES_POSTFIX = 'radioYes';
    public static readonly RADIO_NO_POSTFIX = 'radioNo';
    public static readonly ROW_REMOVE_POSTFIX = 'rowRemove';
    public static readonly METRIC_KEY = 'metric';
    public static readonly DATE_KEY = 'date';
    public static readonly VALUE_KEY = 'value';

    public static readonly DATE_RANGE_PLACEHOLDER_FROM = 'from';
    public static readonly DATE_RANGE_PLACEHOLDER_TO = 'to';
    public static readonly NUMERIC_VALUE_OUT_OF_RANGE_ERROR_TRANSLATION_KEY = 'numericMeausrementField.outOfRangeError';
    public static readonly NUMERIC_VALUE_MIN_VIOLATION_ERROR_TRANSLATION_KEY =
        'numericMeausrementField.minViolationError';
    public static readonly NUMERIC_VALUE_MAX_VIOLATION_ERROR_TRANSLATION_KEY =
        'numericMeausrementField.maxViolationError';
    public static readonly NUMERIC_VALUE_RANGE_PLACEHOLDER_MIN = 'min';
    public static readonly NUMERIC_VALUE_RANGE_PLACEHOLDER_MAX = 'max';
    public static readonly NUMERIC_VALUE_RANGE_PLACEHOLDER_UNIT = 'unit';

    public readonly ROW_ICON_WIDTH = '36px';
    public readonly HIDDEN_COLUMN_WIDTH = '1px';

    @Input() public config: FormFieldConfig;
    @Input() public visible: boolean;

    public measurementOptions: SelectOption[];
    public metricOptions: SelectOption[];
    public radioValue: boolean;
    public maxDate: Date;
    public titleTranslationKey: string;
    public descriptionTranslationKey: string;
    public metricHeaderTranslationKey: string;
    public metricPlaceholderTranslationKey: string;
    public dateHeaderTranslationKey: string;
    public measurementHeaderTranslationKey: string;
    public measurementPlaceholderTranslationKey: string;
    public addMoreTranslationKey: string;
    public radioYesTranslationKey: string;
    public radioNoTranslationKey: string;
    public rowRemoveTranslationKey: string;
    public fieldStatusControlledVisibilty = true;
    public enabled = true;
    public componentUUID: string;
    public dateFormat: string;
    public multiRow: boolean;
    public addRowThrottled: () => void;
    public editableMeasurmentValue = false;

    private radioFieldConfig: FormFieldConfig;
    private measurementFieldConfig: FormFieldConfig;
    private rowsBackUp: MeasurementRow[] = null;
    private rowsBackUpFieldStatuses: FieldStatus[] = null;
    private focusedRowId = -1;
    private blurredRowId = -1;
    private _addedRows: MeasurementRow[] = [];
    private lastSimpleDateValue: Dictionary<string, SimpleDateInputValue> = {};
    private dateRangeFrom: Date;
    private dateRangeTo: Date;
    private dateRangeConfig: DateRangeConfig;

    public ngOnInit() {
        // no secondaryTranslationPrefix means no translation for value options list, hence use editable input field
        this.editableMeasurmentValue = this.config.secondaryTranslationPrefix === null ? true : false;
        this.addRowThrottled = throttle(() => {
            this.focusedRowId = -1;
            const nextRowId = this.getNextRowId();
            this.addedRows = [...this.addedRows, this.createEmptyRow(nextRowId)];
            this.focusOnRow(nextRowId);
        }, 250);

        this.componentUUID = uuid();
        if (this.config.enableFieldStatus) {
            if (this.config.questionGroupId) {
                safeSelectFieldStatus(
                    this.store,
                    this.destroy$,
                    this.config.questionGroupId,
                    FieldStatusType.NOT_READY
                ).subscribe((fieldStatuses: FieldStatus[]) => {
                    this.fieldStatusControlledVisibilty = !fieldStatuses || fieldStatuses.length === 0;
                    this.cd.markForCheck();
                });
            } else {
                safeSelectFieldStatus(this.store, this.destroy$, this.config.key, FieldStatusType.NOT_READY).subscribe(
                    (fieldStatuses: FieldStatus[]) => {
                        this.enabled = !fieldStatuses || fieldStatuses.length === 0;
                        this.cd.markForCheck();
                    }
                );
            }
        }
        if (this.config.dateRangeValidationConfig) {
            this.initDateRangeConfig();
        }
        if (!this.config.allowFutureDate) {
            this.maxDate = new Date();
        }
        this.initFieldConfigs();
        this.initDateFormat();
        this.initSelectOptions();
        this.initTranslationKeys();
        this.multiRow = this.config.multiRow;

        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.radioFieldConfig, null, true)));
        this.store.dispatch(
            new AddAnswer(this.fieldService.getAnswer(this.config, [this.getEmptyRowToPublish()], true))
        );

        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((answers) => {
            this._addedRows = this.prepareSavedAnswersForDisplay(answers);
        });

        safeSelectField(this.store, this.destroy$, this.radioFieldConfig.key, this.radioFieldConfig.formType).subscribe(
            (value) => {
                return (this.radioValue = value);
            }
        );
    }

    public handleDateChange(value: SimpleDateInputValue, row: MeasurementRow, focusOnRow: boolean = true) {
        // We somehow need to store the possible 'unknown date' flag for the 'onBlur'
        this.lastSimpleDateValue[row.rowId] = value;
        this.handleChange(value, row, MeasurementComponent.DATE_KEY, focusOnRow);
    }

    public handleMeasurementChange(value: SelectOption, row: MeasurementRow) {
        this.handleChange(value, row, MeasurementComponent.VALUE_KEY);
    }

    public handleEditableMeasurementChange(value: Event, row: MeasurementRow) {
        this.handleChange(value, row, MeasurementComponent.VALUE_KEY);
    }

    public handleMetricChange(value: SelectOption, row: MeasurementRow) {
        this.handleChange(value, row, MeasurementComponent.METRIC_KEY);
    }

    public removeRow(row: MeasurementRow) {
        this.clearStatusForGivenKey(this.rowKey(row));
        if (this.addedRows.length === 1) {
            this.addedRows = [this.createEmptyRow(0)];
            return;
        }
        this.addedRows = this.addedRows.filter((r) => {
            return r.rowId !== row.rowId;
        });
    }

    public handleRadioChange(value: boolean, isAutoSelection = false) {
        if (!isAutoSelection) {
            if (value) {
                this.focusOnSelectedRadio();
                this.loadBackupRows();
            } else {
                this.updateBackupRows();
                this.clearAllMissingHardErrors();
                this.store.dispatch(
                    new AddAnswer(this.fieldService.getAnswer(this.config, [this.getEmptyRowToPublish()], false))
                );
            }
        }
        this.clearStatusForGivenKey(this.config.key);
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.radioFieldConfig, value, false)));
    }

    private loadBackupRows(): void {
        if (this.rowsBackUp) {
            this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, this.rowsBackUp, false)));
            this.rowsBackUpFieldStatuses.forEach((status) => {
                this.addFieldError(status.fieldKey, status.translationKey, status.statusType, status.meta);
            });
        }
    }

    private clearAllMissingHardErrors(): void {
        this.addedRows.forEach((row) => {
            return this.clearStatusForGivenKey(this.config.key + row.rowId);
        });
    }

    /**
     * Updates the backup rows to current rows
     */
    private updateBackupRows(): void {
        this.store
            .pipe(select(selectFieldStatuses(this.config.key, null, null, true)), take(1))
            .subscribe((statuses) => {
                this.rowsBackUpFieldStatuses = statuses.filter((status) => {
                    return status.fieldKey.length > this.config.key.length;
                });
            });
        this.rowsBackUp = this._addedRows.map((row) => {
            const value = this.editableMeasurmentValue ? row.value : row.value?.id;
            return {
                ...row,
                [MeasurementComponent.METRIC_KEY]: this.getMetricValueToPublish(row),
                [MeasurementComponent.VALUE_KEY]: value ? value : null,
            };
        });
    }

    private getEmptyRowToPublish(): MeasurementRow {
        const defaultRow = { ...this.createEmptyRow(this.getNextRowId()) };
        return { ...defaultRow, metric: this.getMetricValueToPublish(defaultRow) };
    }

    private getMetricValueToPublish(row: MeasurementRow): any {
        return row.metric && row.metric.id ? row.metric.id : row.metric;
    }

    private handleChange(
        valueContainer: any,
        row: MeasurementRow,
        field: 'date' | 'value' | 'metric',
        focusOnRow: boolean = true
    ) {
        const theValue = field === 'date' ? valueContainer.storeValue : valueContainer;
        this.handleRadioChange(true, true);
        this.addedRows = this.addedRows.map((r) => {
            if (r.rowId !== row.rowId) {
                return r;
            }
            return {
                ...r,
                [field]: theValue,
            };
        });

        const rowKey = this.rowKey(row);
        const editedRow: MeasurementRow = this.addedRows.find((r) => {
            return row.rowId === r.rowId;
        });
        const valueCheck: boolean = this.editableMeasurmentValue ? editedRow.value !== null : editedRow.value;

        this.clearValueMissingHardErrorStatusForGivenKey(rowKey);
        let changeChecksFunction: 'checkDateValue' | 'checkNumericMeasurementRange';
        let checkFunctionValueParameter: any;
        switch (field) {
            case 'date': {
                this.clearInvalidValueHardErrorStatusForGivenKey(rowKey);
                this.clearInvalidDateRangeErrors(rowKey);
                changeChecksFunction = 'checkDateValue';
                checkFunctionValueParameter = valueContainer;
                break;
            }
            case 'value': {
                this.clearInvalidNumericValueRangeErrors(rowKey);
                changeChecksFunction = 'checkNumericMeasurementRange';
                checkFunctionValueParameter = editedRow.value;
                break;
            }
        }

        if (
            editedRow.metric &&
            editedRow.date &&
            editedRow.date !== INVALID_DATE &&
            valueCheck &&
            changeChecksFunction
        ) {
            this[changeChecksFunction](checkFunctionValueParameter, rowKey);
        }
        if (focusOnRow) {
            this.focusOnRow(row.rowId);
        }
    }

    private initFieldConfigs() {
        const measurementValuesKey = this.config.valuesKey + MeasurementComponent.MEASUREMENT_VALUES_POSTFIX;

        this.radioFieldConfig = {
            ...this.config,
            key: this.config.key + MeasurementComponent.RADIO_FIELD_POSTFIX,
        };
        this.measurementFieldConfig = {
            ...this.config,
            valuesKey: measurementValuesKey,
            translationPrefix: this.config.secondaryTranslationPrefix,
        };
    }

    private initSelectOptions() {
        this.measurementOptions = this.fieldService.getListValues(this.measurementFieldConfig).map((value) => {
            return { label: this.i18NPipe.transform(value.translationKey), id: value.value };
        });
        this.metricOptions = this.fieldService.getListValues(this.config).map((value) => {
            return { label: this.i18NPipe.transform(value.translationKey), id: value.value };
        });
    }

    private initTranslationKeys() {
        this.titleTranslationKey = this.config.labelKey;
        this.descriptionTranslationKey = this.createTranslationKey(MeasurementComponent.DESCRIPTION_POSTFIX);
        this.metricHeaderTranslationKey = this.createTranslationKey(MeasurementComponent.METRIC_HEADER_POSTFIX);
        this.metricPlaceholderTranslationKey = this.createTranslationKey(
            MeasurementComponent.METRIC_PLACEHOLDER_POSTFIX
        );
        this.dateHeaderTranslationKey = this.createTranslationKey(MeasurementComponent.DATE_HEADER_POSTFIX);
        this.measurementHeaderTranslationKey = this.createTranslationKey(
            MeasurementComponent.MEASUREMENT_HEADER_POSTFIX
        );
        this.measurementPlaceholderTranslationKey = this.createTranslationKey(
            MeasurementComponent.MEASUREMENT_PLACEHOLDER_POSTFIX
        );
        this.addMoreTranslationKey = this.createTranslationKey(MeasurementComponent.ADD_MORE_POSTFIX);
        this.radioNoTranslationKey = this.createTranslationKey(MeasurementComponent.RADIO_NO_POSTFIX);
        this.radioYesTranslationKey = this.createTranslationKey(MeasurementComponent.RADIO_YES_POSTFIX);
        this.rowRemoveTranslationKey = this.createTranslationKey(MeasurementComponent.ROW_REMOVE_POSTFIX);
    }

    private createTranslationKey(key: string) {
        const baseKey = this.config.labelKey.replace('.label', '.');
        return baseKey + key + '.label';
    }

    private getNextRowId(): number {
        if (this.addedRows.length === 0) {
            return 0;
        }

        return (
            Math.max(
                ...this.addedRows.map((r) => {
                    return r.rowId;
                })
            ) + 1
        );
    }

    private createEmptyRow(rowId: number): MeasurementRow {
        return {
            rowId,
            date: null,
            value:
                this.measurementOptions.length > 1 || this.editableMeasurmentValue ? null : this.measurementOptions[0],
            metric: this.metricOptions.length > 1 ? null : this.metricOptions[0],
        };
    }

    private initDateFormat() {
        this.configService.dateFormattingPattern().subscribe((pattern) => {
            this.dateFormat = pattern.longDatePattern.toUpperCase();
        });
    }

    public getRowConfig(row: MeasurementRow): FormFieldConfig {
        return {
            key: this.rowKey(row),
            formType: this.config.formType,
        };
    }

    public prepareSavedAnswersForDisplay(savedAnswers: MeasurementRow[]): MeasurementRow[] {
        const answersToDisplay: MeasurementRow[] = savedAnswers.map((row) => {
            const value = this.editableMeasurmentValue
                ? row.value
                : this.measurementOptions.find((option) => {
                      return option.id === row.value;
                  });
            return {
                ...row,
                [MeasurementComponent.METRIC_KEY]: this.metricOptions.find((option) => {
                    return option.id === row.metric || (row.metric && row.metric.id === option.id);
                }),
                [MeasurementComponent.VALUE_KEY]: value,
                [MeasurementComponent.DATE_KEY]: row.date,
            };
        });
        return answersToDisplay;
    }

    public simpleDateFocusLost(sdv: SimpleDateInputValue, row: MeasurementRow) {
        this.handleDateChange(sdv, row, false);
        this.onBlur(row.rowId);
    }

    public onBlur(rowId: number) {
        this.focusedRowId = -1;
        this.blurredRowId = rowId;
        setTimeout(() => {
            if (this.focusedRowId !== this.blurredRowId && this.radioValue) {
                const blurredRow = this.addedRows.filter((row) => {
                    return row.rowId === rowId;
                })[0];
                this.checkRowMissingValues(blurredRow);
            }
            this.blurredRowId = -1;
        }, 220);
    }

    public onFocus(rowId: number) {
        this.focusedRowId = rowId;
    }

    private checkRowMissingValues(checkedRow: MeasurementRow) {
        const erroneousRowKey = this.rowKey(checkedRow);
        const valueMissingCheck: boolean = this.editableMeasurmentValue ? checkedRow.value === null : !checkedRow.value;
        if (!checkedRow.metric || !checkedRow.date || valueMissingCheck) {
            this.addValueMissingHardErrorStatusForGivenKey(erroneousRowKey);
        } else {
            if (checkedRow.date) {
                if (
                    checkedRow.date === INVALID_DATE ||
                    (this.maxDate && moment(this.maxDate).isBefore(moment(checkedRow.date)))
                ) {
                    this.addInvalidValueHardErrorStatusForGivenKey(erroneousRowKey);
                } else if (this.lastSimpleDateValue[checkedRow.rowId]) {
                    if (
                        this.isInValidDateRange(erroneousRowKey, this.lastSimpleDateValue[checkedRow.rowId]) &&
                        this.lastSimpleDateValue[checkedRow.rowId].isUnknownDayPattern
                    ) {
                        this.addConfirmUnkownDaySoftErrorStatusForGivenKey(erroneousRowKey);
                    }
                }
            }
            if (checkedRow.value !== null) {
                this.checkNumericMeasurementRange(checkedRow.value, erroneousRowKey);
            }
        }
    }

    private initDateRangeConfig() {
        this.dateRangeConfig = this.config.dateRangeValidationConfig;
        combineLatest([
            safeSelectVariable(this.store, this.destroy$, this.dateRangeConfig.fromVariableKey),
            safeSelectVariable(this.store, this.destroy$, this.dateRangeConfig.toVariableKey),
        ])
            .pipe(takeUntil(this.destroy$))
            .subscribe(([fromVariable, toVariable]) => {
                if (!this.dateRangeConfig.fromVariableKey || fromVariable) {
                    this.dateRangeFrom = fromVariable && fromVariable.value;
                }
                if (!this.dateRangeConfig.toVariableKey || toVariable) {
                    this.dateRangeTo = toVariable && toVariable.value;
                }
            });
    }

    private isInValidDateRange(fieldKey: string, dateValueContainer: SimpleDateInputValue): boolean {
        if (this.dateRangeConfig && (this.dateRangeFrom || this.dateRangeTo)) {
            let isAfterFrom = true;
            if (this.dateRangeFrom) {
                isAfterFrom = isAfterDate(
                    dateValueContainer.storeValue,
                    this.dateRangeFrom,
                    dateValueContainer.isUnknownDayPattern,
                    this.dateRangeConfig.fromBoundaryIncluded
                );
            }
            let isBeforeTo = false;
            if (this.dateRangeTo) {
                isBeforeTo = isBeforeDate(
                    dateValueContainer.storeValue,
                    this.dateRangeTo,
                    dateValueContainer.isUnknownDayPattern,
                    this.dateRangeConfig.toBoundaryIncluded
                );
            }
            if (isAfterFrom && isBeforeTo) {
                return true;
            }

            const meta = [
                MeasurementComponent.DATE_RANGE_PLACEHOLDER_FROM +
                    ':' +
                    this.dfp.transform(this.dateRangeFrom, this.dateFormat),
                MeasurementComponent.DATE_RANGE_PLACEHOLDER_TO +
                    ':' +
                    this.dfp.transform(this.dateRangeTo, this.dateFormat),
            ];

            if (!isAfterFrom && this.dateRangeConfig.beforFromTranslationKey) {
                this.addFieldError(
                    fieldKey,
                    this.dateRangeConfig.beforFromTranslationKey,
                    FieldStatusType.HARD_ERROR,
                    meta.join(',')
                );
            }
            if (!isBeforeTo && this.dateRangeConfig.afterToTranslationKey) {
                this.addFieldError(
                    fieldKey,
                    this.dateRangeConfig.afterToTranslationKey,
                    FieldStatusType.HARD_ERROR,
                    meta.join(',')
                );
            }
            if (this.dateRangeConfig.outOfRangeTranslationKey) {
                this.addFieldError(
                    fieldKey,
                    this.dateRangeConfig.outOfRangeTranslationKey,
                    FieldStatusType.HARD_ERROR,
                    meta.join(',')
                );
            }

            return false;
        }

        return true;
    }

    private clearInvalidDateRangeErrors(fieldKey: string) {
        if (this.dateRangeConfig) {
            [
                this.dateRangeConfig.outOfRangeTranslationKey,
                this.dateRangeConfig.afterToTranslationKey,
                this.dateRangeConfig.beforFromTranslationKey,
            ].forEach((tk: string) => {
                if (tk) {
                    this.clearFieldError(fieldKey, tk, FieldStatusType.HARD_ERROR);
                }
            });
        }
    }

    private focusOnRow(rowId: number) {
        setTimeout(() => {
            this.focusedRowId = rowId;
        }, 0);
    }

    public checkMissingMeasurment() {
        setTimeout(() => {
            if (this.radioValue && this.focusedRowId < 0) {
                this.addedRows.forEach((row) => {
                    return this.checkRowMissingValues(row);
                });
            }
        }, 220);
    }

    private focusOnSelectedRadio() {
        setTimeout(() => {
            const yesRadio = document.getElementById('yesRadio' + this.componentUUID);
            yesRadio.focus();
        }, 0);
    }

    public itemDateInputId(row: MeasurementRow) {
        return this.rowKey(row) + 'DateInput';
    }

    public componentVisible(): boolean {
        return this.visible && this.fieldStatusControlledVisibilty;
    }

    public rowKey(row: MeasurementRow): string {
        return this.config.key + row.rowId;
    }

    private checkDateValue(value: any, rowKey: string) {
        if (value && this.isInValidDateRange(rowKey, value) && value.isUnknownDayPattern) {
            this.addConfirmUnkownDaySoftErrorStatusForGivenKey(rowKey);
        } else {
            this.clearConfirmUnkownDaySoftErrorStatusForGivenKey(rowKey);
        }
    }

    private checkNumericMeasurementRange(value: any, rowKey: string) {
        if (this.config.max === null && this.config.min === null) {
            return;
        }
        const errorTranslationKey = this.config.translationPrefix + '.' + this.getCorrectNumericValueRangeErrorSuffix();
        const meta = [
            MeasurementComponent.NUMERIC_VALUE_RANGE_PLACEHOLDER_MIN + ':' + this.config.min,
            MeasurementComponent.NUMERIC_VALUE_RANGE_PLACEHOLDER_MAX + ':' + this.config.max,
            MeasurementComponent.NUMERIC_VALUE_RANGE_PLACEHOLDER_UNIT + ':' + this.config.unit,
        ];
        const inputValue = this.editableMeasurmentValue
            ? (value as number)
            : this.measurementOptions.findIndex((option) => {
                  return option.id === value.id;
              });
        if (inputValue < this.config.min || inputValue > this.config.max) {
            this.addFieldError(rowKey, errorTranslationKey, FieldStatusType.HARD_ERROR, meta.join(','));
        }
    }

    private getCorrectNumericValueRangeErrorSuffix(): string {
        if (this.config.min != null && this.config.max != null) {
            return MeasurementComponent.NUMERIC_VALUE_OUT_OF_RANGE_ERROR_TRANSLATION_KEY;
        }
        if (this.config.min != null) {
            return MeasurementComponent.NUMERIC_VALUE_MIN_VIOLATION_ERROR_TRANSLATION_KEY;
        }
        if (this.config.max != null) {
            return MeasurementComponent.NUMERIC_VALUE_MAX_VIOLATION_ERROR_TRANSLATION_KEY;
        }
    }

    private clearInvalidNumericValueRangeErrors(fieldKey: string) {
        if (this.config.min || this.config.max) {
            [
                MeasurementComponent.NUMERIC_VALUE_OUT_OF_RANGE_ERROR_TRANSLATION_KEY,
                MeasurementComponent.NUMERIC_VALUE_MIN_VIOLATION_ERROR_TRANSLATION_KEY,
                MeasurementComponent.NUMERIC_VALUE_MAX_VIOLATION_ERROR_TRANSLATION_KEY,
            ].forEach((tk: string) => {
                if (tk) {
                    const errorKey = this.config.translationPrefix + '.' + tk;
                    this.clearFieldError(fieldKey, errorKey, FieldStatusType.HARD_ERROR);
                }
            });
        }
    }
}
