<div *ngIf="componentVisible()" class="form-element-container mb-l">
  <div>
    <nh-form-question
      [descriptionTranslationKey]="descriptionTranslationKey"
      [titleTranslationKey]="titleTranslationKey"
    ></nh-form-question>
    <nh-form-error-indicator [config]="config"></nh-form-error-indicator>
    <nh-field-status-indicator [config]="config"></nh-field-status-indicator>

    <div *ngIf="enabled" class="radio flex flex--column mt-l">
      <input
        id="{{ 'noRadio' + componentUUID }}"
        [checked]="radioValue === false"
        class="form-control"
        type="radio"
        (change)="handleRadioChange(false)"
      />
      <label for="{{ 'noRadio' + componentUUID }}" class="mb-xl">
        {{ radioNoTranslationKey | i18n }}
      </label>
      <input
        id="{{ 'yesRadio' + componentUUID }}"
        [checked]="radioValue === true"
        class="form-control mr-s"
        type="radio"
        (blur)="checkMissingMeasurment()"
        (change)="handleRadioChange(true)"
      />
      <label for="{{ 'yesRadio' + componentUUID }}" class="mb-xl">
        {{ radioYesTranslationKey | i18n }}
      </label>
    </div>
  </div>

  <ds-datatable
    *ngIf="enabled"
    [class]="datatableClasses"
    [limit]="100"
    [items]="addedRows"
    [trackItemByAttribute]="'rowId'"
    [expandableRows]="true"
    [expandAll]="true"
  >
    <datatablecolumn [header]="metricHeaderTranslationKey | i18n" [sortable]="metricOptions.length > 1" property="metric.label">
      <ng-template #datatableColumnCell let-item="item">
        <span *ngIf="metricOptions.length === 1" class="cell-label">{{ metricOptions[0].label }}</span>
        <ng-select
          appendTo="body"
          (change)="handleMetricChange($event, item)"
          *ngIf="metricOptions.length > 1"
          [clearable]="false"
          [items]="metricOptions"
          [ngModel]="item.metric"
          [placeholder]="metricPlaceholderTranslationKey | i18n"
          (blur)="onBlur(item.rowId)"
          (focus)="onFocus(item.rowId)"
        ></ng-select>
      </ng-template>
    </datatablecolumn>
    <datatablecolumn
      [header]="measurementHeaderTranslationKey | i18n"
      [sortable]="measurementOptions.length > 1"
      property="measurement.label"
    >
      <ng-template #datatableColumnCell let-item="item">
        <span *ngIf="measurementOptions.length === 1" class="cell-label">{{ measurementOptions[0].label }}</span>
        <div class="numeric-field flex flex--column">
          <input
            *ngIf="editableMeasurmentValue"
            type="number"
            class="numeric-field__input"
            required
            step="any"
            [ngModel]="item.value"
            [placeholder]="measurementPlaceholderTranslationKey | i18n"
            (focus)="onFocus(item.rowId)"
            (blur)="onBlur(item.rowId)"
            (keyup.enter)="onBlur(item.rowId)"
            (ngModelChange)="handleEditableMeasurementChange($event, item)"
          />
          <span class="numeric-field__unit type--color-grey">{{ config.unit | i18n }}</span>
        </div>
        <ng-select
          appendTo="body"
          (change)="handleMeasurementChange($event, item)"
          *ngIf="measurementOptions.length > 1 && !editableMeasurmentValue"
          [clearable]="false"
          [items]="measurementOptions"
          [ngModel]="item.value"
          [placeholder]="measurementPlaceholderTranslationKey | i18n"
          (blur)="onBlur(item.rowId)"
          (focus)="onFocus(item.rowId)"
        ></ng-select>
      </ng-template>
    </datatablecolumn>
    <datatablecolumn [header]="dateHeaderTranslationKey | i18n" [sortable]="true" property="date">
      <ng-template #datatableColumnCell let-item="item">
        <nh-simple-date-input
          #simpleDateInput
          [inputId]="itemDateInputId(item)"
          [dateFormat]="dateFormat"
          [maxDate]="maxDate"
          [dateValue]="item.date"
          (valueUpdate)="handleDateChange($event, item)"
          (focusLost)="simpleDateFocusLost($event, item)"
          (clicked)="onFocus(item.rowId)"
          [configKey]="rowKey(item)"
        ></nh-simple-date-input>
      </ng-template>
    </datatablecolumn>
    <datatablecolumn [maxWidth]="HIDDEN_COLUMN_WIDTH" [minWidth]="HIDDEN_COLUMN_WIDTH" class="hidden-column">
      <ng-template #datatableColumnCell let-item="item">
        <input id="{{ 'rowFocusIndicator' + componentUUID + item.rowId }}" (focus)="onFocus(item.rowId)" (blur)="onBlur(item.rowId)" />
      </ng-template>
    </datatablecolumn>
    <datatablecolumn [maxWidth]="ROW_ICON_WIDTH" class="action-column">
      <ng-template #datatableColumnCell let-item="item">
        <ds-actions-dropdown
          class="measurement__dropdown"
          (optionClick)="removeRow(item)"
          [options]="[{ label: rowRemoveTranslationKey | i18n : i18nPlaceholders, value: item.rowId }]"
        ></ds-actions-dropdown>
      </ng-template>
    </datatablecolumn>
    <ng-template #datatableExpand let-item="item">
      <nh-field-status-indicator [config]="getRowConfig(item)"></nh-field-status-indicator>
    </ng-template>
  </ds-datatable>

  <ds-button
    (buttonClick)="addRowThrottled()"
    *ngIf="multiRow && enabled"
    class="add-more-button flex flex--align-items-center"
    icon="icon-plus-circle"
    variation="secondary"
    >{{ addMoreTranslationKey | i18n | uppercase }}</ds-button
  >
</div>
