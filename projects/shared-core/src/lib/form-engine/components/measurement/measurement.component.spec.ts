import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MeasurementComponent } from './measurement.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('MeasurementComponent', () => {
    let component: MeasurementComponent;
    let fixture: ComponentFixture<MeasurementComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [MeasurementComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(MeasurementComponent);
        component = fixture.componentInstance;
        component.config = { key: 'phq', labelKey: 'test.label', formType: QuestionaryType.BOUNCE_HADS };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
