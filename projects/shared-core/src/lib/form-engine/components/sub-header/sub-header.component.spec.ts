import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SubHeaderComponent } from './sub-header.component';
import { MocksModule } from '@shared-core/testing';

describe('SubHeaderComponent', () => {
    let component: SubHeaderComponent;
    let fixture: ComponentFixture<SubHeaderComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [SubHeaderComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(SubHeaderComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
