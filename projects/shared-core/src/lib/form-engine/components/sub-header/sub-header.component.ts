import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input } from '@angular/core';
import { Store } from '@ngrx/store';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { FormVariablesService } from '../../services/form-variables.service';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputFieldWithVarSupport } from '../form-input-field-variable-support.component';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-sub-header',
    templateUrl: './sub-header.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SubHeaderComponent extends FormInputFieldWithVarSupport implements FormField {
    @Input()
    public config: FormFieldConfig;

    constructor(protected store: Store<FormEngineState>, cd: ChangeDetectorRef, protected fvs: FormVariablesService) {
        super(cd, fvs);
    }
}
