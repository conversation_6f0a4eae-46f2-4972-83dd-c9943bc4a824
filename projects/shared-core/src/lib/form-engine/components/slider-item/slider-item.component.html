<div class="form-element-container slider-item" *ngIf="visible" [nsFormElementContainer]="hasErrors">
  <nh-form-error-indicator *ngIf="!sliderValueChanged" [config]="config" (isFormValid)="handleFormErrors($event)"></nh-form-error-indicator>
  <nh-input-header [config]="config"></nh-input-header>
  <ds-range-slider
    class="slider"
    [invert]="invert"
    [value]="currentValue"
    [labelMin]="labelMin"
    [labelMax]="labelMax"
    [indicatorLabel]="indicatorLabel"
    [min]="min"
    [max]="max"
    [mid]="mid"
    (valueChange)="onSliderValueChange($event)"
  ></ds-range-slider>
</div>
