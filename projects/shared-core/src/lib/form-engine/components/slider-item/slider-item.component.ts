import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { FieldService } from '../../services/field.service';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';
import { safeSelectField } from '../../services/subscription-helper';
import get from 'lodash/get';
import isNil from 'lodash/isNil';
import type { IsFormValidEvent } from '../form-error-indicator/form-error-indicator.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-slider-item',
    styleUrls: ['./slider-item.component.scss'],
    templateUrl: './slider-item.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SliderItemComponent extends FormInputField implements OnInit, FormField, OnDestroy {
    @Input() config: FormFieldConfig;
    @Input() visible: boolean;
    public currentValue: number;
    public labelMax: string;
    public labelMin: string;
    public min: number;
    public max: number;
    public indicatorLabel: string;
    public sliderValueChanged = false;
    hasErrors = false;

    private destroy$ = new Subject<boolean>();
    private debouncer$ = new Subject<number>();

    constructor(
        protected store: Store<FormEngineState>,
        private i18n: I18NPipe,
        private fieldService: FieldService,
        cd: ChangeDetectorRef
    ) {
        super(cd);
    }

    ngOnInit() {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));

        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((value) => {
            this.currentValue = value;
        });

        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        this.config.maxTranslationKey
            ? (this.labelMax = this.i18n.transform(this.config.maxTranslationKey))
            : (this.labelMax = this.i18n.transform(`strengthSelector.${this.config.key}.highest`));
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        this.config.minTranslationKey
            ? (this.labelMin = this.i18n.transform(this.config.minTranslationKey))
            : (this.labelMin = this.i18n.transform(`strengthSelector.${this.config.key}.lowest`));
        const indicatorLabelTranslationKey = get(this.config, 'indicatorLabel');
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        indicatorLabelTranslationKey
            ? (this.indicatorLabel = this.i18n.transform(indicatorLabelTranslationKey))
            : (this.indicatorLabel = '');

        this.min = get(this.config, 'min', 0);
        this.max = get(this.config, 'max', 10);
    }

    get invert() {
        const invert = get(this.config, 'invert');
        return isNil(invert) ? true : invert;
    }

    get mid() {
        const mid = get(this.config, 'mid') ? true : null;
        return mid ? (this.max + this.min) / 2 : null;
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
        this.debouncer$.complete();
    }

    onSliderValueChange(val: number) {
        this.currentValue = Math.round(val);
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, this.currentValue, false)));

        this.sliderValueChanged = !!this.currentValue;
    }

    handleFormErrors($event: IsFormValidEvent): void {
        this.hasErrors = !$event.valid && $event.isDirty;
        this.cd.markForCheck();
    }
}
