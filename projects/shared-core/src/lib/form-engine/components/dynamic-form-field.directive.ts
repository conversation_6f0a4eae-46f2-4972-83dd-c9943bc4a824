import {
  ComponentFactoryResolver,
  ComponentRef,
  Directive,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  Type,
  ViewContainerRef
} from '@angular/core';
import { Store } from '@ngrx/store';
import has from 'lodash/has';
import some from 'lodash/some';
import { combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ConditionType } from '../../generated/models/condition-type';
import { FormItemType } from '../../generated/models/form-item-type';
import { getDestructuredSubArrayFields, isSubArrayField } from '../../util/sub-array-field.utils';
import { FieldConditionMatcher } from '../models/field-condition-matcher.enum';
import { FormFieldConfig } from '../models/form-field-config.interface';
import { FormField } from '../models/form-field.interface';
import { ShowCondition } from '../models/show-condition.interface';
import { FieldService } from '../services/field.service';
import { safeSelectField } from '../services/subscription-helper';
import { FormEngineState } from '../store/reducers/state';
import { selectFormVariableValue } from '../store/selectors/variables.selectors';
import { CheckboxListWithExtraFieldsComponent } from './checkbox-list-with-extra-fields/checkbox-list-with-extra-fields.component';
import { CheckboxListComponent } from './checkbox-list/checkbox-list.component';
import { DateRangeItemComponent } from './date-range-item/date-range-item.component';
import { DateComponent } from './date/date.component';
import { EarlierSymptomComponent } from './earlier-symptom/earlier-symptom.component';
import { EventModalComponent } from './event-modal/event-modal.component';
import { InputGroupComponent } from './input-group/input-group.component';
import { IntegerFieldComponent } from './integer-field/integer-field.component';
import { MeasurementComponent } from './measurement/measurement.component';
import { MultiselectComponent } from './multiselect/multiselect.component';
import { NumericFieldComponent } from './numeric-field/numeric-field.component';
import { PainPointerListComponent } from './pain-pointer-list/pain-pointer-list.component';
import { PhotoItemComponent } from './photo-item/photo-item.component';
import { QuestionGroupComponent } from './question-group/question-group.component';
import { RadioListWithExtraFieldsComponent } from './radio-list-with-extra-fields/radio-list-with-extra-fields.component';
import { RadioListComponent } from './radio-list/radio-list.component';
import { SelectWithRadioComponent } from './select-with-radio/select-with-radio.component';
import { SliderItemComponent } from './slider-item/slider-item.component';
import { SubHeaderComponent } from './sub-header/sub-header.component';
import { TextAreaComponent } from './text-area/text-area.component';
import { TextInputFieldComponent } from './text-input-field/text-input-field.component';
import { ToggleItemComponent } from './toggle-item/toggle-item.component';
import { WizardSectionComponent } from './wizard-section/wizard-section.component';
import { YesNoDateComponent } from './yes-no-date/yes-no-date.component';
import { TextBlockComponent } from './text-block/text-block.component';
import { SetFieldVisibility } from '../store/actions/form.actions';

const components: { [type: string]: Type<FormField> } = {
  [FormItemType.WIZARD_SECTION]: WizardSectionComponent,
  [FormItemType.RADIO]: RadioListComponent,
  [FormItemType.TEXTAREA]: TextAreaComponent,
  [FormItemType.SLIDER]: SliderItemComponent,
  [FormItemType.INTEGER]: IntegerFieldComponent,
  [FormItemType.PAIN_POINTER]: PainPointerListComponent,
  [FormItemType.DATE_RANGE]: DateRangeItemComponent,
  [FormItemType.PHOTO]: PhotoItemComponent,
  [FormItemType.CHECKBOX]: CheckboxListComponent,
  [FormItemType.SUB_HEADER]: SubHeaderComponent,
  [FormItemType.EARLIER_SYMPTOM]: EarlierSymptomComponent,
  [FormItemType.TOGGLE]: ToggleItemComponent,
  [FormItemType.INPUT_GROUP]: InputGroupComponent,
  [FormItemType.YES_NO_DATE]: YesNoDateComponent,
  [FormItemType.DATE]: DateComponent,
  [FormItemType.MULTISELECT]: MultiselectComponent,
  [FormItemType.MEASUREMENT]: MeasurementComponent,
  [FormItemType.CHECKBOX_WITH_EXTRA_FIELDS]: CheckboxListWithExtraFieldsComponent,
  [FormItemType.QUESTION_GROUP]: QuestionGroupComponent,
  [FormItemType.RADIO_WITH_EXTRA_FIELDS]: RadioListWithExtraFieldsComponent,
  [FormItemType.SELECT_WITH_RADIO]: SelectWithRadioComponent,
  [FormItemType.EVENT_MODAL]: EventModalComponent,
  [FormItemType.NUMERIC_FIELD]: NumericFieldComponent,
  [FormItemType.TEXT_INPUT_FIELD]: TextInputFieldComponent,
  [FormItemType.TEXT_BLOCK]: TextBlockComponent
};

@Directive({
  selector: '[nhDynamicFormField]'
})
export class DynamicFormFieldDirective implements OnInit, OnDestroy, FormField {
  @Input()
  public config: FormFieldConfig;

  @Input()
  public fieldHideCondition: boolean;

  @Output()
  public canceled = new EventEmitter<void>();

  @Output()
  public next = new EventEmitter<void>();

  @Output()
  public previous = new EventEmitter<void>();

  @Input()
  public showButtons = true;

  @Input()
  secondaryButtonTranslationKey: string;

  public component: ComponentRef<FormField>;

  private destroy$ = new Subject<boolean>();

  private visibilities: FieldVisibility[];

  constructor(
    private resolver: ComponentFactoryResolver,
    private container: ViewContainerRef,
    private fieldService: FieldService,
    private store: Store<FormEngineState>
  ) {}

  ngOnInit() {
    if (!components[this.config.type]) {
      const supportedTypes = Object.keys(components).join(', ');
      throw new Error(`Trying to use unsupported type: (${this.config.type}), supported types: ${supportedTypes}`);
    }

    safeSelectField(this.store, this.destroy$, 'visibilities', this.config.formType).subscribe(fv => {
      this.visibilities = fv;
    });
    const component = this.resolver.resolveComponentFactory<FormField>(components[this.config.type]);
    this.component = this.container.createComponent(component);
    this.component.instance.config = this.config;
    this.component.instance.fieldHideCondition = this.fieldHideCondition;
    this.component.instance.showButtons = this.showButtons;

    if (this.config.showCondition || this.config.staticShowCondition) {
      let staticConditionVisibility = false;
      if (this.config.staticShowCondition) {
        staticConditionVisibility = this.fieldService.resolveStaticCondition(
          this.config,
          this.config.staticShowCondition,
          this.fieldHideCondition
        );
      }

      if (
        this.config.showCondition &&
        (!this.config.staticShowCondition || (this.config.staticShowCondition && staticConditionVisibility))
      ) {
        this.parseShowCondition();
      } else {
        if (this.config.staticShowCondition) {
          this.component.instance.visibility = staticConditionVisibility;
        }
      }
    } else {
      this.component.instance.visibility = true;
    }

    if (this.component.instance instanceof WizardSectionComponent || this.component.instance instanceof EarlierSymptomComponent) {
      this.component.instance.secondaryButtonTranslationKey = this.secondaryButtonTranslationKey;
      this.component.instance.next.pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.next.emit();
      });

      this.component.instance.previous.pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.previous.emit();
      });

      this.component.instance.canceled.pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.canceled.emit();
      });
    }
    this.setVisibilityToFormState();
  }

  ngOnDestroy() {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  private parseShowCondition() {
    const condition: ShowCondition = this.config.showCondition;
    if (condition.conditionType === ConditionType.FIELD) {
      this.watchForField(condition);
    } else if (condition.conditionType === ConditionType.FORM_VARIABLE) {
      this.watchForFormVariable(condition);
    } else if (condition.conditionType === ConditionType.AND || condition.conditionType === ConditionType.OR) {
      this.resolveMultiCondition(condition);
    } else {
      throw new Error(`Unsupported condition provided: ${condition.conditionType}`);
    }
  }

  private resolveMultiCondition = (condition: ShowCondition) => {
    combineLatest([
      ...condition.conditions.map(innerCondition => {
        if (innerCondition.conditionType === ConditionType.FORM_VARIABLE) {
          return this.store.select(selectFormVariableValue(innerCondition.fieldName));
        } else {
          return safeSelectField(this.store, this.destroy$, innerCondition.fieldName, this.config.formType);
        }
      })
    ])
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        const validities = condition.conditions.map((innerCondition, index) => {
          return this.resolveCondition(value[index], innerCondition);
        });
        this.component.instance.visibility =
          condition.conditionType === ConditionType.AND
            ? validities.every(valid => {
                return valid;
              })
            : validities.some(valid => {
                return valid;
              });
      });
  };

  private watchForField(condition: ShowCondition) {
    let { fieldName } = condition;
    if (isSubArrayField(this.config.key)) {
      const { parentKey, id } = getDestructuredSubArrayFields(this.config.key);
      fieldName = `${parentKey}.${id}.${this.config.showCondition.fieldName}`;
    }
    safeSelectField(this.store, this.destroy$, fieldName, this.config.formType).subscribe(value => {
      this.component.instance.visibility = this.resolveCondition(value, condition);
    });
  }

  private watchForFormVariable(condition: ShowCondition) {
    this.store
      .select(selectFormVariableValue(condition.fieldName))
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.component.instance.visibility = this.resolveCondition(value, condition);
      });
  }

  private resolveCondition(value: any, condition: ShowCondition): boolean {
    switch (condition.fieldConditionMatcher) {
      case FieldConditionMatcher.CONTAINS:
        return this.parseContainsCondition(value, condition);

      case FieldConditionMatcher.CONTAINS_NOT:
        return !this.parseContainsCondition(value, condition);

      case FieldConditionMatcher.IS_NULL:
        return this.parseIsNullCondition(value);

      case FieldConditionMatcher.IS_NOT_NULL:
        return this.parseIsNotNullCondition(value);

      case FieldConditionMatcher.OVER:
        return this.parseOverCondition(value, condition);

      case FieldConditionMatcher.IS_NOT_VISIBLE:
        return this.parseIsNotVisibleCondition(condition.fieldName);
      default:
        throw new Error(`Unsupported field condition matcher: ${condition.fieldConditionMatcher}`);
    }
  }

  private parseContainsCondition(value: any, condition: ShowCondition) {
    const containsObjectWithKeyField = (values: any[]) => {
      return values.some(val => {
        return has(val, 'key');
      });
    };

    if (value === undefined || value === null) {
      return false;
    }

    if (value instanceof Array) {
      if (containsObjectWithKeyField(value)) {
        return condition.values.some(val => {
          return some(value, { key: val });
        });
      }

      const visible = condition.values.some(val => {
        return value.includes(val);
      });

      return visible;
    } else {
      const stringvalue = value + '';
      const visible = condition.values.some(val => {
        return stringvalue === val;
      });

      return visible;
    }
  }

  private parseIsNullCondition(value: any): boolean {
    if (value === undefined || value === null) {
      return true;
    }
    return false;
  }

  private parseIsNotNullCondition(value: any): boolean {
    if (value !== undefined && value !== null) {
      return true;
    }
    return false;
  }

  private parseIsNotVisibleCondition(fieldName: string): boolean {
    return !this.visibilities.find((formField: FieldVisibility) => formField.field === fieldName)?.visibility;
  }

  private parseOverCondition(value: any, condition: ShowCondition): boolean {
    return value !== undefined && value !== null && value >= parseFloat(condition.values[0]);
  }

  private setVisibilityToFormState() {
    this.store.dispatch(
      new SetFieldVisibility({
        formType: this.config.formType,
        field: this.config.key,
        visibility: this.component.instance.visible
      })
    );
  }
}

interface FieldVisibility {
  field: string;
  visibility: boolean;
}
