import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { SimpleDateInputComponent } from './simple-date-input.component';
import { ChangeDetectorRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MocksModule } from '@shared-core/testing';
import { MockStore, provideMockStore } from '@ngrx/store/testing';
import { DateFormatPipe } from '../../../pipes/date-format.pipe';
import { UnknownDateDefault } from '../../models/form-field-config.interface';

describe('SimpleDateInputComponent', () => {
    let component: SimpleDateInputComponent;
    let fixture: ComponentFixture<SimpleDateInputComponent>;
    let changeDetectorRef: ChangeDetectorRef;
    let store: MockStore;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule, FormsModule],
            declarations: [SimpleDateInputComponent],
            providers: [provideMockStore(), DateFormatPipe, ChangeDetectorRef],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(SimpleDateInputComponent);
        component = fixture.componentInstance;
        component.dateFormat = 'MM.DD.YYYY';
        component.allowUnknownDate = true;
        component.unknownDayDefault = UnknownDateDefault.FIRST;
        (component as any).dateDelimiter = '.';
        changeDetectorRef = fixture.debugElement.injector.get(ChangeDetectorRef);
        store = TestBed.inject(MockStore);
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should set the date format and compute related properties', () => {
        const getPrivateField = (component: any, fieldName: string) => component[fieldName];
        const testDateFormat = 'MM.DD.YYYY';
        expect(component['_dateFormat']).toBe(testDateFormat);
        expect(getPrivateField(component, 'dateDelimiter')).toBe('.');
        expect(component.maxlength).toBe(testDateFormat.length);
        expect(getPrivateField(component, 'dateFormatDayIdx')).toBe(1);
        expect(getPrivateField(component, 'dateFormatMonthIdx')).toBe(0);
        expect(getPrivateField(component, 'dateFormatYearIdx')).toBe(2);
    });

    it('should format the date string correctly', () => {
        const inputDate = '10102007';
        const expectedOutput = '10.10.2007';
        const result = component['autoFormatDateString'](inputDate);
        expect(result).toBe(expectedOutput);
    });

    describe('momentFromString', () => {
        it('should return a valid moment object for a valid date', () => {
            const result = component['momentFromString']('10.10.2023');
            expect(result.format('YYYY-MM-DD')).toBe('2023-10-10');
            expect(result.isValid()).toBe(true);
        });
        it('should handle invalid date', () => {
            const result = component['momentFromString']('32.10.2020');
            expect(result.isValid()).toBe(false);
            expect(result.format()).toBe('Invalid date');
        });
    });

    describe('getCurrentValue', () => {
        it('should return a valid date when momentValue is valid', () => {
            const testDate = '10.10.2023';

            const result = component['getCurrentValue'](testDate);

            expect(result.stringValue).toBe(testDate);
            expect(result.momentValue.isValid()).toBe(true);
            expect(result.validDate).toBe(true);
            expect(result.storeValue).toEqual(new Date(2023, 9, 10));
            expect(result.isFuture).toBe(false);
            expect(result.isAfterMaxDate).toBe(null);
            expect(result.isUnknownDayPattern).toBe(false);
        });

        it('should handle unknown date patterns when allowUnknownDate is true', () => {
            const testDate = '10.2023';

            const result = component['getCurrentValue'](testDate);

            expect(result.stringValue).toBe(testDate);
            expect(result.momentValue.isValid()).toBe(true);
            expect(result.storeValue).toEqual(new Date(2023, 9, 1));
            expect(result.validDate).toBe(true);
            expect(result.isUnknownDayPattern).toBe(true);
        });

        it('should return invalid date when momentValue is invalid', () => {
            const testDate = '32.10.2023';

            const result = component['getCurrentValue'](testDate);

            expect(result.stringValue).toBe(testDate);
            expect(result.momentValue.isValid()).toBe(false);
            expect(result.validDate).toBe(false);
            expect(result.storeValue).toBe(null);
            expect(result.isFuture).toBe(null);
            expect(result.isAfterMaxDate).toBe(null);
            expect(result.isUnknownDayPattern).toBe(false);
        });
    });
});
