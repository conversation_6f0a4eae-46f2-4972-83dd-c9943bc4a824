import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild,
} from '@angular/core';
import { Store } from '@ngrx/store';
import moment, { Moment } from 'moment';
import { Subject } from 'rxjs';
import { DateFormatPipe } from '../../../pipes/date-format.pipe';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { UnknownDateDefault } from '../../models/form-field-config.interface';
import { safeSelectFieldStatus } from '../../services/subscription-helper';
import { FormEngineState } from '../../store/reducers/state';

export interface SimpleDateInputValue {
    stringValue: string;
    momentValue: Moment;
    validDate: boolean;
    isFuture: boolean;
    isAfterMaxDate: boolean;
    storeValue: Date;
    // date input has been 'MM/yyyy' -> this will default to a date value of first of the month
    isUnknownDayPattern: boolean;
}

const KEY_CONTROL = 'Control';
const KEY_META = 'Meta';
const KEY_ENTER = 'Enter';
const KEY_TAB = 'Tab';
const KEY_BACKSPACE = 'Backspace';
const KEY_ARROWLEFT = 'ArrowLeft';
const KEY_ARROWRIGHT = 'ArrowRight';
const KEY_DELETE = 'Delete';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-simple-date-input',
    templateUrl: './simple-date-input.component.html',
    styleUrls: ['./simple-date-input.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SimpleDateInputComponent implements OnDestroy, OnInit {
    static readonly UNKNOWN_DAY_SOFT_ERROR_TRANSLATION_KEY = 'fe.error.generic.unkownDayDate';
    static readonly CONTROL_KEYS = [
        KEY_ENTER,
        KEY_ARROWLEFT,
        KEY_ARROWRIGHT,
        KEY_BACKSPACE,
        KEY_TAB,
        KEY_CONTROL,
        KEY_META,
        KEY_DELETE,
    ];
    static readonly NON_UPDATE_KEYS = [KEY_ARROWLEFT, KEY_ARROWRIGHT, KEY_BACKSPACE, KEY_DELETE, KEY_CONTROL, KEY_META];
    static readonly VALUE_KEYS = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
    static readonly SUPPORTED_DATE_DELIMITER = ['/', '.', '-'];

    @ViewChild('dateInput', { static: true }) input: ElementRef;

    public currentStringValue = '';
    public maxlength;

    @Input()
    public configKey: string;
    @Input()
    public maxDate: Date | Moment;
    @Input()
    public inputId = '';
    @Output()
    public valueUpdate = new Subject<SimpleDateInputValue>();
    @Output()
    public focusLost = new Subject<SimpleDateInputValue>();
    @Output()
    public clicked = new Subject<SimpleDateInputValue>();

    private dateFormatMonthIdx;
    private dateFormatDayIdx;
    private dateFormatYearIdx;
    private isUnknownDayDate = false;
    private destroy$ = new Subject<boolean>();
    private dateDelimiter: string;
    private lastKeyDown: string;

    constructor(private dfp: DateFormatPipe, private cd: ChangeDetectorRef, private store: Store<FormEngineState>) {}

    public _dateFormat = '';

    @Input()
    set dateFormat(df: string) {
        if (df) {
            this._dateFormat = df;
            this.dateDelimiter = SimpleDateInputComponent.SUPPORTED_DATE_DELIMITER.find((del) => {
                return this._dateFormat.indexOf(del) >= 0;
            });
            this.maxlength = this._dateFormat.length;

            const splitted = this._dateFormat.toLowerCase().split(this.dateDelimiter);
            this.dateFormatDayIdx = splitted.indexOf('dd');
            this.dateFormatMonthIdx = splitted.indexOf('mm');
            this.dateFormatYearIdx = splitted.indexOf('yyyy');
        }
    }

    private _unknownDayDefault = UnknownDateDefault.FIRST;

    @Input()
    set unknownDayDefault(uddf: UnknownDateDefault) {
        if (uddf) {
            this._unknownDayDefault = uddf;
        }
    }

    private _allowUnknownDate = true;

    @Input()
    set allowUnknownDate(b: boolean) {
        if (b !== null && b !== undefined) {
            this._allowUnknownDate = b;
        }
    }

    @Input()
    set dateValue(dv: Date) {
        if (!dv) {
            this.currentStringValue = null;
            this.cd.markForCheck();
        } else {
            let currentStringValue = this.dateToString(dv);
            if (this.isUnknownDayDate) {
                currentStringValue = this.reformatToUnknownDayString(currentStringValue);
            }
            this.currentStringValue = currentStringValue;
            this.cd.markForCheck();
        }
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
        this.valueUpdate.complete();
        this.focusLost.complete();
        this.clicked.complete();
    }

    ngOnInit() {
        if (this.configKey) {
            safeSelectFieldStatus(
                this.store,
                this.destroy$,
                this.configKey,
                FieldStatusType.SOFT_ERROR,
                SimpleDateInputComponent.UNKNOWN_DAY_SOFT_ERROR_TRANSLATION_KEY
            ).subscribe((fieldStatuses: FieldStatus[]) => {
                if (this._allowUnknownDate) {
                    this.isUnknownDayDate = fieldStatuses && fieldStatuses.length > 0;
                    if (this.isUnknownDayDate) {
                        this.currentStringValue = this.reformatToUnknownDayString(this.currentStringValue);
                        this.cd.markForCheck();
                    }
                }
            });
        }
    }

    // We can not store a invalid date in the DB.
    // In the case of a unknown day, we default to the first of the month.
    // E.g. user input -> 10/2017 => 10/01/2017 in the data base.
    // If a date like that is coming from the DB, we need to check if we acctually deal with a unknown day date
    keyDown($event) {
        if (!this.acceptKeyDown($event)) {
            $event.preventDefault();
            $event.stopPropagation();
        } else {
            this.lastKeyDown = $event.key;
        }
    }

    ngModelChange(newInputValue: string) {
        this.handleNgModelChange(newInputValue.trim());
    }

    focus() {
        this.input.nativeElement.focus();
    }

    parseUnknownDateString(dateString: string): string {
        const splitted = dateString.split(this.dateDelimiter);
        const newDateString = ['', '', ''];
        const isYearFirst = this.isYearFirst();
        newDateString[this.dateFormatDayIdx] = '01';
        newDateString[this.dateFormatMonthIdx] = isYearFirst ? splitted[1] : splitted[0];
        newDateString[this.dateFormatYearIdx] = isYearFirst ? splitted[0] : splitted[1];
        const firstOfMonthString = newDateString.join(this.dateDelimiter);
        if (this._unknownDayDefault === UnknownDateDefault.FIRST) {
            return firstOfMonthString;
        } else {
            const lastOfMonthMoment = this.momentFromString(firstOfMonthString).endOf('month');
            return this.dateToString(lastOfMonthMoment.toDate());
        }
    }

    isUnknownDatePattern(dateString: string): boolean {
        const yearLastRegex = new RegExp('^\\d\\d\\' + this.dateDelimiter + '\\d\\d\\d\\d$');
        const fullDateRegex = new RegExp(
            '^\\d{4}\\' + this.dateDelimiter + '\\d{2}\\' + this.dateDelimiter + '\\d{2}$'
        );
        const partialDateRegex = new RegExp(
            '^\\d{4}\\' + this.dateDelimiter + '\\d{2}(\\' + this.dateDelimiter + '\\d{0,1})?$'
        );

        const isPartialDate = partialDateRegex.test(dateString);
        const isFullDate = fullDateRegex.test(dateString);

        if (this.isYearFirst()) {
            return isPartialDate && !isFullDate;
        }
        return yearLastRegex.test(dateString);
    }

    emitClicked() {
        this.clicked.next(this.getCurrentValue());
    }

    emitValueChange(overwriteStringValue: string) {
        this.valueUpdate.next(this.getCurrentValue(overwriteStringValue));
    }

    emitFocusLost() {
        this.currentStringValue = this.autoFormatDateString(this.currentStringValue);
        this.focusLost.next(this.getCurrentValue(this.currentStringValue));
    }

    // and reformat to 10/2017 in the UI.
    private reformatToUnknownDayString(currentString: string): string {
        const currentMomentValue = this.momentFromString(currentString);
        if (currentString && currentMomentValue.isValid()) {
            const splitted = currentString.split(this.dateDelimiter);
            const isYearFirst = this.isYearFirst();
            const first = isYearFirst ? 0 : this.dateFormatMonthIdx;
            const second = isYearFirst ? this.dateFormatMonthIdx : this.dateFormatYearIdx;
            return splitted[first] + this.dateDelimiter + splitted[second];
        }
        return currentString;
    }

    private getCurrentValue(overwriteStringValue?: string): SimpleDateInputValue {
        const stringValueToUse =
            overwriteStringValue !== null && overwriteStringValue !== undefined
                ? overwriteStringValue
                : this.currentStringValue;

        let currentMomentValue = this.momentFromString(stringValueToUse);
        if (this._allowUnknownDate) {
            this.isUnknownDayDate = this.isUnknownDatePattern(stringValueToUse);
            if (this.isUnknownDayDate) {
                currentMomentValue = this.momentFromString(this.parseUnknownDateString(stringValueToUse));
            }
        }

        const validDate = currentMomentValue ? currentMomentValue.isValid() : false;

        let storeValue;
        if (!validDate) {
            storeValue = null;
        } else {
            storeValue = currentMomentValue.toDate();
        }

        return {
            stringValue: stringValueToUse,
            momentValue: currentMomentValue,
            validDate,
            isFuture: validDate ? currentMomentValue.isAfter(moment()) : null,
            isAfterMaxDate: validDate && this.maxDate ? currentMomentValue.isAfter(moment(this.maxDate)) : null,
            storeValue,
            isUnknownDayPattern: this.isUnknownDayDate,
        };
    }

    // Do this now with hard coded US format to simplify the implementation
    // Some simple manual rules implemented:
    //   01012019 -> 01/01/2019
    //   1/1/2019 -> 01/01/2019
    private autoFormatDateString(v: string): string {
        const isYearFirst = this.isYearFirst();
        if (!v || isYearFirst) {
            return v;
        }
        const newV = v.replace(new RegExp('[^0-9,\\' + this.dateDelimiter + ']', 'g'), '');
        const splitted = newV.split(this.dateDelimiter);
        let autoFormatted = '';

        if (splitted.length === 3) {
            // Handle e.g. '1/10/1987' -> '01/10/1987'
            autoFormatted = splitted
                .map((s, idx) => {
                    if (idx !== this.dateFormatYearIdx && s.length === 1) {
                        return '0' + s;
                    }
                    return s;
                })
                .join(this.dateDelimiter);
        } else if (newV.length >= 8) {
            // Handle e.g. '01101987' -> '01/10/1987'
            autoFormatted = splitted.join('');
            const isYearFirst = this.isYearFirst();
            autoFormatted =
                autoFormatted.substr(0, isYearFirst ? 4 : 2) +
                this.dateDelimiter +
                autoFormatted.substr(isYearFirst ? 4 : 2, 2) +
                this.dateDelimiter +
                autoFormatted.substr(isYearFirst ? 6 : 4, isYearFirst ? 2 : 4);
        }

        const m = this.momentFromString(autoFormatted);
        if (m && m.isValid()) {
            return autoFormatted;
        }

        return v;
    }

    private dateToString(d: Date): string {
        return this.dfp.transform(d, this._dateFormat);
    }

    private momentFromString(s: string): Moment {
        if (s && s.trim().length > 0) {
            return moment(s, this._dateFormat, true);
        } else {
            return null;
        }
    }

    private isSupportedKey(key: string): boolean {
        return (
            SimpleDateInputComponent.CONTROL_KEYS.includes(key) ||
            SimpleDateInputComponent.VALUE_KEYS.includes(key) ||
            key === this.dateDelimiter
        );
    }

    private handleNgModelChange(newInputValue: string) {
        if (!SimpleDateInputComponent.NON_UPDATE_KEYS.includes(this.lastKeyDown)) {
            newInputValue = this.autoFormatDateString(newInputValue);
        }
        this.emitValueChange(newInputValue);
    }

    private isCopyCmd($event): boolean {
        return $event.key === 'v' && (this.lastKeyDown === KEY_CONTROL || this.lastKeyDown === KEY_META);
    }

    private acceptKeyDown($event): boolean {
        return this.isSupportedKey($event.key) || this.isCopyCmd($event);
    }

    // E.g 1990-01-01
    private isYearFirst(): boolean {
        return this.dateFormatYearIdx === 0;
    }
}
