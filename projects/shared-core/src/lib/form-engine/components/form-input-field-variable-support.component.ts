import { ChangeDetector<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, Directive } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { Dictionary } from '../../common-types';
import { FormFieldConfig } from '../models/form-field-config.interface';
import { FormVariablesService } from '../services/form-variables.service';
import { AddAnswer } from '../store/actions/form.actions';
import { FormEngineState } from '../store/reducers/state';
import { FormInputField } from './form-input-field.component';
import { FormVariableValue } from '../models/form-var-value';
import { FormVariableType } from '../models/form-var-type';

@Directive()
export abstract class FormInputFieldWithVarSupport extends FormInputField implements OnInit, OnDestroy {
    i18nPlaceholders: Dictionary<string, string> = {};
    protected destroy$ = new Subject<boolean>();

    protected constructor(protected cd: ChangeDetectorRef, protected formVariablesService: FormVariablesService) {
        super(cd);
    }

    ngOnDestroy(): void {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    ngOnInit() {
        this.formVariablesService.getTranslations(this.store, this.destroy$).subscribe((t) => {
            this.i18nPlaceholders = t;
            this.addAdhocVariablesToPlaceholders();
            this.cd.detectChanges();
        });
    }

    private addAdhocVariablesToPlaceholders() {
        if (this.config && this.config.adhocFormVariables) {
            for (const [key, variableValue] of Object.entries(this.config.adhocFormVariables)) {
                if (variableValue.type === FormVariableType.DATE) {
                    variableValue.value = Number(variableValue.value);
                }
                this.i18nPlaceholders[key] = this.formVariablesService.translateFormVariable(variableValue);
            }
        }
    }
}
