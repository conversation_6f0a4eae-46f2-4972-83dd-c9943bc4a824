<div class="form-element-container" *ngIf="visible" [nsFormElementContainer]="hasErrors">
  <nh-form-error-indicator *ngIf="!valueEntered" [config]="config" (isFormValid)="handleFormErrors($event)"></nh-form-error-indicator>
  <nh-text-input-header [config]="config" [id]="config.key + '-header'"></nh-text-input-header>
  <div class="input-wrapper" [ngClass]="{ 'input-group': !!config.unit }">
    <textarea
      class="form-control fe-text-area-field"
      [id]="config.key | hyphened"
      [ngModel]="value"
      rows="4"
      [name]="config.key | hyphened"
      (ngModelChange)="onChange($event)"
      [maxlength]="config.maxLength"
      [attr.aria-labelledby]="config.key + '-header'"
    ></textarea>
  </div>
</div>
