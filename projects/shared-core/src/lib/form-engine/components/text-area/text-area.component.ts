import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { DecodePipe } from '../../../pipes/decode.pipe';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { FieldService } from '../../services/field.service';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';
import { safeSelectField } from '../../services/subscription-helper';
import type { IsFormValidEvent } from '../form-error-indicator/form-error-indicator.interface';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-text-area',
  templateUrl: './text-area.component.html',
  styleUrls: ['./text-area.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TextAreaComponent extends FormInputField implements OnInit, FormField, OnDestroy {
  @Input()
  public config: FormFieldConfig;

  @Input()
  public visible: boolean;

  public value: string;

  public valueEntered = false;

  private destroy$ = new Subject<boolean>();

  hasErrors = false;

  constructor(
    protected store: Store<FormEngineState>,
    private fieldService: FieldService,
    cd: ChangeDetectorRef,
    private decode: DecodePipe
  ) {
    super(cd);
  }

  ngOnInit() {
    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));
    safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe(value => {
      this.value = this.decode.transform(value);
    });
  }

  ngOnDestroy() {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  public onChange(value: string) {
    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, value, false)));

    // Note: this.value is not same as value
    if (this.value === '') {
      this.valueEntered = false;
    } else {
      this.valueEntered = value?.length > 0;
    }
    this.cd.detectChanges();
  }

  handleFormErrors($event: IsFormValidEvent): void {
    this.hasErrors = !$event.valid && $event.isDirty;
    this.cd.markForCheck();
  }
}
