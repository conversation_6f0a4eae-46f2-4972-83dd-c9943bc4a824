import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import includes from 'lodash/includes';
import pick from 'lodash/pick';
import { Subscription } from 'rxjs';
import { map, filter, distinctUntilChanged } from 'rxjs/operators';
import { SharedSchemaService } from '../../../abstract-services/shared-schema.service';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { FieldService } from '../../services/field.service';
import { AddAnswers } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { selectForm } from '../../store/selectors/form.selectors';
import { FormInputField } from '../form-input-field.component';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-input-group',
    templateUrl: './input-group.component.html',
    styleUrls: ['./input-group.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InputGroupComponent extends FormInputField implements OnInit, FormField, OnDestroy {
    @Input()
    public config: FormFieldConfig;

    public noneVisible: boolean;

    private groupName: string;
    private inputGroupFields: string[];
    public checked: boolean;
    private sub: Subscription;

    constructor(
        protected cd: ChangeDetectorRef,
        protected store: Store<FormEngineState>,
        private schema: SharedSchemaService,
        private fieldService: FieldService
    ) {
        super(cd);
    }

    ngOnInit() {
        const inputGroups = this.schema.getSchema().inputGroups[this.config.formType];

        this.groupName = Object.entries(inputGroups).find(([_field, group]) => {
            return group === this.config.id;
        })[1] as string;

        this.noneVisible =
            Object.values(inputGroups).filter((value) => {
                return value === this.config.id;
            }).length > 1;

        this.inputGroupFields = Object.entries(inputGroups)
            .filter(([_field, group]) => {
                return group === this.groupName;
            })
            .map(([field, _group]) => {
                return field;
            });

        this.sub = this.store
            .select(selectForm(this.config.formType))
            .pipe(
                map((obj) => {
                    return pick(obj, this.inputGroupFields);
                }),
                map((obj) => {
                    return Object.values(obj);
                }),
                filter((ary) => {
                    return includes(ary, true);
                }),
                distinctUntilChanged()
            )
            .subscribe(() => {
                this.checked = false;
                this.cd.markForCheck();
            });
    }

    ngOnDestroy() {
        this.sub.unsubscribe();
    }

    public toggleOption() {
        this.checked = !this.checked;
        if (this.checked) {
            this.handleSelection(false);
        } else {
            this.handleSelection(null);
        }
    }

    private handleSelection(value: false | null) {
        const answers = this.inputGroupFields.map((key) => {
            const config = { ...this.config, key };
            return this.fieldService.getAnswer(config, value, false);
        });

        this.store.dispatch(new AddAnswers(answers));
    }
}
