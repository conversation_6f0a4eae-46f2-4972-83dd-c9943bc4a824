<div class="form-element-container" *ngIf="visible">
    <div class="fe-input-group">
        <p class="content" [innerHTML]="config.headerKey | i18n | safeHtml"></p>
        <div class="input-group-actions" *ngIf="noneVisible">
            <div
                class="checkbox">
                <input
                    type="checkbox"
                    [id]="config.id"
                    [name]="config.id"
                    [checked]="checked"
                    (keyup.space)="toggleOption()"
                    (change)="toggleOption()"
                />
                <label [for]="config.id">{{ 'general.noneOfTheBelow' | i18n }}</label>
            </div>
        </div>
        
    </div>
</div>
