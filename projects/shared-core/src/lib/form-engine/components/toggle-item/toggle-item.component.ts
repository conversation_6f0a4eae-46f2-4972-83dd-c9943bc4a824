import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { ModelValue } from '../../models/model-value.interface';
import { FieldService } from '../../services/field.service';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';
import { safeSelectField } from '../../services/subscription-helper';
import { IsFormValidEvent } from '../form-error-indicator/form-error-indicator.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-toggle-item',
    templateUrl: './toggle-item.component.html',
    styleUrls: ['./toggle-item.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToggleItemComponent extends FormInputField implements OnInit, OnDestroy, FormField {
    @Input()
    public config: FormFieldConfig;

    public toggleValues: ModelValue[];

    public selected: ModelValue;

    public hasErrors = false;

    private destroy$ = new Subject<boolean>();

    constructor(
        protected cd: ChangeDetectorRef,
        protected store: Store<FormEngineState>,
        private fieldService: FieldService
    ) {
        super(cd);
    }

    ngOnInit() {
        this.toggleValues = this.fieldService.getListValues(this.config);

        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));
        // if value is null, then unselect the item
        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((value) => {
            if (value !== undefined) {
                this.selected = this.toggleValues.find((modelValue) => {
                    return modelValue.value === value;
                });
            }
        });
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public toggleOption(value: ModelValue) {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, value.value, false)));
    }

    handleFormErrors($event: IsFormValidEvent): void {
        this.hasErrors = !$event.valid && $event.isDirty;
        this.cd.markForCheck();
    }
}
