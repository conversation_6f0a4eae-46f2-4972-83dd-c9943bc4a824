import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ToggleItemComponent } from './toggle-item.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('ToggleItemComponent', () => {
    let component: ToggleItemComponent;
    let fixture: ComponentFixture<ToggleItemComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [ToggleItemComponent],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(ToggleItemComponent);
        component = fixture.componentInstance;
        component.config = {
            key: 'test',
            labelKey: 'test.label',
            formType: QuestionaryType.BOUNCE_HADS,
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
