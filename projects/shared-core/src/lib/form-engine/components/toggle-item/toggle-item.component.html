<div [nsFormElementContainer]="hasErrors">
    <nh-form-error-indicator
        [config]="config"
        (isFormValid)="handleFormErrors($event)"
    ></nh-form-error-indicator>
    <div class="toggle" *ngIf="visible">
        <div class="toggles">
            <div
                class="toggle-container"
                *ngFor="let value of toggleValues"
                [ngClass]="{ selected: this.selected?.id === value.id }"
                (click)="toggleOption(value)"
                [id]="config.formKey + '-' + config.key + '-' + value.id"
            >
                <div class="toggle-option">
                    <label [for]="config.formKey + '-' + config.key + '-' + value.id"
                    >{{value.translationKey | i18n}}</label>
                </div>
            </div>
        </div>
        <label class="toggle-label">{{ config.labelKey | i18n }}</label>
    </div>
</div>
