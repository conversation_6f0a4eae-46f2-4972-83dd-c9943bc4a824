import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { FormVariablesService } from '../../services/form-variables.service';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputFieldWithVarSupport } from '../form-input-field-variable-support.component';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-question-group',
    templateUrl: './question-group.component.html',
    styleUrls: ['./question-group.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QuestionGroupComponent extends FormInputFieldWithVarSupport implements Form<PERSON>ield {
    @Input()
    public config: FormFieldConfig;

    @Input() public visible: boolean;

    constructor(protected store: Store<FormEngineState>, cd: ChangeDetectorRef, protected fvs: FormVariablesService) {
        super(cd, fvs);
    }
}
