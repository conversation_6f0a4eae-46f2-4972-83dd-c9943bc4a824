@import '../../../../styles/deprecated_mixins.scss';
@import '../../../../styles/deprecated_variables.scss';
.group-name {
  @include text-heading-md();
  font-size: $font-size-medium;
  color: $dark;
}
.values-container {
  margin-bottom: 25px;
  .value {
    @include text-md();
  }
}
.request-card,
#nurse-diary {
  .input-group-container {
    .group-name {
      font-size: 15px;
    }
    .value {
      font-size: 15px;
      color: $body-text-light-color;
    }
  }
}
