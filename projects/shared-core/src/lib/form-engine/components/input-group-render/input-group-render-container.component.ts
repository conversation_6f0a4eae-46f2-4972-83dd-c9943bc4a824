import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { InputGroupViewModel } from '../../models/input-group-view-model.interface';
import { ClearQuestionnaireState } from '../../store/actions/form-render.action';
import { FormEngineState } from '../../store/reducers/state';
import { getFormRenderState } from '../../store/selectors/form-render.selectors';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'input-group-render-container',
    templateUrl: './input-group-render-container.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InputGroupRenderContainerComponent implements OnInit, OnD<PERSON>roy {
    private destroy$ = new Subject<boolean>();
    public inputGroups: InputGroupViewModel[] = [];

    @Input()
    public formName: string;

    constructor(private store: Store<FormEngineState>, private cd: ChangeDetectorRef) {}

    ngOnInit() {
        this.store
            .select(getFormRenderState)
            .pipe(takeUntil(this.destroy$))
            .subscribe((state) => {
                const questionnaires = state.questionnaires;
                if (questionnaires[this.formName]) {
                    this.inputGroups = questionnaires[this.formName].inputGroups.filter((inputGroup) => {
                        return inputGroup.values.length > 0;
                    });
                    this.cd.markForCheck();
                }
            });
    }

    ngOnDestroy(): void {
        this.destroy$.next(true);
        this.store.dispatch(new ClearQuestionnaireState(this.formName));
        this.destroy$.complete();
    }
}
