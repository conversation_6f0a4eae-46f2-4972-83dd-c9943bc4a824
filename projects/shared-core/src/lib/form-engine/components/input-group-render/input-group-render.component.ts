import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { SharedSchemaService } from '../../../abstract-services/shared-schema.service';
import { InputGroupViewModel } from '../../models/input-group-view-model.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'input-group-render',
    styleUrls: ['./input-group-render.component.scss'],
    templateUrl: './input-group-render.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InputGroupRenderComponent implements OnInit {
    @Input()
    public group: InputGroupViewModel;

    public groupName: string;
    public values: string[];

    constructor(private schemaService: SharedSchemaService) {}

    ngOnInit() {
        const translations = this.schemaService.getSchema().inputGroupTranslations[this.group.formName];
        this.groupName = translations[this.group.name];
        this.values = this.group.values.map((value) => {
            return translations[value];
        });
    }
}
