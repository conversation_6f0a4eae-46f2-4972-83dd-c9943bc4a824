import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InputHeaderComponent } from './input-header.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('InputHeaderComponent', () => {
    let component: InputHeaderComponent;
    let fixture: ComponentFixture<InputHeaderComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [InputHeaderComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(InputHeaderComponent);
        component = fixture.componentInstance;
        component.config = {
            key: 'phq',
            labelKey: 'test.label',
            formType: QuestionaryType.BOUNCE_HADS,
            children: [],
            id: 'test',
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
