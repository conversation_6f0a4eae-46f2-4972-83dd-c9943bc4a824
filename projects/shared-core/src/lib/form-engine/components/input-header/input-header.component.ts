import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { Dictionary } from '../../../common-types';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { FormVariablesService } from '../../services/form-variables.service';
import { FormEngineState } from '../../store/reducers/state';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-input-header',
    templateUrl: './input-header.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InputHeaderComponent implements OnInit, OnDestroy, FormField {
    @Input()
    public config: FormFieldConfig;

    i18nPlaceholders: Dictionary<string, string> = {};
    private destroy$ = new Subject<boolean>();

    constructor(
        private store: Store<FormEngineState>,
        private cd: ChangeDetectorRef,
        private fvs: FormVariablesService
    ) {}

    ngOnInit() {
        this.fvs.getTranslations(this.store, this.destroy$).subscribe((t) => {
            this.i18nPlaceholders = t;
            this.cd.markForCheck();
        });
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }
}
