<div *ngIf="visible" class="form-element-container mb-l">
    <nh-form-question
        [descriptionTranslationKey]="subtitleTranslationKey"
        [titleTranslationKey]="titleTranslationKey"
    ></nh-form-question>

    <nh-field-status-indicator [config]="config"></nh-field-status-indicator>

    <ng-select
        [(ngModel)]="selectedOptions"
        [clearSearchOnAdd]="true"
        [addTag]="addAdhoc"
        [items]="options"
        [multiple]="true"
        [closeOnSelect]="false"
        [placeholder]="placeholderTranslationKey | i18n"
        [searchable]="true"
        class="ds-dropdown mt-l fe-multiselect-adhoc-display"
    >
        <ng-template let-item="item" let-search="searchTerm" ng-option-tmp>
            <div class="flex flex--justify-content-space-between flex--align-items-center">
                <span>{{ item.label }}</span>
                <ds-icon *ngIf="selectedOptions.includes(item)" name="icon-check"></ds-icon>
            </div>
        </ng-template>

        <ng-template let-search="searchTerm" ng-tag-tmp>
            <div class="ng-option">
                <div>{{ notFoundTranslationKey | i18n: { search_term: search } }}</div>
                <div class="mb-m">{{ notFoundDescriptionTranslationKey | i18n }}</div>
                <a>{{ notFoundActionTranslationKey | i18n: { search_term: search } }}</a>
            </div>
        </ng-template>
    </ng-select>
</div>
