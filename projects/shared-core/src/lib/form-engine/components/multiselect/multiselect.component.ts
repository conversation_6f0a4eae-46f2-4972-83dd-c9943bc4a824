import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import sortBy from 'lodash/sortBy';
import remove from 'lodash/remove';
import { combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FieldService } from '../../services/field.service';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { selectField } from '../../store/selectors/form.selectors';
import { FormInputField } from '../form-input-field.component';

interface SelectOption {
    id?: string;
    isAdhoc: boolean;
    label: string;
    key: string;
}

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-multiselect',
    templateUrl: './multiselect.component.html',
    styleUrls: ['./multiselect.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MultiselectComponent extends FormInputField implements OnInit, OnDestroy {
    public static readonly ADHOC_FIELD_POSTFIX = 'Adhoc';

    public static readonly NOT_FOUND_POSTFIX = 'notFound';
    public static readonly NOT_FOUND_POSTFIX_DESCRIPTION = 'notFoundDescription';
    public static readonly NOT_FOUND_POSTFIX_ACTION = 'notFoundAction';
    public static readonly SUBTITLE_POSTFIX = 'subtitle';
    public static readonly PLACEHOLDER_POSTFIX = 'placeholder';

    @Input() public config: FormFieldConfig;
    @Input() public visible: boolean;

    public options: SelectOption[];

    public titleTranslationKey: string;
    public subtitleTranslationKey: string;
    public placeholderTranslationKey: string;
    public notFoundTranslationKey: string;
    public notFoundDescriptionTranslationKey: string;
    public notFoundActionTranslationKey: string;

    private adhocFieldConfig: FormFieldConfig;
    private destroy$ = new Subject<boolean>();
    private _selectedOptions: SelectOption[] = [];

    constructor(
        protected store: Store<FormEngineState>,
        protected cd: ChangeDetectorRef,
        private fieldService: FieldService,
        private i18NPipe: I18NPipe
    ) {
        super(cd);
    }

    get selectedOptions() {
        return this._selectedOptions;
    }

    set selectedOptions(options: SelectOption[]) {
        this._selectedOptions = options;
        this.updateStoreAnswers();
    }

    ngOnInit() {
        this.initTranslationKeys();
        this.initFieldConfigs();
        let isFirstEmittedValue = true;
        combineLatest([
            this.store.select(selectField(this.config.formType, this.config.key)),
            this.store.select(selectField(this.config.formType, this.adhocFieldConfig.key)),
        ])
            .pipe(takeUntil(this.destroy$))
            .subscribe(([nonAdhocSelected, adhocSelected]: [string[], string[]]) => {
                if (isFirstEmittedValue) {
                    this.options = this.getNonAdhocOptions();
                    if (adhocSelected) {
                        this.options = sortBy(
                            [
                                ...this.options,
                                ...adhocSelected.map((key) => {
                                    return this.getAdhocSelectOptionFromKey(key);
                                }),
                            ],
                            (option) => {
                                return option.label.toLowerCase();
                            }
                        );
                    }
                    isFirstEmittedValue = false;
                }
                const transformedNonAdhoc: SelectOption[] = nonAdhocSelected
                    ? nonAdhocSelected.map((key) => {
                          return this.getSelectOptionFromKey(key);
                      })
                    : [];
                const transformedAdhoc: SelectOption[] = adhocSelected
                    ? adhocSelected.map((key) => {
                          return this.getAdhocSelectOptionFromKey(key);
                      })
                    : [];
                this._selectedOptions = [...transformedNonAdhoc, ...transformedAdhoc];
                this.cd.markForCheck();
            });
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, [], true)));
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.adhocFieldConfig, [], true)));
    }

    ngOnDestroy(): void {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public addAdhoc(value: string): SelectOption {
        return {
            isAdhoc: true,
            label: value,
            key: value,
        };
    }

    private getNonAdhocOptions(): SelectOption[] {
        return this.fieldService.getListValues(this.config).map((option) => {
            return {
                id: option.id,
                isAdhoc: false,
                label: this.i18NPipe.transform(option.translationKey),
                key: option.value,
            };
        });
    }

    private getSelectOptionFromKey(key: string): SelectOption {
        return {
            isAdhoc: false,
            key,
            label: this.options.find((option) => {
                return option.key === key;
            }).label,
        };
    }

    private getAdhocSelectOptionFromKey(key: string): SelectOption {
        return {
            isAdhoc: true,
            key,
            label: key,
        };
    }

    private initTranslationKeys() {
        const baseLabelKey = this.config.labelKey.replace('.label', '.');

        this.titleTranslationKey = this.config.labelKey;
        this.subtitleTranslationKey = this.createTranslationKey(baseLabelKey, MultiselectComponent.SUBTITLE_POSTFIX);
        this.placeholderTranslationKey = this.createTranslationKey(
            baseLabelKey,
            MultiselectComponent.PLACEHOLDER_POSTFIX
        );
        this.notFoundTranslationKey = this.createTranslationKey(baseLabelKey, MultiselectComponent.NOT_FOUND_POSTFIX);
        this.notFoundDescriptionTranslationKey = this.createTranslationKey(
            baseLabelKey,
            MultiselectComponent.NOT_FOUND_POSTFIX_DESCRIPTION
        );
        this.notFoundActionTranslationKey = this.createTranslationKey(
            baseLabelKey,
            MultiselectComponent.NOT_FOUND_POSTFIX_ACTION
        );
    }

    private createTranslationKey(baseKey: string, key: string) {
        return baseKey + key + '.label';
    }

    private initFieldConfigs() {
        this.adhocFieldConfig = {
            ...this.config,
            key: this.config.key + MultiselectComponent.ADHOC_FIELD_POSTFIX,
        };
    }

    private updateStoreAnswers() {
        this.clearStatusForGivenKey(this.config.key);
        const nonAdhocOptions = [...this.selectedOptions];
        const adhocOptions = remove(nonAdhocOptions, (option) => {
            return option.isAdhoc;
        }).map((option) => {
            return option.key;
        });
        this.store.dispatch(
            new AddAnswer(
                this.fieldService.getAnswer(
                    this.config,
                    nonAdhocOptions.map((option) => {
                        return option.key;
                    }),
                    false
                )
            )
        );
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.adhocFieldConfig, adhocOptions, false)));
    }
}
