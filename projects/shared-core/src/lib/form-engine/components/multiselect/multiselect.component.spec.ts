import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MultiselectComponent } from './multiselect.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('MultiselectComponent', () => {
    let component: MultiselectComponent;
    let fixture: ComponentFixture<MultiselectComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [MultiselectComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(MultiselectComponent);
        component = fixture.componentInstance;
        component.config = {
            key: 'wasPatientPregnantMultipleValue',
            labelKey: 'test.label',
            formType: QuestionaryType.BOUNCE_HADS,
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
