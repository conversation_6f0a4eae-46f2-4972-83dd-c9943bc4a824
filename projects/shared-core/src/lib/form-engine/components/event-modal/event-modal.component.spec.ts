import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { EventModalComponent } from './event-modal.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('EventModalComponent', () => {
    let component: EventModalComponent;
    let fixture: ComponentFixture<EventModalComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [EventModalComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(EventModalComponent);
        component = fixture.componentInstance;
        component.config = { key: 'phq', labelKey: 'test.label', formType: QuestionaryType.BOUNCE_HADS, children: [] };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
