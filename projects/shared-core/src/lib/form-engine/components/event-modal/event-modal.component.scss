@import '../../../../styles/ds-variables.scss';

$border: thin solid lightgrey;
.flex-item--2 {
  flex: 2;
}
.summary {
  background-color: white;
  border: $border !important;
  border-radius: 10px !important;
  cursor: pointer;
  &__event {
    border-bottom: $border;
    &:hover {
      background-color: var(--color-grey-lighten-5, $color-grey-lighten-5);
    }
  }
  &__dropdown {
    fill: $color-brand-2;
    position: relative;
  }
  &__dropdown-content {
    display: inline-flex;
    background-color: white;
    border-radius: 5px;
    cursor: pointer;
    height: 0;
    margin: 0;
    list-style: none;
    padding: $spacing-m;
    right: 0;
    top: 50px;
    transition: opacity 0.3s ease;
    overflow: hidden;
    position: absolute;
    opacity: 0;
    pointer-events: none;
    text-align: center;
    white-space: nowrap;
    &.open {
      border: $border;
      height: auto;
      opacity: 1;
      pointer-events: all;
      z-index: $z-above-all;
    }
  }
}
.add-event-button {
  justify-content: flex-start;
  width: 100%;
}
.event-detail-status {
  margin-right: $spacing-xs;
}
.ng-tooltip {
  width: 340px;
  max-width: 340px;
  text-align: left;
  margin-left: 153px;
  padding: $spacing-m;
  white-space: pre-wrap;
}
.icon-soft-error {
  color: $color-brand-2-darken-1;
}
.icon-soft-error-hover-message {
  color: $color-brand-2-darken-1;
  background-color: $color-brand-2-lighten-4;
}
.icon-soft-error-hover-message::after {
  border-color: $color-brand-2-lighten-4 transparent transparent transparent;
  left: 5%;
}
.icon-soft-error-check-hover-message {
  color: $color-brand-2-darken-1;
  background-color: $color-brand-2-lighten-4;
}
.icon-soft-error-check-hover-message::after {
  border-color: $color-brand-2-lighten-4 transparent transparent transparent;
  left: 5%;
}
