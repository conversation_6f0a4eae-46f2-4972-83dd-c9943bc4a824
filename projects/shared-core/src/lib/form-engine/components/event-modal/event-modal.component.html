<div *ngIf="visible" class="mb-l">
    <nh-form-question
        [descriptionTranslationKey]="descriptionTranslationKey"
        [titleTranslationKey]="titleTranslationKey"
        class="mb-l"
    ></nh-form-question>

    <div *ngIf="addedEvents.length > 0" class="summary__headers flex ml-l mb-s mr-xxl">
        <h6 class="flex-item--1 mr-l">{{ eventTypeHeaderTranslationKey | i18n: i18nPlaceholders }}</h6>
        <h6 class="flex-item--2">{{ detailsHeaderTranslationKey | i18n: i18nPlaceholders }}</h6>
    </div>
    <div class="summary">
        <div class="summary__events">
            <div
                (click)="openEventModal(event.index)"
                *ngFor="let event of addedEvents"
                class="summary__event flex pt-l pb-l pl-l"
            >
                <div class="flex-item--1 mr-l">
                    <h6 class="type--font-weight-bold">{{ getEventType(event.index) }}</h6>
                </div>
                <div class="flex-item--2 flex flex--column">
                    <div *ngFor="let detail of getEventDetails(event.index)">
                        <div class="flex">
                            <ds-icon
                                *ngIf="getEventDetailRowStatusIcon(detail)"
                                class="{{ 'event-detail-status ' + getEventDetailRowStatusIcon(detail) }}"
                                dsTooltip
                                name="{{ getEventDetailRowStatusIcon(detail) }}"
                                placement="top"
                                tooltip="{{ getEventDetailQuestionRowStatusMessage(detail) }}"
                                tooltipCustomClass="{{ getEventDetailRowStatusIcon(detail) + '-hover-message' }}"
                                tooltipTitle="{{ getEventDetailQuestionRowStatusMessageTitle(detail) }}"
                            >
                            </ds-icon>
                            <h6>{{ detail.question }}</h6>
                        </div>
                        <span>{{ detail.answer }}</span>
                    </div>
                </div>
                <div class="summary__dropdown">
                    <ds-actions-dropdown
                        (optionClick)="removeEvent(event.index)"
                        (toggleClick)="handleDropdownTrigger(event.index)"
                        (visibilityChange)="handleDropdownVisibilityChange($event, event.index)"
                        [options]="[{ label: removeEventTranslationKey | i18n: i18nPlaceholders, value: event.index }]"
                    ></ds-actions-dropdown>
                </div>
            </div>
        </div>
        <ds-button
            *ngIf="canAddNewEvent()"
            (buttonClick)="openEventModal(getNextEventId())"
            class="add-event-button"
            icon="icon-plus-circle"
            size="large"
            variation="secondary"
            >{{ addEventButtonTranslationKey | i18n: i18nPlaceholders }}</ds-button
        >
    </div>

    <nh-field-status-indicator [config]="config"></nh-field-status-indicator>
</div>
