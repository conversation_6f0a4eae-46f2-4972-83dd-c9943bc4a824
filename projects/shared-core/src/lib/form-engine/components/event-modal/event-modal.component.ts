import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { select, Store } from '@ngrx/store';
import omit from 'lodash/omit';
import { combineLatest } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { ConfigurationProviderService } from '../../../abstract-services/configuration-provider.service';
import { FormItemType } from '../../../generated/models/form-item-type';
import { DateFormatPipe } from '../../../pipes/date-format.pipe';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { checkFieldValidity } from '../../../util/field-validation';
import { getDestructuredSubArrayFields } from '../../../util/sub-array-field.utils';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { ViewComponentState } from '../../models/view-component-state';
import { setHighestPriorityStatus } from '../../services/field-status.service';
import { FieldValueTranslationService } from '../../services/field-value-translation.service';
import { FieldService } from '../../services/field.service';
import { FormVariablesService } from '../../services/form-variables.service';
import { safeSelectField, safeSelectFieldStatus } from '../../services/subscription-helper';
import {
    AddOrUpdateFieldStatus,
    RemoveAllFieldStatusesWithKeyIncluding,
} from '../../store/actions/field-status.actions';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { selectAllComponentStates, selectComponentState } from '../../store/selectors/component-state.selectors';
import { EXTRA_FIELDS_POSTFIXES } from '../extra-field-postfixes';
import { FormInputFieldWithVarSupport } from '../form-input-field-variable-support.component';
import { SubformComponent } from '../sub-form/subform.component';
import {
    getEventDetailQuestionRowStatusIcon,
    getEventDetailQuestionRowStatusMessage,
    getEventDetailQuestionRowStatusMessageTitle,
    getEventDetailQuestionStatus,
} from './event-modal-summary.service';
import { Modal, ModalAction, ModalComponent } from '../../../ds/components/modal/modal.component';
import { DsModalService } from '../../../ds/services/modal.service';

export interface Answer {
    [field: string]: any;
}

export interface FieldTranslationValue {
    answer: string;
    question: string;
    fieldKey: string;
    eventId: string;
}

export interface FieldTranslation {
    [field: string]: FieldTranslationValue;
}

type ModalActionButtonType = 'primary' | 'secondary' | 'cancel';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-event-modal',
    templateUrl: './event-modal.component.html',
    styleUrls: ['./event-modal.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EventModalComponent extends FormInputFieldWithVarSupport implements OnInit {
    public static readonly MODAL_SAVE_BUTTON_POSTFIX = 'modalSaveButton';
    public static readonly MODAL_ADD_ANOTHER_BUTTON_POSTFIX = 'modalAddAnotherButton';
    public static readonly MODAL_CANCEL_BUTTON_POSTFIX = 'modalCancelButton';
    public static readonly DESCRIPTION_POSTFIX = 'description';
    public static readonly ADD_EVENT_BUTTON_POSTFIX = 'addEventButton';
    public static readonly REMOVE_EVENT_POSTFIX = 'removeEvent';
    public static readonly EVENT_TYPE_OPTIONS_POSTFIX = 'Event';
    public static readonly EVENT_TYPE_HEADER_POSTFIX = 'eventTypeHeader';
    public static readonly DETAILS_HEADER_POSTFIX = 'detailsHeader';
    static readonly MISSING_FIELD_TRANSLATION_KEY = 'fe.error.generic.missingFieldOrQuestion';

    @Input() public config: FormFieldConfig;
    @Input() public visible: boolean;

    public titleTranslationKey: string;
    public descriptionTranslationKey: string;
    public addEventButtonTranslationKey: string;
    public removeEventTranslationKey: string;
    public eventTypeHeaderTranslationKey: string;
    public detailsHeaderTranslationKey: string;
    public addedEvents: Answer[] = [];
    public eventTranslations: Map<number, any> = new Map<number, any>();
    public openDropdownEventId: number;

    private preOpenModalFieldStatuses = [];
    private modalRef: ModalComponent;
    private activeEventModalId: number = null;
    private isModalContentValid = true;
    private fieldStatuses: Map<string, FieldStatus> = new Map();
    private dateFormatPattern = 'mm/dd/yyyy';

    constructor(
        protected cd: ChangeDetectorRef,
        protected fvs: FormVariablesService,
        protected store: Store<FormEngineState>,
        private modalService: DsModalService,
        private i18nPipe: I18NPipe,
        private dfPipe: DateFormatPipe,
        private fieldService: FieldService,
        private fieldValueTranslatorService: FieldValueTranslationService,
        private configurationProviderService: ConfigurationProviderService
    ) {
        super(cd, fvs);
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.initTranslationKeys();

        // Initialize with server saved values
        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType)
            .pipe(take(1))
            .subscribe((events) => {
                if (Array.isArray(events)) {
                    this.addedEvents = events;
                    events.forEach((event) => {
                        this.updateEventTranslations(event.index);
                    });
                } else {
                    this.addedEvents = [];
                }
                this.cd.markForCheck();
            });

        this.configurationProviderService
            .dateFormattingPattern()
            .pipe(takeUntil(this.destroy$))
            .subscribe((dfp) => {
                this.dateFormatPattern = dfp.longDatePattern;
            });

        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, [], true)));
        if (this.config.enableFieldStatus) {
            this.startModalValidityListener();
        }
        this.clearHiddenFieldsErrorsOnVisibiltyChanges();
        this.restoreAnswersOnVisibilityChange();
    }

    /**
     * Opens an existing or new event modal
     *
     * @param eventId - Either an existing event ID or the next valid ID if this is a new event
     * @see getNextEventId
     */
    public openEventModal(eventId: number): void {
        this.activeEventModalId = eventId;
        this.preOpenModalFieldStatuses = this.config.children
            .map((item) => {
                let resultFs: FieldStatus[] = [];
                const fullFieldKey = `${this.config.key}.${this.activeEventModalId}.${item.key}`;
                safeSelectFieldStatus(this.store, this.destroy$, fullFieldKey, null, null, true)
                    .pipe(take(1))
                    .subscribe((fs) => {
                        resultFs = fs;
                    });
                return resultFs;
            })
            .flatMap((fs) => {
                return fs;
            });
        this.clearValueMissingHardErrorStatusForGivenKey(this.config.key);

        this.addedEvents = this.addedEvents.map((e) => {
            return { ...e, isActive: e.index === this.activeEventModalId };
        });
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, this.addedEvents, false)));

        const isNewEvent =
            this.addedEvents.findIndex((event) => {
                return event.index === eventId;
            }) === -1;
        if (isNewEvent) {
            this.store.dispatch(
                new AddAnswer(
                    this.fieldService.getAnswer(
                        this.config,
                        [...this.addedEvents, { index: eventId, isActive: true }],
                        false
                    )
                )
            );
        }
        const popupRef = this.modalService.openPopup(null, this.getModalOptions(this.activeEventModalId));
        this.modalRef = popupRef.instance as ModalComponent;
    }

    public getEventType(eventId: number): string {
        return this.config.eventConfig.typeFieldTranslationKey
            ? this.i18nPipe.transform(this.config.eventConfig.typeFieldTranslationKey, this.i18nPlaceholders)
            : this.eventTranslations.get(eventId)[this.config.eventConfig.typeField].answer;
    }

    public getEventDetails(eventId: number): FieldTranslationValue[] | any[] {
        if (this.config.eventConfig.typeField) {
            return Object.values(omit(this.eventTranslations.get(eventId), this.config.eventConfig.typeField)).filter(
                (translation) => {
                    return translation.answer !== null && translation.answer !== undefined;
                }
            );
        }

        return Object.values(this.eventTranslations.get(eventId)).filter((translation: FieldTranslation) => {
            return translation.answer !== null && translation.answer !== undefined;
        });
    }

    public removeEvent(eventId: number): void {
        this.addedEvents = this.addedEvents.filter((e) => {
            return e.index !== eventId;
        });
        this.eventTranslations.delete(eventId);
        this.removeAllFieldStatusesForEvent(eventId);
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, this.addedEvents, false)));
    }

    public handleDropdownTrigger(eventId: number): void {
        if (this.openDropdownEventId === eventId) {
            this.openDropdownEventId = null;
            return;
        }
        this.openDropdownEventId = eventId;
    }

    public handleDropdownVisibilityChange(isVisible: boolean, eventId: number): void {
        this.openDropdownEventId = isVisible ? eventId : null;
    }

    public getNextEventId(): number {
        return this.addedEvents.length === 0
            ? 0
            : Math.max(
                  ...this.addedEvents.map((event) => {
                      return event.index;
                  })
              ) + 1;
    }

    getEventDetailRowStatusIcon(detail: FieldTranslationValue): string {
        return getEventDetailQuestionRowStatusIcon(
            this.fieldStatuses,
            this.config.key,
            detail.eventId,
            detail.fieldKey
        );
    }

    getEventDetailQuestionStatus(detail: FieldTranslationValue): any {
        return getEventDetailQuestionStatus(this.fieldStatuses, this.config.key, detail.eventId, detail.fieldKey);
    }

    getEventDetailQuestionRowStatusIcon(detail: FieldTranslationValue): string {
        return getEventDetailQuestionRowStatusIcon(
            this.fieldStatuses,
            this.config.key,
            detail.eventId,
            detail.fieldKey
        );
    }

    getEventDetailQuestionRowStatusMessage(detail: FieldTranslationValue): string {
        return getEventDetailQuestionRowStatusMessage(
            this.fieldStatuses,
            this.config.key,
            detail.eventId,
            detail.fieldKey,
            this.i18nPipe,
            this.dfPipe,
            this.i18nPlaceholders,
            this.dateFormatPattern
        );
    }

    getEventDetailQuestionRowStatusMessageTitle(detail: FieldTranslationValue): string {
        return getEventDetailQuestionRowStatusMessageTitle(
            this.fieldStatuses,
            this.config.key,
            detail.eventId,
            detail.fieldKey,
            this.i18nPipe,
            this.i18nPlaceholders
        );
    }

    public canAddNewEvent(): boolean {
        return !(
            this.config.eventConfig &&
            this.config.eventConfig.maxNumberOfEvents &&
            this.addedEvents.length === this.config.eventConfig.maxNumberOfEvents
        );
    }

    /**
     * Setup listener for store answers and set button disabled state based on field statuses
     */
    private startModalValidityListener(): void {
        safeSelectFieldStatus(this.store, this.destroy$, this.config.key, null, null, true).subscribe(
            (fieldStatuses) => {
                let noErrors = true;
                let hasAcceptedTerminationError = false;
                this.fieldStatuses.clear();

                fieldStatuses.forEach((fieldStatus) => {
                    const belongsToActiveModal = fieldStatus.fieldKey.startsWith(
                        this.config.key + `.${this.activeEventModalId}`
                    );
                    noErrors = (noErrors && !belongsToActiveModal) || fieldStatus.accepted;
                    hasAcceptedTerminationError =
                        hasAcceptedTerminationError ||
                        (belongsToActiveModal &&
                            fieldStatus.statusType === FieldStatusType.TERMINATION_ERROR &&
                            fieldStatus.accepted);

                    setHighestPriorityStatus(this.fieldStatuses, fieldStatus);
                    const fieldStatusKey: string = fieldStatus.fieldKey;
                    EXTRA_FIELDS_POSTFIXES.forEach((extraFieldPostFix) => {
                        const possibleParentQuestionKey = fieldStatusKey.endsWith(extraFieldPostFix)
                            ? fieldStatusKey.substring(0, fieldStatusKey.lastIndexOf(extraFieldPostFix))
                            : null;
                        if (possibleParentQuestionKey) {
                            const parentStatus = { ...fieldStatus, fieldKey: possibleParentQuestionKey };
                            setHighestPriorityStatus(this.fieldStatuses, parentStatus);
                        }
                    });
                });

                const isValid = noErrors || hasAcceptedTerminationError;
                this.setModalActionsDisabledState(!isValid);
                this.isModalContentValid = isValid;
            }
        );
    }

    private validateEmptyValuesForActiveModalFields(): void {
        combineLatest([
            safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType),
            this.store.pipe(select(selectAllComponentStates)),
        ])
            .pipe(take(1))
            .subscribe(([events, componentStates]) => {
                const fieldStatuses = this.getModalFieldConfigs(this.activeEventModalId).flatMap((fieldConfig) => {
                    const { fieldKey } = getDestructuredSubArrayFields(fieldConfig.key);
                    const componentState =
                        componentStates &&
                        componentStates.find((cs: ViewComponentState) => {
                            return cs.id === fieldConfig.key;
                        });

                    return !componentState || componentState.visible || componentState.visible === undefined
                        ? checkFieldValidity(
                              { ...fieldConfig, key: fieldKey },
                              events.find((event) => {
                                  return event.index === this.activeEventModalId;
                              })
                          )
                        : [];
                });
                fieldStatuses.forEach((status) => {
                    this.store.dispatch(
                        new AddOrUpdateFieldStatus({
                            ...status,
                            fieldKey: `${this.config.key}.${this.activeEventModalId}.${status.fieldKey}`,
                        })
                    );
                });
            });
    }

    private setModalActionsDisabledState(value: boolean): void {
        if (this.modalRef) {
            if (this.modalRef.modal.primaryAction) {
                this.modalRef.modal.primaryAction.isDisabled = value;
            }
            if (this.modalRef.modal.secondaryAction) {
                this.modalRef.modal.secondaryAction.isDisabled = value;
            }
        }
    }

    private initTranslationKeys(): void {
        this.titleTranslationKey = this.config.labelKey;
        this.descriptionTranslationKey = this.getTranslationKeyFromPostfix(EventModalComponent.DESCRIPTION_POSTFIX);
        this.addEventButtonTranslationKey = this.getTranslationKeyFromPostfix(
            EventModalComponent.ADD_EVENT_BUTTON_POSTFIX
        );
        this.removeEventTranslationKey = this.getTranslationKeyFromPostfix(EventModalComponent.REMOVE_EVENT_POSTFIX);
        this.detailsHeaderTranslationKey = this.getTranslationKeyFromPostfix(
            EventModalComponent.DETAILS_HEADER_POSTFIX
        );
        this.eventTypeHeaderTranslationKey = this.getTranslationKeyFromPostfix(
            EventModalComponent.EVENT_TYPE_HEADER_POSTFIX
        );
    }

    private getModalOptions(eventId: number): Modal {
        return {
            appendToBody: true,
            header: this.i18nPipe.transform(this.addEventButtonTranslationKey, this.i18nPlaceholders),
            primaryAction: this.getModalActionForButton('primary', eventId),
            secondaryAction: this.getModalActionForButton('secondary', eventId),
            cancelAction: this.getModalActionForButton('cancel', eventId),
            closeAction: this.getModalActionForButton('cancel', eventId),
            backdropAction: this.getModalActionForButton('cancel', eventId),
            content: this.getModalFieldConfigs(eventId),
            childComponent: SubformComponent,
        };
    }

    private getModalActionForButton(buttonType: ModalActionButtonType, eventId: number): ModalAction {
        switch (buttonType) {
            case 'primary':
                return {
                    closeOnClick: false,
                    label: this.i18nPipe.transform(
                        this.getTranslationKeyFromPostfix(EventModalComponent.MODAL_SAVE_BUTTON_POSTFIX),
                        this.i18nPlaceholders
                    ),
                    onClick: () => {
                        return this.handleSave(eventId);
                    },
                    isDisabled: false,
                };
            case 'secondary':
                if (this.shouldHideSecondaryButton()) {
                    return null;
                }

                return {
                    closeOnClick: false,
                    label: this.i18nPipe.transform(
                        this.getTranslationKeyFromPostfix(EventModalComponent.MODAL_ADD_ANOTHER_BUTTON_POSTFIX),
                        this.i18nPlaceholders
                    ),
                    onClick: () => {
                        return this.handleAddAnother(eventId);
                    },
                    isDisabled: false,
                };
            case 'cancel':
                return {
                    closeOnClick: true,
                    label: this.i18nPipe.transform(
                        this.getTranslationKeyFromPostfix(EventModalComponent.MODAL_CANCEL_BUTTON_POSTFIX),
                        this.i18nPlaceholders
                    ),
                    onClick: () => {
                        return this.handleCancel();
                    },
                };
            default:
                console.error('Invalid button type supplied');
                return null;
        }
    }

    private getTranslationKeyFromPostfix(postfix: string) {
        const baseKey = this.config.labelKey.replace('.label', '.');
        return baseKey + postfix + '.label';
    }

    private getModalFieldConfigs(eventId: number): FormFieldConfig[] {
        return this.config.children.map((config) => {
            return {
                ...config,
                formKey: this.config.formKey,
                formType: this.config.formType,
                key: `${this.config.key}.${eventId}.${config.key}`,
            };
        });
    }

    private updateEventTranslations(eventId: number): void {
        const fieldConfigs = this.getModalFieldConfigs(eventId);
        const answerTranslations = fieldConfigs.map((fieldConfig) => {
            return this.fieldValueTranslatorService.getTranslationForFieldValue(fieldConfig);
        });
        combineLatest(answerTranslations).subscribe((at) => {
            const translations = at.reduce((acc, translation, index) => {
                const keys = getDestructuredSubArrayFields(fieldConfigs[index].key);
                return {
                    ...acc,
                    [keys.fieldKey]: {
                        answer: translation,
                        question: this.i18nPipe.transform(fieldConfigs[index].labelKey, this.i18nPlaceholders),
                        fieldKey: keys.fieldKey,
                        eventId,
                    },
                };
            }, {});
            this.eventTranslations.set(eventId, translations);
        });
    }

    /**
     * These are defined as arrow functions so they bind 'this' when passed as callback
     */
    private handleAddAnother = (eventId: number): void => {
        this.handleSave(eventId).then((wasSaveSuccessful: boolean) => {
            if (wasSaveSuccessful) {
                this.openEventModal(this.getNextEventId());
            }
        });
    };

    private handleSave = (eventId: number): Promise<boolean> =>
        // Give some delay here to give fields time to trigger errors
        {
            return new Promise((resolve) => {
                return setTimeout(resolve, 200);
            }).then(() => {
                this.validateEmptyValuesForActiveModalFields();
                return safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType)
                    .pipe(take(1))
                    .toPromise()
                    .then((events: Answer[]) => {
                        if (this.config.enableFieldStatus && !this.isModalContentValid) {
                            return false;
                        }
                        const cleanedEvents = events.map((event) => {
                            return omit(event, 'isActive');
                        });
                        this.addedEvents = cleanedEvents;
                        this.store.dispatch(
                            new AddAnswer(this.fieldService.getAnswer(this.config, this.addedEvents, false))
                        );
                        this.updateEventTranslations(eventId);
                        this.modalRef.closeModal();
                        this.cd.markForCheck();
                        return true;
                    });
            });
        };

    // Clear the intermediate answers by restoring the previous local state
    private handleCancel = (): void => {
        // clear also "fieldmissing" statuses with generic message
        for (const item of this.config.children) {
            this.clearStatusForGivenKey(`${this.config.key}.${this.activeEventModalId}.${item.key}`);
            if (
                [
                    FormItemType.YES_NO_DATE,
                    FormItemType.RADIO_WITH_EXTRA_FIELDS,
                    FormItemType.CHECKBOX_WITH_EXTRA_FIELDS,
                ].includes(item.type)
            ) {
                this.clearStatusForGivenKey(`${this.config.key}.${this.activeEventModalId}.${item.key}Date`);
                this.clearStatusForGivenKey(`${this.config.key}.${this.activeEventModalId}.${item.key}Text`);
            }
        }
        // Reset field statuses from before
        this.preOpenModalFieldStatuses.forEach((fs: FieldStatus) => {
            this.store.dispatch(new AddOrUpdateFieldStatus(fs));
        });
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, this.addedEvents, false)));
    };

    private clearHiddenFieldsErrorsOnVisibiltyChanges() {
        this.store.pipe(select(selectAllComponentStates), takeUntil(this.destroy$)).subscribe((componentStates) => {
            if (componentStates) {
                componentStates.forEach((cs: ViewComponentState) => {
                    if (cs.visible === false) {
                        this.clearStatusForGivenKey(cs.id);
                    }
                });
            }
        });
    }

    private restoreAnswersOnVisibilityChange(): void {
        this.store
            .pipe(select(selectComponentState(this.config.key)), takeUntil(this.destroy$))
            .subscribe((componentState) => {
                if (componentState && componentState.visible === true) {
                    this.store.dispatch(
                        new AddAnswer(this.fieldService.getAnswer(this.config, this.addedEvents, false))
                    );
                }
            });
    }

    private removeAllFieldStatusesForEvent(eventId: number) {
        this.store.dispatch(new RemoveAllFieldStatusesWithKeyIncluding(`${this.config.key}.${eventId}`));
    }

    private shouldHideSecondaryButton(): boolean {
        return (
            this.config.eventConfig &&
            this.config.eventConfig.maxNumberOfEvents &&
            this.addedEvents.length + 1 >= this.config.eventConfig.maxNumberOfEvents
        );
    }
}
