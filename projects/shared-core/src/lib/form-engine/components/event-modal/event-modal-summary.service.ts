import { Injectable } from '@angular/core';
import { FieldStatus } from '../../models/field-status.interface';
import {
    getFieldStatusIcon,
    getFieldStatusMessageTitleTranslation,
    getFieldStatusMessageTranslation,
} from '../../services/field-status.service';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { Dictionary } from '../../../common-types';
import { DateFormatPipe } from '../../../pipes/date-format.pipe';

export function getEventDetailQuestionStatus(
    fieldStatues: Map<string, FieldStatus>,
    fieldKey: string,
    eventIndex: string,
    detailKey: string
) {
    return fieldStatues.get(fieldKey + '.' + eventIndex + '.' + detailKey);
}

export function getEventDetailQuestionRowStatusIcon(
    fieldStatues: Map<string, FieldStatus>,
    fieldKey: string,
    eventIndex: string,
    detailKey: string
): string {
    const questionStatus = getEventDetailQuestionStatus(fieldStatues, fieldKey, eventIndex, detailKey);
    return getFieldStatusIcon(questionStatus);
}

export function getEventDetailQuestionRowStatusMessage(
    fieldStatues: Map<string, FieldStatus>,
    fieldKey: string,
    eventIndex: string,
    detailKey: string,
    i18nPipe: I18NPipe,
    dfPipe: DateFormatPipe,
    placeHoldersTranslationValues: Dictionary<string, string>,
    dateFormatPattern: string
): string {
    const questionStatus = getEventDetailQuestionStatus(fieldStatues, fieldKey, eventIndex, detailKey);
    return getFieldStatusMessageTranslation(
        questionStatus,
        i18nPipe,
        dfPipe,
        placeHoldersTranslationValues,
        dateFormatPattern
    );
}

export function getEventDetailQuestionRowStatusMessageTitle(
    fieldStatues: Map<string, FieldStatus>,
    fieldKey: string,
    eventIndex: string,
    detailKey: string,
    i18nPipe: I18NPipe,
    placeHoldersTranslationValues: Dictionary<string, string>
): string {
    const questionStatus = getEventDetailQuestionStatus(fieldStatues, fieldKey, eventIndex, detailKey);
    return getFieldStatusMessageTitleTranslation(questionStatus, i18nPipe, placeHoldersTranslationValues);
}
