import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { FormField } from '../../models/form-field.interface';
import { FormVariablesService } from '../../services/form-variables.service';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputFieldWithVarSupport } from '../form-input-field-variable-support.component';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { FormFieldConfig } from '../../models/form-field-config.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-form-question',
    templateUrl: './form-question.component.html',
    styleUrls: ['./form-question.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormQuestionComponent extends FormInputFieldWithVarSupport implements FormField, OnInit {
    @Input()
    public titleTranslationKey: string;

    @Input()
    public descriptionTranslationKey: string;

    @Input()
    public config: FormFieldConfig;

    constructor(
        protected store: Store<FormEngineState>,
        cd: ChangeDetectorRef,
        protected formVariablesService: FormVariablesService,
        private i18nPipe: I18NPipe
    ) {
        super(cd, formVariablesService);
    }

    set visibility(visibility: boolean) {}

    public isDescriptionTranslationValid(): boolean {
        return (
            this.descriptionTranslationKey &&
            this.i18nPipe.transform(this.descriptionTranslationKey, this.i18nPlaceholders) !==
                this.descriptionTranslationKey
        );
    }
}
