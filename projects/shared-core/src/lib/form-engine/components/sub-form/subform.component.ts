import { ChangeDetectorRef, Component } from '@angular/core';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { Modal, ModalInit } from '../../../ds/components/modal/modal.component';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-subform',
    templateUrl: './subform.component.html',
})
export class SubformComponent implements ModalInit {
    public fields: FormFieldConfig[];

    constructor(private cd: ChangeDetectorRef) {}

    public modalInit(modal: Modal) {
        this.fields = modal.content;
        this.cd.detectChanges();
    }
}
