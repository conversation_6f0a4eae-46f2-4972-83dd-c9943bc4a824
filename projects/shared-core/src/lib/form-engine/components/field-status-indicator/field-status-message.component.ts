import { ChangeDetectorRef, Component, Input, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { ConfigurationProviderService } from '../../../abstract-services/configuration-provider.service';
import { Dictionary } from '../../../common-types';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { FormVariablesService } from '../../services/form-variables.service';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputFieldWithVarSupport } from '../form-input-field-variable-support.component';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'field-status-message',
    templateUrl: './field-status-message.component.html',
})
export class FieldStatusMessageComponent extends FormInputFieldWithVarSupport {
    @Input()
    public fieldStatus: FieldStatus;

    @Output()
    public acceptedChanged = new Subject<boolean>();

    public dateFormatPattern = 'MM/dd/yyyy';

    constructor(
        protected store: Store<FormEngineState>,
        private cfgSrv: ConfigurationProviderService,
        cd: ChangeDetectorRef,
        protected fvs: FormVariablesService,
        private i18nPipe: I18NPipe
    ) {
        super(cd, fvs);
        this.cfgSrv.dateFormattingPattern().subscribe((dfp) => {
            this.dateFormatPattern = dfp.longDatePattern;
        });
    }

    public FieldStatusTypes = FieldStatusType;

    private parseFieldStatusMeta(): Dictionary<string, string> {
        const dict = {};
        if (this.fieldStatus && this.fieldStatus.meta) {
            const splitted1 = this.fieldStatus.meta.split(',');
            splitted1.forEach((s) => {
                const splitted2 = s.split(':');
                if (splitted2.length === 2) {
                    dict[splitted2[0]] = splitted2[1];
                }
            });
        }
        return dict;
    }

    public getSoftErrorMessage(): string {
        let questionStatusTranslation = '';
        if (this.fieldStatus) {
            const specialKey =
                this.fieldStatus.translationKey + (this.fieldStatus.accepted ? '.accepted' : '.unaccepted');
            questionStatusTranslation = this.i18nPipe.transform(specialKey, this.getPlaceholder());
            if (questionStatusTranslation === specialKey) {
                questionStatusTranslation = this.i18nPipe.transform(
                    this.fieldStatus.translationKey,
                    this.getPlaceholder()
                );
            }
        }
        return questionStatusTranslation;
    }

    public getPlaceholder(): Dictionary<string, string> {
        const i18nPlaceholders = this.i18nPlaceholders ? this.i18nPlaceholders : {};
        const fieldStatusMeta = this.parseFieldStatusMeta();
        return {
            ...this.i18nPlaceholders,
            ...fieldStatusMeta,
        };
    }

    public toggleErrorAcceptanceCheckbox() {
        this.acceptedChanged.next(!this.fieldStatus.accepted);
    }
}
