@import '/projects/shared-core/src/styles/ds-variables.scss';
:host:last-of-type {
  .field-status-message-container {
    margin-bottom: $spacing-l;
  }
}
.caret-up {
  display: inline-block;
  border-width: 8px;
  border-bottom-style: solid;
  border-left-style: solid;
  border-right-style: solid;
  border-right-color: transparent !important;
  border-left-color: transparent !important;
  margin-left: $spacing-m;
  width: 0;
  height: 0;
}
.soft-error-message-container {
  .caret-up {
    border-color: $color-brand-2-lighten-4;
  }
  .soft-error-message-content {
    background-color: $color-brand-2-lighten-4;
    color: $color-brand-2-darken-2;
    .error-text {
      color: $color-brand-2-darken-2;
    }
    .soft-error-message-acceptedby {
      font-style: italic;
    }
  }
}
.hard-error-message-container {
  .caret-up {
    border-color: $color-brand-3-lighten-4;
  }
  .hard-error-message-content {
    background-color: $color-brand-3-lighten-4;
    color: $color-brand-3-darken-3;
    .error-text {
      color: $color-brand-3-darken-3;
    }
  }
}
.termination-error-message-container {
  .caret-up {
    border-color: $color-brand-1-lighten-4;
  }
  .termination-error-message-content {
    background-color: $color-brand-1-lighten-4;
    color: $color-brand-1-darken-2;
    .error-text {
      color: $color-brand-1-darken-2;
    }
  }
}
.not-ready-message-container {
  .caret-up {
    border-color: var(--form-element-background-color-disabled);
  }
  .not-ready-message-content {
    background-color: var(--form-element-background-color-disabled);
  }
}
.neg-mt-l {
  margin-top: -2.4rem;
}
