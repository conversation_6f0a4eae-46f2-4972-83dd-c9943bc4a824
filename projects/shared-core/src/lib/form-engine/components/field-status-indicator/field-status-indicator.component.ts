import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ConfigurationProviderService } from '../../../abstract-services/configuration-provider.service';
import { User } from '../../../generated/models/user';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { safeSelectFieldStatus } from '../../services/subscription-helper';
import { AddOrUpdateFieldStatus } from '../../store/actions/field-status.actions';
import { FormEngineState } from '../../store/reducers/state';

/*
 * This component has some overlapping with the FormErrorIndicatorComponent.
 * However, it is used for manual error / warning / query handling.
 * To avoid side effects it has been placed in a own component instead of extending the FormErrorIndicatorComponent
 * and making it more configurable.
 *
 * Could be refactored later depending on how the component evolves.
 */
@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-field-status-indicator',
    templateUrl: './field-status-indicator.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FieldStatusIndicatorComponent implements OnInit, OnDestroy {
    @Input()
    public config: FormFieldConfig;

    private destroy$ = new Subject<boolean>();
    public fieldStatuses: FieldStatus[];
    private activeUser: User;
    private readonly ACCEPTABLE_FIELD_STATUSES = [FieldStatusType.TERMINATION_ERROR, FieldStatusType.SOFT_ERROR];

    constructor(
        private store: Store<FormEngineState>,
        private cd: ChangeDetectorRef,
        private confProvider: ConfigurationProviderService
    ) {}

    ngOnInit() {
        safeSelectFieldStatus(this.store, this.destroy$, this.config.key).subscribe((fieldStatuses: FieldStatus[]) => {
            this.updateFieldStatuses(fieldStatuses);
        });
        this.confProvider
            .activeUser()
            .pipe(takeUntil(this.destroy$))
            .subscribe((user: User) => {
                this.activeUser = user;
            });
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public handleAcceptedChanged(accepted: boolean, fieldStatus: FieldStatus) {
        if (this.activeUser && this.activeUser.userId) {
            if (this.ACCEPTABLE_FIELD_STATUSES.includes(fieldStatus.statusType)) {
                this.store.dispatch(
                    new AddOrUpdateFieldStatus({
                        ...fieldStatus,
                        lastModified: new Date(),
                        accepted,
                        userId: this.activeUser.userId,
                        userName: this.activeUser.fullName,
                    })
                );
            }
        }
    }

    private updateFieldStatuses(fieldStatuses: FieldStatus[]) {
        this.fieldStatuses = fieldStatuses;
        this.cd.markForCheck();
    }
}
