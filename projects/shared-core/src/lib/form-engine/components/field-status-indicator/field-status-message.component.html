<div
    *ngIf="fieldStatus.statusType == FieldStatusTypes.SOFT_ERROR"
    [className]="
        'field-status-message-container soft-error-message-container flex flex--column ' +
        (fieldStatus && !fieldStatus.accepted ? 'field-status-message-container-unaccepted' : '')
    "
>
    <div class="caret-up"></div>
    <div class="soft-error-message-content flex flex--column pl-m pr-m pb-s pt-s">
        <ds-checkbox (checkboxChange)="toggleErrorAcceptanceCheckbox()" [checked]="fieldStatus.accepted">
            <span class="error-text">{{ getSoftErrorMessage() }}</span>
        </ds-checkbox>
        <div *ngIf="fieldStatus.accepted" class="last-modified pl-xl">
            <span class="last-modified__name">{{ fieldStatus.userName }} - </span>
            <span class="last-modified__date">{{ fieldStatus.lastModified | nhDateFormat: dateFormatPattern }}</span>
        </div>
    </div>
</div>

<div
    *ngIf="
        fieldStatus.statusType == FieldStatusTypes.HARD_ERROR ||
        fieldStatus.statusType == FieldStatusTypes.MISSING_VALUE
    "
    [className]="
        'field-status-message-container hard-error-message-container flex flex--column ' +
        (fieldStatus && !fieldStatus.accepted ? 'field-status-message-container-unaccepted' : '')
    "
>
    <div class="caret-up"></div>
    <div class="hard-error-message-content flex pl-m pr-m pb-s pt-s">
        <ds-icon name="icon-hard-error" size="16"></ds-icon>
        <span class="error-text ml-xs"> {{ fieldStatus.translationKey | i18n: getPlaceholder() }}</span>
    </div>
</div>

<div
    *ngIf="fieldStatus.statusType == FieldStatusTypes.TERMINATION_ERROR"
    [className]="
        'field-status-message-container termination-error-message-container flex flex--column ' +
        (fieldStatus && !fieldStatus.accepted ? 'field-status-message-container-unaccepted' : '')
    "
>
    <div class="caret-up"></div>
    <div class="termination-error-message-content flex flex--column pl-m pr-m pb-s pt-s">
        <ds-checkbox (checkboxChange)="toggleErrorAcceptanceCheckbox()" [checked]="fieldStatus.accepted">
            <div class="error-text">
                <strong class="termination-error__title">Failed eligibility criteria</strong><br />
                <span>{{ fieldStatus.translationKey | i18n: getPlaceholder() }}</span>
            </div>
        </ds-checkbox>
        <div *ngIf="fieldStatus.accepted" class="last-modified pl-xl">
            <span class="last-modified__name">{{ fieldStatus.userName }} - </span>
            <span class="last-modified__date">{{ fieldStatus.lastModified | nhDateFormat: dateFormatPattern }}</span>
        </div>
    </div>
</div>

<div
    *ngIf="fieldStatus.statusType == FieldStatusTypes.NOT_READY"
    [className]="
        'field-status-message-container not-ready-message-container flex flex--column ' +
        (fieldStatus && !fieldStatus.accepted ? 'field-status-message-container-unaccepted' : '')
    "
>
    <div class="caret-up"></div>
    <div class="not-ready-message-content flex pl-m pr-m pb-s pt-s">
        {{ fieldStatus.translationKey | i18n: getPlaceholder() }}
    </div>
</div>
