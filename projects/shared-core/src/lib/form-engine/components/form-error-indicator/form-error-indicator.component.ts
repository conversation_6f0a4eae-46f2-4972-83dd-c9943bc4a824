import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ERROR_POSITION } from '../../../constants';
import { CapitalFirstLetterPipe } from '../../../pipes/capital-first-letter.pipe';
import { FormValidator } from '../../form-validator/form-validator';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FieldService } from '../../services/field.service';
import { SetFieldValidity } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { safeSelectField } from '../../services/subscription-helper';
import { IsFormValidEvent } from './form-error-indicator.interface';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-form-error-indicator',
    templateUrl: './form-error-indicator.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormErrorIndicatorComponent implements OnInit, OnDestroy {
    @Input()
    public config: FormFieldConfig;

    @Input()
    public other: boolean;

    @Input()
    public position: ERROR_POSITION = ERROR_POSITION.TOP;

    public validator: FormValidator;
    public otherValidator: FormValidator;
    public errorFields: string[];
    public otherErrorFields: string[];

    @Output() isFormValid: EventEmitter<IsFormValidEvent> = new EventEmitter<IsFormValidEvent>();

    private destroy$ = new Subject<boolean>();
    private currentValue: any;
    private otherValue: string;
    private otherKey = '';

    private formGroup: UntypedFormGroup;
    private otherFormGroup: UntypedFormGroup;

    constructor(
        private fieldService: FieldService,
        private store: Store<FormEngineState>,
        private cd: ChangeDetectorRef,
        private capital: CapitalFirstLetterPipe
    ) {}

    ngOnInit() {
        let firstEmit = true;
        let firstOtherEmit = true;
        const validators = this.fieldService.getValidators(this.config);
        if (validators.length > 0) {
            this.formGroup = new UntypedFormGroup({
                [this.config.key]: new UntypedFormControl('', validators),
            });

            this.validator = new FormValidator(this.formGroup);

            safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((value) => {
                this.currentValue = value;
                if (value !== undefined) {
                    if (firstEmit) {
                        firstEmit = false;
                        this.validate(value, value !== null);
                    } else {
                        this.validate(value, true);
                    }
                }
            });
        }

        if (this.other) {
            this.otherKey = `other${this.capital.transform(this.config.key)}`;
            safeSelectField(this.store, this.destroy$, this.otherKey, this.config.formType).subscribe((value) => {
                this.otherValue = value;
                if (firstOtherEmit && !value) {
                    return;
                }
                firstOtherEmit = false;
                this.validate(this.currentValue, true);
            });

            this.otherFormGroup = new UntypedFormGroup({
                [this.otherKey]: new UntypedFormControl('', [Validators.maxLength(2000), Validators.minLength(0)]),
            });
            this.otherValidator = new FormValidator(this.otherFormGroup);
        }

        this.fieldService
            .listenForValidate()
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
                if (this.formGroup) {
                    this.formGroup.markAsDirty();
                }

                if (this.otherFormGroup) {
                    this.otherFormGroup.markAsDirty();
                }

                this.validate(this.currentValue, true);
            });
    }

    ngOnDestroy() {
        this.dispatchSetFormValidity(true);
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    private validate(value: any, setData: boolean) {
        if (!this.validator) {
            return;
        }

        this.setData(value, setData);

        let valid = this.validator.valid;
        if (!this.config.required) {
            valid = valid || !value || value.length < 1;
        }

        if (this.other && this.otherValidator && !this.otherValidator.valid) {
            valid = false;
        }

        this.dispatchSetFormValidity(valid);

        if (this.formGroup?.dirty) {
            this.errorFields = Object.keys(this.validator.errors);
        }

        if (this.other && this.otherValidator && this.otherFormGroup && this.otherFormGroup.dirty) {
            this.otherErrorFields = Object.keys(this.otherValidator.errors);
        }

        this.emitIsFormValid();
        this.cd.markForCheck();
    }

    private setData(value: any, setData: boolean): void {
        if (setData) {
            this.validator.setFormData({
                [this.config.key]: value,
            });

            if (this.other && this.otherValidator) {
                this.otherValidator.setFormData({
                    [this.otherKey]: this.otherValue,
                });

                if (!value || value.length < 1) {
                    this.validator.setFormData({
                        [this.config.key]: this.otherValue,
                    });
                }
            }
        }
    }

    private dispatchSetFormValidity(valid: boolean): void {
        this.store.dispatch(
            new SetFieldValidity({
                formType: this.config.formType,
                field: this.config.key,
                valid,
            })
        );
    }

    private emitIsFormValid(): void {
        this.isFormValid.emit({
            valid: this.formGroup.valid,
            errors: this.formGroup.errors,
            isDirty: this.formGroup.dirty,
            formGroup: this.formGroup,
        });
    }
}
