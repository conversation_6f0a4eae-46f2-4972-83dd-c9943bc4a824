import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { FormErrorIndicatorComponent } from './form-error-indicator.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('FormErrorIndicatorComponent', () => {
    let component: FormErrorIndicatorComponent;
    let fixture: ComponentFixture<FormErrorIndicatorComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [FormErrorIndicatorComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(FormErrorIndicatorComponent);
        component = fixture.componentInstance;
        component.config = { key: 'phq', labelKey: 'test.label', formType: QuestionaryType.BOUNCE_HADS, children: [] };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
