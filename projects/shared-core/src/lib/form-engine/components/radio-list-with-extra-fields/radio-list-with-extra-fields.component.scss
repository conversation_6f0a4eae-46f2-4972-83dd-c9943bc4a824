@import '../../../../styles/deprecated_variables.scss';
@import '../../../../styles/ds-variables.scss';

$border: thin solid lightgrey;

nh-form-question {
  margin-bottom: $spacing-l;
  :host.horizontal & {
    flex: 0 0 30%;
    margin-bottom: 0;
    margin-right: $spacing-l;
  }
}
.radio-list-with-extra-fields > nh-form-error-indicator {
  margin-top: $spacing-s;
  :host.horizontal & {
    margin-top: 0;
    order: 1;
    flex: 0 1 100%;
  }
}
.radio-list-with-extra-fields > nh-field-status-indicator {
  margin-top: $spacing-s;
  :host.horizontal & {
    margin-top: 0;
    order: 1;
    flex: 0 1 100%;
  }
}
:root {
  display: block;
}
:host.horizontal {
  border: $border;
  border-bottom: none;
  background-color: white;
  &:first-of-type {
    border-top-left-radius: $border-radius-md;
    border-top-right-radius: $border-radius-md;
  }
  &:last-of-type {
    border-bottom-right-radius: $border-radius-md;
    border-bottom-left-radius: $border-radius-md;
    border-bottom: $border;
  }
}
.radio {
  flex: 1;
  :host.horizontal & {
    margin-right: $spacing-l;
    min-width: 0;
    &:last-of-type {
      margin-right: 0;
    }
  }
}
.radio-list-with-extra-fields {
  display: flex;
  flex-direction: column;
  :host.horizontal & {
    flex-direction: row;
    flex-wrap: wrap;
    padding: $spacing-l;
  }
}
.radio-list-with-extra-fields__options {
  display: flex;
  flex-direction: column;
  :host.horizontal & {
    flex: 1 1 calc(70% - calc(2 * var(--spacing-l)));
    flex-direction: row;
    min-width: calc(70% - calc(2 * var(--spacing-l)));
  }
}
.text-input {
  max-width: 500px;
}
.spacing {
  margin-right: $spacing-xs;
}
.extra-field-wrapper {
  width: 207px;
}
.flex--2 {
  flex: 2;
}
