<div *ngIf="componentVisible()" class="radio-list-with-extra-fields form-element-container {{ getWrapperExtraClasses().join(' ') }}">
  <nh-form-question
    [descriptionTranslationKey]="descriptionTranslationKey"
    [titleTranslationKey]="titleTranslationKey"
    [config]="getQuestionHeaderConfig()"
  ></nh-form-question>
  <nh-form-error-indicator [config]="config"></nh-form-error-indicator>
  <nh-field-status-indicator [config]="config"></nh-field-status-indicator>
  <div *ngIf="enabled" class="radio-list-with-extra-fields__options">
    <div *ngFor="let option of options; let i = index" class="radio" [class.flex--2]="i === 0">
      <input
        [id]="option.id"
        [checked]="option.value === selectedOption"
        class="form-control"
        type="radio"
        (change)="setSelectedOption(option.value)"
      />
      <label [for]="option.id">{{ option.translationKey | i18n }}</label>

      <ng-container [ngSwitch]="getExtraFieldType(option)">
        <ng-container *ngSwitchCase="'DATE'">
          <div class="extra-field-wrapper date-picker pb-s pl-l">
            <nh-simple-date-input
              #simpleDateInput
              [inputId]="extraFieldInputId(option.value, 'DATE')"
              [dateFormat]="dateFormat"
              [dateValue]="option.value === selectedOption ? selectedDate : null"
              [maxDate]="getExtraDateFieldMaxDate(option)"
              (valueUpdate)="setSelectedOptionAndDate(option.value, $event)"
              (focusLost)="simpleDateFocusLost(option.value, $event)"
              (clicked)="setSelectedOptionAndDate(option.value, $event)"
              [configKey]="dateFieldConfig.key"
              [unknownDayDefault]="config.unknownDayDefault"
              [allowUnknownDate]="config.allowUnknownDate"
            ></nh-simple-date-input>
          </div>
          <div *ngIf="option.value === selectedOption" class="pb-s pl-l">
            <nh-field-status-indicator [config]="this.dateFieldConfig"></nh-field-status-indicator>
          </div>
        </ng-container>

        <ng-container *ngSwitchCase="'TEXT'">
          <div class="text-input pb-s pl-l">
            <input
              type="text"
              id="{{ option.value + textIdPostfix }}"
              (ngModelChange)="setSelectedOptionAndInput(option.value, $event)"
              [ngModel]="textInput"
              [placeholder]="getPlaceholder(option) | i18n"
              (blur)="checkMissingExtraTextValue(option.value)"
              (click)="setSelectedOptionAndInput(option.value, textInput)"
            />
          </div>
          <div *ngIf="option.value === selectedOption" class="pb-s pl-l">
            <nh-field-status-indicator [config]="this.textFieldConfig"></nh-field-status-indicator>
          </div>
        </ng-container>

        <ng-container *ngSwitchCase="'DROPDOWN'">
          <div class="text-input pl-l">
            <ng-select
              class="ds-dropdown"
              [id]="extraFieldInputId(option.value, 'DROPDOWN')"
              (change)="setSelectedOptionAndDropdownValue(option.value, $event)"
              [ngModel]="selectedDropdownOption"
              [clearable]="false"
              [items]="dropdownOptions"
              [placeholder]="getPlaceholder(option) | i18n"
              (blur)="checkMissingExtraDropdownValue()"
              (click)="setSelectedOptionAndDropdown(option.value)"
            ></ng-select>
          </div>
          <div *ngIf="option.value === selectedOption" class="pb-s pl-l mt-xs">
            <nh-field-status-indicator [config]="this.dropdownFieldConfig"></nh-field-status-indicator>
          </div>
        </ng-container>

        <ng-container *ngSwitchCase="'NUMERIC_FIELD'">
          <div class="text-input pl-l">
            <nh-numeric-field
              [inputId]="extraFieldInputId(option.value, 'NUMERIC_FIELD')"
              [config]="numericFieldConfig"
              [visible]="true"
              (click)="setSelectedOption(option.value, true, true, true, false)"
            ></nh-numeric-field>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
</div>
