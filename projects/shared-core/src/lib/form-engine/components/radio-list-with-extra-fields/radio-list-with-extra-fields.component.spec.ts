import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { RadioListWithExtraFieldsComponent } from './radio-list-with-extra-fields.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../../generated/models/questionary-type';

describe('RadioListWithExtraFields', () => {
    let component: RadioListWithExtraFieldsComponent;
    let fixture: ComponentFixture<RadioListWithExtraFieldsComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            declarations: [RadioListWithExtraFieldsComponent],
            imports: [MocksModule],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(RadioListWithExtraFieldsComponent);
        component = fixture.componentInstance;
        component.config = {
            key: 'test',
            labelKey: 'test.label',
            formType: QuestionaryType.BOUNCE_HADS,
            enableFieldStatus: true,
            extraWrapperClasses: ['test'],
            optionsWithExtraField: [
                {
                    key: 'list',
                    type: 'DROPDOWN',
                    allowFutureDate: false,
                    valuesKey: 'dd',
                },
            ],
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
