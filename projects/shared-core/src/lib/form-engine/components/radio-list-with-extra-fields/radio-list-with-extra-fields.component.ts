import {
    ChangeDetectionStrategy,
    ChangeDetector<PERSON>ef,
    Component,
    HostBinding,
    Input,
    On<PERSON><PERSON>roy,
    OnInit,
    QueryList,
    ViewChildren,
} from '@angular/core';
import { Store } from '@ngrx/store';
import moment, { Moment } from 'moment';
import { Subject } from 'rxjs';
import { ConfigurationProviderService } from '../../../abstract-services/configuration-provider.service';
import { Dictionary } from '../../../common-types';
import { FormItemType } from '../../../generated/models/form-item-type';
import { FieldStatus, FieldStatusType } from '../../models/field-status.interface';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { ModelValue } from '../../models/model-value.interface';
import { FieldService } from '../../services/field.service';
import { FormVariablesService } from '../../services/form-variables.service';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { FormInputField } from '../form-input-field.component';
import { SimpleDateInputComponent, SimpleDateInputValue } from '../simple-date-input/simple-date-input.component';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { NgSelectComponent } from '@ng-select/ng-select';
import { safeSelectField, safeSelectFieldStatus } from '../../services/subscription-helper';
import { UpdateComponentState } from '../../store/actions/component-state.action';
import has from 'lodash/has';
import difference from 'lodash/difference';
import { ListEntry } from '../../../generated/models/list-entry';
import { NumericFieldComponent } from '../numeric-field/numeric-field.component';
import * as uuid from 'uuid';

interface SelectOption {
    id?: string;
    label: string;
}

interface RadioListWithExtraFieldsConfig extends FormFieldConfig {
    extraWrapperClasses: string[];
}

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-radio-list-with-extra-fields',
    templateUrl: './radio-list-with-extra-fields.component.html',
    styleUrls: ['./radio-list-with-extra-fields.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RadioListWithExtraFieldsComponent extends FormInputField implements OnInit, FormField, OnDestroy {
    public static readonly DATE_FIELD_CONFIG_POSTFIX = 'Date';
    public static readonly TEXT_FIELD_CONFIG_POSTFIX = 'Text';
    public static readonly DROPDOWN_FIELD_CONFIG_POSTFIX = 'Dropdown';
    public static readonly NUMERIC_FIELD_CONFIG_POSTIFX = 'Value';

    public static readonly DESCRIPTION_POSTFIX = 'description';
    public static readonly EXTRA_POSTFIX = 'description';
    @ViewChildren(SimpleDateInputComponent) simpleDateInputs: QueryList<SimpleDateInputComponent>;
    @ViewChildren(NgSelectComponent) dropdownInputs: QueryList<NgSelectComponent>;
    @ViewChildren(NumericFieldComponent) numericFieldInputs: QueryList<NumericFieldComponent>;

    @Input() public config: RadioListWithExtraFieldsConfig;
    @Input() public visible: boolean;

    @HostBinding('class') get hostClasses() {
        if (this.config.horizontal && this.visible) {
            return 'horizontal';
        }
    }

    public titleTranslationKey: string;
    public descriptionTranslationKey: string;
    public dropdownPlaceholderTranslationKey: string;
    public dateFormat: string;
    public options: ModelValue[];
    public selectedOption: string;
    public selectedDate: Date = null;
    public textInput: string;
    public dropdownOptions: SelectOption[];
    public selectedDropdownOption: SelectOption;
    public fieldStatusControlledVisibilty = true;
    public enabled = true;
    public dateFieldConfig: FormFieldConfig;
    public textFieldConfig: FormFieldConfig;
    public dropdownFieldConfig: FormFieldConfig;
    public numericFieldConfig: FormFieldConfig;
    public textIdPostfix = 'text' + uuid.v4();

    private destroy$ = new Subject<boolean>();

    public unknownDayDefault = null;

    public i18nPlaceholders: Dictionary<string, string> = {};

    constructor(
        protected store: Store<FormEngineState>,
        protected cd: ChangeDetectorRef,
        private fieldService: FieldService,
        private configService: ConfigurationProviderService,
        private fvs: FormVariablesService,
        private i18NPipe: I18NPipe
    ) {
        super(cd);
    }

    public ngOnInit() {
        if (this.config.enableFieldStatus) {
            if (this.config.questionGroupId) {
                safeSelectFieldStatus(
                    this.store,
                    this.destroy$,
                    this.config.questionGroupId,
                    FieldStatusType.NOT_READY
                ).subscribe((fieldStatuses: FieldStatus[]) => {
                    this.fieldStatusControlledVisibilty = !fieldStatuses || fieldStatuses.length === 0;
                    this.cd.markForCheck();
                });
            } else {
                safeSelectFieldStatus(this.store, this.destroy$, this.config.key, FieldStatusType.NOT_READY).subscribe(
                    (fieldStatuses: FieldStatus[]) => {
                        this.enabled = !fieldStatuses || fieldStatuses.length === 0;
                        this.cd.markForCheck();
                    }
                );
            }
        }

        this.initFieldConfigs();
        this.initDatePickerConfig();
        this.initTranslationKeys();

        if (this.config.optionsProviderFieldKeys && this.config.optionsProviderFieldKeys.length > 0) {
            const keysOptionsMap = new Map();
            this.config.optionsProviderFieldKeys.forEach((key) => {
                safeSelectField(this.store, this.destroy$, key, this.config.formType).subscribe((keyOptions: any[]) => {
                    const optionsToAdd = keyOptions && keyOptions.length > 0 ? keyOptions : [];
                    keysOptionsMap.set(key, optionsToAdd);
                    let combinedOptions = [];
                    keysOptionsMap.forEach((options: string[], optionskey: string) => {
                        combinedOptions = [...combinedOptions, ...options];
                    });
                    this.updateWithProviderOptions(combinedOptions);
                });
            });
        } else {
            this.options = this.fieldService.getListValues(this.config);
        }

        this.dropdownOptions = this.fieldService.getListValues(this.dropdownFieldConfig).map((option) => {
            return {
                id: option.value,
                label: this.i18NPipe.transform(option.translationKey),
            };
        });

        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, null, true)));
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.dateFieldConfig, null, true)));
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.textFieldConfig, '', true)));
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.dropdownFieldConfig, null, true)));
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.numericFieldConfig, null, true)));

        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((value) => {
            return (this.selectedOption = value);
        });

        safeSelectField(this.store, this.destroy$, this.dateFieldConfig.key, this.dateFieldConfig.formType).subscribe(
            (value) => {
                this.selectedDate = value;
            }
        );

        safeSelectField(this.store, this.destroy$, this.textFieldConfig.key, this.textFieldConfig.formType).subscribe(
            (value) => {
                return (this.textInput = value);
            }
        );

        safeSelectField(
            this.store,
            this.destroy$,
            this.dropdownFieldConfig.key,
            this.dropdownFieldConfig.formType
        ).subscribe((selectedOption) => {
            return (this.selectedDropdownOption = this.dropdownOptions.filter((option) => {
                return option.id === selectedOption;
            })[0]);
        });

        const numericFieldOption = this.config.optionsWithExtraField.find((option) => {
            return option.type === 'NUMERIC_FIELD';
        });
        if (numericFieldOption) {
            safeSelectField(this.store, this.destroy$, this.numericFieldConfig.key, this.config.formType).subscribe(
                (value) => {
                    if (value) {
                        this.setSelectedOption(numericFieldOption.key);
                    }
                }
            );
        }

        this.fvs.getTranslations(this.store, this.destroy$).subscribe((t) => {
            this.i18nPlaceholders = t;
            this.cd.markForCheck();
        });
    }

    public ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public setSelectedOption(
        value: string,
        resetDate: boolean = true,
        resetInput: boolean = true,
        resetDropdown: boolean = true,
        resetNumericInput: boolean = true
    ) {
        if (value !== this.selectedOption) {
            this.clearStatusForGivenKey(this.config.key);
            this.clearStatusForGivenKey(this.dateFieldConfig.key);
            this.clearStatusForGivenKey(this.textFieldConfig.key);
            this.clearStatusForGivenKey(this.dropdownFieldConfig.key);
            this.clearStatusForGivenKey(this.numericFieldConfig.key);
            if (resetDate) {
                this.resetDate();
            }
            if (resetInput) {
                this.resetInput();
            }
            if (resetDropdown) {
                this.resetDropdown();
            }
            if (resetNumericInput) {
                this.resetNumericInput();
            } else {
                this.numericFieldConfig = { ...this.numericFieldConfig, enableFieldStatus: true };
            }
            this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, value, false)));
            const extraFieldConfig = this.config.optionsWithExtraField.filter((extraConfig) => {
                return extraConfig.key === value;
            })[0];
            if (extraFieldConfig) {
                if (extraFieldConfig.type === 'TEXT') {
                    this.autoFocusAssociatedText(value);
                } else {
                    this.autoFocusExtraFieldComponent(value, extraFieldConfig.type);
                }
            }
            this.cd.markForCheck();
        }
    }

    private autoFocusAssociatedText(value: string) {
        setTimeout(() => {
            const associatedTextId = value + this.textIdPostfix;
            const associatedText = document.getElementById(associatedTextId);
            if (associatedText) {
                associatedText.focus();
            }
        }, 10);
    }

    private autoFocusExtraFieldComponent(value: string, type: string) {
        setTimeout(() => {
            const extraFieldInputId = this.extraFieldInputId(value, type);
            const extraFieldComponentList = this.getExtraFiledsOfType(type);
            const associatedDropdownInput = extraFieldComponentList.toArray().filter((component) => {
                return (
                    (component instanceof NgSelectComponent && component.element.id === extraFieldInputId) ||
                    (!(component instanceof NgSelectComponent) && component.inputId === extraFieldInputId)
                );
            });
            if (associatedDropdownInput && associatedDropdownInput.length > 0) {
                associatedDropdownInput[0].focus();
            }
        }, 10);
    }

    private getExtraFiledsOfType(
        type: string
    ): QueryList<SimpleDateInputComponent | NgSelectComponent | NumericFieldComponent> {
        switch (type) {
            case 'DATE':
                return this.simpleDateInputs;
            case 'DROPDOWN':
                return this.dropdownInputs;
            case 'NUMERIC_FIELD':
                return this.numericFieldInputs;
            default:
                return new QueryList<any>();
        }
    }

    public setSelectedOptionAndDate(option: string, sdv: SimpleDateInputValue) {
        this.setSelectedOption(option, false, true, true);
        this.clearStatusForGivenKey(this.dateFieldConfig.key);
    }

    public setSelectedOptionAndInput(option: string, value: string) {
        this.setSelectedOption(option, true, false, true);
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.textFieldConfig, value, false)));
        if (value && value.length > 0) {
            this.clearStatusForGivenKey(this.textFieldConfig.key);
        }
    }

    public setSelectedOptionAndDropdown(option: string) {
        this.setSelectedOption(option, true, true, false);
        this.clearStatusForGivenKey(this.dropdownFieldConfig.key);
    }

    public hasExtraField(option: ModelValue): boolean {
        return !!this.getExtraFieldType(option);
    }

    public getExtraFieldType(option: ModelValue): string {
        if (!this.config.optionsWithExtraField || this.config.optionsWithExtraField.length === 0) {
            return null;
        }
        const extraOption = this.config.optionsWithExtraField.find((extra) => {
            return extra.key === option.value;
        });
        return extraOption ? extraOption.type : null;
    }

    public getExtraDateFieldMaxDate(option: ModelValue): Moment {
        let maxDate: Moment;
        if (this.config.optionsWithExtraField && this.config.optionsWithExtraField.length > 0) {
            const extraOption = this.config.optionsWithExtraField.find((extra) => {
                return extra.key === option.value;
            });
            maxDate = extraOption && !extraOption.allowFutureDate ? moment() : undefined;
        }
        return maxDate;
    }

    private resetDate() {
        this.selectedDate = null;
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.dateFieldConfig, null, false)));
    }

    private resetInput() {
        this.textInput = null;
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.textFieldConfig, null, false)));
    }

    private resetDropdown() {
        this.selectedDropdownOption = null;
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.dropdownFieldConfig, null, false)));
    }

    private resetNumericInput() {
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.numericFieldConfig, null, false)));
        this.numericFieldConfig = { ...this.numericFieldConfig, enableFieldStatus: false };
    }

    private initFieldConfigs() {
        const dropdownExtraOption = this.config.optionsWithExtraField.find((extra) => {
            return extra.type === 'DROPDOWN';
        });
        const dropdownValuesKey = dropdownExtraOption ? dropdownExtraOption.valuesKey : null;
        const dropdownValuesTranslationPrefix = dropdownExtraOption
            ? dropdownExtraOption.valuesTranslationPrefix
            : null;
        const numericFieldConfig = this.config.optionsWithExtraField.find((extra) => {
            return extra.type === 'NUMERIC_FIELD';
        });

        this.dateFieldConfig = {
            ...this.config,
            type: FormItemType.DATE,
            key: this.config.key + RadioListWithExtraFieldsComponent.DATE_FIELD_CONFIG_POSTFIX,
        };
        this.textFieldConfig = {
            ...this.config,
            type: FormItemType.TEXT,
            key: this.config.key + RadioListWithExtraFieldsComponent.TEXT_FIELD_CONFIG_POSTFIX,
        };
        this.dropdownFieldConfig = {
            ...this.config,
            key: this.config.key + RadioListWithExtraFieldsComponent.DROPDOWN_FIELD_CONFIG_POSTFIX,
            valuesKey:
                this.config.optionsWithExtraField && this.config.optionsWithExtraField.length > 0
                    ? dropdownValuesKey
                    : this.config.key + RadioListWithExtraFieldsComponent.DROPDOWN_FIELD_CONFIG_POSTFIX,
            translationPrefix:
                this.config.optionsWithExtraField && this.config.optionsWithExtraField.length > 0
                    ? dropdownValuesTranslationPrefix
                    : this.config.translationPrefix + RadioListWithExtraFieldsComponent.DROPDOWN_FIELD_CONFIG_POSTFIX,
        };
        this.numericFieldConfig = {
            ...this.config,
            key: this.config.key + RadioListWithExtraFieldsComponent.NUMERIC_FIELD_CONFIG_POSTIFX,
            labelKey: null,
            subLabelKey: null,
            enableFieldStatus: true,
            unit: numericFieldConfig ? numericFieldConfig.unit : null,
            max: numericFieldConfig ? numericFieldConfig.max : null,
            min: numericFieldConfig ? numericFieldConfig.min : null,
        };
    }

    private initDatePickerConfig() {
        this.configService.dateFormattingPattern().subscribe((pattern) => {
            this.dateFormat = pattern.longDatePattern.toUpperCase();
        });
    }

    private initTranslationKeys() {
        const baseLabelKey = this.config.labelKey.replace('.label', '.');

        this.titleTranslationKey = this.config.labelKey;
        this.descriptionTranslationKey =
            baseLabelKey + RadioListWithExtraFieldsComponent.DESCRIPTION_POSTFIX + '.label';
        this.dropdownPlaceholderTranslationKey = baseLabelKey;
    }

    public checkMissingExtraDateValue(optionValue: string, sdv: SimpleDateInputValue) {
        if (!sdv.momentValue) {
            this.addValueMissingHardErrorStatusForGivenKey(this.dateFieldConfig.key);
        } else if (!sdv.validDate) {
            this.addInvalidValueHardErrorStatusForGivenKey(this.dateFieldConfig.key);
        } else if (sdv.isUnknownDayPattern) {
            this.addConfirmUnkownDaySoftErrorStatusForGivenKey(this.dateFieldConfig.key);
        } else if (sdv.isAfterMaxDate) {
            this.addFutureDateHardErrorStatusForGivenKey(this.dateFieldConfig.key);
        }
    }

    public checkMissingExtraTextValue(optionValue: string) {
        const associatedTextId = optionValue + this.textIdPostfix;
        const associatedText: any = document.getElementById(associatedTextId);
        if (!associatedText.value || associatedText.value === '') {
            this.addValueMissingHardErrorStatusForGivenKey(this.textFieldConfig.key);
        }
    }

    public checkMissingExtraDropdownValue() {
        if (!this.selectedDropdownOption || !this.selectedDropdownOption.id || this.selectedDropdownOption.id === '') {
            this.addValueMissingHardErrorStatusForGivenKey(this.dropdownFieldConfig.key);
        }
    }

    public simpleDateFocusLost(optionValue: string, sdv: SimpleDateInputValue) {
        this.setSelectedOptionAndDate(optionValue, sdv);
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.dateFieldConfig, sdv.storeValue, false)));
        this.checkMissingExtraDateValue(optionValue, sdv);
    }

    public extraFieldInputId(optionValue: string, fieldType: string): string {
        const typeParts = fieldType.split('_');
        const inputIdPostfixBase =
            typeParts.length === 1
                ? fieldType.charAt(0) + fieldType.slice(1).toLowerCase()
                : this.buildCamelCase(typeParts);
        const inputIdPostfix = inputIdPostfixBase + 'Input';
        return this.config.key + optionValue + inputIdPostfix;
    }

    private buildCamelCase(parts: string[]): string {
        let output = '';
        parts.forEach((part) => {
            return (output = output + part.charAt(0) + part.slice(1).toLowerCase());
        });
        return output;
    }

    public componentVisible(): boolean {
        return this.visible && this.fieldStatusControlledVisibilty;
    }

    public setSelectedOptionAndDropdownValue(option: string, value: SelectOption) {
        this.setSelectedOption(option, true, true, false);
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.dropdownFieldConfig, value.id, false)));
        this.clearValueMissingHardErrorStatusForGivenKey(this.dropdownFieldConfig.key);
    }

    public getPlaceholder(option: ModelValue): string {
        if (!this.config.optionsWithExtraField || this.config.optionsWithExtraField.length === 0) {
            return '';
        }
        const extraOption = this.config.optionsWithExtraField.find((extra) => {
            return extra.key === option.value;
        });
        const isPlaceholderDefined = !!extraOption.placeholderTranslationKey;
        return isPlaceholderDefined ? extraOption.placeholderTranslationKey : '';
    }

    public getWrapperExtraClasses(): string[] {
        return this.config.extraWrapperClasses ? this.config.extraWrapperClasses : [];
    }

    set visibility(visibility: boolean) {
        this.visible = visibility;

        this.store.dispatch(
            new UpdateComponentState({
                id: this.config.key,
                visible: visibility,
            })
        );
        if (!this.visible && this.config.key) {
            this.clearAllAnswersAndStatuses();
        }
        this.cd.markForCheck();
    }

    private clearAllAnswersAndStatuses() {
        this.clearAnswerForFieldKey(this.config.key);
        this.clearStatusForGivenKey(this.config.key);
        if (this.dateFieldConfig) {
            this.clearAnswerForFieldKey(this.dateFieldConfig.key);
            this.clearStatusForGivenKey(this.dateFieldConfig.key);
        }
        if (this.dropdownFieldConfig) {
            this.clearAnswerForFieldKey(this.dropdownFieldConfig.key);
            this.clearStatusForGivenKey(this.dropdownFieldConfig.key);
        }
        if (this.textFieldConfig) {
            this.clearAnswerForFieldKey(this.textFieldConfig.key);
            this.clearStatusForGivenKey(this.textFieldConfig.key);
        }
    }

    private clearAnswerForFieldKey(fieldKey: string) {
        this.store.dispatch(
            new AddAnswer({
                type: this.config.formType,
                field: fieldKey,
                answer: null,
                required: false,
                initialValue: false,
            })
        );
    }

    private updateWithProviderOptions(options: any[]) {
        if (this.config.optionsProviderControlsVisibility) {
            const visible = !options || options.length === 0 ? false : true;
            this.visibility = visible;
        }
        // Radio list works with ModelValue but gets ListEntry from the Schema
        // Work with ListEntry to keep it consistent with the logic in FieldService
        const currentKeys = this.options
            ? this.options.map((v) => {
                  return v.value;
              })
            : [];
        const newKeys = options
            ? options.map((v) => {
                  return has(v, 'key') ? v.key : v;
              })
            : [];
        const sameKeys =
            currentKeys.length > 0 &&
            newKeys.length > 0 &&
            currentKeys.length === newKeys.length &&
            difference(newKeys, currentKeys).length === 0;

        if (this.config.optionsProviderControlsVisibility && !this.componentVisible()) {
            this.resetSelectedValuesAndOptions([]);
        } else if (!sameKeys) {
            // Here we assume that the option is a simple type if it doesn't have a 'key' field
            const listEntryOptions: ListEntry[] = options.map((option) => {
                if (has(option, 'key')) {
                    return option;
                }
                return {
                    key: option,
                };
            });
            this.resetSelectedValuesAndOptions(listEntryOptions);
        }

        this.cd.markForCheck();
    }

    private resetSelectedValuesAndOptions(options: ListEntry[]) {
        this.options = this.fieldService.getListValues(this.config, options);
        if (
            this.selectedOption &&
            !options
                .map((o) => {
                    return o.key;
                })
                .includes(this.selectedOption)
        ) {
            this.selectedOption = null;
            this.clearAllAnswersAndStatuses();
        }
    }

    public getQuestionHeaderConfig(): FormFieldConfig {
        return { adhocFormVariables: this.config.adhocFormVariables };
    }
}
