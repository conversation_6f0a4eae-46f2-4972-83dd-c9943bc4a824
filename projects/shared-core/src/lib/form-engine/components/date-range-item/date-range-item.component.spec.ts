import { ComponentFixture, TestBed } from '@angular/core/testing';

import { By } from '@angular/platform-browser';
import { ChangeDetectorRef, Component, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MocksModule } from '@shared-core/testing';
import { SymptomDateOption } from '../../models/symptom-date-options.enum';
import { DateRangeItemComponent } from './date-range-item.component';
import { FormElementContainerDirective } from '../../directives/form-element-container/form-element-container.directive';
import { FormErrorIndicatorComponent } from '../form-error-indicator/form-error-indicator.component';
import { CommonModule } from '@angular/common';
import { FormFieldConfig } from '../../models/form-field-config.interface';

@Component({
    selector: `test-host-component`,
    template: `<nh-date-range-item [visible]="visible" [config]="config"></nh-date-range-item>`,
})
class TestHostComponent {
    @ViewChild(DateRangeItemComponent) dateRangeItemComponent: DateRangeItemComponent;
    visible: boolean;
    config: FormFieldConfig;

    setVisible(visible: boolean): void {
        this.visible = visible;
    }

    setConfig(config: FormFieldConfig): void {
        this.config = config;
    }
}

describe('DateRangeItemComponent', () => {
    let component: TestHostComponent;
    let fixture: ComponentFixture<TestHostComponent>;
    let changeDetectorRef: ChangeDetectorRef;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [MocksModule, CommonModule],
            declarations: [
                TestHostComponent,
                DateRangeItemComponent,
                FormElementContainerDirective,
                FormErrorIndicatorComponent,
            ],
        }).compileComponents();

        fixture = TestBed.createComponent(TestHostComponent);
        component = fixture.componentInstance;
        component.setConfig({ key: 'test' });
        changeDetectorRef = fixture.debugElement.injector.get(ChangeDetectorRef);
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    describe('#optionChanged', () => {
        it.each([
            {
                option: SymptomDateOption.SYMPTOM_CONSTANT,
                expected: [],
            },
            {
                option: SymptomDateOption.SYMPTOM_OTHER_DATES,
                expected: [Validators.required],
            },
            {
                option: SymptomDateOption.SYMPTOM_TODAY,
                expected: [],
            },
        ])('should set validators', ({ option, expected }) => {
            const spy = (component.dateRangeItemComponent['setDateRangeValidators'] = jest.fn());
            component.dateRangeItemComponent.optionChanged(option);
            fixture.detectChanges();

            expect(spy).toHaveBeenCalledTimes(1);
            expect(spy).toHaveBeenCalledWith(expected);
        });
    });

    describe('#handleFormErrors', () => {
        const formGroup = new FormGroup({ dateRange: new FormControl() });

        it.each([
            {
                event: {
                    valid: true,
                    errors: null,
                    isDirty: true,
                    formGroup,
                },
                expected: {
                    hasErrors: false,
                    formGroup,
                },
            },
            {
                event: {
                    valid: true,
                    errors: null,
                    isDirty: false,
                    formGroup,
                },
                expected: {
                    hasErrors: false,
                    formGroup,
                },
            },
            {
                event: {
                    valid: false,
                    errors: { dateRange: true },
                    isDirty: false,
                    formGroup,
                },
                expected: {
                    hasErrors: false,
                    formGroup,
                },
            },
            {
                event: {
                    valid: false,
                    errors: { dateRange: true },
                    isDirty: true,
                    formGroup,
                },
                expected: {
                    hasErrors: true,
                    formGroup,
                },
            },
        ])('should handle form errors event', ({ event, expected }) => {
            const spy = jest.spyOn(changeDetectorRef.constructor.prototype, 'markForCheck').mockClear();
            component.dateRangeItemComponent.handleFormErrors(event);
            fixture.detectChanges();

            expect(component.dateRangeItemComponent.hasErrors).toBe(expected.hasErrors);
            expect(component.dateRangeItemComponent['formGroup']).toStrictEqual(expected.formGroup);
            expect(spy).toHaveBeenCalledTimes(1);
        });
    });

    describe('#setDateRangeValidators', () => {
        it('should call setValidators', () => {
            component.dateRangeItemComponent['formGroup'] = new FormGroup({ dateRange: new FormControl() });
            fixture.detectChanges();
            const spy = jest.spyOn(component.dateRangeItemComponent['formGroup'].controls.dateRange, 'setValidators');
            const validators = [];
            component.dateRangeItemComponent['setDateRangeValidators'](validators);

            expect(spy).toHaveBeenCalledTimes(1);
            expect(spy).toHaveBeenCalledWith(validators);
        });
    });

    describe('visible', () => {
        const selector = '#date-range-item-container';

        it('should be rendered', () => {
            component.setVisible(true);
            fixture.detectChanges();
            const element = fixture.debugElement.query(By.css(selector));

            expect(element).toBeTruthy();
        });

        it('should not be rendered', () => {
            component.setVisible(false);
            fixture.detectChanges();
            const element = fixture.debugElement.query(By.css(selector));

            expect(element).not.toBeTruthy();
        });
    });
});
