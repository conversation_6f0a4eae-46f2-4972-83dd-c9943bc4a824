import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { AddAnswer } from '../../store/actions/form.actions';
import { FormEngineState } from '../../store/reducers/state';
import { Subject } from 'rxjs';
import { HyphenedPipe } from '../../../pipes/hyphened.pipe';
import { FormFieldConfig } from '../../models/form-field-config.interface';
import { FormField } from '../../models/form-field.interface';
import { SymptomDateOption } from '../../models/symptom-date-options.enum';
import { FieldService } from '../../services/field.service';
import { FormInputField } from '../form-input-field.component';
import moment, { Moment } from 'moment';
import { safeSelectField } from '../../services/subscription-helper';
import type { IsFormValidEvent } from '../form-error-indicator/form-error-indicator.interface';
import { FormGroup, ValidatorFn, Validators } from '@angular/forms';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-date-range-item',
  templateUrl: './date-range-item.component.html',
  styleUrls: ['./date-range-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DateRangeItemComponent extends FormInputField implements OnInit, OnDestroy, FormField {
  @Input()
  public config: FormFieldConfig;

  @Input()
  public visible: boolean;

  public peakDateConfig: FormFieldConfig;
  public symptomDateConfig: FormFieldConfig;
  public dateOptionValues = Object.values(SymptomDateOption);
  public selectedDateOption: SymptomDateOption;
  public showDatepicker = false;
  public values: Moment[];
  hasErrors = false;
  private formGroup: FormGroup;
  private destroy$ = new Subject<boolean>();
  private initialValues = true;

  constructor(
    protected store: Store<FormEngineState>,
    private fieldService: FieldService,
    private hyphened: HyphenedPipe,
    cd: ChangeDetectorRef
  ) {
    super(cd);
  }

  ngOnInit() {
    this.peakDateConfig = {
      ...this.config,
      key: 'peakDate'
    };

    this.symptomDateConfig = {
      ...this.config,
      key: 'symptomDate'
    };

    this.dispatchAnswers(null, null, null, true);

    safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe(values => {
      if (values) {
        this.values = values;
        if (this.initialValues) {
          // Handle the form edit case to show the correct state of the date item
          this.initialValues = false;
          if (values.length > 0) {
            const dates = this.values.map(value => {
              return moment(value);
            });
            this.selectedDateOption =
              dates.length === 1 && dates[0].isSame(moment(), 'd')
                ? SymptomDateOption.SYMPTOM_TODAY
                : SymptomDateOption.SYMPTOM_OTHER_DATES;
          } else {
            this.selectedDateOption = SymptomDateOption.SYMPTOM_CONSTANT;
          }
        }
        this.showDatepicker = this.selectedDateOption === SymptomDateOption.SYMPTOM_OTHER_DATES;
      } else {
        this.initialValues = false;
        this.values = [];
      }
      this.cd.markForCheck();
    });
  }

  ngOnDestroy() {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  public getOptionId(option: SymptomDateOption) {
    return `${this.config.formKey}-${this.hyphened.transform(this.config.key)}-${this.hyphened.transform(option)}`;
  }

  public optionChanged(option: SymptomDateOption) {
    const isOptionOtherDates = option === SymptomDateOption.SYMPTOM_OTHER_DATES;
    this.showDatepicker = isOptionOtherDates;
    this.selectedDateOption = option;
    this.cd.markForCheck();

    if (option === SymptomDateOption.SYMPTOM_CONSTANT) {
      this.setDateRangeValidators([]);
      this.dispatchAnswers(null, null, moment());
      this.hasErrors = false;
    } else if (isOptionOtherDates) {
      this.setDateRangeValidators([Validators.required]);
      this.dispatchAnswers(null);
    } else if (option === SymptomDateOption.SYMPTOM_TODAY) {
      this.hasErrors = false;
      this.setDateRangeValidators([]);
      this.dispatchAnswers([moment()], moment(), moment());
    }
    this.cd.markForCheck();
  }

  handleFormErrors($event: IsFormValidEvent): void {
    this.hasErrors = !$event.valid && $event.isDirty;
    this.formGroup = $event.formGroup;
    this.cd.markForCheck();
  }

  private setDateRangeValidators(validators: ValidatorFn | ValidatorFn[]): void {
    const dateRangeFormGroup = this.formGroup?.get('dateRange');
    dateRangeFormGroup?.setValidators(validators);
    dateRangeFormGroup?.updateValueAndValidity({ onlySelf: true });
  }

  private dispatchAnswers(
    datePickerValue: moment.Moment[] | null,
    peakDateValue?: moment.Moment | null | undefined,
    symptomDateValue?: moment.Moment | null | undefined,
    initialValue = false
  ): void {
    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, datePickerValue, initialValue)));

    if (typeof peakDateValue !== 'undefined') {
      this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.peakDateConfig, peakDateValue, initialValue)));
    }

    if (typeof symptomDateValue !== 'undefined') {
      this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.symptomDateConfig, symptomDateValue, initialValue)));
    }
  }
}
