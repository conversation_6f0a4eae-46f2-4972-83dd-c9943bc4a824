<nh-form-error-indicator *ngIf="isErrorVisible" [config]="config"></nh-form-error-indicator>
<div class="form-element-container date-picker-container">
    <div class="primary-picker picker"></div>
    <div class="secondary-picker picker"></div>
    <div class="date-range-information clearfix margin-top-10">
        <div class="earlier-range-container info">
            <div class="earlier-selected clearfix"></div>
            <div class="info-title">{{ 'patient.wizard.dateRange.earlierSelectedInfo' | i18n }}</div>
        </div>
        <div class="earlier-peak-container info">
            <div class="earlier-peak clearfix"></div>
            <div class="info-title">{{ 'patient.wizard.dateRange.earlierPeakInfo' | i18n }}</div>
        </div>
    </div>
</div>
<div
    class="form-element-container date-picker-container"
    *ngIf="selectedDates.length > 1"
    [nsFormElementContainer]="!peakDatePicked"
>
    <div class="header">
        <h4>{{ 'patient.durationRange.peakDate.instruction' | i18n }}</h4>
    </div>
    <nh-form-error-indicator
        *ngIf="!peakDatePicked"
        [config]="peakDateConfig"
    ></nh-form-error-indicator>
    <nh-datepicker
        class="slide-down"
        [dateRange]="selectedDates"
        [firstMonthStart]="firstMonthStart"
        [secondMonthStart]="secondMonthStart"
        [config]="peakDateConfig"
        [symptomDateConfig]="symptomDateConfig"
        (peakDateFromChild)="setPeakDatePickedFromChild($event)"
    ></nh-datepicker>
</div>
