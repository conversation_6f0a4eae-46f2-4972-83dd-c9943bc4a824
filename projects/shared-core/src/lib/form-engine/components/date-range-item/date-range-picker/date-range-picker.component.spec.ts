import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import * as jquery from 'jquery';

import { DateRangePickerComponent } from './date-range-picker.component';
import { MocksModule } from '@shared-core/testing';

describe('DateRangePickerComponent', () => {
    let component: DateRangePickerComponent;
    let fixture: ComponentFixture<DateRangePickerComponent>;

    beforeEach(waitForAsync(() => {
        jquery.fn.extend({
            datepicker() {
                return this;
            },
        });
        (window as any).$ = jquery;
        TestBed.configureTestingModule({
            schemas: [NO_ERRORS_SCHEMA],
            imports: [MocksModule],
            declarations: [DateRangePickerComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(DateRangePickerComponent);
        component = fixture.componentInstance;
        component.config = { key: 'test' };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
