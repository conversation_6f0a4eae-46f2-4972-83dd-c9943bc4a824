import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { AddAnswer } from '../../../store/actions/form.actions';
import { FormEngineState } from '../../../store/reducers/state';
import { Subject } from 'rxjs';
import { take } from 'rxjs/operators';
import { ISO_DATE_FORMAT } from '../../../../constants';
import { TDateRangeInterval } from '../../../interface/date-range-interval.interface';
import { DatepickerValue } from '../../../models/datepicker-value.enum';
import { FormFieldConfig } from '../../../models/form-field-config.interface';
import { FormField } from '../../../models/form-field.interface';
import { DatepickerService } from '../../../services/datepicker.service';
import { FieldService } from '../../../services/field.service';
import moment, { Moment } from 'moment';
import { safeSelectField } from '../../../services/subscription-helper';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-date-range-picker',
  templateUrl: './date-range-picker.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DateRangePickerComponent implements OnInit, OnDestroy, FormField {
  @Input()
  public config: FormFieldConfig;

  @Input()
  public isErrorVisible: boolean;

  @Input()
  public peakDateConfig: FormFieldConfig;

  @Input()
  public symptomDateConfig: FormFieldConfig;

  public showSecondary = false;
  public firstMonthStart: Moment;
  public secondMonthStart: Moment;

  public selectedDates: Moment[] = [];
  public peakDate: Date;
  public peakDateISO: string;
  public automaticPeakDate = false;
  public peakDatePicked = false;

  private initialValues = true;

  private currentlySelectedDatesISOMap: { [key: string]: Moment } = {};
  private lastNumberOfSelectedDates: number;
  private currentlySelectedRangeStartsAndEnds: TDateRangeInterval;

  private destroy$ = new Subject<boolean>();
  private $primaryPicker: JQuery;
  private $secondaryPicker: JQuery;

  constructor(
    private fieldService: FieldService,
    private store: Store<FormEngineState>,
    private el: ElementRef,
    private datepickerService: DatepickerService,
    private cd: ChangeDetectorRef
  ) {}

  ngOnInit() {
    safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((values: Moment[]) => {
      if (values) {
        // Ensure that all the dates are in moment, in edit mode the dates originally comes as "raw" numbers
        values = values.map(date => {
          return moment.isMoment(date) ? date : moment(date);
        });
        this.selectedDates = values;
      } else {
        this.selectedDates = values = [];
      }

      this.currentlySelectedDatesISOMap = values.reduce((currentDates, date) => {
        return {
          ...currentDates,
          [date.format(ISO_DATE_FORMAT)]: date
        };
      }, {});

      this.currentlySelectedRangeStartsAndEnds = this.datepickerService.calculateDateRangeStartsAndEnds(values, ISO_DATE_FORMAT);

      if (values.length === 1) {
        this.peakDate = values[0].toDate();
        this.peakDateISO = values[0].format(ISO_DATE_FORMAT);
      } else {
        let resetPeakDate = true;
        if (
          values.length > 1 &&
          (this.lastNumberOfSelectedDates === undefined ||
            (this.lastNumberOfSelectedDates !== undefined && this.lastNumberOfSelectedDates <= 1))
        ) {
        } else if (this.peakDate) {
          if (this.currentlySelectedDatesISOMap[this.peakDateISO]) {
            resetPeakDate = false;
          }
        }

        if (resetPeakDate) {
          this.peakDate = undefined;
          this.peakDateISO = undefined;
        }
      }

      this.lastNumberOfSelectedDates = values.length;

      if (this.initialValues) {
        this.initialValues = false;
        this.initialiseDatePickers();
      }
      this.updateDatepicker();
    });
    this.cd.detectChanges();
  }

  ngOnDestroy() {
    this.destroy$.next(true);
    this.destroy$.complete();

    if (this.$primaryPicker) {
      this.$primaryPicker.off(DatepickerValue.CHANGE_DATE);
    }

    if (this.$secondaryPicker) {
      this.$secondaryPicker.off(DatepickerValue.CHANGE_DATE);
    }
  }

  private initialiseDatePickers() {
    this.$primaryPicker = $(this.el.nativeElement.querySelector('.primary-picker'));

    let minDate: Date;
    let maxDate: Date;
    const now = moment();
    const furthestMinDate = moment().subtract(1, 'month');

    if (!!this.selectedDates && this.selectedDates.length > 0) {
      const firstSelectedDate = this.selectedDates[0].clone();
      minDate = firstSelectedDate.toDate();
      maxDate = firstSelectedDate.clone().add(1, 'month').toDate();
    }

    if (!maxDate || moment(maxDate).isAfter(now)) {
      maxDate = now.toDate();
    }

    if (!minDate || furthestMinDate.isBefore(minDate)) {
      minDate = furthestMinDate.toDate();
    }

    if (this.config.since) {
      minDate = moment(this.config.since).toDate();
    }

    const options = this.datepickerService.getDatepickerOptions(minDate, maxDate);
    options.beforeShowDay = (date: any) => {
      return this.resolveDateClass(date);
    };
    options.hideDisabledWeeks = true;

    this.firstMonthStart = moment(minDate).startOf(DatepickerValue.MONTH);
    this.secondMonthStart = moment(maxDate).startOf(DatepickerValue.MONTH);

    if (maxDate && minDate && moment(maxDate).month() !== moment(minDate).month()) {
      this.showSecondary = true;

      const secondaryOptions = { ...options };

      options.endDate = moment(minDate).endOf(DatepickerValue.MONTH).toDate();

      secondaryOptions.startDate = moment(maxDate).startOf(DatepickerValue.MONTH).toDate();

      (this.$primaryPicker as any).datepicker(options).on(DatepickerValue.CHANGE_DATE, (event: any) => {
        return this.dateChanged(event);
      });

      this.$secondaryPicker = $(this.el.nativeElement.querySelector('.secondary-picker'));
      (this.$secondaryPicker as any).datepicker(secondaryOptions).on(DatepickerValue.CHANGE_DATE, (event: any) => {
        return this.dateChanged(event);
      });
    } else {
      (this.$primaryPicker as any).datepicker(options).on(DatepickerValue.CHANGE_DATE, (event: any) => {
        return this.dateChanged(event);
      });
    }

    setTimeout(() => {
      this.setDatepickerNavAccessibility('.primary-picker');
      this.setDatepickerNavAccessibility('.secondary-picker');
    }, 0);
  }

  private resolveDateClass(currentDate: any): { classes: string; childElement: string } {
    const classes: string[] = [];
    const currentDateString: string = moment(currentDate).format(ISO_DATE_FORMAT);

    if (this.currentlySelectedDatesISOMap[currentDateString]) {
      classes.push(DatepickerValue.SELECTED);
    }

    if (this.datepickerService.isLastDayOfMonthMemoized(currentDateString)) {
      classes.push(DatepickerValue.LAST);
    }

    if (this.datepickerService.isFirstDayOfMonthMemoized(currentDateString)) {
      classes.push(DatepickerValue.FIRST);
    }

    if (this.config.disabled) {
      classes.push(DatepickerValue.VIEW);
    }

    return {
      classes: [...classes, ...this.resolveCurrentSelectedDatesClasses(currentDate, currentDateString)].join(' '),
      childElement: DatepickerValue.CIRCLE
    };
  }

  private resolveCurrentSelectedDatesClasses(currentDate: Moment, currentDAteISO: string) {
    const classes: string[] = [];
    if (this.currentlySelectedDatesISOMap[currentDAteISO]) {
      classes.push(DatepickerValue.ACTIVE);
    }

    if (
      !this.currentlySelectedRangeStartsAndEnds ||
      !this.currentlySelectedRangeStartsAndEnds.rangeStartsISO ||
      !this.currentlySelectedRangeStartsAndEnds.rangeEndsISO
    ) {
      classes.push(DatepickerValue.SINGLE_SELECT);
    } else {
      const isStart = this.currentlySelectedRangeStartsAndEnds.rangeStartsISO.indexOf(currentDAteISO) >= 0;
      const isEnd = this.currentlySelectedRangeStartsAndEnds.rangeEndsISO.indexOf(currentDAteISO) >= 0;

      if (isEnd && isStart) {
        classes.push(DatepickerValue.SINGLE_SELECT);
      } else {
        if (isStart) {
          classes.push(DatepickerValue.START);
        } else if (isEnd) {
          classes.push(DatepickerValue.END);
        } else {
          classes.push(DatepickerValue.MIDDLE);
        }
      }
    }

    if (moment(currentDate).isSame(moment(), 'day')) {
      classes.push(DatepickerValue.TODAY_DATE);
    }

    return classes;
  }

  private dateChanged(event: any) {
    if (this.config.disabled) {
      event.stopPropagation();
      return;
    }

    const selectedDate = this.datepickerService.getFormattedDate(moment(event.date));

    const newValues = [...this.selectedDates];

    const index = newValues.findIndex(date => {
      return selectedDate.isSame(date, 'd');
    });
    if (index >= 0) {
      newValues.splice(index, 1);
    } else {
      newValues.push(selectedDate);
    }

    this.store.dispatch(
      new AddAnswer(this.fieldService.getAnswer(this.config, this.datepickerService.sortMomentArrayAscending(newValues), false))
    );

    safeSelectField(this.store, this.destroy$, this.peakDateConfig.key, this.peakDateConfig.formType)
      .pipe(take(1))
      .subscribe((date: Moment) => {
        if (date) {
          const isInSymptomaticDays =
            newValues.find((momentDate: Moment) => {
              return momentDate.isSame(date, 'day');
            }) != null;
          // If previous peak date is not in the symptom dates anymore
          if (!isInSymptomaticDays) {
            this.updateSymptomAndPeakDate(null);
          }
        }

        // Pre-pick a symptom and peak date when there is only one symptomatic date
        if (newValues.length === 1) {
          this.updateSymptomAndPeakDate(newValues[0]);
          this.automaticPeakDate = true;
        } else if (newValues.length === 0) {
          this.updateSymptomAndPeakDate(null);
        } else if (newValues.length > 1 && this.automaticPeakDate) {
          this.updateSymptomAndPeakDate(null);
          this.automaticPeakDate = false;
        } else {
          this.automaticPeakDate = false;
        }
      });
  }

  private updateDatepicker() {
    (this.$primaryPicker as any).datepicker('update');
    if (this.$secondaryPicker) {
      (this.$secondaryPicker as any).datepicker('update');
    }
    this.cd.markForCheck();
  }

  private updateSymptomAndPeakDate(value?: Moment) {
    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.peakDateConfig, value, false)));
    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.symptomDateConfig, value, false)));
  }

  public setPeakDatePickedFromChild(pdSelected: boolean) {
    this.peakDatePicked = pdSelected;
    this.cd.detectChanges();
  }

  private setDatepickerNavAccessibility(containerSelector: string) {
    const container = this.el.nativeElement.querySelector(containerSelector);
    if (!container) return;

    const prev = container.querySelector('.datepicker .prev');
    const next = container.querySelector('.datepicker .next');

    if (prev) {
      prev.setAttribute('aria-hidden', 'true');
      prev.setAttribute('tabindex', '-1');
    }
    if (next) {
      next.setAttribute('aria-hidden', 'true');
      next.setAttribute('tabindex', '-1');
    }
  }
}
