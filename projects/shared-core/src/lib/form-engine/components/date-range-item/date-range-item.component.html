<div id="date-range-item-container" *ngIf="visible" [nsFormElementContainer]="hasErrors">
  <nh-form-error-indicator
    [config]="config"
    *ngIf="!selectedDateOption && !showDatepicker"
    (isFormValid)="handleFormErrors($event)"
  ></nh-form-error-indicator>
  <nh-form-error-indicator [config]="config" *ngIf="showDatepicker" (isFormValid)="handleFormErrors($event)"></nh-form-error-indicator>
  <div>
    <div class="header">
      <h4>
        <span [innerHTML]="config.labelKey | i18n | safeHtml"></span>
        <span *ngIf="config.required">*</span>
      </h4>
    </div>
    <div class="input-wrapper">
      <div class="radio" *ngFor="let option of dateOptionValues">
        <input
          class="form-control"
          type="radio"
          [id]="getOptionId(option)"
          [value]="option"
          [name]="getOptionId(option)"
          [checked]="selectedDateOption === option"
          (change)="optionChanged(option)"
        />
        <label [for]="getOptionId(option)">
          {{ 'patient.wizard.symptomDateOption.' + option | i18n }}
        </label>
      </div>
    </div>
    <div *ngIf="showDatepicker">
      <nh-date-range-picker
        class="slide-down"
        [config]="config"
        [peakDateConfig]="peakDateConfig"
        [symptomDateConfig]="symptomDateConfig"
        [isErrorVisible]="false"
      ></nh-date-range-picker>
    </div>
    <p *ngIf="values?.length > 1">{{ 'patient.wizard.dateRange.symptomsAtTheWorst' | i18n }}</p>
  </div>
</div>
