import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import moment from 'moment';

import { DatepickerComponent } from './datepicker.component';
import { MocksModule } from '@shared-core/testing';
import { By } from "@angular/platform-browser";

global['$'] = global['jQuery'] = $;

describe('DatepickerComponent', () => {
    let component: DatepickerComponent;
    let fixture: ComponentFixture<DatepickerComponent>;
    const fistCalendarSelector = '.start-month';
    const secondCalendarSelector = '.end-month';
    const testDummyDateChanges = { dateRange: {
            firstChange: true,
            previousValue: undefined,
            currentValue: undefined,
            isFirstChange: function (): boolean {
                return this.firstChange;
            }
        } }

    beforeEach(waitForAsync(() => {
        $.fn.extend({
            datepicker() {
                return this;
            },
        });

        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [DatepickerComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(DatepickerComponent);
        component = fixture.componentInstance;
        component.config = { key: 'test' };
        component.dateRange = [moment(new Date('2020-03-22T00:00:00')), moment(new Date('2020-04-22T00:00:00'))];
        component.firstMonthStart = moment(new Date('2020-03-01T00:00:00'));
        component.secondMonthStart = moment(new Date('2020-04-01T00:00:00'));
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('when first and second month are the same then start month calendar is shown and second month calendar is hidden', () => {
        component.dateRange = [moment(new Date('2020-04-12T00:00:00')), moment(new Date('2020-04-22T00:00:00'))];
        component.firstMonthStart = moment(new Date('2020-04-01T00:00:00'));
        component.secondMonthStart = moment(new Date('2020-04-01T00:00:00'));
        component.ngOnChanges(testDummyDateChanges);
        fixture.detectChanges();
        const firstCalendar = fixture.debugElement.query(By.css(fistCalendarSelector));
        const secondCalendar = fixture.debugElement.query(By.css(secondCalendarSelector));
        const firstCalendarStyle = firstCalendar.attributes.style;
        const secondCalendarStyle = secondCalendar.attributes.style;
        expect(firstCalendarStyle).toMatch(/display: block/);
        expect(secondCalendarStyle).toMatch(/display: none/);
    });

    it('when first and second month are different and first date in selected date range is before second month start then first month calendar is shown', () => {
        component.dateRange = [moment(new Date('2020-03-22T00:00:00')), moment(new Date('2020-04-22T00:00:00'))];
        component.ngOnChanges(testDummyDateChanges);
        fixture.detectChanges();
        const firstCalendar = fixture.debugElement.query(By.css(fistCalendarSelector));
        const firstCalendarStyle = firstCalendar.attributes.style;
        expect(firstCalendarStyle).toMatch(/display: block/);
    });

    it('when first and second month are different and first date in selected date range is in second month then first month calendar is hidden and second month calendar is shown', () => {
        component.dateRange = [moment(new Date('2020-04-12T00:00:00')), moment(new Date('2020-04-22T00:00:00'))];
        component.ngOnChanges(testDummyDateChanges);
        fixture.detectChanges();
        const firstCalendar = fixture.debugElement.query(By.css(fistCalendarSelector));
        const secondCalendar = fixture.debugElement.query(By.css(secondCalendarSelector));
        const firstCalendarStyle = firstCalendar.attributes.style;
        const secondCalendarStyle = secondCalendar.attributes.style;
        expect(firstCalendarStyle).toMatch(/display: none/);
        expect(secondCalendarStyle).toMatch(/display: block/);
    });

    it('when first and second month are different and latest date in selected date range is in second month then second month calendar is shown', () => {
        component.dateRange = [moment(new Date('2020-03-22T00:00:00')), moment(new Date('2020-04-22T00:00:00'))];
        component.ngOnChanges(testDummyDateChanges);
        fixture.detectChanges();
        const secondCalendar = fixture.debugElement.query(By.css(secondCalendarSelector));
        const secondCalendarStyle = secondCalendar.attributes.style;
        expect(secondCalendarStyle).toMatch(/display: block/);
    });

    it('when first and second month are different and latest date in selected date range is before second month start then second month calendar is hidden', () => {
        component.dateRange = [moment(new Date('2020-03-22T00:00:00')), moment(new Date('2020-03-29T00:00:00'))];
        component.ngOnChanges(testDummyDateChanges);
        fixture.detectChanges();
        const secondCalendar = fixture.debugElement.query(By.css(secondCalendarSelector));
        const secondCalendarStyle = secondCalendar.attributes.style;
        expect(secondCalendarStyle).toMatch(/display: none/);
    });

    it('when selected date range is empty then first and second month are hidden', () => {
        component.dateRange = [];
        component.ngOnChanges(testDummyDateChanges);
        fixture.detectChanges();
        const firstCalendar = fixture.debugElement.query(By.css(fistCalendarSelector));
        const secondCalendar = fixture.debugElement.query(By.css(secondCalendarSelector));
        const firstCalendarStyle = firstCalendar.attributes.style;
        const secondCalendarStyle = secondCalendar.attributes.style;
        expect(firstCalendarStyle).toMatch(/display: none/);
        expect(secondCalendarStyle).toMatch(/display: none/);
    });
});
