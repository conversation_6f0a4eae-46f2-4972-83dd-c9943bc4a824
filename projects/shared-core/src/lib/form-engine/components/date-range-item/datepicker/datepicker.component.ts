import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    Output,
    OnChanges,
    OnDestroy,
    OnInit,
    SimpleChanges,
    EventEmitter,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { ISO_DATE_FORMAT } from '../../../../constants';
import { ArrayUtils } from '../../../../util/array.utils';
import { DatepickerValue } from '../../../models/datepicker-value.enum';
import { FormFieldConfig } from '../../../models/form-field-config.interface';
import { FormField } from '../../../models/form-field.interface';
import { DatepickerService } from '../../../services/datepicker.service';
import { FieldService } from '../../../services/field.service';
import { AddAnswer } from '../../../store/actions/form.actions';
import { FormEngineState } from '../../../store/reducers/state';
import moment, { Moment } from 'moment';
import { safeSelectField } from '../../../services/subscription-helper';
import { DatepickerEventObject, JQueryUI } from '../../../interface/datepicker-options';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-datepicker',
    templateUrl: './datepicker.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DatepickerComponent implements OnInit, OnDestroy, OnChanges, FormField {
    @Input() config: FormFieldConfig;
    @Input() visible: boolean;
    @Input() symptomDateConfig: FormFieldConfig;
    @Input() dateRange: Moment[];
    @Input() firstMonthStart: Moment;
    @Input() secondMonthStart: Moment;

    @Input()
    public managed: boolean;

    @Output() peakDateFromChild = new EventEmitter<boolean>();

    public showFirstCalendar = true;
    public showSecondCalendar = true;

    private destroy$ = new Subject<boolean>();

    private selectedDatesMap: { [key: string]: Moment } = {};
    private rangeStartsISO: string[] = [];
    private rangeEndsISO: string[] = [];

    private selectedDateIso: string;
    private selectedDate: Moment;
    private peakDateSelected: boolean;

    private $firstPicker: JQueryUI;
    private $secondPicker: JQueryUI;

    constructor(
        private store: Store<FormEngineState>,
        private fieldService: FieldService,
        private datepickerService: DatepickerService,
        private el: ElementRef,
        private cd: ChangeDetectorRef
    ) {}

    ngOnInit() {
        safeSelectField(this.store, this.destroy$, this.config.key, this.config.formType).subscribe((date: Moment) => {
            if (!date) {
                return;
            }
            if (this.dateRange.length === 1 && date) {
                this.selectedDate = moment.isMoment(date) ? date : moment(date);
                this.selectedDateIso = this.selectedDate.format(ISO_DATE_FORMAT);
                this.updateDatepicker();
            }
        });

        this.peakDateFromChild.emit(false);
        this.createSelectedISODatesMap();
        this.initDatepickers();
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
        if (!this.managed) {
            this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.config, undefined, false)));
        }
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes.dateRange) {
            this.updateAfterDateRangeChange();

            if (!changes.dateRange.firstChange) {
                this.$firstPicker?.datepicker('setDate', null);
                this.$secondPicker?.datepicker('setDate', null);
                this.peakDateFromChild.emit(false);
            }
        }
    }

    private createSelectedISODatesMap() {
        this.selectedDatesMap = {};
        this.dateRange.forEach((date: Moment, _index: number) => {
            this.selectedDatesMap[date.format(ISO_DATE_FORMAT)] = date;
        });
    }

    private initDatepickers() {
        const firstMonthEnd = this.firstMonthStart.clone().endOf('month');
        const secondMonthEnd = this.secondMonthStart.clone().endOf('month');

        const firstCalendarOptions = this.datepickerService.getDatepickerOptions(
            this.firstMonthStart.toDate(),
            firstMonthEnd.toDate()
        );

        firstCalendarOptions.beforeShowDay = (date: string) => {
            return this.resolveDateClass(date);
        };
        firstCalendarOptions.hideDisabledWeeks = true;

        this.$firstPicker = $(
            this.el.nativeElement.querySelector('.nh-datepicker-picker.start-month')
        ) as unknown as JQueryUI;
        this.$firstPicker.datepicker(firstCalendarOptions).on('changeDate', (event: DatepickerEventObject) => {
            return this.dateChanged(event);
        });

        const secondCalendarOptions = this.datepickerService.getDatepickerOptions(
            this.secondMonthStart.toDate(),
            secondMonthEnd.toDate()
        );

        secondCalendarOptions.beforeShowDay = (date: string) => {
            return this.resolveDateClass(date);
        };
        secondCalendarOptions.hideDisabledWeeks = true;

        this.$secondPicker = $(
            this.el.nativeElement.querySelector('.nh-datepicker-picker.end-month')
        ) as unknown as JQueryUI;
        this.$secondPicker.datepicker(secondCalendarOptions).on('changeDate', (event: DatepickerEventObject) => {
            return this.dateChanged(event);
        });

        this.showFirstCalendarIfNecessary();
        this.showSecondCalendarIfNecessary();

        this.cd.markForCheck();
    }

    private showFirstCalendarIfNecessary() {
        if (!this.$firstPicker) {
            return;
        }

        if (this.dateRange.length > 0) {
            const earliest = this.dateRange[0];
            this.showFirstCalendar =
                this.firstMonthStart.isSame(this.secondMonthStart) || earliest.isBefore(this.secondMonthStart);
            if (this.showFirstCalendar) {
                this.$firstPicker.removeClass('hide-datepicker-days-header');
            } else {
                this.$firstPicker.addClass('hide-datepicker-days-header');
            }
        } else {
            this.showFirstCalendar = false;
        }
    }

    private showSecondCalendarIfNecessary() {
        if (!this.$secondPicker) {
            return;
        }

        if (this.dateRange.length > 0) {
            const latestDate = ArrayUtils.last(this.dateRange);
            this.showSecondCalendar = this.firstMonthStart.isSame(this.secondMonthStart)
                ? false
                : latestDate.isSameOrAfter(this.secondMonthStart);
        } else {
            this.showSecondCalendar = false;
        }
    }

    private calculateDateRangeStartsAndEnds() {
        const rangeStartEnds = this.datepickerService.calculateDateRangeStartsAndEnds(this.dateRange, ISO_DATE_FORMAT);
        this.rangeStartsISO = rangeStartEnds.rangeStartsISO;
        this.rangeEndsISO = rangeStartEnds.rangeEndsISO;
    }

    private updateAfterDateRangeChange() {
        this.createSelectedISODatesMap();

        if (this.selectedDateIso) {
            if (!this.selectedDatesMap[this.selectedDateIso]) {
                this.selectedDate = undefined;
                this.selectedDateIso = undefined;
            }
        }

        this.calculateDateRangeStartsAndEnds();
        this.showFirstCalendarIfNecessary();
        this.showSecondCalendarIfNecessary();
        this.updateDatepicker();
    }

    private resolveDateClass(date: string): { classes: string; childElement: string } {
        const classes: string[] = [];
        const isoDate = moment(date).format(ISO_DATE_FORMAT);

        if (this.selectedDatesMap[isoDate]) {
            classes.push(DatepickerValue.SELECTED);
        } else {
            classes.push(DatepickerValue.DISABLED);
        }

        const isStart = this.rangeStartsISO.includes(isoDate);
        const isEnd = this.rangeEndsISO.includes(isoDate);

        if (isEnd && isStart) {
            classes.push(DatepickerValue.SINGLE_SELECT);
        } else {
            if (isStart) {
                classes.push(DatepickerValue.START);
            }

            if (isEnd) {
                classes.push(DatepickerValue.END);
            }
        }

        if (this.selectedDateIso && this.selectedDateIso === isoDate) {
            classes.push(DatepickerValue.ACTIVE);
        }

        if (this.config.disabled) {
            classes.push(DatepickerValue.VIEW);
        }

        return {
            classes: classes.join(' '),
            childElement: DatepickerValue.CIRCLE,
        };
    }

    private dateChanged(event: DatepickerEventObject) {
        this.handlePeakDate(event);

        if (this.config.disabled) {
            event.stopPropagation();
            this.updateDatepicker();
            return;
        }

        const day = moment(event.date);
        this.store.dispatch(
            new AddAnswer(this.fieldService.getAnswer(this.config, this.peakDateSelected ? day : null, false))
        );
        this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.symptomDateConfig, day, false)));
    }

    private updateDatepicker() {
        if (this.$firstPicker) {
            this.$firstPicker.datepicker('update');
        }

        if (this.$secondPicker) {
            this.$secondPicker.datepicker('update');
        }
        this.cd.markForCheck();
    }

    private handlePeakDate(event: Event) {
        this.peakDateSelected = false;
        const currentTarget = event.currentTarget as HTMLElement;
        const fDatePickerDate = this.$firstPicker.datepicker('getDate').toString();
        const tDatePickerDate = this.$secondPicker.datepicker('getDate').toString();
        const handleDate = (className: 'start-month' | 'end-month') => {
            const isStart = className === 'start-month';
            const date = isStart ? fDatePickerDate : tDatePickerDate;
            if (
                currentTarget.classList.value.includes(className) &&
                date !== 'Invalid Date' &&
                !this.peakDateSelected
            ) {
                (isStart ? this.$secondPicker : this.$firstPicker).datepicker('setDate', null);
                this.peakDateSelected = true;
            }
        };

        handleDate('start-month');
        handleDate('end-month');

        if (this.peakDateSelected) {
            this.peakDateFromChild.emit(true);
        }
    }
}
