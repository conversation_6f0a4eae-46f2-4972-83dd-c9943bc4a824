import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject, Observable } from 'rxjs';
import { debounceTime, take, takeUntil } from 'rxjs/operators';
import { LoadingSpinnerService } from '../../abstract-services/loading-spinner.service';
import { InquiryType } from '../../generated/models/inquiry-type';
import { SymptomReport } from '../../generated/models/symptom-report';
import { I18NPipe } from '../../pipes/i18n.pipe';
import { SymptomInquirySubmit } from '../interface/symptom-inquiry-submit.interface';
import { SymptomInquiryInformationViewModel } from '../models/symptom-inquiry-information-view-model';
import { FieldService } from '../services/field.service';
import { SetAdditionalQuestions, SetNextOfKinAnswer } from '../store/actions/inquiry.action';
import { FormState } from '../store/reducers/form.reducer';
import { FormEngineState } from '../store/reducers/state';
import { getFormsState } from '../store/selectors/form.selectors';
import { getAdditionalQuestions, getUnselectedSymptoms } from '../store/selectors/inquiry.selectors';

const DELAY = 500;
const TRIES_TO_WAIT_BEFORE_SUBMIT = 960;

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-inquiry-summary',
  templateUrl: './inquiry-summary.component.html',
  styleUrls: ['./inquiry-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class InquirySummaryComponent implements OnInit {
  @Input() formVisibility: { [type: string]: boolean };
  @Input() info: SymptomInquiryInformationViewModel;
  @Input() isValid: boolean;
  @Input() saveTranslationKey: string;
  @Input() askNextOfKin: boolean;
  @Input() askAdditionalQuestions: boolean;
  @Input() secondaryButtonTranslationKey: string;

  @Output() canceled = new EventEmitter<void>();
  @Output() save = new EventEmitter<SymptomInquirySubmit>();

  public showAdditionalQuestions: boolean;
  public additionalQuestions = '';
  public nextOfKinAnswered = false;
  public questionnaire: SymptomReport;
  public formSubmitted = false;
  public noSymptoms$: Observable<string[]>;

  private destroy$ = new Subject<boolean>();
  private uploading = false;

  constructor(
    private store: Store<FormEngineState>,
    private cd: ChangeDetectorRef,
    private i18n: I18NPipe,
    private loading: LoadingSpinnerService,
    private fieldService: FieldService
  ) {}

  ngOnInit() {
    this.checkAdditionalQuestionsVisibility();
    this.listenForFormChanges();
    this.noSymptoms$ = this.store.select(getUnselectedSymptoms);
  }

  public onChange(value) {
    this.store.dispatch(new SetAdditionalQuestions(value));
  }

  public toggleNextOfKin() {
    this.nextOfKinAnswered = !this.nextOfKinAnswered;
    this.store.dispatch(new SetNextOfKinAnswer(this.nextOfKinAnswered));
    this.questionnaire = {
      ...this.questionnaire,
      nextOfKinAnswer: this.nextOfKinAnswered
    };
    this.cd.markForCheck();
  }

  public sendToClinic() {
    if (!this.isValid) {
      this.fieldService.validate();
      return;
    }

    if (this.formSubmitted) {
      return;
    }
    this.formSubmitted = true;

    this.loading.loading();
    this.submit();
  }

  private submit(tryCount = 0) {
    if (this.uploading && tryCount < TRIES_TO_WAIT_BEFORE_SUBMIT) {
      setTimeout(() => {
        this.submit(tryCount + 1);
      }, DELAY);
      return;
    }

    const message = {
      title: this.i18n.transform('symptomInquiry.submitSymptomReport.message.title'),
      content: this.i18n.transform('symptomInquiry.submitSymptomReport.message.content')
    };

    this.save.next({
      questionnaire: this.questionnaire,
      messageId: this.info.messageId,
      patientId: this.info.patientId,
      message
    });
  }

  private listenForFormChanges() {
    // Take first state without debounce
    this.store
      .select(getFormsState)
      .pipe(take(1))
      .subscribe(initialState => {
        this.parseForms(initialState);

        this.store
          .select(getFormsState)
          .pipe(takeUntil(this.destroy$), debounceTime(DELAY))
          .subscribe(state => {
            this.parseForms(state);
          });
      });
  }

  private checkAdditionalQuestionsVisibility() {
    const inquiryType = this.info.symptomInquiry.type;

    this.showAdditionalQuestions =
      this.askAdditionalQuestions &&
      !this.info.symptomInquiry.subInquiry &&
      (inquiryType === InquiryType.TREATMENT_VISIT || inquiryType === InquiryType.CLINIC_APPOINTMENT);

    if (this.showAdditionalQuestions) {
      this.listenForAdditionalQuestions();
    }
  }

  private listenForAdditionalQuestions() {
    this.store
      .select(getAdditionalQuestions)
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        if (value) {
          this.additionalQuestions = value;

          this.questionnaire = {
            ...this.questionnaire,
            additionalQuestions: value
          };
        }
      });
  }

  private parseForms(state: FormState) {
    const uploading: { [form: string]: boolean } = {};
    const symptoms = [];

    Object.entries(state.forms).forEach(([key, value]) => {
      if (key === 'distress' && (value.distressIntensity === undefined || value.distressIntensity === null)) {
        return;
      }

      if (key === 'weight' && (value.weight === undefined || value.weight === null)) {
        return;
      }

      // If the questionnaire is not status check, don't submit its data to BE
      if (this.info.symptomInquiry.type !== InquiryType.TO_POST_TREATMENT && key === 'statusCheck') {
        return;
      }

      // skip the case for statusCheck
      if (key !== 'statusCheck' && !this.formVisibility[key]) {
        return;
      }

      const symptom = { ...value };
      uploading[key] = symptom.uploading;

      this.fieldService.clearViewValues(symptom);

      symptom.classType = symptom.type = key;
      if (!symptom.symptomDate) {
        symptom.symptomDate = new Date();
        // for status check questionnaire, the symptom should be always today
        if (this.info.symptomInquiry.type === InquiryType.TO_POST_TREATMENT) {
          symptom.dateRange = [symptom.symptomDate];
          symptom.peakDate = symptom.symptomDate;
        }
      }

      symptoms.push(symptom);
    });

    this.questionnaire = {
      symptoms,
      additionalQuestions: this.additionalQuestions
    };
    this.uploading = Object.values(uploading).some(val => {
      return val;
    });

    this.cd.markForCheck();
  }
}
