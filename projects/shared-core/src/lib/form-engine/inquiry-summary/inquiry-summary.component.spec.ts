import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockModule } from 'ng-mocks';
import { InquirySummaryComponent } from './inquiry-summary.component';
import { MocksModule } from '@shared-core/testing';
import { QuestionaryType } from '../../generated/models/questionary-type';
import { NO_ERRORS_SCHEMA, NgModule } from '@angular/core';
import { Gender } from '../../generated/models/gender';
import { QuestionnaireSummaryContentComponent } from '../summary/questionnaire-summary-content.component';

@NgModule({
    declarations: [QuestionnaireSummaryContentComponent],
    exports: [QuestionnaireSummaryContentComponent],
})
class QuestionnaireSummaryContentComponentModule {}

describe('InquirySummaryComponent', () => {
    let component: InquirySummaryComponent;
    let fixture: ComponentFixture<InquirySummaryComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule, MockModule(QuestionnaireSummaryContentComponentModule)],
            declarations: [InquirySummaryComponent],
            schemas: [NO_ERRORS_SCHEMA],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(InquirySummaryComponent);
        component = fixture.componentInstance;
        component.info = {
            symptomInquiry: {},
            gender: Gender.FEMALE,
        };
        component.formVisibility = {
            [QuestionaryType.BOUNCE_HADS]: true,
        };
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
