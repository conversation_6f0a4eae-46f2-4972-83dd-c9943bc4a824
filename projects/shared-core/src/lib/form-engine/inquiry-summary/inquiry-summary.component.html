<div class="form-element-container">
  <div class="form-group" *ngIf="showAdditionalQuestions">
    <h4 id="additional-questions-label">{{ 'patient.adverseEventQuestionnaire.additionalQuestions.title' | i18n }}</h4>
    <div class="input-wrapper">
      <textarea
        class="form-control textarea-field"
        id="aeq-additional-questions"
        name="aeq-additional-questions"
        rows="4"
        aria-labelledby="additional-questions-label"
        [ngModel]="additionalQuestions"
        (ngModelChange)="onChange($event)"
      ></textarea>
    </div>
  </div>
  <div class="wizard-actions upper-wizard-actions">
    <div class="next-of-kin-section horizontally-centered" *ngIf="askNextOfKin">
      <div class="checkbox">
        <input type="checkbox" id="next-of-kin" name="next-of-kin" [checked]="nextOfKinAnswered" (change)="toggleNextOfKin()" />
        <label for="next-of-kin">{{ 'symptomInquiry.summary.nextOfKin.checkboxLabel' | i18n }}</label>
      </div>
    </div>
  </div>
  <questionnaire-summary-content
    [theQuestionary]="questionnaire"
    [gender]="info.gender"
    [onlySubmittedPhotos]="false"
    [noSymptoms]="noSymptoms$ | async"
  ></questionnaire-summary-content>
  <div class="wizard-actions">
    <div class="wizard-button">
      <div class="summary-step-buttons center">
        <ds-button variation="secondary" class="button-cancel" (buttonClick)="canceled.emit()">
          {{ secondaryButtonTranslationKey | i18n }}
        </ds-button>
        <ds-button class="button-send-to-clinic" (buttonClick)="sendToClinic()">
          {{ saveTranslationKey | i18n }}
        </ds-button>
      </div>
    </div>
  </div>
</div>
