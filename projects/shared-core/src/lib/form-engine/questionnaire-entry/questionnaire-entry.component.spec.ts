import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { QuestionnaireEntryComponent } from './questionnaire-entry.component';
import { MocksModule } from '@shared-core/testing';

describe('QuestionnaireEntryComponent', () => {
    let component: QuestionnaireEntryComponent;
    let fixture: ComponentFixture<QuestionnaireEntryComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [QuestionnaireEntryComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(QuestionnaireEntryComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
