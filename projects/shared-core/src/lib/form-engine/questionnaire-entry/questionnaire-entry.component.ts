import { ChangeDetectorRef, Component, EventEmitter, HostBinding, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { QuestionnaireInquirySubmit } from '../interface/questionnaire-inquiry-submit.interface';
import { QuestionnaireInquiryInformationViewModel } from '../models/questionnaire-inquiry-information-view-model';
import { ScrollBehavior } from '../models/scroll-behavior.enum';
import { FieldService } from '../services/field.service';
import { FormScrollService } from '../services/form-scroll.service';
import { NextSection, PreviousSection, ResetFormState } from '../store/actions/form.actions';
import { LoadQuestionnaire, ResetQuestionnaireState } from '../store/actions/questionnaire.action';
import { FormEngineState } from '../store/reducers/state';
import { selectCurrentSection, selectForm } from '../store/selectors/form.selectors';
import { getFormValidity } from '../store/selectors/inquiry.selectors';
import { getQuestionnaireState } from '../store/selectors/questionnaire.selectors';
import { NoonalyticCategories } from '../../utils/analytics/models';
import { QuestionnaireInquiryInformation } from '../../generated/models/questionnaire-inquiry-information';
import { I18NPipe } from '../../pipes/i18n.pipe';
import { DsModalService } from '../../ds/services/modal.service';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-questionnaire-entry',
  templateUrl: './questionnaire-entry.component.html',
  styleUrls: ['./questionnaire-entry.component.scss']
})
export class QuestionnaireEntryComponent implements OnInit, OnDestroy {
  @HostBinding('class.theme--patient') get forPatientHostClass() {
    return !this.showHeader;
  }

  @Input() showHeader: boolean;
  @Input() questionnaireId: string;
  @Input() askNextOfKin: boolean;
  @Input() showCloseButton = false;
  @Input() translationKeyHeader: string;
  @Input() translationKeyFooter: string;

  analyticsInitialized = false;
  _analyticCategory: NoonalyticCategories = null;
  @Input()
  set analyticCategory(category: NoonalyticCategories) {
    this._analyticCategory = category;
    if (this._analyticCategory && this.info && !this.analyticsInitialized) {
      this.analyticsInitialized = true;
    }
  }

  get analyticCategory() {
    return this._analyticCategory;
  }

  @Output() handleCancel = new EventEmitter<void>();
  @Output() goBack = new EventEmitter<boolean>();
  @Output() setPatientId = new EventEmitter<string>();
  @Output() save = new EventEmitter<QuestionnaireInquirySubmit>();
  @Output() closeButtonClicked = new EventEmitter<void>();
  @Output() questionnaireLoaded = new EventEmitter<QuestionnaireInquiryInformation>();

  public showSummary = false;
  public isValid = false;
  public info: QuestionnaireInquiryInformationViewModel;
  public summaryEndEvent: any;

  private initialized = false;
  private totalSections: number;
  private currentSection: number;

  private destroy$ = new Subject<boolean>();

  constructor(
    private store: Store<FormEngineState>,
    private fieldService: FieldService,
    private cd: ChangeDetectorRef,
    private formScrollService: FormScrollService
  ) {}

  ngOnInit() {
    // Make sure questionnaire form is loaded cleanly
    this.store.dispatch(new ResetQuestionnaireState());
    this.store.dispatch(new LoadQuestionnaire(this.questionnaireId));

    this.store
      .select(getQuestionnaireState)
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => {
        if (state.loaded && !this.initialized) {
          this.setPatientId.next(state.info.patientId);
          if (!state.info.answerable) {
            this.goBack.next(state.info.answerable);
            return;
          }

          this.initialized = true;
          this.info = new QuestionnaireInquiryInformationViewModel();
          this.info.copyFields(state.info);
          this.listenForSection();

          if (this.analyticCategory && this.info && !this.analyticsInitialized) {
            this.analyticsInitialized = true;
          }

          this.questionnaireLoaded.emit(state.info);
        }
      });

    this.store
      .select(getFormValidity)
      .pipe(takeUntil(this.destroy$))
      .subscribe(valids => {
        this.isValid = Object.values(valids).every(valid => {
          return valid;
        });
        this.cd.markForCheck();
      });
  }

  ngOnDestroy() {
    this.store.dispatch(new ResetQuestionnaireState());
    this.store.dispatch(new ResetFormState());
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  public next() {
    if (this.isValid) {
      if (this.currentSection < this.totalSections) {
        this.store.dispatch(new NextSection(this.info.questionnaireType));
      } else {
        this.showSummary = true;
        this.scrollToSummary();
      }
    } else {
      this.fieldService.validate();
    }
  }

  public previous() {
    if (this.showSummary) {
      this.showSummary = false;
    }
    if (this.currentSection > 1) {
      this.store.dispatch(new PreviousSection(this.info.questionnaireType));
    }
  }

  public cancel() {
    this.handleCancel.next();
  }

  public onClose() {
    this.closeButtonClicked.next();
  }

  private listenForSection() {
    const complete$ = new Subject<boolean>();
    this.store
      .select(selectForm(this.info.questionnaireType))
      .pipe(takeUntil(complete$))
      .subscribe(form => {
        if (form) {
          this.totalSections = form.totalSections;
          complete$.next(true);
          complete$.complete();

          this.store
            .select(selectCurrentSection(this.info.questionnaireType))
            .pipe(takeUntil(this.destroy$))
            .subscribe(currentSection => {
              this.currentSection = currentSection;
            });
        }
      });
  }

  public scrollToSummary() {
    this.formScrollService.scrollToElement('.final-summary', ScrollBehavior.SMOOTH);
  }

  public onSave($event): void {
    this.save.next($event);
  }
}
