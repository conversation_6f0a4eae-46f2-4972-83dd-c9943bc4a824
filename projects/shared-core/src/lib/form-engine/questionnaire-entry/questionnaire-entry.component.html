<div id="wizard">
  <div *ngIf="showCloseButton" class="wizard-top-actions">
    <button class="close-button" [attr.aria-label]="'general.close' | i18n" (click)="onClose()"></button>
  </div>

  <div class="symptom-forms">
    <div class="symptom-form nh-grid-form-fullpage">
      <nh-form-generator
        *ngIf="info"
        [formType]="info.questionnaireType"
        [info]="info"
        (next)="next()"
        (previous)="previous()"
        (canceled)="cancel()"
      ></nh-form-generator>
    </div>
  </div>

  <div class="wizard-section nh-grid-form-fullpage" *ngIf="showSummary">
    <nh-questionnaire-summary
      class="final-summary"
      [info]="info"
      [isValid]="isValid"
      [askNextOfKin]="askNextOfKin"
      (canceled)="cancel()"
      (save)="onSave($event)"
    ></nh-questionnaire-summary>
  </div>
</div>
