import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { YesNoComponent } from './yes-no.component';
import { MocksModule } from '@shared-core/testing';
import { Store } from '@ngrx/store';
import { SelectSymptomType, UnselectSymptomType } from '../store/actions/inquiry.action';
import { SymptomType } from '../../generated/models/symptom-type';

describe('YesNoComponent', () => {
    let component: YesNoComponent;
    let fixture: ComponentFixture<YesNoComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [YesNoComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(YesNoComponent);
        component = fixture.componentInstance;
        component.type = SymptomType.SYMPTOM_CHILLS;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('disables inputs when symptom is in diary', () => {
        fixture = TestBed.createComponent(YesNoComponent);
        component = fixture.componentInstance;
        component.type = SymptomType.SYMPTOM_CHILLS;
        component.fromDiary = [SymptomType.SYMPTOM_CHILLS];
        fixture.detectChanges();
        const hostElement: HTMLElement = fixture.nativeElement;
        const disabledInputs = hostElement.querySelectorAll('input[disabled]');
        expect(disabledInputs.length).toBe(2);
    });

    it('dispatches SelectSymptomType when selected', () => {
        const store = TestBed.inject(Store);
        const spy = jest.spyOn(store, 'dispatch');
        component.inputChanged(true);
        expect(spy).toHaveBeenCalledWith(new SelectSymptomType('chills'));
    });

    it('dispatches UnselectSymptomType when unselected', () => {
        const store = TestBed.inject(Store);
        const spy = jest.spyOn(store, 'dispatch');
        component.inputChanged(false);
        expect(spy).toHaveBeenCalledWith(new UnselectSymptomType('chills'));
    });
});
