import { ChangeDetectionStrategy, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SymptomType } from '../../generated/models/symptom-type';
import { HyphenedPipe } from '../../pipes/hyphened.pipe';
import { SelectSymptomType, UnselectSymptomType } from '../store/actions/inquiry.action';
import { FormEngineState } from '../store/reducers/state';
import { getSymptomTypesState, selectType } from '../store/selectors/inquiry.selectors';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-yes-no',
    templateUrl: './yes-no.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class YesNoComponent implements OnInit, OnD<PERSON>roy {
    @Input() type: SymptomType;
    @Input() fromDiary: SymptomType[];

    public selected: boolean;
    public disabled: boolean;

    private destroy$ = new Subject<boolean>();
    private typeFound$ = new Subject<boolean>();

    constructor(private store: Store<FormEngineState>, private hyphened: HyphenedPipe) {}

    ngOnInit() {
        this.disabled = this.fromDiary && this.fromDiary.includes(this.type);
        this.store
            .select(getSymptomTypesState)
            .pipe(takeUntil(this.typeFound$))
            .subscribe((state) => {
                if (this.type && state && (state[this.type] || state[this.type] === false)) {
                    this.subscribeToType();
                    this.typeFound$.next(true);
                    this.typeFound$.complete();
                }
            });
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public hyphenedIdentifier() {
        return this.hyphened.transform(this.type);
    }

    public inputChanged(value: boolean) {
        if (value) {
            this.store.dispatch(new SelectSymptomType(this.type));
        } else {
            this.store.dispatch(new UnselectSymptomType(this.type));
        }
    }

    private subscribeToType() {
        this.store
            .select(selectType(this.type))
            .pipe(takeUntil(this.destroy$))
            .subscribe((state) => {
                this.selected = state;
            });
    }
}
