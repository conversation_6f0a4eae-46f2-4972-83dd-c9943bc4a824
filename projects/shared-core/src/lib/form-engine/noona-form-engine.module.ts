import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { ScPipesModule } from '../pipes/sc-pipes.module';
import { UtilsModule } from '../utils/utils.module';
import { CheckboxListHeaderComponent } from './components/checkbox-list-header/checkbox-list-header.component';
import { CheckboxListWithExtraFieldsComponent } from './components/checkbox-list-with-extra-fields/checkbox-list-with-extra-fields.component';
import { CheckboxListComponent } from './components/checkbox-list/checkbox-list.component';
import { DateRangeItemComponent } from './components/date-range-item/date-range-item.component';
import { DateRangePickerComponent } from './components/date-range-item/date-range-picker/date-range-picker.component';
import { DatepickerComponent } from './components/date-range-item/datepicker/datepicker.component';
import { DateComponent } from './components/date/date.component';
import { DynamicFormFieldDirective } from './components/dynamic-form-field.directive';
import { EarlierSymptomComponent } from './components/earlier-symptom/earlier-symptom.component';
import { FieldStatusIndicatorComponent } from './components/field-status-indicator/field-status-indicator.component';
import { FieldStatusMessageComponent } from './components/field-status-indicator/field-status-message.component';
import { FormErrorIndicatorComponent } from './components/form-error-indicator/form-error-indicator.component';
import { FormQuestionComponent } from './components/form-question/form-question.component';
import { InputGroupRenderContainerComponent } from './components/input-group-render/input-group-render-container.component';
import { InputGroupRenderComponent } from './components/input-group-render/input-group-render.component';
import { InputGroupComponent } from './components/input-group/input-group.component';
import { InputHeaderComponent } from './components/input-header/input-header.component';
import { IntegerFieldComponent } from './components/integer-field/integer-field.component';
import { MeasurementComponent } from './components/measurement/measurement.component';
import { MultiselectComponent } from './components/multiselect/multiselect.component';
import { NhDropdownComponent } from './components/nh-dropdown/nh-dropdown.component';
import { PainPointerListComponent } from './components/pain-pointer-list/pain-pointer-list.component';
import { PhotoItemComponent } from './components/photo-item/photo-item.component';
import { QuestionGroupComponent } from './components/question-group/question-group.component';
import { RadioListWithExtraFieldsComponent } from './components/radio-list-with-extra-fields/radio-list-with-extra-fields.component';
import { RadioListComponent } from './components/radio-list/radio-list.component';
import { SelectWithRadioComponent } from './components/select-with-radio/select-with-radio.component';
import { SliderItemComponent } from './components/slider-item/slider-item.component';
import { SubHeaderComponent } from './components/sub-header/sub-header.component';
import { TextAreaComponent } from './components/text-area/text-area.component';
import { TextInputHeaderComponent } from './components/text-input-header/text-input-header.component';
import { ToggleItemComponent } from './components/toggle-item/toggle-item.component';
import { WizardSectionComponent } from './components/wizard-section/wizard-section.component';
import { YesNoDateComponent } from './components/yes-no-date/yes-no-date.component';
import { CrfFormEntryComponent } from './crf-form-entry/crf-form-entry.component';
import { FormGeneratorComponent } from './form-generator/form-generator.component';
import { InquiryEntryComponent } from './inquiry-entry/inquiry-entry.component';
import { InquirySummaryComponent } from './inquiry-summary/inquiry-summary.component';
import { QuestionnaireEntryComponent } from './questionnaire-entry/questionnaire-entry.component';
import { QuestionnaireSummaryComponent } from './questionnaire-summary/questionnaire-summary.component';
import { CrfFormEffects } from './store/effects/crf.effect';
import { FormEffects } from './store/effects/form.effect';
import { InquiryEffects } from './store/effects/inquiry.effect';
import { PatientEffects } from './store/effects/patient.effect';
import { QuestionnaireEffects } from './store/effects/questionnaire.effect';
import { reducers } from './store/reducers/reducers';
import { FORM_ENGINE_MODULE_NAME, getInitialFormEngineState } from './store/reducers/state';
import { QuestionnaireSummaryContentComponent } from './summary/questionnaire-summary-content.component';
import { SymptomSummaryContentComponent } from './summary/symptom-summary-modal/symptom-summary-content.component';
import { SymptomSelectionComponent } from './symptom-selection/symptom-selection.component';
import { MultipleSymptomSelectionComponent } from './symptom/multiple-symptom-selection/multiple-symptom-selection.component';
import { SymptomAlertSummaryComponent } from './symptom/symptom-alert-summary/symptom-alert-summary.component';
import { SymptomEntriesComponent } from './symptom/symptom-entries/symptom-entries.component';
import { SymptomSummariesComponent } from './symptom/symptom-summaries/symptom-summaries.component';
import { YesNoComponent } from './yes-no/yes-no.component';
import { SimpleDateInputComponent } from './components/simple-date-input/simple-date-input.component';
import { EventModalComponent } from './components/event-modal/event-modal.component';
import { SubformComponent } from './components/sub-form/subform.component';
import { NumericFieldComponent } from './components/numeric-field/numeric-field.component';
import { TextInputFieldComponent } from './components/text-input-field/text-input-field.component';
import { StatusCheckSummaryComponent } from './summary/status-check-summary/status-check-summary.component';
import { TextBlockComponent } from './components/text-block/text-block.component';
import { DsIconModule } from '../ds/components/icon/icon.module';
import { DsButtonModule } from '../ds/components/button/button.module';
import { DsModalModule } from '../ds/components/modal/modal.module';
import { DsDataTableModule } from '../ds/components/datatable/datatable.module';
import { DsCheckboxModule } from '../ds/components/checkbox/checkbox.module';
import { DsActionButtonModule } from '../ds/components/action-button/action-button.module';
import { DsActionsDropdownModule } from '../ds/components/actions-dropdown/actions-dropdown.module';
import { DsRangeSliderModule } from '../ds/components/range-slider/range-slider.module';
import { FormElementContainerDirective } from './directives/form-element-container/form-element-container.directive';
import { OnDestroyPainDirective } from './components/pain-pointer-list/on-destroy-pain-directive';

const effects: any[] = [InquiryEffects, FormEffects, QuestionnaireEffects, CrfFormEffects, PatientEffects];

const demoAppExports = [
  MeasurementComponent,
  RadioListWithExtraFieldsComponent,
  RadioListComponent,
  CheckboxListWithExtraFieldsComponent,
  MultiselectComponent,
  SelectWithRadioComponent,
  FieldStatusIndicatorComponent,
  FieldStatusMessageComponent,
  DateComponent,
  EventModalComponent,
  QuestionGroupComponent,
  NumericFieldComponent,
  CheckboxListComponent
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    StoreModule.forFeature(FORM_ENGINE_MODULE_NAME, reducers, {
      initialState: getInitialFormEngineState
    }),
    EffectsModule.forFeature(effects),
    ScPipesModule,
    UtilsModule,
    DsButtonModule,
    DsIconModule,
    NgSelectModule,
    DsDataTableModule,
    DsCheckboxModule,
    DsActionButtonModule,
    DsRangeSliderModule,
    DsModalModule,
    DsActionsDropdownModule,
    ReactiveFormsModule
  ],
  declarations: [
    SimpleDateInputComponent,
    FieldStatusIndicatorComponent,
    FieldStatusMessageComponent,
    FormQuestionComponent,
    QuestionnaireEntryComponent,
    InquiryEntryComponent,
    SymptomSelectionComponent,
    YesNoComponent,
    FormGeneratorComponent,
    DynamicFormFieldDirective,
    WizardSectionComponent,
    QuestionGroupComponent,
    RadioListComponent,
    TextAreaComponent,
    InputHeaderComponent,
    TextInputHeaderComponent,
    FormErrorIndicatorComponent,
    SliderItemComponent,
    IntegerFieldComponent,
    PainPointerListComponent,
    DateRangeItemComponent,
    DateRangePickerComponent,
    DatepickerComponent,
    PhotoItemComponent,
    CheckboxListComponent,
    CheckboxListHeaderComponent,
    InquirySummaryComponent,
    SubHeaderComponent,
    EarlierSymptomComponent,
    ToggleItemComponent,
    InputGroupComponent,
    QuestionnaireSummaryComponent,
    NhDropdownComponent,
    SymptomEntriesComponent,
    MultipleSymptomSelectionComponent,
    SymptomSummariesComponent,
    SymptomAlertSummaryComponent,
    SymptomSummaryContentComponent,
    QuestionnaireSummaryContentComponent,
    InputGroupRenderComponent,
    InputGroupRenderContainerComponent,
    CrfFormEntryComponent,
    YesNoDateComponent,
    DateComponent,
    MultiselectComponent,
    MeasurementComponent,
    CheckboxListWithExtraFieldsComponent,
    RadioListWithExtraFieldsComponent,
    SelectWithRadioComponent,
    EventModalComponent,
    SubformComponent,
    NumericFieldComponent,
    TextInputFieldComponent,
    StatusCheckSummaryComponent,
    TextBlockComponent,
    FormElementContainerDirective,
    OnDestroyPainDirective
  ],
  exports: [
    NhDropdownComponent,
    SymptomEntriesComponent,
    SymptomSummariesComponent,
    QuestionnaireSummaryComponent,
    QuestionnaireEntryComponent,
    InputGroupRenderContainerComponent,
    InquiryEntryComponent,
    SymptomSummaryContentComponent,
    QuestionnaireSummaryContentComponent,
    CrfFormEntryComponent,
    StatusCheckSummaryComponent,
    FormGeneratorComponent,
    DsRangeSliderModule,
    ...demoAppExports
  ]
})
export class NoonaFormEngineModule {}
