import { Directive, ElementRef, Input, Renderer2 } from '@angular/core';
import kebabCase from 'lodash/kebabCase';
import { formElementContainerDefaultStyles, formElementContainerErrorStyles } from './form-element-container.const';
import { FormElementContainerStyles } from './form-element-container.interface';

/**
 * Note: the current implementation of FieldService#updateFormFieldStyle is unstable
 * due to some change detection issues, spaghetti code validation triggers
 * and any changes in the markup of the components that might affect the selectors below so it is more secure
 * to use a directive to manipulate the styles of the form element container
 *
 * @example
 * Please, refer to projects/shared-core/src/lib/form-engine/components/pain-pointer-list/pain-pointer-list.component.html
 * for a usage example
 */
@Directive({
    selector: '[nsFormElementContainer]',
})
export class FormElementContainerDirective {
    @Input() set nsFormElementContainer($event: boolean) {
        ['padding', 'border', 'borderRadius'].forEach((i: keyof FormElementContainerStyles) => {
            this.setStyle($event, i);
        });
    }

    constructor(private element: ElementRef, private renderer: Renderer2) {}

    private setStyle($event: boolean, property: keyof FormElementContainerStyles): void {
        const style = $event ? formElementContainerErrorStyles[property] : formElementContainerDefaultStyles[property];
        this.renderer.setStyle(this.element.nativeElement, kebabCase(property), style);
    }
}
