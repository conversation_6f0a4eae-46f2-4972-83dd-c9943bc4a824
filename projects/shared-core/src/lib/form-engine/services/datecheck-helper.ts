import { DateType } from '../../abstract-services/form-specific-handler.service';
import moment from 'moment';

export const isAfterDate = (
    dateToCheck: DateType,
    rangeStart: DateType,
    isUnkownDate: boolean,
    inclusive: boolean = true
): boolean => {
    const inclusivity = inclusive ? '[]' : '()';
    return isInDateRange(dateToCheck, rangeStart, null, isUnkownDate, inclusivity);
};

export const isBeforeDate = (
    dateToCheck: DateType,
    rangeEnd: DateType,
    isUnknownDate: boolean,
    inclusive: boolean = true
): boolean => {
    const inclusivity = inclusive ? '[]' : '()';
    return isInDateRange(dateToCheck, null, rangeEnd, isUnknownDate, inclusivity);
};

export const isInDateRange = (
    dateToCheck: DateType,
    rangeStart: DateType,
    rangeEnd: DateType,
    isUnknownDate: boolean,
    inclusivity: '()' | '[)' | '(]' | '[]' = '[]'
): boolean => {
    const dateToCheckMoment = moment(dateToCheck);
    const rangeStartMoment = rangeStart ? moment(rangeStart) : moment('01-01-1000');
    const rangeEndMoment = rangeEnd ? moment(rangeEnd) : moment('01-01-3000');
    const granularity = isUnknownDate ? 'month' : 'day';
    return dateToCheckMoment.isBetween(rangeStartMoment, rangeEndMoment, granularity, inclusivity);
};
