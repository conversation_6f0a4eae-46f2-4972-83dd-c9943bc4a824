import { Injectable } from '@angular/core';

import moment, { Moment } from 'moment';
import { NoonaLocaleService } from '../../abstract-services/noona-locale.service';
import { NoonaDatepickerOptions } from '../interface/datepicker-options';

@Injectable({
    providedIn: 'root',
})
export class DatepickerService {
    private lastDayMap: { [key: string]: boolean } = {};
    private firstDayMap: { [key: string]: boolean } = {};

    constructor(private localeService: NoonaLocaleService) {}

    /**
     * Gets the datepicker options.
     *
     */
    public getDatepickerOptions(minDate?: Date, maxDate?: Date): NoonaDatepickerOptions {
        const options: NoonaDatepickerOptions = {
            language: this.localeService.getLocale(),
            maxViewMode: 0,
            inline: true,
            autoclose: true,
        };

        if (minDate) {
            options.startDate = minDate;
        }

        if (maxDate) {
            options.endDate = maxDate;
        }

        return options;
    }

    /**
     * Is the given date the last day of the month.
     */
    public isLastDayOfMonth(date: any): boolean {
        return moment(date).isSame(moment(date).endOf('month'), 'day');
    }

    /**
     * Is the given date the first day of the month
     *
     */
    public isFirstDayOfMonth(date: any): boolean {
        return moment(date).isSame(moment(date).startOf('month'), 'day');
    }

    /**
     * Is the given date the last day of the month. Does essentially same as the {@link isLastDayOfMonth} but caches the
     * queried dates into map so querying same dates multiple times is much faster
     *
     */
    public isLastDayOfMonthMemoized(isoDate: string): boolean {
        if (!isoDate) {
            return false;
        }
        if (!this.lastDayMap[isoDate]) {
            this.lastDayMap[isoDate] = this.isLastDayOfMonth(moment(isoDate));
        }
        return this.lastDayMap[isoDate];
    }

    /**
     * Is the given date the first day of the month. Does essentially same as the {@link isFirstDayOfMonth} but caches the
     * queried dates into map so querying same dates multiple times is much faster
     *
     */
    public isFirstDayOfMonthMemoized(isoDate: string): boolean {
        if (!isoDate) {
            return false;
        }
        if (!this.firstDayMap[isoDate]) {
            this.firstDayMap[isoDate] = this.isFirstDayOfMonth(moment(isoDate));
        }
        return this.firstDayMap[isoDate];
    }

    /**
     * Gets the given date with current time.
     *
     */
    public getFormattedDate(date: Moment): Moment {
        const today = moment();

        return date.set({
            hour: today.get('hour'),
            minute: today.get('minute'),
            second: today.get('second'),
        });
    }

    /**
     * Sort an array of Moment dates ascending.
     */
    public sortMomentArrayAscending(dates: Moment[]): Moment[] {
        return dates.sort((a: Moment, b: Moment) => {
            return a.toDate().getTime() - b.toDate().getTime();
        });
    }

    /**
     * Takes an array of dates and identifies intervals in it.
     * Returns the different interval start and end dates as well as an array of arrays representing the different intervals.
     */
    public calculateDateRangeStartsAndEnds(allDates: any[], isoFormat?: string) {
        const datesSorted = this.sortMomentArrayAscending(
            allDates.map((date) => {
                return moment(date);
            })
        );

        const rangeStarts: Moment[] = [];
        const rangeEnds: Moment[] = [];
        const segmented: Moment[][] = [];
        const rangeStartsISO: string[] = [];
        const rangeEndsISO: string[] = [];

        let currentSegement: Moment[];
        for (let i = 0; i < datesSorted.length; i++) {
            if (!currentSegement) {
                currentSegement = [];
                segmented.push(currentSegement);
            }

            const dCurrent = datesSorted[i];
            if (i === 0) {
                rangeStarts.push(dCurrent);
                if (isoFormat) {
                    rangeStartsISO.push(dCurrent.format(isoFormat));
                }
            }

            if (i === datesSorted.length - 1) {
                rangeEnds.push(dCurrent);
                if (isoFormat) {
                    rangeEndsISO.push(dCurrent.format(isoFormat));
                }
                break;
            }

            const dNext = datesSorted[i + 1];
            const nextDateIsTomorrow = dCurrent.clone().add(1, 'day').isSame(dNext, 'day');
            if (!nextDateIsTomorrow) {
                rangeEnds.push(dCurrent);
                if (isoFormat) {
                    rangeEndsISO.push(dCurrent.format(isoFormat));
                }

                rangeStarts.push(dNext);
                if (isoFormat) {
                    rangeStartsISO.push(dNext.format(isoFormat));
                }
                currentSegement = undefined;
            }
        }

        return {
            rangeStarts,
            rangeEnds,
            segmented,
            rangeStartsISO,
            rangeEndsISO,
        };
    }
}
