import { select, Store } from '@ngrx/store';
import { selectField } from '../store/selectors/form.selectors';
import { distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { FormType } from '../../constants';
import isArray from 'lodash/isArray';
import { selectFieldStatuses } from '../store/selectors/field-status.selectors';
import { FieldStatus, FieldStatusType } from '../models/field-status.interface';
import { Subject, Observable } from 'rxjs';
import {
    selectFormVariable,
    selectFormVariableArrayValue,
    selectFormVariableValue,
} from '../store/selectors/variables.selectors';

const DEBOUNCE_TIME = 100;

export const safeSelectField = (
    store: Store<any>,
    destroy$: Subject<boolean>,
    key: string,
    form: FormType
): Observable<any> => {
    return store.pipe(select(selectField(form, key)), takeUntil(destroy$), distinctUntilChanged(storeValueComparison));
};

export const safeSelectVariableValue = (
    store: Store<any>,
    destroy$: Subject<boolean>,
    key: string
): Observable<any> => {
    return store.pipe(
        select(selectFormVariableValue(key)),
        takeUntil(destroy$),
        distinctUntilChanged(storeValueComparison)
    );
};

export const safeSelectVariableArrayValue = (
    store: Store<any>,
    destroy$: Subject<boolean>,
    key: string
): Observable<any> => {
    return store.pipe(
        select(selectFormVariableArrayValue(key)),
        takeUntil(destroy$),
        distinctUntilChanged(storeValueComparison)
    );
};

export const safeSelectVariable = (store: Store<any>, destroy$: Subject<boolean>, key: string): Observable<any> => {
    return store.pipe(select(selectFormVariable(key)), takeUntil(destroy$), distinctUntilChanged(storeValueComparison));
};

export const safeSelectFieldStatus = (
    store: Store<any>,
    destroy$: Subject<boolean>,
    field?: string,
    fsType?: FieldStatusType,
    translationKey?: string,
    includeChildKeys?: boolean
): Observable<FieldStatus[]> => {
    return store.pipe(
        select(selectFieldStatuses(field, fsType, translationKey, includeChildKeys)),
        takeUntil(destroy$),
        distinctUntilChanged(storeValueComparison)
    );
};

const storeValueComparison = (prev: any, cur: any): boolean => {
    if (prev === cur) {
        return true;
    }
    if (isArray(prev) && isArray(cur)) {
        if (prev.length === cur.length) {
            if (prev.length === 0) {
                return true;
            }
            for (let idx = 0; idx < prev.length; idx++) {
                if (prev[idx] !== cur[idx]) {
                    return false;
                }
            }
            return true;
        }
    }
    return false;
};
