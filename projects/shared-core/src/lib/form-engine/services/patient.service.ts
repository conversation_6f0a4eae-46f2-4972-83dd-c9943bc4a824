import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SharedApi } from '../../abstract-services/shared-api.service';
import { Patient } from '../../generated/models/patient';
import { PatientContactInformation } from '../../generated/models/patient-contact-information';

@Injectable({
    providedIn: 'root',
})
export class PatientService {
    constructor(private api: SharedApi) {}

    public getPatient(patientId: string): Observable<Patient> {
        return this.api.medicalApi.getPatientById(patientId);
    }

    public getPatientInformation(patientId: string): Observable<PatientContactInformation> {
        return this.api.medicalApi.getPatientInformation(patientId);
    }
}
