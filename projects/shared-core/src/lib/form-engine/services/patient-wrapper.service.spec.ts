import { Observable, of } from 'rxjs';

import { Application } from '../../models/application';
import { ConfigurationProviderService } from '../../abstract-services/configuration-provider.service';
import { Patient } from '../../generated/models/patient';
import { PatientContactInformation } from '../../generated/models/patient-contact-information';
import { PatientService } from './patient.service';
import { PatientWrapperService } from './patient-wrapper.service';
import { TestBed } from '@angular/core/testing';

class MockPatientService {
    getPatient(patientId: string): Observable<Patient> {
        return of({ id: patientId });
    }
    getPatientInformation(patientId: string): Observable<PatientContactInformation> {
        return of({ id: patientId });
    }
}

class MockConfigurationProviderService {
    site(): Application {
        return Application.PATIENT;
    }
}

describe('PatientWrapperService', () => {
    beforeEach(() => {
        return TestBed.configureTestingModule({
            providers: [
                { provide: PatientService, useClass: MockPatientService },
                { provide: ConfigurationProviderService, useClass: MockConfigurationProviderService },
            ],
        });
    });

    it('should be created', () => {
        const service: PatientWrapperService = TestBed.inject(PatientWrapperService);
        expect(service).toBeTruthy();
    });
});
