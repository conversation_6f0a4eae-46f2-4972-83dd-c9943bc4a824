import { Injectable } from '@angular/core';
import { ValidatorFn, Validators } from '@angular/forms';
import { Observable, Subject } from 'rxjs';
import { SharedSchemaService } from '../../abstract-services/shared-schema.service';
import { photoGroupDescriptionFormKey } from '../../constants';
import { ConditionType } from '../../generated/models/condition-type';
import { FormItemType } from '../../generated/models/form-item-type';
import { ListEntry } from '../../generated/models/list-entry';
import { CamelCasePipe } from '../../pipes/camel-case.pipe';
import { CapitalFirstLetterPipe } from '../../pipes/capital-first-letter.pipe';
import { HyphenedPipe } from '../../pipes/hyphened.pipe';
import { NoonaEncodePipe } from '../../pipes/noona-encode.pipe';
import { Answer } from '../models/answer.interface';
import { FormFieldConfig, Required } from '../models/form-field-config.interface';
import { FormType } from '../models/form-type.enum';
import { InquiryType } from '../models/inquiry-type.enum';
import { ModelValue } from '../models/model-value.interface';
import { RequiredType } from '../models/required-type.enum';
import { StaticShowConditionType } from '../models/static-show-condition-type.enum';
import { StaticShowCondition } from '../models/static-show-condition.interface';
import { INVALID_DATE } from '../models/invalid-date';
import get from 'lodash/get';
import forEach from 'lodash/forEach';
import {
    formElementContainerDefaultStyles,
    formElementContainerErrorStyles,
} from '../directives/form-element-container/form-element-container.const';
import { getStyle } from '../directives/form-element-container/form-element-container.utils';

@Injectable({
    providedIn: 'root',
})
export class FieldService {
    private validate$: Subject<boolean> = new Subject<boolean>();
    private nextFormSection$: Subject<void> = new Subject<void>();

    constructor(
        private schema: SharedSchemaService,
        private camelCase: CamelCasePipe,
        private capitalizeFirstLetter: CapitalFirstLetterPipe,
        private hyphened: HyphenedPipe,
        private encode: NoonaEncodePipe
    ) {}

    getNextFormSectionAsObservable(): Observable<void> {
        return this.nextFormSection$.asObservable();
    }

    emitNextFormSection(): void {
        this.nextFormSection$.next();
    }

    public getListValues(config: FormFieldConfig, listEntries: ListEntry[] = null): ModelValue[] {
        const { formKey, key, valuesKey } = config;
        const options = get(config, ['templateOptions', 'options'], []);
        if (options && options.length > 0) {
            return options;
        }
        const values: ListEntry[] = listEntries || this.schema.getSelectLists()[valuesKey];
        if (!values || values.length < 1) {
            return [];
        }

        const staticShowConditions = this.schema.getFieldValueStaticConditions();
        return values
            .filter((value) => {
                const staticConditionKey =
                    this.camelCase.transform(formKey).replace('Form', '') +
                    this.capitalizeFirstLetter.transform(key) +
                    this.capitalizeFirstLetter.transform(value.key);
                return this.resolveStaticCondition(config, staticShowConditions[staticConditionKey], false);
            })
            .map((value) => {
                return {
                    value: this.convertToRequiredType(value.key),
                    selected: false,
                    translationKey: `${config.translationPrefix}.${value.key}.label`,
                    id: `${formKey}-${this.hyphened.transform(key)}-${this.hyphened.transform(value.key)}`,
                };
            });
    }

    public resolveStaticCondition(
        config: FormFieldConfig,
        staticCondition?: StaticShowCondition,
        shouldHideField?: boolean
    ): boolean {
        if (!staticCondition) {
            return true;
        }

        if (staticCondition.conditionType === ConditionType.AND) {
            return staticCondition.conditions.every((condition) => {
                return this.parseStaticConditionField(config, condition, shouldHideField);
            });
        } else {
            return this.parseStaticConditionField(config, staticCondition, shouldHideField);
        }
    }

    private applyToPostTreatmentCondition(isToPostTreamt: boolean, originalCondition: boolean) {
        const SHOW_FIELD = true;
        return isToPostTreamt ? originalCondition : SHOW_FIELD;
    }

    private parseStaticConditionField(
        config: FormFieldConfig,
        condition: StaticShowCondition,
        shouldHideField: boolean
    ): boolean {
        switch (condition.name) {
            case StaticShowConditionType.TREATMENT_MODULE_CONTAINS:
                return this.containsModuleType(config, condition);

            case StaticShowConditionType.TREATMENT_MODULE_NOT_CONTAINS:
                return !this.containsModuleType(config, condition);

            case StaticShowConditionType.FORM_IS: {
                const requiredType = condition.values[0];

                if (requiredType === FormType.FULL) {
                    return config.fromInquiry && !config.subInquiry;
                }

                if (requiredType === FormType.SUBSET) {
                    return config.subInquiry || !config.fromInquiry;
                }

                if (requiredType === FormType.QUESTIONNAIRE) {
                    return config.fromInquiry;
                }

                throw new Error('Invalid form type provided in condition:' + condition);
            }

            case StaticShowConditionType.FORM_IS_NOT: {
                const requiredType = condition.values[0];

                if (requiredType === FormType.FULL) {
                    return (config.subInquiry && config.fromInquiry) || !config.fromInquiry;
                }

                if (requiredType === FormType.SUBSET) {
                    return !config.subInquiry && config.fromInquiry;
                }

                if (requiredType === FormType.QUESTIONNAIRE) {
                    return this.applyToPostTreatmentCondition(shouldHideField, !config.fromInquiry);
                }

                throw new Error('Invalid form type provided in condition:' + condition);
            }

            case StaticShowConditionType.INQUIRY_IS_NOT: {
                const requiredType = condition.values[0];

                if (requiredType === InquiryType.MAIN) {
                    return config.subInquiry;
                }

                if (requiredType === InquiryType.SUB) {
                    return !config.subInquiry;
                }

                throw new Error('Invalid inquiry type provided in condition:' + condition);
            }

            case StaticShowConditionType.ENTRY_IS_NOT: {
                return config.currentState.indexOf(condition.values[0]) < 0;
            }

            default:
                return condition.values.some((value) => {
                    return config.gender === value;
                });
        }
    }

    private containsModuleType(config: FormFieldConfig, staticCondition: StaticShowCondition) {
        return staticCondition.values.some((value) => {
            return config.treatmentModuleTypes.some((type) => {
                return value === type;
            });
        });
    }

    public parseCondition(required: Required): boolean {
        if (!required) {
            return false;
        }

        if (required.conditionType === RequiredType.BOOLEAN_TRUE) {
            return true;
        }

        if (required.conditionType === RequiredType.BOOLEAN_FALSE) {
            return false;
        }

        return false;
    }

    public getValidators(config: FormFieldConfig): ValidatorFn[] {
        const validators: ValidatorFn[] = [];

        if (this.parseCondition(config.required)) {
            validators.push(Validators.required);
        }

        if (config.type !== FormItemType.PHOTO) {
            if (config.maxLength && config.maxLength > 0) {
                validators.push(Validators.maxLength(config.maxLength));
            }

            if (config.minLength && config.minLength > 0) {
                validators.push(Validators.minLength(config.minLength));
            }

            if (config.max && config.max > 0) {
                validators.push(Validators.max(config.max));
            }

            if (config.min !== undefined || config.min !== null) {
                validators.push(Validators.min(config.min));
            }
        }

        return validators;
    }

    public getAnswer(config: FormFieldConfig, value: any, initialValue: boolean): Answer {
        let answer = value;
        if ([FormItemType.TEXTAREA].includes(config.type) && !!answer) {
            answer = this.encode.transform(value);
        }

        return {
            type: config.formType,
            field: config.key,
            answer,
            required: this.parseCondition(config.required),
            initialValue,
        };
    }
    public validate() {
        this.validate$.next(true);
    }

    public listenForValidate(): Subject<boolean> {
        return this.validate$;
    }

    public clearViewValues(form: any) {
        delete form.formValid;
        delete form.validFields;
        delete form.earlierSymptom;
        delete form.uploading;
        delete form.currentSection;
        delete form.totalSections;
        delete form[photoGroupDescriptionFormKey];
        delete form.isDirty;
        delete form.visibilities;
        forEach(form, (v, k) => {
            if (v === INVALID_DATE) {
                form[k] = null;
            }
            if (v && v.date && v.date === INVALID_DATE) {
                const newV = { ...v };
                newV.date = null;
                form[k] = newV;
            }
        });
    }

    private convertToRequiredType(value: any): any {
        if (value === 'true') {
            return true;
        }

        if (value === 'false') {
            return false;
        }

        return value;
    }

    /**
     * Note: the current implementation of FieldService#updateFormFieldStyle is unstable
     * due to some change detection issues, spaghetti code validation triggers
     * and any changes in the markup of the components that might affect the selectors below so it is more secure
     * to use a directive (please, see FormElementContainerDirective in
     * projects/shared-core/src/lib/form-engine/directives/form-element-container/form-element-container.directive.ts)
     * to manipulate the styles of the form element container
     */
    updateFormFieldStyle() {
        const formElements = document.getElementsByClassName('form-element-container');
        const requiredFieldErrorElements = document.getElementsByClassName('error-required-field');

        // Timeout is added to ensure all the items are checked before triggering detectChanges().
        setTimeout(() => {
            Array.from(formElements).forEach((element: HTMLElement) => {
                const hasErrors = Array.from(requiredFieldErrorElements).some((errorElement) => {
                    return element.contains(errorElement);
                });
                if (hasErrors) {
                    element.setAttribute('style', getStyle(formElementContainerErrorStyles));
                } else {
                    element.setAttribute('style', getStyle(formElementContainerDefaultStyles));
                }
            });
        });
    }

    clearFormFieldStyle() {
        const formElements = document.getElementsByClassName('form-element-container');

        // Timeout is added to ensure all the items are checked before triggering detectChanges().
        setTimeout(() => {
            Array.from(formElements).forEach((element: HTMLElement) => {
                element.setAttribute('style', getStyle(formElementContainerDefaultStyles));
            });
        });
    }
}
