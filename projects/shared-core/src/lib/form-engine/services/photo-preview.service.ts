import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { PreviewOption } from '../../utils/photo-uploader/preview-option.interface';

/**
 * This is a convenience service between InquiryEntryComponent and PhotoUploaderComponent. There are so many intermediate
 * components between them that it's cleaner and easier to use this event bus service for them to communicate via.
 */
@Injectable({
    providedIn: 'root',
})
export class PhotoPreviewService {
    private openPreviewSource = new Subject<PreviewOption>();

    openPreview$ = this.openPreviewSource.asObservable();

    openPreview(previewOption: PreviewOption) {
        this.openPreviewSource.next(previewOption);
    }
}
