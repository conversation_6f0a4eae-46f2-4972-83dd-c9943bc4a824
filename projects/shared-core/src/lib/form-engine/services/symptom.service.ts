import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable, of } from 'rxjs';
import { SharedApi } from '../../abstract-services/shared-api.service';
import { SharedSchemaService } from '../../abstract-services/shared-schema.service';
import { Dictionary } from '../../common-types';
import { Case } from '../../generated/models/case';
import { Symptom } from '../../generated/models/symptom';
import { SymptomCollection } from '../../generated/models/symptom-collection';
import { SymptomType } from '../../generated/models/symptom-type';
import { TriageInstruction } from '../../generated/models/triage-instruction';
import { SetSymptomInformations } from '../store/actions/inquiry.action';
import { FormEngineState } from '../store/reducers/state';

@Injectable({
    providedIn: 'root',
})
export class SymptomService {
    constructor(private store: Store<FormEngineState>, private api: SharedApi, private schema: SharedSchemaService) {}

    public loadSymptomsInfo(symptomTypes: SymptomType[], startDate: number | string | Date, patientId: string) {
        this.api.medicalApi.getSymptomInformation(symptomTypes, startDate, patientId).subscribe((info) => {
            this.store.dispatch(new SetSymptomInformations(info));
        });
    }

    public getAlerts(symptomCollection: SymptomCollection): Observable<TriageInstruction> {
        if (!symptomCollection.patient) {
            // if no patient then return empty TriageInstruction
            return of({ alerts: [], careInstructions: [] });
        }
        return this.api.symptomApi.getAlertsAndInstructions(symptomCollection.patient.id, symptomCollection.symptoms);
    }

    // eslint-disable-next-line @typescript-eslint/naming-convention, no-underscore-dangle, id-blacklist, id-match
    public getCaseSymptoms(case_: Case): Observable<SymptomCollection> {
        return this.api.symptomApi.getCaseSymptoms(case_.id);
    }

    getSymptomAttributes(item: Symptom, photoFields?: string[]): string[] {
        if (!item || !item.type) {
            return [];
        }

        const type = item.type + (item.symptomDate ? 'Symptom' : 'Questionary');

        if (!photoFields) {
            photoFields = this.getPhotoFields(item);
        }

        const attributeList = this.schema.getAttributeLists();
        const typedAttributeList = attributeList[type] || attributeList[item.type];

        if (!typedAttributeList) {
            return [];
        }

        return typedAttributeList.filter((attribute) => {
            return photoFields.indexOf(attribute) < 0;
        });
    }

    getPhotoFields(symptom: Symptom): string[] {
        if (!symptom) {
            return [];
        }

        return this.schema.getPhotoFields()[symptom.type] || [];
    }

    getItemsTranslations(item: Symptom): { [key in string]: string } {
        if (!item) {
            return {};
        }

        const typeName = item.type + (item.symptomDate ? 'Symptom' : 'Questionary');
        return this.schema.getTranslations()[typeName];
    }

    getPainPointers(): Dictionary<string, string> {
        return this.schema.getBodyDiagrams();
    }
}
