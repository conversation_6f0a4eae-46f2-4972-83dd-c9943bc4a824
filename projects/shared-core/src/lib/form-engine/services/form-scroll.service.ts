import { Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class FormScrollService {
    public scrollToElement(selector: string, behavior: ScrollBehavior, tryCount?: number) {
        const elem = $(selector)[0];
        if (!elem) {
            if (tryCount > 5) {
                return;
            }
            setTimeout(() => {
                this.scrollToElement(selector, behavior, tryCount ? tryCount + 1 : 1);
            }, 50);
        } else {
            this.scrollToElementSpecifically(selector, behavior, 'start', 'nearest', tryCount ? 1 : tryCount + 1);
        }
    }

    /**
     * Provides more control how to scroll into specific element
     *
     * @param selector      CSS selector, will match to the first of the matching elements
     * @param behavior      smooth / instant
     * @param vAlign        how scroll aligns to the element vertically: "start" | "center" | "end" | "nearest"
     * @param hAlign        how scroll aligns to the element horizontally: "start" | "center" | "end" | "nearest"
     * @param tryCount      TODO: Add description
     */
    public scrollToElementSpecifically(
        selector: string,
        behavior: Sc<PERSON>Behavior,
        vAlign: ScrollLogicalPosition,
        hAlign: ScrollLogicalPosition,
        tryCount?: number
    ) {
        const elem = $(selector)[0];
        if (!elem) {
            if (tryCount > 5) {
                return;
            }
            setTimeout(() => {
                this.scrollToElementSpecifically(selector, behavior, vAlign, hAlign, tryCount ? 1 : tryCount + 1);
            }, 50);
        } else {
            elem.scrollIntoView({
                behavior,
                block: vAlign,
                inline: hAlign,
            });
        }
    }
}
