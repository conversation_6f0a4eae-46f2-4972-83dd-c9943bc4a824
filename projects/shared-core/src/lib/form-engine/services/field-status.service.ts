import { FieldStatus, FieldStatusType } from '../models/field-status.interface';
import { I18NPipe } from '../../pipes/i18n.pipe';
import { Dictionary } from '../../common-types';
import { DateFormatPipe } from '../../pipes/date-format.pipe';

const FIELD_STATUS_PRIORITY_MAP: { [key: string]: number } = {
    [FieldStatusType.SOFT_ERROR]: 1,
    [FieldStatusType.HARD_ERROR]: 2,
    [FieldStatusType.TERMINATION_ERROR]: 3,
};

export function getFieldStatusIcon(questionStatus: FieldStatus): string {
    if (questionStatus) {
        let statusIcon: string = questionStatus.statusType;
        if (statusIcon === FieldStatusType.MISSING_VALUE) {
            statusIcon = FieldStatusType.HARD_ERROR;
        }
        statusIcon = 'icon-' + statusIcon.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
        return questionStatus.accepted && questionStatus.statusType !== FieldStatusType.TERMINATION_ERROR
            ? statusIcon + '-check'
            : statusIcon;
    } else {
        return '';
    }
}

export function setHighestPriorityStatus(fieldStatuses: Map<string, FieldStatus>, status: FieldStatus) {
    const currentStatus = fieldStatuses.has(status.fieldKey) ? fieldStatuses.get(status.fieldKey) : null;
    if (
        !currentStatus ||
        FIELD_STATUS_PRIORITY_MAP[currentStatus.statusType] < FIELD_STATUS_PRIORITY_MAP[status.statusType]
    ) {
        fieldStatuses.set(status.fieldKey, status);
    }
}

export function getFieldStatusMessageTitleTranslation(
    questionStatus: FieldStatus,
    i18nPipe: I18NPipe,
    placeHoldersTranslationValues: Dictionary<string, string>
): string {
    if (questionStatus && questionStatus.statusType) {
        const titleKey = questionStatus.translationKey + '.title';
        const title = i18nPipe.transform(titleKey, placeHoldersTranslationValues);
        return title === titleKey ? '' : title;
    }
}

export function getFieldStatusMessageTranslation(
    questionStatus: FieldStatus,
    i18nPipe: I18NPipe,
    dateFormatPipe: DateFormatPipe,
    placeHoldersTranslationValues: Dictionary<string, string>,
    dateFormatPattern: string
) {
    let questionStatusTranslation = '';
    if (questionStatus) {
        const specialKey = questionStatus.translationKey + (questionStatus.accepted ? '.accepted' : '.unaccepted');
        questionStatusTranslation = i18nPipe.transform(specialKey, placeHoldersTranslationValues);
        if (questionStatusTranslation === specialKey) {
            questionStatusTranslation = i18nPipe.transform(
                questionStatus.translationKey,
                placeHoldersTranslationValues
            );
        }
        if (questionStatus.accepted && questionStatus.userName && questionStatus.lastModified) {
            questionStatusTranslation =
                questionStatusTranslation +
                '\n\n' +
                questionStatus.userName +
                '  -  ' +
                dateFormatPipe.transform(questionStatus.lastModified, dateFormatPattern);
        }
    }
    return questionStatusTranslation;
}
