import { Injectable } from '@angular/core';
import { PatientService } from './patient.service';
import { ConfigurationProviderService } from '../../abstract-services/configuration-provider.service';
import { Application } from '../../models/application';
import { Observable, of } from 'rxjs';
import { Patient } from '../../generated/models/patient';
import { PatientContactInformation } from '../../generated/models/patient-contact-information';

@Injectable({
    providedIn: 'root',
})
export class PatientWrapperService {
    private app: Application;

    constructor(private patientService: PatientService, private configProviderService: ConfigurationProviderService) {}

    public getPatient(patientId: string): Observable<Patient> {
        if (this.getApp() === Application.PATIENT) {
            return of({ id: patientId });
        }
        return this.patientService.getPatient(patientId);
    }

    public getPatientInformation(patientId: string): Observable<PatientContactInformation> {
        if (this.getApp() === Application.PATIENT) {
            return of({ id: patientId });
        }
        return this.patientService.getPatientInformation(patientId);
    }

    private getApp() {
        if (!this.app) {
            this.app = this.configProviderService.site();
        }
        return this.app;
    }
}
