import { Injectable } from '@angular/core';
import { select, Store } from '@ngrx/store';
import moment from 'moment';
import { Observable, Subject, combineLatest } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { ConfigurationProviderService } from '../../abstract-services/configuration-provider.service';
import { DateFormattingPattern } from '../../abstract-services/interfaces/date-formatting-pattern.interface';
import { FormType } from '../../constants';
import { FormItemType } from '../../generated/models/form-item-type';
import { I18NPipe } from '../../pipes/i18n.pipe';
import { CheckboxListWithExtraFieldsComponent } from '../components/checkbox-list-with-extra-fields/checkbox-list-with-extra-fields.component';
import { RadioListWithExtraFieldsComponent } from '../components/radio-list-with-extra-fields/radio-list-with-extra-fields.component';
import { FormFieldConfig } from '../models/form-field-config.interface';
import { FormState } from '../store/reducers/form.reducer';
import { selectField } from '../store/selectors/form.selectors';

@Injectable({
    providedIn: 'root',
})
export class FieldValueTranslationService {
    constructor(
        private store: Store<FormState>,
        private i18nPipe: I18NPipe,
        private configService: ConfigurationProviderService
    ) {}

    public getTranslationForFieldValue(fieldConfig: FormFieldConfig): Observable<string> {
        switch (fieldConfig.type) {
            case FormItemType.CHECKBOX_WITH_EXTRA_FIELDS:
                return this.getTranslationForCheckboxWithExtraFieldsValue(fieldConfig);
            case FormItemType.RADIO_WITH_EXTRA_FIELDS:
                return this.getTranslationForRadioListWithExtraFieldsValue(fieldConfig);
            case FormItemType.RADIO:
                return this.getTranslationForRadioValue(fieldConfig);
            case FormItemType.CHECKBOX:
            case FormItemType.MULTISELECT:
                return this.getTranslationForCheckboxValue(fieldConfig);
            case FormItemType.DATE:
                return this.getTranslationForDateValue(fieldConfig);
            case FormItemType.NUMERIC_FIELD:
                return this.getTranslationForNumericFieldValue(fieldConfig);
            default:
                return this.getSingleFormFieldValue(fieldConfig.formType, fieldConfig.key);
        }
    }

    private getTranslationForCheckboxWithExtraFieldsValue(fieldConfig: FormFieldConfig): Observable<string> {
        return combineLatest([
            this.getSingleFormFieldValue(fieldConfig.formType, fieldConfig.key),
            this.getSingleFormFieldValue(
                fieldConfig.formType,
                fieldConfig.key + CheckboxListWithExtraFieldsComponent.SELECTED_RADIO_OPTION_FIELD_POSTFIX
            ),
            this.getSingleFormFieldValue(
                fieldConfig.formType,
                fieldConfig.key + CheckboxListWithExtraFieldsComponent.TEXT_FIELD_POSTFIX
            ),
        ]).pipe(
            map(([checkedOptions, selectedRadioOption, textInput]: [string[], string, string]) => {
                if (selectedRadioOption) {
                    return this.getOptionTranslation(fieldConfig.translationPrefix, selectedRadioOption);
                }
                if (!checkedOptions) {
                    return '';
                }
                const extraTextField =
                    fieldConfig.optionsWithExtraField &&
                    fieldConfig.optionsWithExtraField.find((option) => {
                        return option.type === 'TEXT';
                    });
                if (textInput && extraTextField && checkedOptions.includes(extraTextField.key)) {
                    return (
                        checkedOptions
                            .filter((option) => {
                                return option !== extraTextField.key;
                            })
                            .map((option) => {
                                return this.getOptionTranslation(fieldConfig.translationPrefix, option);
                            })
                            .join(', ') + `, ${textInput}`
                    );
                }
                return checkedOptions
                    .map((option) => {
                        return this.getOptionTranslation(fieldConfig.translationPrefix, option);
                    })
                    .join(', ');
            })
        );
    }

    private getTranslationForRadioListWithExtraFieldsValue(fieldConfig: FormFieldConfig): Observable<string> {
        return combineLatest([
            this.getSingleFormFieldValue(fieldConfig.formType, fieldConfig.key),
            this.getSingleFormFieldValue(
                fieldConfig.formType,
                fieldConfig.key + RadioListWithExtraFieldsComponent.DATE_FIELD_CONFIG_POSTFIX
            ),
            this.getSingleFormFieldValue(
                fieldConfig.formType,
                fieldConfig.key + RadioListWithExtraFieldsComponent.TEXT_FIELD_CONFIG_POSTFIX
            ),
            this.configService.dateFormattingPattern(),
            this.getSingleFormFieldValue(
                fieldConfig.formType,
                fieldConfig.key + RadioListWithExtraFieldsComponent.NUMERIC_FIELD_CONFIG_POSTIFX
            ),
        ]).pipe(
            map(
                ([checkedOption, date, textInput, dateFormattingPattern, numericInput]: [
                    string,
                    string,
                    string,
                    DateFormattingPattern,
                    number
                ]) => {
                    if (!checkedOption) {
                        return '-';
                    }

                    const extraTextField =
                        fieldConfig.optionsWithExtraField &&
                        fieldConfig.optionsWithExtraField.find((option) => {
                            return option.type === 'TEXT';
                        });
                    if (textInput && extraTextField && checkedOption === extraTextField.key) {
                        return `${this.getOptionTranslation(
                            fieldConfig.translationPrefix,
                            checkedOption
                        )} ${textInput}`;
                    }

                    const extraDateField =
                        fieldConfig.optionsWithExtraField &&
                        fieldConfig.optionsWithExtraField.find((option) => {
                            return option.type === 'DATE';
                        });
                    if (date && extraDateField && checkedOption === extraDateField.key) {
                        return `${this.getOptionTranslation(fieldConfig.translationPrefix, checkedOption)} ${moment(
                            date
                        ).format(dateFormattingPattern.longDatePattern)}`;
                    }

                    const extraNumericField =
                        fieldConfig.optionsWithExtraField &&
                        fieldConfig.optionsWithExtraField.find((option) => {
                            return option.type === 'NUMERIC_FIELD';
                        });
                    if (numericInput && extraNumericField && checkedOption === extraNumericField.key) {
                        return `${this.getOptionTranslation(
                            fieldConfig.translationPrefix,
                            checkedOption
                        )} ${numericInput}`;
                    }

                    return this.getOptionTranslation(fieldConfig.translationPrefix, checkedOption);
                }
            )
        );
    }

    private getTranslationForRadioValue(fieldConfig: FormFieldConfig): Observable<string> {
        return this.getSingleFormFieldValue(fieldConfig.formType, fieldConfig.key).pipe(
            map((value) => {
                return value ? this.getOptionTranslation(fieldConfig.translationPrefix, value) : '-';
            })
        );
    }

    private getTranslationForCheckboxValue(fieldConfig: FormFieldConfig): Observable<string> {
        return this.getSingleFormFieldValue(fieldConfig.formType, fieldConfig.key).pipe(
            map((answer) => {
                return answer
                    .map((option) => {
                        return this.getOptionTranslation(fieldConfig.translationPrefix, option);
                    })
                    .join('. ');
            })
        );
    }

    private getTranslationForDateValue(fieldConfig: FormFieldConfig): Observable<string> {
        return combineLatest([
            this.configService.dateFormattingPattern(),
            this.getSingleFormFieldValue(fieldConfig.formType, fieldConfig.key),
        ]).pipe(
            map(([dateFormattingPattern, value]: [DateFormattingPattern, string]) => {
                return moment(value).format(dateFormattingPattern.longDatePattern);
            })
        );
    }

    private getTranslationForNumericFieldValue(fieldConfig: FormFieldConfig): Observable<string> {
        const unitTranslation = fieldConfig.unit ? this.i18nPipe.transform(fieldConfig.unit) : '';
        return this.getSingleFormFieldValue(fieldConfig.formType, fieldConfig.key).pipe(
            map((value) => {
                return `${value} ${unitTranslation}`;
            })
        );
    }

    private getSingleFormFieldValue(formType: FormType, fieldKey: string): Observable<any> {
        return this.store.pipe(select(selectField(formType, fieldKey)), take(1));
    }

    private getOptionTranslation(translationPrefix: string, optionKey: string): string {
        const optionTranslationKey = translationPrefix + '.' + optionKey + '.label';
        const optionTranslation = this.i18nPipe.transform(optionTranslationKey);
        return optionTranslation === optionTranslationKey ? optionKey : optionTranslation;
    }
}
