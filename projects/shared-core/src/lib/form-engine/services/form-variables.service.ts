import { Injectable } from '@angular/core';
import { select, Store } from '@ngrx/store';
import moment from 'moment';
import isArray from 'lodash/isArray';
import lodashIsNaN from 'lodash/isNaN';
import isString from 'lodash/isString';
import { Observable, Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { ConfigurationProviderService } from '../../abstract-services/configuration-provider.service';
import { DateFormattingPattern } from '../../abstract-services/interfaces/date-formatting-pattern.interface';
import { Dictionary } from '../../common-types';
import { DateFormatPipe } from '../../pipes/date-format.pipe';
import { I18NPipe } from '../../pipes/i18n.pipe';
import { FormVariableType } from '../models/form-var-type';
import { FormVariableValue } from '../models/form-var-value';
import { FormEngineState } from '../store/reducers/state';
import { getAllFormVariables } from '../store/selectors/variables.selectors';

@Injectable({
    providedIn: 'root',
})
export class FormVariablesService {
    private dateFormattingPattern: DateFormattingPattern;

    constructor(
        private cfgSrv: ConfigurationProviderService,
        private dfPipe: DateFormatPipe,
        private i18nPipe: I18NPipe
    ) {
        this.cfgSrv.dateFormattingPattern().subscribe((dfp) => {
            this.dateFormattingPattern = dfp;
        });
    }

    /*
     * Observable to get the text representation of a variable, hence, the way a variable would be displayed in
     * an, e.g., instruction text.
     */
    getTranslations(store: Store<FormEngineState>, destroy$: Subject<boolean>): Observable<Dictionary<string, string>> {
        return store.pipe(
            select(getAllFormVariables),
            takeUntil(destroy$),
            map((variables: FormVariableValue[]) => {
                const translations: Dictionary<string, string> = {};
                if (variables) {
                    variables.forEach((v) => {
                        translations[v.key] = this.translateFormVariable(v);
                    });
                }
                return translations;
            })
        );
    }

    /*
     * In the db all variables, no matter what type, are just stored as strings.
     * Function will perform needed transformation - if any - from in-browser representation
     * to corresponding string version.
     */
    getValueForSave(fv: FormVariableValue): string {
        if (fv.value) {
            if (fv.type === FormVariableType.DATE) {
                if (fv.value.toDate) {
                    return fv.value.toDate().getTime();
                }
                if (fv.value.getTime) {
                    return fv.value.getTime();
                }
                if (isString(fv.value)) {
                    return moment(fv.value).toDate().getTime() + '';
                }
            }
            if (fv.type === FormVariableType.ENUMERATION) {
                if (isArray(fv.value)) {
                    return fv.value.join(',');
                }
            }
        }
        return fv.value;
    }

    /*
     * See 'getValueForSave'
     */
    getValueForUsage(fv: FormVariableValue): any {
        if (fv.value) {
            if (fv.type === FormVariableType.DATE) {
                if (!fv.value.toDate) {
                    const asInt = parseInt(fv.value, 10);
                    if (lodashIsNaN(asInt)) {
                        // We assume this is a date string now (should not happen)
                        return moment(fv.value);
                    } else {
                        return moment(new Date(asInt));
                    }
                }
            }
            if (fv.type === FormVariableType.ENUMERATION) {
                if (!isArray(fv.value)) {
                    const splitted = fv.value.split(',');
                    if (splitted && splitted.length > 0) {
                        return splitted;
                    }
                }
            }
            if (fv.type === FormVariableType.NUMBER) {
                return fv.value;
            }
        }
        return fv.value;
    }

    /*
     * Transfrom a variable value to it's readable / printable format.
     */
    translateFormVariable(fv: FormVariableValue): string {
        if (fv.value) {
            if (fv.type === FormVariableType.DATE) {
                const patternName = fv.meta;
                const pattern = this.dateFormattingPattern[patternName];
                return this.dfPipe.transform(fv.value, pattern);
            }
            if (fv.type === FormVariableType.ENUMERATION) {
                const values = this.getValueForUsage(fv);
                if (values) {
                    const asArray = isArray(values) ? values : [values];
                    const translations = asArray.map((v) => {
                        const translationKey = fv.meta.replace('{0}', v);
                        return this.i18nPipe.transform(translationKey);
                    });
                    return translations.join(', ');
                }
                return '';
            }
            if (fv.type === FormVariableType.NUMBER || fv.type === FormVariableType.TEXT) {
                return fv.value;
            }
            if (fv.type === FormVariableType.BOOLEAN) {
                const v = fv.value;
                return fv.meta ? this.i18nPipe.transform(fv.meta.replace('{0}', v)) : v;
            }
        }
        return '[' + fv.key + ']';
    }
}
