import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { SharedApi } from '../../abstract-services/shared-api.service';
import { Message } from '../../generated/models/message';
import { Questionary } from '../../generated/models/questionary';
import { QuestionnaireInquiryInformation } from '../../generated/models/questionnaire-inquiry-information';

@Injectable({
    providedIn: 'root',
})
export class QuestionnaireService {
    constructor(private api: SharedApi) {}

    public loadQuestionnaireInformation(questionnaireInquiryId: string): Observable<QuestionnaireInquiryInformation> {
        return this.api.medicalApi.getQuestionnaireInformation(questionnaireInquiryId);
    }

    public saveQuestionnaire(
        questionnaire: any,
        message: Message,
        firstMessageId: string,
        patientId: string
    ): Observable<Questionary> {
        return this.api.medicalApi.addQuestionary(patientId, firstMessageId, message, questionnaire);
    }

    public getQuestionnaireLimitedLoginLink(questionnaireId: string): Observable<string> {
        return this.api.medicalApi.getQuestionnaireLimitedLoginLink(questionnaireId);
    }

    public patientDeclined(questionnaireId: string): Observable<boolean> {
        return this.api.questionnaireApi.declineQuestionnaire(questionnaireId);
    }
}
