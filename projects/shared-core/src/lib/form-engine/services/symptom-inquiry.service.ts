import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SharedApi } from '../../abstract-services/shared-api.service';
import { Message } from '../../generated/models/message';
import { MessageType } from '../../generated/models/message-type';
import { SaveSymptomResult } from '../../generated/models/save-symptom-result';
import { SymptomInquiryInformation } from '../../generated/models/symptom-inquiry-information';
import { SymptomReport } from '../../generated/models/symptom-report';
import { SymptomReportPair } from '../../generated/models/symptom-report-pair';
import { TopicType } from '../../generated/models/topic-type';

@Injectable({
    providedIn: 'root',
})
export class SymptomInquiryService {
    constructor(private api: SharedApi) {}

    public loadInquiryData(id: string): Observable<SymptomInquiryInformation> {
        return this.api.medicalApi.getSymptomInquiryInformationWithId(id);
    }

    /**
     * Adds the given symptom report and ties it to the given first message.
     */
    public addSymptomReport(
        report: SymptomReport,
        firstMessageId: string,
        patientId: string,
        message: Message
    ): Observable<SymptomReportPair> {
        const finalMessage = {
            ...message,
            messageDate: new Date(),
            type: MessageType.ADVICE_REQUEST,
            topicType: TopicType.SYMPTOM_INQUIRY_REQUEST,
        };

        return this.api.medicalApi.addSymptomReport(report, patientId, firstMessageId, finalMessage);
    }

    public sendSymptomReport(
        reportId: string,
        firstMessageId: string,
        patientId: string,
        emergencySymptoms: any
    ): Observable<SaveSymptomResult[]> {
        return this.api.medicalApi.sendSymptomReport(reportId, firstMessageId, patientId, emergencySymptoms);
    }
}
