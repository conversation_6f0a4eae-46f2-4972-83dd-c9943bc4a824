import { Injectable } from '@angular/core';
import { genderSpecificIllustrations } from '../../constants';
import { Gender } from '../../generated/models/gender';
import { SymptomType } from '../../generated/models/symptom-type';
import { HyphenedPipe } from '../../pipes/hyphened.pipe';

@Injectable({
    providedIn: 'root',
})
export class IllustrationService {
    constructor(private hyphened: HyphenedPipe) {}

    public getIllustrationClass(type: SymptomType, gender: Gender | string): string {
        let symptomType = this.hyphened.transform(type).replace(/-/g, '_');

        if (genderSpecificIllustrations.includes(type)) {
            symptomType = `${symptomType}_${gender}`;
        }

        return `symptom_${symptomType}`;
    }
}
