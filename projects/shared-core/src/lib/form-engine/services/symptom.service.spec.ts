import { inject, TestBed } from '@angular/core/testing';
import { SymptomService } from './symptom.service';
import { MocksModule } from '@shared-core/testing';
import { SharedApi } from '../../abstract-services/shared-api.service';

describe('SymptomService', () => {
    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            providers: [
                {
                    provide: SharedApi,
                    useValue: {},
                },
            ],
        });
    });

    it('should be created', inject([SymptomService], (service: SymptomService) => {
        expect(service).toBeTruthy();
    }));
});
