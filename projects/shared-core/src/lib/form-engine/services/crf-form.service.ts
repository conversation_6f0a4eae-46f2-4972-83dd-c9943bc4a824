import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SharedApi } from '../../abstract-services/shared-api.service';
import { CrfFormInformation } from '../models/crf/crf-form-information';

@Injectable({
    providedIn: 'root',
})
export class CrfFormService {
    constructor(private api: SharedApi) {}

    public loadCrfFormInformation(crfFormId: string): Observable<CrfFormInformation> {
        return this.api.crfFormApi.getCrfFormInformation(crfFormId);
    }
}
