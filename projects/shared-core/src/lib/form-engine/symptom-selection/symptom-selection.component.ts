import {
    AfterContentChecked,
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    isDevMode,
    Inject,
} from '@angular/core';
import { Store } from '@ngrx/store';
import moment, { Moment } from 'moment';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ConfigurationProviderService } from '../../abstract-services/configuration-provider.service';
import { femaleSymptoms, maleSymptoms, wellnessSymptomTypes } from '../../constants';
import { Gender } from '../../generated/models/gender';
import { InquiryType } from '../../generated/models/inquiry-type';
import { SymptomInquiry } from '../../generated/models/symptom-inquiry';
import { SymptomInquiryInformation } from '../../generated/models/symptom-inquiry-information';
import { SymptomType } from '../../generated/models/symptom-type';
import { Application } from '../../models/application';
import { I18NPipe } from '../../pipes/i18n.pipe';
import { IllustrationService } from '../services/illustration.service';
import {
    SelectSymptomType,
    SetRequiredSymptomTypes,
    SetSymptomTypes,
    UnselectSymptomType,
} from '../store/actions/inquiry.action';
import { FormEngineState } from '../store/reducers/state';
import get from 'lodash/get';
import { NoonaLocaleService } from '../../abstract-services/noona-locale.service';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'nh-symptom-selection',
    templateUrl: './symptom-selection.component.html',
    styleUrls: ['./symptom-selection.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SymptomSelectionComponent implements AfterContentChecked, OnDestroy, OnInit {
    @Input() inquiryInfo: SymptomInquiryInformation;
    @Input() site: Application;

    @Output() setAnswerSince = new EventEmitter<Moment>();

    public description = '';
    public answerSince: Moment;
    public symptomTypes: SymptomType[];
    public symptomInDiary: SymptomType[];
    locale: string;
    iconPath = '';

    private destroy$ = new Subject<boolean>();
    private dateFormat: string;
    private initialized = false;

    constructor(
        private i18n: I18NPipe,
        private store: Store<FormEngineState>,
        private illustrationService: IllustrationService,
        private dateFormattingService: ConfigurationProviderService,
        private localeService: NoonaLocaleService,
        @Inject('env') private env
    ) {
        this.dateFormattingService
            .dateFormattingPattern()
            .pipe(takeUntil(this.destroy$))
            .subscribe((pattern) => {
                if (pattern) {
                    this.dateFormat = pattern.longDatePattern;
                }
            });
    }

    ngOnInit() {
        // NOONA-16582 quick fix for not shown icons.
        this.iconPath = this.env.assetPath + 'icons/';
        this.locale = this.localeService.getLocale();
    }

    ngAfterContentChecked() {
        if (this.inquiryInfo && !this.initialized) {
            this.initialized = true;
            const inquiry = this.inquiryInfo.symptomInquiry;
            this.setDescription(inquiry);
            this.setSymptomTypes(inquiry);
            this.setSymptomInDiary(this.inquiryInfo);
        }
    }

    ngOnDestroy() {
        this.destroy$.next(true);
        this.destroy$.complete();
    }

    public isLongSymptomName(type: string): boolean {
        return this.i18n.transform('fe.formNames.' + type).length > 55;
    }

    public getIllustrationClass(type: SymptomType): string {
        return this.illustrationService.getIllustrationClass(type, this.inquiryInfo.gender);
    }

    private setDescription(inquiry: SymptomInquiry) {
        const description = this.i18n.transform(`symptomInquiry.symptomSelection.section.${inquiry.type}.description`);

        if ([InquiryType.BASELINE, InquiryType.FOLLOWUP].indexOf(inquiry.type) >= 0) {
            this.answerSince = moment().subtract(29, 'd');
            this.description = description;
        } else {
            this.answerSince = moment(this.inquiryInfo.lastAeq);
            this.description = description.replace(
                /{DATE_OF_PREVIOUS_QUESTIONNAIRE}/g,
                this.answerSince.format(this.dateFormat)
            );
        }
        this.setAnswerSince.emit(this.answerSince);
    }

    private setSymptomTypes(inquiry: SymptomInquiry) {
        const symptomTypes = inquiry.symptomTypes;
        const symptomTypesToRemove = this.inquiryInfo.gender === Gender.FEMALE ? maleSymptoms : femaleSymptoms;

        symptomTypesToRemove.push(SymptomType.SYMPTOM_GENERAL_CONDITION);

        this.symptomTypes = symptomTypes.filter((type) => {
            return !symptomTypesToRemove.includes(type);
        });
        this.store.dispatch(new SetRequiredSymptomTypes(this.symptomTypes));
        this.store.dispatch(new SetSymptomTypes(wellnessSymptomTypes.concat(this.symptomTypes)));

        this.dispatchSelectedSymptoms();
    }

    private dispatchSelectedSymptoms() {
        if (this.inquiryInfo.symptomsInDiary.length > 0) {
            this.dispatchSelectedSymptomsFromDiary();
        } else if (this.site === Application.CLINIC) {
            this.dispatchSymptomTypesForClinician();
        }
    }

    private setSymptomInDiary(inquiry: SymptomInquiryInformation) {
        const { symptomsInDiary = [] } = inquiry;
        this.symptomInDiary = symptomsInDiary;
    }

    public printDiaryIfSymptomIsInDiary(symptomType: SymptomType): string {
        if (!this.symptomInDiary || this.symptomInDiary.length <= 0) {
            return '';
        }
        if (this.symptomInDiary.indexOf(symptomType) >= 0) {
            return '(' + this.i18n.transform('pageTitle.diaryTimeline') + ')';
        }
        return '';
    }

    private dispatchSelectedSymptomsFromDiary() {
        this.symptomTypes.forEach((type) => {
            if (this.inquiryInfo.symptomsInDiary.includes(type)) {
                this.store.dispatch(new SelectSymptomType(type));
            } else {
                this.store.dispatch(new UnselectSymptomType(type));
            }
        });
    }

    private dispatchSymptomTypesForClinician() {
        this.symptomTypes.forEach((type) => {
            return this.store.dispatch(new UnselectSymptomType(type));
        });
    }

    get isStatusCheckQuestionnaire() {
        const questionnaireType = get(this.inquiryInfo, ['symptomInquiry', 'type']);
        return questionnaireType === InquiryType.TO_POST_TREATMENT;
    }
}
