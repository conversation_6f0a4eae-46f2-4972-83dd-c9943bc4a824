import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SymptomSelectionComponent } from './symptom-selection.component';
import { MocksModule } from '@shared-core/testing';

describe('SymptomSelectionComponent', () => {
    let component: SymptomSelectionComponent;
    let fixture: ComponentFixture<SymptomSelectionComponent>;

    beforeEach(waitForAsync(() => {
        TestBed.configureTestingModule({
            imports: [MocksModule],
            declarations: [SymptomSelectionComponent],
        }).compileComponents();
    }));

    beforeEach(() => {
        fixture = TestBed.createComponent(SymptomSelectionComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});
