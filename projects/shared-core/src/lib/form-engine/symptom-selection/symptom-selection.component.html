<div class="symptom-select">
    <div class="wizard-section" *ngIf="inquiryInfo as info; else loading">
        <div class="form-element-container">
            <div class="section-header">
                <div class="form-element-container">
                    <h1 class="section-title" *ngIf="!isStatusCheckQuestionnaire">
                        {{ 'symptomInquiry.symptomSelection.section.' + info.symptomInquiry.type + '.title' | i18n }}
                    </h1>
                    <h1 class="section-title" *ngIf="isStatusCheckQuestionnaire">
                        {{ 'symptomInquiry.symptomSelection.section.toPostTreatment.title' | i18n }}
                    </h1>
                    <p class="section-description" *ngIf="!isStatusCheckQuestionnaire">{{ description }}</p>
                    <p class="section-description" *ngIf="isStatusCheckQuestionnaire">
                        {{ 'symptomInquiry.symptomSelection.section.toPostTreatment.description' | i18n }}
                    </p>
                </div>
                <div class="infobox diary-entry-info mb-l" *ngIf="info.symptomsInDiary.length > 0">
                    <div class="icon information-icon"></div>
                    <div class="information-description">
                        {{ 'symptomInquiry.symptomSelection.symptomsFromDiary' | i18n }}
                    </div>
                </div>
            </div>
            <ul class="symptom-grid clearfix">
                <li class="symptom-item" *ngFor="let type of symptomTypes">
                    <div class="symptom-illustration-title">
                        <div
                            class="symptom-illustration-title-text"
                            [ngClass]="{ 'long-text': isLongSymptomName(type) }"
                        >
                            {{ 'fe.formNames.' + type | i18n }} {{ printDiaryIfSymptomIsInDiary(type) | lowercaseExceptGerman: locale }}
                        </div>
                    </div>
                    <img class="symptom-illustration-image" [src]="iconPath + getIllustrationClass(type) + '.svg'" alt=""/>
                    <nh-yes-no [type]="type" [fromDiary]="info.symptomsInDiary"></nh-yes-no>
                </li>
            </ul>
        </div>
    </div>
    <ng-template #loading>
        <div class="wizard-section loading">
            <div class="form-element-container">
                <div class="text-placeholder text-medium text-large animated-background"></div>
                <div class="text-placeholder text-long animated-background"></div>
                <div class="text-placeholder text-medium animated-background"></div>
                <div class="block-placeholder col-md-12 animated-background" *ngFor="let i of [1, 2, 3, 4, 5, 6]"></div>
            </div>
        </div>
    </ng-template>
</div>
