import { UntypedFormGroup } from '@angular/forms';
import every from 'lodash/every';
import isEmpty from 'lodash/isEmpty';
import { isSubArrayField } from '../../util/sub-array-field.utils';

export class FormValidator {
    public errors: any = {};

    public valid = false;

    constructor(private formGroup: UntypedFormGroup) {}

    public setFormData(data: any) {
        this.formGroup.patchValue(data);
        this.formGroup.markAsDirty();
        this.validate();
    }

    public validate() {
        // Get errors from form controls
        this.errors = Object.keys(this.formGroup.controls).reduce((errors, key) => {
            if (isSubArrayField(key)) {
                return { ...errors, [key]: this.formGroup.controls[key].errors };
            }
            return { ...errors, [key]: this.formGroup.get(key).errors };
        }, {});

        this.valid = every(Object.keys(this.errors), (key) => {
            return isEmpty(this.errors[key]);
        });
    }
}
