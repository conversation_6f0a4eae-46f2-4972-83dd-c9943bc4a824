import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InquiryEntryComponent } from './inquiry-entry.component';
import { MocksModule } from '@shared-core/testing';
import { SymptomService } from '../services/symptom.service';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('InquiryEntryComponent', () => {
  let component: InquiryEntryComponent;
  let fixture: ComponentFixture<InquiryEntryComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MocksModule],
      declarations: [InquiryEntryComponent],
      providers: [
        {
          provide: SymptomService,
          useValue: {}
        }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InquiryEntryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit scroll to top event when input flag for scrolling is true', () => {
    component.secondaryButtonScrollsToTop = true;
    const spy = jest.spyOn(component.scrollToTop, 'next');
    component.handleSecondaryButton();
    expect(spy).toHaveBeenCalledWith();
  });

  it('should emit cancel event with patient id when input flag for scrolling is false', () => {
    const testPatientId = 'testPatientId';
    component.info = { patientId: testPatientId };
    component.secondaryButtonScrollsToTop = false;
    const spy = jest.spyOn(component.canceled, 'next');
    component.handleSecondaryButton();
    expect(spy).toHaveBeenCalledWith(testPatientId);
  });

  it('should use translation key for back to top for scrolling is true', () => {
    component.secondaryButtonScrollsToTop = true;
    const translationKeyForSecondaryButton = component.getSecondaryButtonTranslationKey();
    expect(translationKeyForSecondaryButton).toBe('symptomInquiry.symptomsSelection.toTop');
  });

  it('should use translation key for back to top for scrolling is false', () => {
    component.secondaryButtonScrollsToTop = false;
    const translationKeyForSecondaryButton = component.getSecondaryButtonTranslationKey();
    expect(translationKeyForSecondaryButton).toBe('general.cancel');
  });
});
