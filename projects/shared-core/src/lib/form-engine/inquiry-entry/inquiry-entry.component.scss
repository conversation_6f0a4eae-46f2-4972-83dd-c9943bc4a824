@import '../../../styles/deprecated_variables.scss';
@import '../../../styles/ds-variables.scss';
.close-button:after {
  content: '×';
}
.center {
  text-align: center;
}
.status-check-container {
  padding-bottom: $spacing-xl;
}
.status-check-section::ng-deep {
  .form-element-container {
    margin-bottom: 25px !important;
  }
}
#wizard::ng-deep {
  .section-description {
    font-size: 19px;
    font-family: 'PaulGroteskSoft', helvetica, arial, sans-serif;
    font-weight: 300;
    letter-spacing: -0.4px;
    margin: 20px 0;
  }
}

ds-button.button-next-invalid {
  padding: 0 20px;
}
