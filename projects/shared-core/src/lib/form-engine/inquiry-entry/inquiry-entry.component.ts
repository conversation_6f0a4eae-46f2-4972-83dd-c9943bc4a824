import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  HostBinding,
  Input,
  OnDestroy,
  OnInit,
  Output
} from '@angular/core';
import { select, Store } from '@ngrx/store';
import { Moment } from 'moment';
import { Observable, Subject } from 'rxjs';
import { take, takeUntil, tap, withLatestFrom } from 'rxjs/operators';
import { FormType } from '../../constants';
import { Application } from '../../models/application';
import { SymptomInquirySubmit } from '../interface/symptom-inquiry-submit.interface';
import { ScrollBehavior } from '../models/scroll-behavior.enum';
import { SymptomInquiryInformationViewModel } from '../models/symptom-inquiry-information-view-model';
import { FieldService } from '../services/field.service';
import { FormScrollService } from '../services/form-scroll.service';
import { SymptomService } from '../services/symptom.service';
import { ResetFormState, StartNewForm } from '../store/actions/form.actions';
import { LoadSymptomInquiry, ResetInquiryState, ShowNextForm } from '../store/actions/inquiry.action';
import { FormEngineState } from '../store/reducers/state';
import {
  getAllSymptomTypes,
  getInquiryState,
  getRequiredTypesState,
  selectAllInvalidForms,
  selectAreAllFormsValid
} from '../store/selectors/inquiry.selectors';
import { NoonalyticCategories } from '../../utils/analytics/models';
import { InquiryType } from '../../generated/models/inquiry-type';
import { selectField, selectForm } from '../store/selectors/form.selectors';
import { SymptomType } from '../../generated/models/symptom-type';
import { PhotoPreviewService } from '../services/photo-preview.service';
import { PreviewOption } from '../../utils/photo-uploader/preview-option.interface';
import { statusCheckFields } from './status-check-fields';
import get from 'lodash/get';
import { SymptomInquiryInformation } from '../../generated/models/symptom-inquiry-information';

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: 'nh-inquiry-entry',
  templateUrl: './inquiry-entry.component.html',
  styleUrls: ['./inquiry-entry.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class InquiryEntryComponent implements OnInit, OnDestroy {
  @HostBinding('class.theme--patient') get forPatientHostClass() {
    return !this.showHeader;
  }
  @Input() showHeader: boolean;
  @Input() inquiryId: string;
  @Input() saveTranslationKey: string;
  @Input() site: Application;
  @Input() showCloseButton = false;
  @Input() askNextOfKin: boolean;
  @Input() askAdditionalQuestions: boolean;
  @Input() secondaryButtonScrollsToTop: boolean;

  @Output() canceled = new EventEmitter<string>();
  @Output() save = new EventEmitter<SymptomInquirySubmit>();
  @Output() goBack = new EventEmitter<string>();
  @Output() scrollToTop = new EventEmitter<void>();
  @Output() openPhotoPreview = new EventEmitter<PreviewOption>();
  @Output() questionnaireLoaded = new EventEmitter<SymptomInquiryInformation>();

  public info: SymptomInquiryInformationViewModel;
  public isValid = false;
  public showSummary = false;
  public firstStep = true;
  public error: string = undefined;
  public questionnaire;
  public answerSince: Moment;
  public types$: Observable<FormType[]>;
  public formVisibility: { [type: string]: boolean };
  public anySymptom$: Observable<boolean>;
  public statuCheckDone = false;
  private isStatusCheckFormValid = false;
  public statusCheck$: Observable<any>;
  private destroy$ = new Subject<boolean>();
  private completeValid$ = new Subject<boolean>();
  private initialized = false;
  private anySymptom = false;
  public statusCheckFields = statusCheckFields;

  analyticsInitialized = false;
  _analyticCategory: NoonalyticCategories = null;
  @Input()
  set analyticCategory(category: NoonalyticCategories) {
    this._analyticCategory = category;
    if (this._analyticCategory && this.info && !this.analyticsInitialized) {
      this.analyticsInitialized = true;
    }
  }
  get analyticCategory() {
    return this._analyticCategory;
  }

  get questionnaireType() {
    return get(this.info, ['symptomInquiry', 'type']);
  }

  get isStatusCheckQuestionnaire() {
    return this.questionnaireType === InquiryType.TO_POST_TREATMENT;
  }

  constructor(
    private store: Store<FormEngineState>,
    private cd: ChangeDetectorRef,
    private fieldService: FieldService,
    private symptomService: SymptomService,
    private formScrollService: FormScrollService,
    private photoPreviewService: PhotoPreviewService
  ) {}

  ngOnInit() {
    // Make sure inquiry form is loaded cleanly
    this.store.dispatch(new StartNewForm({ type: SymptomType.SYMPTOM_STATUS_CHECK }));
    this.store.dispatch(new ResetInquiryState());
    this.store.dispatch(new LoadSymptomInquiry(this.inquiryId));
    this.store
      .select(selectForm(SymptomType.SYMPTOM_STATUS_CHECK))
      .pipe(takeUntil(this.destroy$))
      .subscribe(form => {
        if (form && form.formValid) {
          this.isStatusCheckFormValid = form.formValid;
          if (form.formValid) {
            this.error = undefined;
          }
        } else {
          this.isStatusCheckFormValid = false;
        }
      });

    this.anySymptom$ = this.store.select(selectField(SymptomType.SYMPTOM_STATUS_CHECK, 'anySymptom')).pipe(
      tap(b => {
        this.anySymptom = b;
      }),
      takeUntil(this.destroy$)
    );

    this.store
      .select(selectForm(SymptomType.SYMPTOM_STATUS_CHECK))
      .pipe(takeUntil(this.destroy$))
      .subscribe(form => {
        if (form && form.formValid) {
          this.isStatusCheckFormValid = form.formValid;
          if (form.formValid) {
            this.error = undefined;
          }
        } else {
          this.isStatusCheckFormValid = false;
        }
      });

    this.anySymptom$ = this.store.select(selectField(SymptomType.SYMPTOM_STATUS_CHECK, 'anySymptom')).pipe(
      tap(b => {
        this.anySymptom = b;
      }),
      takeUntil(this.destroy$)
    );

    this.store
      .select(getInquiryState)
      .pipe(takeUntil(this.destroy$))
      .subscribe(inquiryState => {
        if (inquiryState.error) {
          return;
        }

        if (inquiryState.loaded && !this.initialized) {
          if (inquiryState.info.expired) {
            this.goBack.next(inquiryState.info.patientId);
            return;
          }
          this.initialized = true;
          this.info = inquiryState.info;

          if (this.info.symptomsInDiary) {
            this.symptomService.loadSymptomsInfo(this.info.symptomsInDiary, this.info.lastAeq, this.info.patientId);

            if (this.info.symptomInquiry.type) {
              if (this.analyticCategory && !this.analyticsInitialized) {
                this.analyticsInitialized = true;
              }
            }
          }

          this.questionnaireLoaded.emit(inquiryState.info);
        }

        if (inquiryState.showSummary && !this.showSummary) {
          this.showSummary = true;
          this.scrollToSummary();
        }

        this.formVisibility = inquiryState.formVisibility;
        this.cd.markForCheck();
      });

    this.store
      .select(getRequiredTypesState)
      .pipe(takeUntil(this.completeValid$))
      .subscribe(requiredTypes => {
        if (requiredTypes) {
          this.isValid = requiredTypes.length < 1;
          if (this.isValid) {
            this.error = undefined;
          }
        }
      });

    this.types$ = this.store.select(getAllSymptomTypes);

    this.photoPreviewService.openPreview$.pipe(takeUntil(this.destroy$)).subscribe(previewOption => {
      this.openPhotoPreview.emit(previewOption);
    });
  }

  ngOnDestroy() {
    this.store.dispatch(new ResetInquiryState());
    this.store.dispatch(new ResetFormState());
    this.destroy$.complete();
  }

  public showQuestionnaire() {
    if (this.isStatusCheckFormValid) {
      this.statuCheckDone = true;
      if (this.anySymptom) {
        setTimeout(() => {
          this.scrollToQuestionnaire();
        }, 0);
      } else {
        this.showSummary = true;
        this.scrollToSummary();
      }
    } else {
      this.fieldService.updateFormFieldStyle();
      this.fieldService.validate();
      this.error = 'wizard.errors.submitFailed';
    }
  }

  public next() {
    if (this.isValid) {
      if (this.firstStep) {
        this.firstStep = false;
        this.completeValid$.next(true);
        this.completeValid$.complete();
        this.listenToFormValidity();
      }

      this.error = undefined;
      this.store.dispatch(new ShowNextForm());
    } else {
      if (this.firstStep) {
        this.error = 'wizard.errors.chooseYesOrNo';
      } else {
        this.fieldService.validate();
      }
      this.fieldService.updateFormFieldStyle();
    }
  }

  public nextInvalidSection() {
    this.store.pipe(select(selectAllInvalidForms), withLatestFrom(this.types$), take(1)).subscribe(([invalidForms, types]) => {
      if (invalidForms.length > 0 && types.length > 0) {
        const invalidFormsSortedByOrderInDOM = invalidForms.sort((a, b) => {
          return types.indexOf(a) - types.indexOf(b);
        });

        this.formScrollService.scrollToElement(
          `.symptom-form--${invalidFormsSortedByOrderInDOM[0]} .wizard-section`,
          ScrollBehavior.SMOOTH
        );
      }
    });
  }

  public scrollToSummary() {
    this.formScrollService.scrollToElement('#inquiry-summary', ScrollBehavior.SMOOTH);
  }

  public scrollToQuestionnaire() {
    this.formScrollService.scrollToElement(`#inquiry-${this.questionnaireType}`, ScrollBehavior.SMOOTH);
  }

  handleSecondaryButton() {
    if (this.secondaryButtonScrollsToTop) {
      this.scrollToTop.next();
    } else {
      this.canceled.next(this.info.patientId);
    }
  }

  public onClose() {
    this.canceled.next(this.info.patientId);
  }

  private listenToFormValidity() {
    this.store
      .select(selectAreAllFormsValid)
      .pipe(takeUntil(this.destroy$))
      .subscribe(isValid => {
        this.isValid = isValid;
        this.cd.detectChanges();
      });
  }

  public getSecondaryButtonTranslationKey(): string {
    return this.secondaryButtonScrollsToTop ? 'symptomInquiry.symptomsSelection.toTop' : 'general.cancel';
  }
}
