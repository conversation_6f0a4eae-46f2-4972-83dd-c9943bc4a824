<div id="questionnaire-{{ questionnaireType }}">
  <div id="wizard">
    <div *ngIf="showCloseButton" class="wizard-top-actions">
      <button class="close-button" (click)="onClose()" [attr.aria-label]="'general.close' | i18n"></button>
    </div>

    <!--additional questions for status check: start-->
    <div class="symptom-inquiry nh-grid-form-fullpage status-check-container" *ngIf="isStatusCheckQuestionnaire">
      <h1 class="section-title">{{ 'symptomInquiry.symptomsSelection.statusCheck.title' | i18n }}</h1>
      <p class="section-description">
        {{ 'symptomInquiry.symptomsSelection.statusCheck.description' | i18n }}
      </p>
      <section class="wizard-section status-check-section">
        <ng-container class="no-padding" *ngFor="let field of statusCheckFields" nhDynamicFormField [config]="field"></ng-container>
      </section>
      <div class="wizard-actions">
        <div class="wizard-button symptom-inquiry">
          <div class="errors" *ngIf="error">
            <span class="error">{{ error | i18n }}</span>
          </div>
          <div class="step-buttons center">
            <ds-button variation="secondary" class="button-cancel" (buttonClick)="onClose()">
              {{ 'general.cancel' | i18n }}
            </ds-button>
            <ds-button id="questionnaire-button-next" class="button-next" (buttonClick)="showQuestionnaire()">
              {{ 'general.next' | i18n }}
            </ds-button>
          </div>
        </div>
      </div>
    </div>
    <!--end-->

    <div class="symptom-inquiry nh-grid-form-fullpage" id="inquiry-{{ questionnaireType }}">
      <div *ngIf="!isStatusCheckQuestionnaire || ((anySymptom$ | async) && statuCheckDone)">
        <nh-symptom-selection [inquiryInfo]="info" [site]="site" (setAnswerSince)="answerSince = $event"></nh-symptom-selection>
        <div class="wizard-actions">
          <div class="wizard-button symptom-inquiry">
            <div class="errors" *ngIf="error">
              <span class="error">{{ error | i18n }}</span>
            </div>
            <div class="step-buttons center">
              <ds-button variation="secondary" class="button-cancel" (buttonClick)="onClose()">
                {{ 'general.cancel' | i18n }}
              </ds-button>
              <ds-button
                *ngIf="firstStep || (isValid && !showSummary)"
                id="questionnaire-button-next"
                class="button-next"
                (buttonClick)="next()"
              >
                {{ 'general.next' | i18n }}
              </ds-button>
              <ds-button *ngIf="!firstStep && !isValid" class="button-next-invalid" (buttonClick)="nextInvalidSection()">
                {{ 'symptomInquiry.symptomsSelection.toNextInvalidStep' | i18n }}
              </ds-button>
              <ds-button *ngIf="showSummary" class="button-to-summary" [isDisabled]="!isValid" (buttonClick)="scrollToSummary()">
                {{ 'symptomInquiry.symptomsSelection.toSummary' | i18n }}
              </ds-button>
            </div>
          </div>
        </div>
        <div class="symptom-forms" *ngIf="!firstStep">
          <div class="symptom-form symptom-form--{{ type }}" *ngFor="let type of types$ | async">
            <nh-form-generator
              *ngIf="formVisibility[type]"
              [formType]="type"
              [info]="info"
              [answerSince]="answerSince"
              [secondaryButtonTranslationKey]="getSecondaryButtonTranslationKey()"
              (next)="next()"
              (canceled)="handleSecondaryButton()"
            ></nh-form-generator>
          </div>
        </div>
      </div>
      <div class="wizard-section" *ngIf="showSummary" id="inquiry-summary">
        <ng-container>
          <h1 class="section-title">{{ 'nurse.symptomInquiry.summary.section.title' | i18n }}</h1>
          <p class="section-description">
            {{ 'nurse.symptomInquiry.summary.section.description' | i18n }}
          </p>
        </ng-container>
        <nh-inquiry-summary
          class="final-summary"
          [askNextOfKin]="askNextOfKin"
          [askAdditionalQuestions]="askAdditionalQuestions"
          [info]="info"
          [isValid]="isValid"
          [formVisibility]="formVisibility"
          [saveTranslationKey]="saveTranslationKey"
          [secondaryButtonTranslationKey]="getSecondaryButtonTranslationKey()"
          (canceled)="handleSecondaryButton()"
          (save)="save.next($event)"
        ></nh-inquiry-summary>
      </div>
    </div>
  </div>
</div>
