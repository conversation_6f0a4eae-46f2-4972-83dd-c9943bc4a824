import { FormItemType } from '../../generated/models/form-item-type';
import { LabelLocation } from '../models/label-location.enum';

const showAnyContactConditions = {
    type: 'or',
    conditionType: 'or',
    fieldConditionType: 'remove',
    fieldConditionMatcher: 'contains',
    conditions: [
        {
            type: 'field',
            conditionType: 'field',
            fieldConditionType: 'remove',
            fieldConditionMatcher: 'contains',
            fieldName: 'anySymptom',
            fieldType: 'radioList',
            values: ['true'],
        },
        {
            type: 'field',
            conditionType: 'field',
            fieldConditionType: 'remove',
            fieldConditionMatcher: 'contains',
            fieldName: 'topics',
            fieldType: 'checkboxList',
            values: ['other', 'medication', 'appointment', 'insureOrFinanc'],
        },
    ],
};
const showMedicationQuestionConditions = {
    type: 'or',
    conditionType: 'or',
    fieldConditionType: 'remove',
    fieldConditionMatcher: 'contains',
    conditions: [
        {
            type: 'field',
            conditionType: 'field',
            fieldConditionType: 'remove',
            fieldConditionMatcher: 'contains',
            fieldName: 'topics',
            fieldType: 'checkboxList',
            values: ['medication'],
        },
    ],
};
const showAppointmentQuestionConditions = {
    type: 'or',
    conditionType: 'or',
    fieldConditionType: 'remove',
    fieldConditionMatcher: 'contains',
    conditions: [
        {
            type: 'field',
            conditionType: 'field',
            fieldConditionType: 'remove',
            fieldConditionMatcher: 'contains',
            fieldName: 'topics',
            fieldType: 'checkboxList',
            values: ['appointment'],
        },
    ],
};
const showInsureOrFinancQuestionConditions = {
    type: 'or',
    conditionType: 'or',
    fieldConditionType: 'remove',
    fieldConditionMatcher: 'contains',
    conditions: [
        {
            type: 'field',
            conditionType: 'field',
            fieldConditionType: 'remove',
            fieldConditionMatcher: 'contains',
            fieldName: 'topics',
            fieldType: 'checkboxList',
            values: ['insureOrFinanc'],
        },
    ],
};
const showContactMethodsConditions = {
    type: 'or',
    conditionType: 'or',
    fieldConditionType: 'remove',
    fieldConditionMatcher: 'contains',
    conditions: [
        {
            type: 'field',
            conditionType: 'field',
            fieldConditionType: 'remove',
            fieldConditionMatcher: 'contains',
            fieldName: 'anyContact',
            fieldType: 'radioList',
            values: ['true'],
        },
    ],
};
const showContactTimeConditions = {
    type: 'or',
    conditionType: 'or',
    fieldConditionType: 'remove',
    fieldConditionMatcher: 'contains',
    conditions: [
        {
            type: 'field',
            conditionType: 'field',
            fieldConditionType: 'remove',
            fieldConditionMatcher: 'contains',
            fieldName: 'contactMethods',
            fieldType: 'radioList',
            values: ['phoneCall'],
        },
    ],
};
export const statusCheckFields = [
    {
        type: FormItemType.SLIDER,
        key: 'fellingIntensity',
        required: {
            type: 'booleanTrue',
            conditionType: 'booleanTrue',
            conditionAttribute: 'true',
        },
        showCondition: null,
        staticShowCondition: null,
        labelKey: 'fe.statusCheck.fellingIntensity.title',
        min: 0,
        max: 10,
        maxTranslationKey: 'fe.statusCheck.fellingIntensity.great',
        minTranslationKey: 'fe.statusCheck.fellingIntensity.awfull',
        formKey: 'statusCheckForm',
        formType: 'statusCheck',
        fromInquiry: true,
        subInquiry: false,
        invert: false,
        mid: false,
        indicatorLabel: '',
    },
    {
        type: FormItemType.RADIO,
        key: 'anySymptom',
        required: {
            type: 'booleanTrue',
            conditionType: 'booleanTrue',
            conditionAttribute: 'true',
        },
        showCondition: null,
        staticShowCondition: null,
        labelKey: 'fe.statusCheck.anySymption.title',
        labelLocation: LabelLocation.TOP,
        formKey: 'statusCheckForm',
        formType: 'statusCheck',
        fromInquiry: true,
        subInquiry: false,
        templateOptions: {
            options: [
                {
                    id: 'statusCheckForm-any-sympton-false',
                    selected: true,
                    translationKey: 'fe.statusCheck.anySymption.false.label',
                    value: false,
                },
                {
                    id: 'statusCheckForm-any-sympton-true',
                    selected: false,
                    translationKey: 'fe.statusCheck.anySymption.true.label',
                    value: true,
                },
            ],
        },
    },
    {
        type: FormItemType.CHECKBOX,
        key: 'topics',
        required: null,
        showCondition: null,
        staticShowCondition: null,
        labelKey: 'fe.statusCheck.topics.title',
        labelLocation: LabelLocation.TOP,
        formKey: 'statusCheckForm',
        formType: 'statusCheck',
        fromInquiry: true,
        subInquiry: false,
        templateOptions: {
            options: [
                {
                    id: 'option0',
                    translationKey: 'fe.statusCheck.topics.medication.label',
                    selected: false,
                    value: 'medication',
                },
                {
                    id: 'option1',
                    translationKey: 'fe.statusCheck.topics.appointment.label',
                    selected: false,
                    value: 'appointment',
                },
                {
                    id: 'option2',
                    translationKey: 'fe.statusCheck.topics.insureOrFinanc.label',
                    selected: false,
                    value: 'insureOrFinanc',
                },
                {
                    id: 'noissues',
                    translationKey: 'fe.statusCheck.topics.noissues.label',
                    selected: false,
                    value: 'noissues',
                },
                {
                    id: 'other',
                    translationKey: 'fe.statusCheck.topics.other.label',
                    selected: false,
                    value: 'other',
                },
            ],
        },
    },
    {
        type: FormItemType.TEXTAREA,
        key: 'medicationQuestion',
        required: {
            type: 'booleanTrue',
            conditionType: 'booleanTrue',
            conditionAttribute: 'true',
        },
        horizontal: false,
        showCondition: showMedicationQuestionConditions,
        staticShowCondition: null,
        labelKey: 'fe.statusCheck.medicationQuestion.title',
        labelLocation: LabelLocation.TOP,
        formKey: 'statusCheckForm',
        formType: 'statusCheck',
        fromInquiry: true,
        subInquiry: false,
    },
    {
        type: FormItemType.TEXTAREA,
        key: 'appointmentQuestion',
        required: {
            type: 'booleanTrue',
            conditionType: 'booleanTrue',
            conditionAttribute: 'true',
        },
        horizontal: false,
        showCondition: showAppointmentQuestionConditions,
        staticShowCondition: null,
        labelKey: 'fe.statusCheck.appointmentQuestion.title',
        labelLocation: LabelLocation.TOP,
        formKey: 'statusCheckForm',
        formType: 'statusCheck',
        fromInquiry: true,
        subInquiry: false,
    },
    {
        type: FormItemType.TEXTAREA,
        key: 'insureOrFinancQuestion',
        required: {
            type: 'booleanTrue',
            conditionType: 'booleanTrue',
            conditionAttribute: 'true',
        },
        horizontal: false,
        showCondition: showInsureOrFinancQuestionConditions,
        staticShowCondition: null,
        labelKey: 'fe.statusCheck.insureOrFinancQuestion.title',
        labelLocation: LabelLocation.TOP,
        formKey: 'statusCheckForm',
        formType: 'statusCheck',
        fromInquiry: true,
        subInquiry: false,
    },
    {
        type: FormItemType.RADIO,
        key: 'anyContact',
        required: {
            type: 'booleanTrue',
            conditionType: 'booleanTrue',
            conditionAttribute: 'true',
        },
        showCondition: showAnyContactConditions,
        staticShowCondition: null,
        labelKey: 'fe.statusCheck.anyContact.title',
        labelLocation: LabelLocation.TOP,
        formKey: 'statusCheckForm',
        formType: 'statusCheck',
        fromInquiry: true,
        subInquiry: false,
        templateOptions: {
            options: [
                {
                    id: 'statusCheckForm-any-contact-false',
                    selected: false,
                    translationKey: 'fe.statusCheck.anyContact.false.label',
                    value: false,
                },
                {
                    id: 'statusCheckForm-any-contact-true',
                    selected: false,
                    translationKey: 'fe.statusCheck.anyContact.true.label',
                    value: true,
                },
            ],
        },
    },
    {
        type: FormItemType.RADIO,
        key: 'contactMethods',
        required: {
            type: 'booleanTrue',
            conditionType: 'booleanTrue',
            conditionAttribute: 'true',
        },
        showCondition: showContactMethodsConditions,
        staticShowCondition: null,
        labelKey: 'fe.statusCheck.contactMethods.title',
        labelLocation: LabelLocation.TOP,
        formKey: 'statusCheckForm',
        formType: 'statusCheck',
        fromInquiry: true,
        subInquiry: false,
        templateOptions: {
            options: [
                {
                    id: 'statusCheckForm-contact-methods-noonaMessage',
                    selected: false,
                    translationKey: 'fe.statusCheck.contactMethods.noonaMessage.label',
                    value: 'noonaMessage',
                },
                {
                    id: 'statusCheckForm-contact-methods-phoneCall',
                    selected: true,
                    translationKey: 'fe.statusCheck.contactMethods.phoneCall.label',
                    value: 'phoneCall',
                },
            ],
        },
    },
    {
        type: FormItemType.TEXTAREA,
        key: 'contactTime',
        required: {
            type: 'booleanTrue',
            conditionType: 'booleanTrue',
            conditionAttribute: 'true',
        },
        horizontal: false,
        showCondition: showContactTimeConditions,
        staticShowCondition: null,
        labelKey: 'fe.statusCheck.contactTime.title',
        labelLocation: LabelLocation.TOP,
        formKey: 'statusCheckForm',
        formType: 'statusCheck',
        fromInquiry: true,
        subInquiry: false,
    },
];
