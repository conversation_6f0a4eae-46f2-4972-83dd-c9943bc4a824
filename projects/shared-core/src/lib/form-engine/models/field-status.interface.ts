export interface FieldStatus {
    id?: string;
    formType: string;
    fieldKey: string;
    statusType?: FieldStatusType;
    translationKey: string;
    accepted?: boolean;
    lastModified?: Date;
    userId?: string;
    userName?: string;
    meta?: string;
}

export enum FieldStatusType {
    HARD_ERROR = 'hardError',
    SOFT_ERROR = 'softError',
    TERMINATION_ERROR = 'terminationError',
    NOT_READY = 'notReady',
    MISSING_VALUE = 'missingValue',
}
