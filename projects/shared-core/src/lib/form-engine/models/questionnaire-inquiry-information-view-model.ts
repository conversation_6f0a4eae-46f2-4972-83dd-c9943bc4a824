import { Gender } from '../../generated/models/gender';
import { QuestionaryType } from '../../generated/models/questionary-type';
import { QuestionnaireInquiryInformation } from '../../generated/models/questionnaire-inquiry-information';
import { Symptom } from '../../generated/models/symptom';
import { TreatmentModuleType } from '../../generated/models/treatment-module-type';

export class QuestionnaireInquiryInformationViewModel implements QuestionnaireInquiryInformation {
    questionnaireType?: QuestionaryType;
    messageId?: string;
    patientId?: string;
    gender?: Gender;
    answerable?: boolean;
    treatmentModuleType?: TreatmentModuleType;
    symptomsInDiary: Symptom[];

    public copyFields(from: QuestionnaireInquiryInformation) {
        this.questionnaireType = from.questionnaireType;
        this.messageId = from.messageId;
        this.patientId = from.patientId;
        this.gender = from.gender;
        this.answerable = from.answerable;
        this.treatmentModuleType = from.treatmentModuleType;
    }
}
