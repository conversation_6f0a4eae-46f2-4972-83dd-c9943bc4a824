import { ConditionType } from '../../generated/models/condition-type';
import { FormItemType } from '../../generated/models/form-item-type';
import { FieldConditionMatcher } from './field-condition-matcher.enum';
import { FieldConditionType } from './field-condition-type.enum';

export interface ShowCondition {
    conditionType?: ConditionType;
    fieldConditionMatcher?: FieldConditionMatcher;
    fieldConditionType?: FieldConditionType;
    fieldName?: string;
    fieldType?: FormItemType;
    type?: ConditionType;
    values?: any[];

    conditions?: ShowCondition[];
}
