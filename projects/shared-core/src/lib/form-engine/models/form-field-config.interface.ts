import { FormType } from '../../constants';
import { FormItemType } from '../../generated/models/form-item-type';
import { Gender } from '../../generated/models/gender';
import { HeightUnit } from '../../generated/models/height-unit';
import { TreatmentModuleType } from '../../generated/models/treatment-module-type';
import { Application } from '../../models/application';
import { LabelLocation } from './label-location.enum';
import { UnitType } from './unit-type.enum';
import { FormVariableValue } from './form-var-value';
import { Dictionary } from '../../common-types';
import { ModelValue } from './model-value.interface';

export interface FormFieldConfig {
    children?: FormFieldConfig[];
    collection?: boolean;
    descriptionKey?: string;
    disabled?: boolean;
    formKey?: string;
    formType?: FormType;
    headerKey?: string;
    heightUnit?: HeightUnit;
    hideTitle?: boolean;
    hint?: string;
    indent?: boolean;
    key?: string;
    id?: string;
    labelKey?: string;
    subLabelKey?: string;
    labelLocation?: LabelLocation;
    maxLength?: number;
    minLength?: number;
    placeholderKey?: string;
    required?: Required;
    sectionTitle?: string;
    showCondition?: any;
    staticShowCondition?: any;
    tooltipKey?: string;
    translationPrefix?: string;
    type?: FormItemType;
    unit?: any;
    unitType?: UnitType;
    valuesKey?: string;
    width?: any;
    maxTranslationKey?: string;
    minTranslationKey?: string;
    max?: number;
    min?: number;
    gender?: Gender;
    since?: number | string | Date;
    sectionNumber?: number;
    sectionCount?: number;
    fromInquiry?: boolean;
    subInquiry?: boolean;
    treatmentModuleTypes?: TreatmentModuleType[];
    currentState?: string;
    site?: Application;
    // This field is not saved when submitting the form.
    // Can be some helper field needed for implementing some specific logic in the form.
    transientItem?: boolean;
    // Indicate that a field is getting its value options from the selection of another field.
    // Provider field might be transient.
    optionsProviderFieldKeys?: string[];
    // Indicate whether the visibility of the field which gets its value options from the selection of another field depends
    // on availablity of the other fields selections or not.
    optionsProviderControlsVisibility?: boolean;
    // MultiComponent options
    secondaryTranslationPrefix?: string;
    multiRow?: boolean;
    // Specifies the options that should include an extra field for the RadioListWithExtraFields, defined by key
    optionsWithExtraField?: ExtraField[];
    radioOption?: string;
    textOption?: string;
    // ID of a group of fields which belong to one question.
    questionGroupId?: string;
    // Flag for components that could be rendered either vertically or horizontally e.g. radio-list.component
    horizontal?: boolean;
    enableFieldStatus?: boolean;
    allowFutureDate?: boolean;
    eventConfig?: EventConfig;
    allowUnknownDate?: boolean;
    unknownDayDefault?: UnknownDateDefault;
    dateRangeValidationConfig?: DateRangeConfig;
    adhocFormVariables?: Dictionary<string, FormVariableValue>;
    templateOptions?: TemplateOptions;
    textKey?: string;
    invert?: boolean;
    mid?: boolean;
    indicatorLabel?: string;
    footerKey?: string;
}

export interface TemplateOptions {
    options?: ModelValue[];
}

export interface DateRangeConfig {
    fromVariableKey?: string;
    fromBoundaryIncluded?: boolean;
    toVariableKey?: string;
    toBoundaryIncluded?: boolean;
    outOfRangeTranslationKey?: string;
    afterToTranslationKey?: string;
    beforFromTranslationKey?: string;
}

export enum UnknownDateDefault {
    FIRST = 'first',
    LAST = 'last',
}

export interface Required {
    type?: string;
    conditionType?: string;
    conditionAttribute?: string;
}

export interface ExtraField {
    key: string;
    type: 'DATE' | 'TEXT' | 'DROPDOWN' | 'NUMERIC_FIELD';
    allowFutureDate?: boolean;
    valuesKey?: string;
    valuesTranslationPrefix?: string;
    placeholderTranslationKey?: string;
    min?: number;
    max?: number;
    unit?: string;
    mid?: number;
}

export interface EventConfig {
    // One of these needs to be defined and they control the event summary header. typeField is used when the header is dynamic
    // based on some answers, e.g. from a radio list. It should be the key of a field component.
    // typeFieldTranslationKey is used when the header is static.
    typeField?: string;
    typeFieldTranslationKey?: string;
    maxNumberOfEvents?: number;
}
