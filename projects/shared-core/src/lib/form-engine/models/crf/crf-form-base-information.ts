import { TreatmentModuleType } from '../../../generated/models/treatment-module-type';
import { FieldStatus } from '../field-status.interface';
import { CrfFormType } from './crf-form-type';
import { CRFVariableValue } from './crf-var-value';
import { Form } from '../form.interface';

export interface CrfFormBaseInformation {
    id?: string;
    crfFormType?: CrfFormType;
    answerable?: boolean;
    site?: any;
    study?: any;
    crf?: any;
    lastModifiedBy?: any;
    variableValuesLastSave?: CRFVariableValue[];
    variableValuesWithUpdates?: CRFVariableValue[];
    formFieldStatuses?: FieldStatus[];
    abstractionTimeStart?: Date;
    abstractionTimeEnd?: Date;
    // Can be used to provide a specific schema which will be used instead of the static schema
    schema?: any;
}
