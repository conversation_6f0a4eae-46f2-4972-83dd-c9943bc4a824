import { CrfFormBaseInformation } from './crf-form-base-information';
import { CrfFormType } from './crf-form-type';
import { CRFVariableValue } from './crf-var-value';
import { FieldStatus } from '../field-status.interface';

export class CrfFormInformationViewModel implements CrfFormBaseInformation {
    id?: string;
    crfFormType?: CrfFormType;
    answerable?: boolean;
    site?: any;
    study?: any;
    crf?: any;
    lastModifiedBy?: any;
    variableValuesLastSave?: CRFVariableValue[];
    variableValuesWithUpdates?: CRFVariableValue[];
    formFieldStatuses?: FieldStatus[];

    public copyFields(from: CrfFormBaseInformation) {
        this.id = from.id;
        this.crfFormType = from.crfFormType;
        this.answerable = from.answerable;
        this.site = from.site;
        this.study = from.study;
        this.crf = from.crf;
        this.lastModifiedBy = from.lastModifiedBy;
        this.variableValuesLastSave = from.variableValuesLastSave;
        this.variableValuesWithUpdates = from.variableValuesWithUpdates;
        this.formFieldStatuses = from.formFieldStatuses;
    }
}
