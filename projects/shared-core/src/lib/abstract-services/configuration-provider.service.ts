import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Dictionary } from '../common-types';
import { MeasurementSystem } from '../generated/models/measurement-system';
import { QuestionarySettings } from '../generated/models/questionary-settings';
import { User } from '../generated/models/user';
import { Application } from '../models/application';
import { DateFormattingPattern } from './interfaces/date-formatting-pattern.interface';

@Injectable()
export abstract class ConfigurationProviderService {
    abstract authorizationHeader(): Dictionary<string, string> | undefined;
    abstract dateFormattingPattern(): Observable<DateFormattingPattern>;
    abstract measurementSystem(): Observable<MeasurementSystem>;
    abstract questionarySettings(): any[];
    abstract baseUrl(): string;
    abstract site(): Application;
    abstract activeUser(): Observable<User>;
}
