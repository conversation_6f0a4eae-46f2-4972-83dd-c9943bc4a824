import { Observable } from 'rxjs';
import { MedicalInformation } from '../../generated/models/medical-information';
import { Symptom } from '../../generated/models/symptom';
import { SymptomCollection } from '../../generated/models/symptom-collection';
import { TriageInstruction } from '../../generated/models/triage-instruction';

export interface SymptomApi {
    getAlertsAndInstructions(patientId: string, symptoms: Symptom[]): Observable<TriageInstruction>;
    getCaseSymptoms(caseId: string): Observable<SymptomCollection>;
    getRequiredMedicalInformation(patientId: string): Observable<MedicalInformation>;
}
