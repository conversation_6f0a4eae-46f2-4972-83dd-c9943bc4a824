import { Observable } from 'rxjs';
import { Message } from '../../generated/models/message';
import { Patient } from '../../generated/models/patient';
import { PatientContactInformation } from '../../generated/models/patient-contact-information';
import { PhotoGroup } from '../../generated/models/photo-group';
import { PhotoGroupModel } from '../../generated/models/photo-group-model';
import { PhotoMetadata } from '../../generated/models/photo-metadata';
import { PhotoScale } from '../../generated/models/photo-scale';
import { Questionary } from '../../generated/models/questionary';
import { QuestionnaireInquiryInformation } from '../../generated/models/questionnaire-inquiry-information';
import { SaveSymptomResult } from '../../generated/models/save-symptom-result';
import { SymptomComparisonLevel } from '../../generated/models/symptom-comparison-level';
import { SymptomInformation } from '../../generated/models/symptom-information';
import { SymptomInquiryInformation } from '../../generated/models/symptom-inquiry-information';
import { SymptomReport } from '../../generated/models/symptom-report';
import { SymptomReportPair } from '../../generated/models/symptom-report-pair';
import { SymptomType } from '../../generated/models/symptom-type';

export interface MedicalApi {
    // Required
    getSymptomInformation(
        symptomTypes: SymptomType[],
        startDate: number | string | Date,
        patientId: string
    ): Observable<{ [key in SymptomType]: SymptomInformation }>;
    getSymptomInquiryInformationWithId(inquiryId: string): Observable<SymptomInquiryInformation>;
    addSymptomReport(
        symptomReport: SymptomReport,
        patientId: string,
        firstMessageId: string,
        message: Message
    ): Observable<SymptomReportPair>;
    sendSymptomReport(
        symptomReportId: string,
        firstMessageId: string,
        patientId: string,
        emergencySymptoms: { [key in string]: SymptomComparisonLevel }
    ): Observable<SaveSymptomResult[]>;
    refreshUserSession(): Observable<void>;
    getQuestionnaireInformation(questionnaireInquiryId: string): Observable<QuestionnaireInquiryInformation>;
    addQuestionary(
        patientId: string,
        firstMessageId: string,
        message: Message,
        questionnaire: Questionary
    ): Observable<Questionary>;
    getPhotoMetadata1(originalId: string, photoGroup: PhotoGroup, photoScale: PhotoScale): Observable<PhotoMetadata>;
    getPhotoMetadata(photoGroupIds: string[], onlySubmitted: boolean): Observable<PhotoGroupModel[]>;
    createPhotoGroup(patientId?: string): Observable<PhotoGroup>;
    removePhoto(originalPhotoId: string): Observable<boolean>;

    // Optional, not used by patient API
    getPatientById?(patientId: string): Observable<Patient>;

    getPatientInformation?(patientId: string): Observable<PatientContactInformation>;

    getQuestionnaireLimitedLoginLink?(questionnaireId: string): Observable<string>;
}
