import { select, Store } from '@ngrx/store';
import { Moment } from 'moment';
import { combineLatest, Observable, Subject } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { FormType } from '../constants';
import { SimpleDateInputComponent } from '../form-engine/components/simple-date-input/simple-date-input.component';
import { CrfFormSubmit } from '../form-engine/models/crf/crf-form-submit.interface';
import { FieldStatus, FieldStatusType } from '../form-engine/models/field-status.interface';
import { FormFieldConfig } from '../form-engine/models/form-field-config.interface';
import { ViewComponentState } from '../form-engine/models/view-component-state';
import { isAfterDate, isBeforeDate, isInDateRange } from '../form-engine/services/datecheck-helper';
import {
    safeSelectField,
    safeSelectFieldStatus,
    safeSelectVariable,
    safeSelectVariableArrayValue,
    safeSelectVariableValue,
} from '../form-engine/services/subscription-helper';
import { AddOrUpdateFieldStatus, RemoveFieldStatus } from '../form-engine/store/actions/field-status.actions';
import { AddAnswer } from '../form-engine/store/actions/form.actions';
import { UpdateFormVariable } from '../form-engine/store/actions/variables.action';
import { FormEngineState } from '../form-engine/store/reducers/state';
import { selectAllComponentStates } from '../form-engine/store/selectors/component-state.selectors';
import { selectFieldStatuses } from '../form-engine/store/selectors/field-status.selectors';
import { FormItem } from '../generated/models/form-item';
import { checkFieldValidity } from '../util/field-validation';

export type DateType = number | Date | Moment;

export abstract class FormSpecificHandler {
    protected formType: FormType;
    protected formSchema: FormItem[];
    protected validationStatuses: FieldStatus[] = [];
    protected store: Store<FormEngineState>;
    protected destroy$: Subject<boolean>;

    // Ignore lint warning regarding protected constructor. Will break factory function in study!
    constructor(formType: FormType, formSchema: FormItem[]) {
        this.formType = formType;
        this.formSchema = formSchema;
    }

    abstract init(store: Store<FormEngineState>, destroy$: Subject<boolean>, extraInfo: any);
    abstract checkFormSpecificFieldsValidity(submitContent: CrfFormSubmit);

    checkFormValidity(submitContent: CrfFormSubmit): boolean {
        const hasAcceptedTerminationErrors = submitContent.fieldStatuses.some((status) => {
            return status.statusType === FieldStatusType.TERMINATION_ERROR && status.accepted === true;
        });
        const hasUnacceptedStatuses = submitContent.fieldStatuses.some((status) => {
            return !status.accepted;
        });
        if (hasAcceptedTerminationErrors) {
            return true;
        } else if (hasUnacceptedStatuses) {
            return false;
        }

        this.store
            .pipe(select(selectAllComponentStates))
            .pipe(take(1))
            .subscribe((componentStates) => {
                let validationStatuses = [];
                this.formSchema.forEach((field) => {
                    const fieldConfig: FormFieldConfig = { ...field, formType: this.formType };
                    const componentState =
                        componentStates &&
                        componentStates.find((cs: ViewComponentState) => {
                            return cs.id === fieldConfig.key;
                        });
                    if (!componentState || componentState.visible || componentState.visible === undefined) {
                        validationStatuses = [
                            ...validationStatuses,
                            ...checkFieldValidity(fieldConfig, submitContent.crfForm),
                        ];
                    }
                });
                this.validationStatuses = validationStatuses;
            });
        this.checkFormSpecificFieldsValidity(submitContent);
        this.publishValidationStatuses();
        return this.validationStatuses.length === 0;
    }

    publishValidationStatuses() {
        this.validationStatuses.forEach((status) => {
            return this.store.dispatch(new AddOrUpdateFieldStatus(status));
        });
    }

    updateFiledAnswer(fieldKey: string, answer: any) {
        this.store.dispatch(
            new AddAnswer({
                answer: null,
                field: fieldKey,
                required: false,
                type: this.formType,
                initialValue: false,
            })
        );
    }

    updateVariable(key: string, changes: any) {
        this.store.dispatch(
            new UpdateFormVariable({
                id: key,
                changes,
            })
        );
    }

    selectFieldStatus(
        field?: string,
        fsType?: FieldStatusType,
        translationKey?: string,
        includeChildKeys?: boolean
    ): Observable<any> {
        return safeSelectFieldStatus(this.store, this.destroy$, field, fsType, translationKey, includeChildKeys);
    }

    selectVariable(key: string): Observable<any> {
        return safeSelectVariable(this.store, this.destroy$, key);
    }

    selectVariableValue(key: string): Observable<any> {
        return safeSelectVariableValue(this.store, this.destroy$, key);
    }

    selectVariableArrayValue(key: string): Observable<any> {
        return safeSelectVariableArrayValue(this.store, this.destroy$, key);
    }

    selectField(key: string, form: FormType): Observable<any> {
        return safeSelectField(this.store, this.destroy$, key, form);
    }

    selectMultipleFields(fields: string[]): Observable<any[]> {
        return combineLatest(
            fields.map((field) => {
                return this.selectField(field, this.formType);
            })
        ).pipe(takeUntil(this.destroy$));
    }

    private hasFieldUnknownDate(configKey: string): boolean {
        // If value is in store, subscribtion is guarantied to resolve right away!
        let isUnknownDate = false;
        this.store
            .select(
                selectFieldStatuses(
                    configKey,
                    FieldStatusType.SOFT_ERROR,
                    SimpleDateInputComponent.UNKNOWN_DAY_SOFT_ERROR_TRANSLATION_KEY
                )
            )
            .pipe(take(1))
            .subscribe((fieldStatuses: FieldStatus[]) => {
                isUnknownDate = fieldStatuses && fieldStatuses.length > 0;
            });
        return isUnknownDate;
    }

    isAfterDate(configKey: string, dateToCheck: DateType, rangeStart: DateType, inclusive: boolean = true): boolean {
        const isUnkownDate = this.hasFieldUnknownDate(configKey);
        return isAfterDate(dateToCheck, rangeStart, isUnkownDate, inclusive);
    }

    isBeforeDate(configKey: string, dateToCheck: DateType, rangeEnd: DateType, inclusive: boolean = true): boolean {
        const isUnknownDate = this.hasFieldUnknownDate(configKey);
        return isBeforeDate(dateToCheck, rangeEnd, isUnknownDate, inclusive);
    }

    isInDateRange(
        configKey: string,
        dateToCheck: DateType,
        rangeStart: DateType,
        rangeEnd: DateType,
        inclusivity: '()' | '[)' | '(]' | '[]' = '[]'
    ): boolean {
        const isUnknownDate = this.hasFieldUnknownDate(configKey);
        return isInDateRange(dateToCheck, rangeStart, rangeEnd, isUnknownDate, inclusivity);
    }

    addFieldError(erroFieldKey: string, errorTranslationKey: string, errorType: FieldStatusType, meta?: string) {
        this.store.dispatch(
            new AddOrUpdateFieldStatus({
                formType: this.formType,
                fieldKey: erroFieldKey,
                translationKey: errorTranslationKey,
                statusType: errorType,
                meta,
            })
        );
    }

    clearFieldError(erroFieldKey: string, errorTranslationKey: string, erroType: FieldStatusType) {
        this.store.dispatch(
            new RemoveFieldStatus({
                formType: this.formType,
                fieldKey: erroFieldKey,
                translationKey: errorTranslationKey,
                statusType: erroType,
            })
        );
    }
}
