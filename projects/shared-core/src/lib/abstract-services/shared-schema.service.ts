import { Injectable } from '@angular/core';
import { Dictionary } from '../common-types';
import { Condition } from '../generated/models/condition';
import { ListEntry } from '../generated/models/list-entry';
import { NoonaSchema } from '../generated/models/noona-schema';

@Injectable()
export abstract class SharedSchemaService {
    abstract getSelectLists(): Dictionary<string, ListEntry[]>;
    abstract getFieldValueStaticConditions(): Dictionary<string, Condition>;
    abstract getSchema(): NoonaSchema;
    abstract getTranslations(): Dictionary<string, Dictionary<string, string>>;
    abstract getAttributeLists(): Dictionary<string, string[]>;
    abstract getPhotoFields(): Dictionary<string, string[]>;
    abstract getBodyDiagrams(): Dictionary<string, string>;
    abstract overwriteFormSchema(formKey: string, schema: any);
}
