import { Injectable } from '@angular/core';
import { FormSpecificHandlerFactory } from './form-specific-handler-factory.service';
import { CrfFormApi } from './interfaces/crf-form-api.interface';
import { MedicalApi } from './interfaces/medical-api.interface';
import { QuestionnaireApi } from './interfaces/questionnaire-api.interface';
import { SymptomApi } from './interfaces/symptom-api.interface';

@Injectable()
export abstract class SharedApi {
    abstract medicalApi?: MedicalApi;
    abstract symptomApi?: SymptomApi;
    abstract questionnaireApi?: QuestionnaireApi;
    abstract crfFormApi?: CrfFormApi;
    abstract formSpecificHandlerFactory?: FormSpecificHandlerFactory;
}
