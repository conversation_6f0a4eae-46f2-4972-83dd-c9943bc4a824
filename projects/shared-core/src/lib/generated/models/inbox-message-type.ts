// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.dto.InboxMessageType

export enum InboxMessageType {
    MESSAGE = 'MESSAGE',
    SUPPORTMESSAGE = 'SUPPORTMESSAGE',
    NOTIFICATION = 'NOTIFICATION',
}

export const AllInboxMessageType = [
    InboxMessageType.MESSAGE,
    InboxMessageType.SUPPORTMESSAGE,
    InboxMessageType.NOTIFICATION,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
