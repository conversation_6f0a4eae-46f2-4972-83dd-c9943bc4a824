// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.AutomaticRule

import { AutomaticAdviceType } from './automatic-advice-type';
import { ClinicLocalisation } from './clinic-localisation';
export interface AutomaticRule {
    status?: boolean;
    automaticRuleName?: string;
    type?: AutomaticAdviceType;
    priority?: number;
    ruleId?: string;
    key?: string;
    description?: string;
    url_fi_FI?: string;
    url_sv_FI?: string;
    url_en_GB?: string;
    dialogTextTranslations?: ClinicLocalisation[];
    messageTranslations?: ClinicLocalisation[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
