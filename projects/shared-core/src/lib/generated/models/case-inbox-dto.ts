// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseInboxDto

import { CaseType } from './case-type';
import { InquiryStatus } from './inquiry-status';
import { QuestionaryType } from './questionary-type';
import { TopicType } from './topic-type';
import { User } from './user';
export interface CaseInboxDto {
    messageId?: string;
    caseId?: string;
    messageDate?: Date | number | string;
    sender?: User;
    title?: string;
    read?: boolean;
    topicType?: TopicType;
    caseType?: CaseType;
    symptomInquiry?: boolean;
    questionnaireInquiry?: boolean;
    inquiryStatus?: InquiryStatus;
    questionnaireInquiryType?: QuestionaryType;
    removed?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
