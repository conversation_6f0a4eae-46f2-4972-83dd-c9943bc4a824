// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.report.model.ProcessType

export enum ProcessType {
    PATIENT = 'patient',
    CASE = 'case',
    SMART_SYMPTOM_INQUIRY_STATUS_ID = 'smartSymptomInquiryStatusId',
    CLINIC_CONNECTION_REQUEST = 'clinicConnectionRequest',
    CONSENT = 'consent',
    MESSAGE = 'message',
    QUESTIONARY_INQUIRY = 'questionaryInquiry',
    SYMPTOM_INQUIRY = 'symptomInquiry',
}

export const AllProcessType = [
    ProcessType.PATIENT,
    ProcessType.CASE,
    ProcessType.SMART_SYMPTOM_INQUIRY_STATUS_ID,
    ProcessType.CLINIC_CONNECTION_REQUEST,
    ProcessType.CONSENT,
    ProcessType.MESSAGE,
    ProcessType.QUESTIONARY_INQUIRY,
    ProcessType.SYMPTOM_INQUIRY,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
