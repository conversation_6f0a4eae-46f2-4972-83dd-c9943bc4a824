// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.PhotoSet

import { PhotoMetadata } from './photo-metadata';
export interface PhotoSet {
    originalId?: string;
    thumbnail?: PhotoMetadata;
    small?: PhotoMetadata;
    large?: PhotoMetadata;
    extraLarge?: PhotoMetadata;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
