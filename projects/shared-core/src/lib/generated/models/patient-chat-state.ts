// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.support.model.PatientChatState

import { ChatToggleState } from './chat-toggle-state';
import { Patient } from './patient';
export interface PatientChatState {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    patient?: Patient;
    open?: boolean;
    minimizedState?: ChatToggleState;
    messageSent?: boolean;
    firstMessageId?: string;
    lastTimeActive?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
