// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.treatment.PatientTreatmentModule

import { QuestionnaireInquiry } from './questionnaire-inquiry';
import { QuestionnaireTemplate } from './questionnaire-template';
import { ScheduledMessage } from './scheduled-message';
import { SymptomInquiry } from './symptom-inquiry';
import { TreatmentModuleSubscription } from './treatment-module-subscription';
export interface PatientTreatmentModule {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    main?: boolean;
    treatmentModule?: any;
    startDate?: Date | number | string;
    endDate?: Date | number | string;
    subscriptions?: TreatmentModuleSubscription[];
    symptomInquiries?: SymptomInquiry[];
    lineOfTreatment?: number;
    scheduledMessages?: ScheduledMessage[];
    questionnaireInquiries?: QuestionnaireInquiry[];
    secretKeyId?: string;
    templateUsed?: QuestionnaireTemplate;
    scheduledActivationDate?: Date | number | string;
    scheduledForMain?: boolean;
    scheduledClosingDate?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
