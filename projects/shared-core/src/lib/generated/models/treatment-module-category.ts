// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.treatment.TreatmentModuleCategory

export enum TreatmentModuleCategory {
    RECOVERY = 'recovery',
    ACTIVE_TREATMENT = 'activeTreatment',
}

export const AllTreatmentModuleCategory = [TreatmentModuleCategory.RECOVERY, TreatmentModuleCategory.ACTIVE_TREATMENT];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
