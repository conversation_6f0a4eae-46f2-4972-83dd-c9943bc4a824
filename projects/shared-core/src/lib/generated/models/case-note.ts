// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseNote

import { Patient } from './patient';
import { User } from './user';
export interface CaseNote {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    patient?: Patient;
    nurse?: User;
    title?: string;
    content?: string;
    reminder?: boolean;
    reminderDate?: Date | number | string;
    secretKeyId?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
