// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.analytics.model.reporting.StanfordPatientStatusWeekView

import { EducationModuleCounts } from './education-module-counts';
import { FamilyResourceGuideCounts } from './family-resource-guide-counts';
import { MyHealthStatusCounts } from './my-health-status-counts';
export interface StanfordPatientStatusWeekView {
    careTeam?: string;
    firstDay?: Date | number | string;
    lastDay?: Date | number | string;
    myHealth?: MyHealthStatusCounts;
    family?: FamilyResourceGuideCounts;
    educationModules?: EducationModuleCounts;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
