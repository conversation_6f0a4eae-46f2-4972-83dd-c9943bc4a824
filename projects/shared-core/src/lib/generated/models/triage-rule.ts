// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.triage.model.TriageRule

import { TreatmentModule } from './treatment-module';
import { TriageRuleNote } from './triage-rule-note';
import { TriageRuleOperation } from './triage-rule-operation';
export interface TriageRule {
    id?: string;
    module?: TreatmentModule;
    triageRuleOperation?: TriageRuleOperation;
    triageNotes?: TriageRuleNote[];
    enabled?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
