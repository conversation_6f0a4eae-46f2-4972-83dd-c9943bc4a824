// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.integration.UranusIntegrationView

export enum UranusIntegrationView {
    SIS = 'SIS',
    KUN = 'KUN',
    RTG = 'RTG',
    LAB = 'LAB',
    KLF = 'KLF',
    PAT = 'PAT',
    ANE = 'ANE',
    SOS = 'SOS',
    RAV = 'RAV',
    PSL = 'PSL',
    HOKE = 'HOKE',
    LAH = 'LÄH',
    PAL = 'PAL',
    LAA = 'LÄÄ',
    DG = 'DG',
    PUH = 'PUH',
    TOI = 'TOI',
    FYST = 'FYST',
    NPSY = 'NPSY',
    NEUVO = 'NEUVO',
    OPI = 'OPI',
    RIS = 'RIS',
    KNF = 'KNF',
    VLAA = 'VLÄÄ',
    ERITYIS = 'ERITYIS',
    PAIK = 'PAIK',
    KIR = 'KIR',
    RKT = 'RKT',
    NKIR = 'NKIR',
    GYN = 'GYN',
    HOSU = 'HOSU',
    YHOS = 'YHOS',
    PHAL = 'PHAL',
    ENSIH = 'ENSIH',
    HOI = 'HOI',
    PAIV = 'PAIV',
    HAM = 'HAM',
    PALL = 'PALL',
    SEX = 'SEX',
    OPT = 'OPT',
    LAS = 'LAS',
    SIL = 'SIL',
    KNK = 'KNK',
    FON = 'FON',
    SUU = 'SUU',
    IHO = 'IHO',
    SAD = 'SÄD',
    PSY = 'PSY',
    LPSY = 'LPSY',
    NEU = 'NEU',
    LNEU = 'LNEU',
    KEU = 'KEU',
    AJANV = 'AJANV',
    LII = 'LII',
    GEN = 'GEN',
    TYO = 'TYÖ',
    FYS = 'FYS',
    GER = 'GER',
    YLE = 'YLE',
}

export const AllUranusIntegrationView = [
    UranusIntegrationView.SIS,
    UranusIntegrationView.KUN,
    UranusIntegrationView.RTG,
    UranusIntegrationView.LAB,
    UranusIntegrationView.KLF,
    UranusIntegrationView.PAT,
    UranusIntegrationView.ANE,
    UranusIntegrationView.SOS,
    UranusIntegrationView.RAV,
    UranusIntegrationView.PSL,
    UranusIntegrationView.HOKE,
    UranusIntegrationView.LAH,
    UranusIntegrationView.PAL,
    UranusIntegrationView.LAA,
    UranusIntegrationView.DG,
    UranusIntegrationView.PUH,
    UranusIntegrationView.TOI,
    UranusIntegrationView.FYST,
    UranusIntegrationView.NPSY,
    UranusIntegrationView.NEUVO,
    UranusIntegrationView.OPI,
    UranusIntegrationView.RIS,
    UranusIntegrationView.KNF,
    UranusIntegrationView.VLAA,
    UranusIntegrationView.ERITYIS,
    UranusIntegrationView.PAIK,
    UranusIntegrationView.KIR,
    UranusIntegrationView.RKT,
    UranusIntegrationView.NKIR,
    UranusIntegrationView.GYN,
    UranusIntegrationView.HOSU,
    UranusIntegrationView.YHOS,
    UranusIntegrationView.PHAL,
    UranusIntegrationView.ENSIH,
    UranusIntegrationView.HOI,
    UranusIntegrationView.PAIV,
    UranusIntegrationView.HAM,
    UranusIntegrationView.PALL,
    UranusIntegrationView.SEX,
    UranusIntegrationView.OPT,
    UranusIntegrationView.LAS,
    UranusIntegrationView.SIL,
    UranusIntegrationView.KNK,
    UranusIntegrationView.FON,
    UranusIntegrationView.SUU,
    UranusIntegrationView.IHO,
    UranusIntegrationView.SAD,
    UranusIntegrationView.PSY,
    UranusIntegrationView.LPSY,
    UranusIntegrationView.NEU,
    UranusIntegrationView.LNEU,
    UranusIntegrationView.KEU,
    UranusIntegrationView.AJANV,
    UranusIntegrationView.LII,
    UranusIntegrationView.GEN,
    UranusIntegrationView.TYO,
    UranusIntegrationView.FYS,
    UranusIntegrationView.GER,
    UranusIntegrationView.YLE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
