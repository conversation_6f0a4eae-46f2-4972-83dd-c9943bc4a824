// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.treatment.PatientTreatmentModulePage

import { CareTeam } from './care-team';
import { CodeDefinition } from './code-definition';
import { MedicationUnit } from './medication-unit';
import { PatientTreatmentModule } from './patient-treatment-module';
import { QuestionnaireTemplate } from './questionnaire-template';
import { TreatmentModule } from './treatment-module';
export interface PatientTreatmentModulePage {
    patientTreatmentModules?: PatientTreatmentModule[];
    availableTreatmentModules?: TreatmentModule[];
    careTeams?: CareTeam[];
    gatherMedicationInformation?: boolean;
    medicationUnits?: MedicationUnit[];
    activeSubstances?: CodeDefinition[];
    enabledTemplates?: QuestionnaireTemplate[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
