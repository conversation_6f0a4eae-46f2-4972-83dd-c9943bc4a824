// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.integration.LinkingRequest

import { ClinicLinkingContext } from './clinic-linking-context';
export interface LinkingRequest {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    clinicId?: string;
    requestingUserId?: string;
    context?: ClinicLinkingContext;
    readUserId?: string;
    writeUserId?: string;
    readTokenVerified?: boolean;
    writeTokenVerified?: boolean;
    metaInformation?: { [key in string]: string };
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
