// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.SymptomComparisonLevel

export enum SymptomComparisonLevel {
    NEW = 'new',
    EASED = 'eased',
    INTENSIFIED = 'intensified',
    SAME = 'same',
}

export const AllSymptomComparisonLevel = [
    SymptomComparisonLevel.NEW,
    SymptomComparisonLevel.EASED,
    SymptomComparisonLevel.INTENSIFIED,
    SymptomComparisonLevel.SAME,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
