// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.templates.QuestionnaireTemplatePage

import { CareTeam } from './care-team';
import { ClinicLanguage } from './clinic-language';
import { QuestionnaireTemplate } from './questionnaire-template';
import { TreatmentModule } from './treatment-module';
export interface QuestionnaireTemplatePage {
    treatmentModules?: TreatmentModule[];
    careTeams?: CareTeam[];
    templates?: QuestionnaireTemplate[];
    clinicLanguage?: ClinicLanguage;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
