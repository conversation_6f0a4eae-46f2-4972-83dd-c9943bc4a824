// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.diarytimeline.diaryentry.DiaryEntry

import { DiaryEntryWellnessData } from './diary-entry-wellness-data';
import { PhotoGroup } from './photo-group';
export interface DiaryEntry {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    date?: Date | number | string;
    text?: string;
    wellnessData?: DiaryEntryWellnessData;
    photo?: PhotoGroup;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
