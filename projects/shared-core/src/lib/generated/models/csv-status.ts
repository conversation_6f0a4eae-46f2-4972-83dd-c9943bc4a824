// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.processing.model.CsvStatus

export enum CsvStatus {
    LOADED = 0,
    PROCESSING = 1,
    PROCESSING_DONE = 2,
    CLOSED = 3,
}

export const AllCsvStatus = [CsvStatus.LOADED, CsvStatus.PROCESSING, CsvStatus.PROCESSING_DONE, CsvStatus.CLOSED];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
