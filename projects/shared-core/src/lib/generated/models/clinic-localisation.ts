// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.content.model.ClinicLocalisation

import { Language } from './language';
export interface ClinicLocalisation {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    localisationKey?: string;
    locale?: Language;
    content?: string;
    defaultText?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
