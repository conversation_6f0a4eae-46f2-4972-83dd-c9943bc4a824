// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.processing.model.Csv

import { CareTeam } from './care-team';
import { CsvError } from './csv-error';
import { CsvStatus } from './csv-status';
import { CsvType } from './csv-type';
import { TreatmentModule } from './treatment-module';
import { TreatmentModuleSubscription } from './treatment-module-subscription';
export interface Csv {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    name?: string;
    type?: CsvType;
    status?: CsvStatus;
    subscriptions?: TreatmentModuleSubscription[];
    totalRows?: number;
    currentRow?: number;
    startDateTime?: Date | number | string;
    completedDateTime?: Date | number | string;
    errors?: CsvError[];
    diagnosisCode?: string;
    treatmentModule?: TreatmentModule;
    careTeam?: CareTeam;
    proxyPatients?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
