// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.templates.questionnaires.QuestionnaireTemplate

import { ModuleTemplate } from './module-template';
export interface QuestionnaireTemplate {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    name?: string;
    aeModuleEnabled?: boolean;
    modules?: ModuleTemplate[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
