// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.analytics.model.reporting.QuestionnaireWeekView

import { QuestionaryType } from './questionary-type';
import { QuestionnaireCounts } from './questionnaire-counts';
export interface QuestionnaireWeekView {
    careTeamName?: string;
    firstDay?: Date | number | string;
    lastDay?: Date | number | string;
    counts?: { [key in QuestionaryType]: QuestionnaireCounts };
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
