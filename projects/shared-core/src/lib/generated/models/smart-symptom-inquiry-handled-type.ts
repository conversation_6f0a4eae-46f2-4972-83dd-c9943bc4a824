// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.SmartSymptomInquiryHandledType

export enum SmartSymptomInquiryHandledType {
    STOPPED = 'stopped',
    SELECTED = 'selected',
    EXTENDED = 'extended',
}

export const AllSmartSymptomInquiryHandledType = [
    SmartSymptomInquiryHandledType.STOPPED,
    SmartSymptomInquiryHandledType.SELECTED,
    SmartSymptomInquiryHandledType.EXTENDED,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
