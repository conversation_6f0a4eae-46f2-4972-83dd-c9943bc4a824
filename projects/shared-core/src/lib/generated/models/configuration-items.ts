// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.ConfigurationItems

import { ApiStrategy } from './api-strategy';
import { CaseStatus } from './case-status';
import { CaseTypeSetting } from './case-type-setting';
import { DateFormattingPattern } from './date-formatting-pattern';
import { IdentityCodeAlgorithm } from './identity-code-algorithm';
import { MeasurementSystem } from './measurement-system';
import { PatientStatusType } from './patient-status-type';
import { QuestionarySettings } from './questionary-settings';
import { StatusIntegration } from './status-integration';
import { TopicType } from './topic-type';
export interface ConfigurationItems {
    measurementSystem?: MeasurementSystem;
    patientMessagingEnabled?: boolean;
    dateFormattingPattern?: DateFormattingPattern;
    aeqAdditionalQuestions?: boolean;
    consultationEnabled?: boolean;
    clinicUserNotificationsEnabled?: boolean;
    identityCodeAlgorithm?: IdentityCodeAlgorithm;
    isIntegrationEnabled?: boolean;
    generalQuestionsEnabled?: boolean;
    isClinicianIdentityCodeEnabled?: boolean;
    contactPatientEnabled?: boolean;
    customServiceName?: boolean;
    clinicId?: string;
    clinicName?: string;
    enabledNonClinicalTopicTypes?: TopicType[];
    caseTypeSettings?: CaseTypeSetting[];
    enabledCaseStatuses?: CaseStatus[];
    registerDataFileEnabled?: boolean;
    symptomInquiriesEnabled?: boolean;
    selfSignupEnabled?: boolean;
    termsOfUseVisible?: boolean;
    clinicLastChanged?: number;
    nurseCanControlPatient?: boolean;
    patientFeedbackEnabled?: boolean;
    analyticsViewForClinicUsers?: boolean;
    analyticsViewForNoonaUsers?: boolean;
    trackedStatuses?: PatientStatusType[];
    statusIntegrationEnabled?: boolean;
    statusIntegration?: StatusIntegration;
    fullAeqReportEnabled?: boolean;
    questionarySettings?: QuestionarySettings[];
    reportIntegrationEnabled?: boolean;
    showingOfLastNameEnabled?: boolean;
    caseOutcomeEnabled?: boolean;
    caseDelayReasonEnabled?: boolean;
    ssnPrimaryIdentifier?: boolean;
    caseManagementEnabled?: boolean;
    clinicianExternalIdEnabled?: boolean;
    medicalRecordsVisible?: boolean;
    educationEnabled?: boolean;
    apiStrategy?: ApiStrategy;
    labResultsEnabled?: boolean;
    labResultNotificationsEnabled?: boolean;
    labResultNotifyOncePerDayEnabled?: boolean;
    providerAnalyticsEnabled?: boolean;
    upcomingAppointmentsEnabled?: boolean;
    smartSymptomFollowUpEnabled?: boolean;
    customBrandingEnabled?: boolean;
    providerAnalyticsVisibleForAllowedClinicUsers?: boolean;
    askNextOfKin?: boolean;
    clinicianIdentityCodeEnabled?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
