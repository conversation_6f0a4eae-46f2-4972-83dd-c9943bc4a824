// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.MessageType

export enum MessageType {
    ADVICE_REQUEST = 0,
    ADVICE_RESPONSE = 1,
    DEPRECATED = 2,
    DEPRECATED2 = 3,
    INTERNAL_MESSAGE = 4,
    GENERAL_PATIENT = 5,
    PRIVATE = 6,
    DIRECT = 7,
    CLINIC_CONNECTION = 8,
}

export const AllMessageType = [
    MessageType.ADVICE_REQUEST,
    MessageType.ADVICE_RESPONSE,
    MessageType.DEPRECATED,
    MessageType.DEPRECATED2,
    MessageType.INTERNAL_MESSAGE,
    MessageType.GENERAL_PATIENT,
    MessageType.PRIVATE,
    MessageType.DIRECT,
    MessageType.CLINIC_CONNECTION,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
