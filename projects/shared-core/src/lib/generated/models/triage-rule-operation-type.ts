// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.triage.enums.TriageRuleOperationType

export enum TriageRuleOperationType {
    NOP = 'NOP',
    AND = 'AND',
    OR = 'OR',
    NOT = 'NOT',
    EQUAL = 'EQUAL',
    LESS_THAN = 'LESS_THAN',
    LESS_OR_EQUAL = 'LESS_THAN_EQUAL',
    MORE_THAN = 'MORE_THAN',
    MORE_OR_EQUAL = 'MORE_THAN_EQUAL',
}

export const AllTriageRuleOperationType = [
    TriageRuleOperationType.NOP,
    TriageRuleOperationType.AND,
    TriageRuleOperationType.OR,
    TriageRuleOperationType.NOT,
    TriageRuleOperationType.EQUAL,
    TriageRuleOperationType.LESS_THAN,
    TriageRuleOperationType.LESS_OR_EQUAL,
    TriageRuleOperationType.MORE_THAN,
    TriageRuleOperationType.MORE_OR_EQUAL,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
