// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.UserStatus

export enum UserStatus {
    ACTIVE = 0,
    WAITING_FOR_INVITATION = 1,
    INVITATION_SENT = 2,
    INVITATION_REMINDER_SENT = 3,
    WAITING_FOR_ACCOUNT = 4,
    ACCOUNT_SENT = 5,
    LOGIN_REMINDER_SENT = 6,
    INVITATION_EXPIRED = 7,
    DECLINED = 8,
    ACCOUNT_LOCKED = 9,
    ACCOUNT_CLOSED = 10,
    SMS_SENDING_UNSUCCESSFUL = 11,
    WAITING_FOR_INFORMATION = 12,
    EHR_SYSTEM_INITIALIZATION = 13,
    PROXY_PATIENT = 14,
    WITHOUT_QUESTIONARY = 15,
}

export const AllUserStatus = [
    UserStatus.ACTIVE,
    UserStatus.WAITING_FOR_INVITATION,
    UserStatus.INVITATION_SENT,
    UserStatus.INVITATION_REMINDER_SENT,
    UserStatus.WAITING_FOR_ACCOUNT,
    UserStatus.ACCOUNT_SENT,
    UserStatus.LOGIN_REMINDER_SENT,
    UserStatus.INVITATION_EXPIRED,
    UserStatus.DECLINED,
    UserStatus.ACCOUNT_LOCKED,
    UserStatus.ACCOUNT_CLOSED,
    UserStatus.SMS_SENDING_UNSUCCESSFUL,
    UserStatus.WAITING_FOR_INFORMATION,
    UserStatus.EHR_SYSTEM_INITIALIZATION,
    UserStatus.PROXY_PATIENT,
    UserStatus.WITHOUT_QUESTIONARY,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
