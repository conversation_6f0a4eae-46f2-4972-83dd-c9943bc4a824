// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.api.dtos.clinicdataexport.ClinicDataExportRequestDto

import { ClinicDataExportRequestStatus } from './clinic-data-export-request-status';
import { Code } from './code';
import { DataExportDataType } from './data-export-data-type';
import { DataExportExportType } from './data-export-export-type';
import { TreatmentModuleType } from './treatment-module-type';
export interface ClinicDataExportRequestDto {
    id?: string;
    status?: ClinicDataExportRequestStatus;
    date?: Date | number | string;
    clinicName?: string;
    requestedBy?: string;
    description?: string;
    approvedBy?: string;
    approvedAt?: Date | number | string;
    treatmentModuleTypes?: TreatmentModuleType[];
    careTeams?: string[];
    icd10Codes?: Code[];
    dataTypes?: DataExportDataType[];
    exportType?: DataExportExportType;
    isSsnIncluded?: boolean;
    isMrnIncluded?: boolean;
    isPatientNameIncluded?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
