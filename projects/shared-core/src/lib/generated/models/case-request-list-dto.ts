// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseRequestListDto

import { AcuteSymptoms } from './acute-symptoms';
import { CaseDelayReason } from './case-delay-reason';
import { CaseNote } from './case-note';
import { CaseOrigin } from './case-origin';
import { CaseOutcome } from './case-outcome';
import { CasePriority } from './case-priority';
import { CaseStatus } from './case-status';
import { CaseType } from './case-type';
import { Message } from './message';
import { Patient } from './patient';
export interface CaseRequestListDto {
    id?: string;
    title?: string;
    caseDate?: Date | number | string;
    status?: CaseStatus;
    assignee?: string;
    assigneeName?: string;
    itemId?: string;
    createdDate?: Date | number | string;
    priority?: CasePriority;
    careTeams?: string[];
    caseOrigin?: CaseOrigin;
    description?: string;
    acuteSymptoms?: AcuteSymptoms[];
    caseOutcomes?: CaseOutcome[];
    caseDelayReason?: CaseDelayReason;
    hipaaVerified?: boolean;
    notes?: string;
    patientVoiceUnderstanding?: boolean;
    caseType?: CaseType;
    messages?: string[];
    consultingTeams?: string[];
    consultingUser?: string;
    escalated?: boolean;
    consultedTeam?: string;
    processed?: boolean;
    consultedUser?: string;
    caseNotesObjects?: CaseNote[];
    messageObjects?: Message[];
    removed?: boolean;
    patient?: Patient;
    modifiedDate?: Date | number | string;
    submitTime?: Date | number | string;
    caseLogsVisible?: boolean;
    closedDate?: Date | number | string;
    patientCreated?: boolean;
    reminderDate?: Date | number | string;
    publicDate?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
