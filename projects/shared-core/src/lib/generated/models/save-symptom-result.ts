// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.SaveSymptomResult

import { AutoSubmitStatus } from './auto-submit-status';
import { Symptom } from './symptom';
import { SymptomPrioritisationResult } from './symptom-prioritisation-result';
export interface SaveSymptomResult {
    autoSubmitStatus?: AutoSubmitStatus;
    symptom?: Symptom;
    symptomPrioritisationResult?: SymptomPrioritisationResult;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
