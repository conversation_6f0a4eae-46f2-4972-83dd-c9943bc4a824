// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.SupportMessageType

export enum SupportMessageType {
    SUPPORT_REQUEST = 0,
    SUPPORT_RESPONSE = 1,
    SUPPORT_CHAT = 2,
}

export const AllSupportMessageType = [
    SupportMessageType.SUPPORT_REQUEST,
    SupportMessageType.SUPPORT_RESPONSE,
    SupportMessageType.SUPPORT_CHAT,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
