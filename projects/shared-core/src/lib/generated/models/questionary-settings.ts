// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.QuestionarySettings

import { QuestionaryType } from './questionary-type';
export interface QuestionarySettings {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    questionaryType?: QuestionaryType;
    enabled?: boolean;
    scoreVisible?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
