// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.library.dto.EducationalBinderDto

export interface EducationalBinderDto {
    id?: string;
    userId?: string;
    itemIds?: string[];
    revision?: number;
    creatorName?: string;
    createdAt?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
