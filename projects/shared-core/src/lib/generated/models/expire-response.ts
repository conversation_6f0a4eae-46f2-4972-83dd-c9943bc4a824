// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.questionnaire.ExpireResponse

import { InquiryStatus } from './inquiry-status';
import { User } from './user';
export interface ExpireResponse {
    expired?: boolean;
    expiredByDate?: Date | number | string;
    expiredByNurse?: User;
    cannotExpireDueToAnsweredStatus?: boolean;
    currentStatus?: InquiryStatus;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
