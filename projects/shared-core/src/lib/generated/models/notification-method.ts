// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.NotificationMethod

export enum NotificationMethod {
    EMAIL = 'email',
    SMS = 'sms',
    EMAIL_AND_SMS = 'emailAndSms',
    NONE = 'none',
    NATIVE_NOTIFICATION = 'nativeNotification',
}

export const AllNotificationMethod = [
    NotificationMethod.EMAIL,
    NotificationMethod.SMS,
    NotificationMethod.EMAIL_AND_SMS,
    NotificationMethod.NONE,
    NotificationMethod.NATIVE_NOTIFICATION,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
