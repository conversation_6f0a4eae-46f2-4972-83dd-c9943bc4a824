import { FieldGrading } from './field-grading';
import { Message } from './message';
import { SymptomComparisonLevel } from './symptom-comparison-level';
export interface Symptom {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    symptomDate?: Date | number | string;
    type?: any;
    dtype?: string;
    description?: string;
    submitted?: boolean;
    closed?: boolean;
    message?: Message;
    followUpSubmitted?: boolean;
    followUpLinked?: boolean;
    priority?: number;
    adverseEffectStatus?: SymptomComparisonLevel;
    dateRange?: Date[] | number[] | string[];
    peakDate?: Date | number | string;
    nextOfKinAnswer?: boolean;
    getHasSymptomReport?: boolean;
    getMaximumScaling?: FieldGrading;
    anySymptom?: boolean;
    fellingIntensity?: number;
    topics?: string[];
    contactMethods?: string;
    contactTime?: string;
    otherTopics?: string;
    classType?: string;
    anyContact?: boolean;
    medicationQuestion?: string;
    appointmentQuestion?: string;
    insureOrFinancQuestion?: string;
}
