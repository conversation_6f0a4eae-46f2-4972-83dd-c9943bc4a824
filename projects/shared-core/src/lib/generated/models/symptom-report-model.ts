// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.SymptomReportModel

import { Symptom } from './symptom';
import { SymptomComparisonLevel } from './symptom-comparison-level';
import { SymptomGradeInformation } from './symptom-grade-information';
import { SymptomInquiry } from './symptom-inquiry';
import { SymptomReport } from './symptom-report';
import { SymptomType } from './symptom-type';
export interface SymptomReportModel {
    symptomReport?: SymptomReport;
    symptomComparisons?: { [key in SymptomType]: SymptomComparisonLevel };
    dateOfPreviousAEQ?: Date | number | string;
    children?: SymptomReport[];
    symptomGradeInformation?: { [key in SymptomType]: SymptomGradeInformation };
    symptomsOutsideTheReport?: Symptom[];
    aeqStartDate?: Date | number | string;
    symptomaticDays?: number;
    symptomInquiry?: SymptomInquiry;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
