// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.labsview.dto.LabVisitDto

import { ReportDto } from './report-dto';
export interface LabVisitDto {
    latestUpdated?: Date | number | string;
    visitDate?: string;
    description?: string;
    reports?: ReportDto[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
