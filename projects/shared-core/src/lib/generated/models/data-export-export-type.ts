// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.DataExportExportType

export enum DataExportExportType {
    CUSTOM = 'custom',
    ALLACTIVEPATIENTS = 'allactivepatients',
}

export const AllDataExportExportType = [DataExportExportType.CUSTOM, DataExportExportType.ALLACTIVEPATIENTS];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
