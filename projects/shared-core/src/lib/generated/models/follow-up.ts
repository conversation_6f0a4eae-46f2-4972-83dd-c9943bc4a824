// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.FollowUp

import { Symptom } from './symptom';
export interface FollowUp {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    symptom?: Symptom;
    symptoms?: Symptom[];
    beginDate?: Date | number | string;
    endDate?: Date | number | string;
    closed?: Date | number | string;
    submitted?: boolean;
    priority?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
