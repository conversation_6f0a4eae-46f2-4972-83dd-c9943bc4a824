// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseLogEntry

import { CaseLogType } from './case-log-type';
import { Patient } from './patient';
import { User } from './user';
export interface CaseLogEntry {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    patient?: Patient;
    caseId?: string;
    message?: string;
    caseVersionId?: string;
    actor?: User;
    caseLogType?: CaseLogType;
    timestamp?: Date | number | string;
    revision?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
