// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.CareTeamRelationshipType

export enum CareTeamRelationshipType {
    BELONGS_TO = 'belongsTo',
    NOT_BELONGING_TO = 'notBelongingTo',
}

export const AllCareTeamRelationshipType = [
    CareTeamRelationshipType.BELONGS_TO,
    CareTeamRelationshipType.NOT_BELONGING_TO,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
