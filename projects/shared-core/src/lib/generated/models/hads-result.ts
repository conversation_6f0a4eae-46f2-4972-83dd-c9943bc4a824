// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.medical.questionnaire.HadsResult

export enum HadsResult {
    CASE_A_1 = 'caseA1',
    CASE_A_2 = 'caseA2',
    CASE_B_1 = 'caseB1',
    CASE_B_2 = 'caseB2',
    CASE_B_3 = 'caseB3',
    NONE = 'none',
}

export const AllHadsResult = [
    HadsResult.CASE_A_1,
    HadsResult.CASE_A_2,
    HadsResult.CASE_B_1,
    HadsResult.CASE_B_2,
    HadsResult.CASE_B_3,
    HadsResult.NONE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
