// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.analytics.model.reporting.TotalPatientWeekView

export interface TotalPatientWeekView {
    firstDay?: Date | number | string;
    lastDay?: Date | number | string;
    activeCount?: number;
    invitedCount?: number;
    closedCount?: number;
    clinicHandlesCount?: number;
    totalCount?: number;
    changedActiveCount?: number;
    changedInvitedCount?: number;
    changedClosedCount?: number;
    changedClinicHandlesCount?: number;
    changedTotalCount?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
