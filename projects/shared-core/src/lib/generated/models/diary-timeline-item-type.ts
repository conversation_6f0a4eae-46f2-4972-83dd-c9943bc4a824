// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.diarytimeline.timelineintem.DiaryTimelineItemType

export enum DiaryTimelineItemType {
    SYMPTOM = 'symptom',
    DIARYENTRY = 'diaryEntry',
    EVENT = 'event',
    QUESTIONARY_INQUIRY = 'questionaryInquiry',
    QUESTIONARY_ANSWER = 'questionaryAnswer',
    SYMPTOM_INQUIRY_INQUIRY = 'symptomInquiryInquiry',
    SYMPTOM_INQUIRY_ANSWER = 'symptomInquiryAnswer',
    WELLBEEING_SUMMARY = 'wellBeeingSummary',
    INTEGRATION_APPOINTMENT = 'integrationAppointment',
}

export const AllDiaryTimelineItemType = [
    DiaryTimelineItemType.SYMPTOM,
    DiaryTimelineItemType.DIARYENTRY,
    DiaryTimelineItemType.EVENT,
    DiaryTimelineItemType.QUESTIONARY_INQUIRY,
    DiaryTimelineItemType.QUESTIONARY_ANSWER,
    DiaryTimelineItemType.SYMPTOM_INQUIRY_INQUIRY,
    DiaryTimelineItemType.SYMPTOM_INQUIRY_ANSWER,
    DiaryTimelineItemType.WELLBEEING_SUMMARY,
    DiaryTimelineItemType.INTEGRATION_APPOINTMENT,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
