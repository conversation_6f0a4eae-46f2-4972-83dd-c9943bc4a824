// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.triage.model.TriageTreatmentModule

import { ListEntry } from './list-entry';
import { TreatmentModule } from './treatment-module';
export interface TriageTreatmentModule {
    treatmentModule?: TreatmentModule;
    symptomGradings?: { [key in string]: { [key in string]: { [key in string]: ListEntry[] } } };
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
