// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.ScheduledMessage

import { InquiryStatus } from './inquiry-status';
import { Language } from './language';
import { PatientTreatmentModule } from './patient-treatment-module';
import { TopicType } from './topic-type';
export interface ScheduledMessage {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    sendingDate?: Date | number | string;
    patientTreatmentModule?: PatientTreatmentModule;
    type?: TopicType;
    status?: InquiryStatus;
    language?: Language;
    title?: string;
    body?: string;
    compositeStatus?: InquiryStatus;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
