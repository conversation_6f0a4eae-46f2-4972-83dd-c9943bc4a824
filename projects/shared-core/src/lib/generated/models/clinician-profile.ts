// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.user.ClinicianProfile

import { CaseType } from './case-type';
import { User } from './user';
export interface ClinicianProfile {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    user?: User;
    selectedQuestionnaires?: string[];
    selectedCaseTypes?: CaseType[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
