// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.treatment.CancerType

export enum CancerType {
    UNKNOWN = 'unknown',
    BREAST = 'breast',
    PROSTATE = 'prostate',
    KIDNEY = 'kidney',
    BLADDER = 'bladder',
    ESOPHAGUS = 'esophagus',
    STOMACH = 'stomach',
    BOWEL = 'bowel',
    PANCREAS = 'pancreas',
    GALL_BLADDER = 'gall_bladder',
    LUNG = 'lung',
    HEAD_OR_NECK = 'head_or_neck',
    BRAIN = 'brain',
    LYMPHOMA = 'lymphoma',
    TESTICLE = 'testicle',
    BONE_SARCOMA = 'bone_sarcoma',
    SOFT_TISSUE_SARCOMA = 'soft_tissue_sarcoma',
    THYROID = 'thyroid',
    NET = 'net',
    GIST = 'gist',
    MELANOMA = 'melanoma',
    GYNECOLOGICAL = 'gynecological',
    MYELOMA = 'myeloma',
    ACUTE_LEUKEMIA = 'acute_leukemia',
    CHRONIC_LEUKEMIA = 'chronic_leukemia',
}

export const AllCancerType = [
    CancerType.UNKNOWN,
    CancerType.BREAST,
    CancerType.PROSTATE,
    CancerType.KIDNEY,
    CancerType.BLADDER,
    CancerType.ESOPHAGUS,
    CancerType.STOMACH,
    CancerType.BOWEL,
    CancerType.PANCREAS,
    CancerType.GALL_BLADDER,
    CancerType.LUNG,
    CancerType.HEAD_OR_NECK,
    CancerType.BRAIN,
    CancerType.LYMPHOMA,
    CancerType.TESTICLE,
    CancerType.BONE_SARCOMA,
    CancerType.SOFT_TISSUE_SARCOMA,
    CancerType.THYROID,
    CancerType.NET,
    CancerType.GIST,
    CancerType.MELANOMA,
    CancerType.GYNECOLOGICAL,
    CancerType.MYELOMA,
    CancerType.ACUTE_LEUKEMIA,
    CancerType.CHRONIC_LEUKEMIA,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
