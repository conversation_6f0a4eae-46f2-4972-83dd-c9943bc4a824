// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.api.dtos.provideranalytics.PagedProviderAnalyticsRequest

import { ProviderAnalyticsFilters } from './provider-analytics-filters';
export interface PagedProviderAnalyticsRequest {
    page?: number;
    limit?: number;
    filters?: ProviderAnalyticsFilters;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
