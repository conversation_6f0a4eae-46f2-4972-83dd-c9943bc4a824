// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.templates.questionnaires.SymptomSchedulingEntry

import { InquiryType } from './inquiry-type';
import { SchedulingIntervalType } from './scheduling-interval-type';
import { SchedulingTriggerType } from './scheduling-trigger-type';
export interface SymptomSchedulingEntry {
    orderIndex?: number;
    interval?: number;
    intervalUnit?: SchedulingIntervalType;
    triggerType?: SchedulingTriggerType;
    repeating?: boolean;
    repeatCount?: number;
    repeatType?: SchedulingIntervalType;
    inquiryType?: InquiryType;
    subInquiriesEnabled?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
