// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.Notification

import { CareTeam } from './care-team';
export interface Notification {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    title?: string;
    content?: string;
    expiration?: Date | number | string;
    sent?: boolean;
    careTeams?: CareTeam[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
