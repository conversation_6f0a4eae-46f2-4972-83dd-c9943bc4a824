// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.clinicconnection.models.enums.ClinicConnectionRequestStatus

export enum ClinicConnectionRequestStatus {
    WAIT_FOR_CONFIRM = 'waitforconfirm',
    IN_PROGRESS = 'inprogress',
    FINISHED = 'finished',
    REJECTED = 'rejected',
    FAILED = 'failed',
    CANCELED = 'canceled',
}

export const AllClinicConnectionRequestStatus = [
    ClinicConnectionRequestStatus.WAIT_FOR_CONFIRM,
    ClinicConnectionRequestStatus.IN_PROGRESS,
    ClinicConnectionRequestStatus.FINISHED,
    ClinicConnectionRequestStatus.REJECTED,
    ClinicConnectionRequestStatus.FAILED,
    ClinicConnectionRequestStatus.CANCELED,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
