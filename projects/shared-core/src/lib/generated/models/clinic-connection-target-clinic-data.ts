// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.clinicconnection.models.entities.ClinicConnectionTargetClinicData

import { Gender } from './gender';
import { IdentityCodeAlgorithm } from './identity-code-algorithm';
import { TreatmentModuleSubscription } from './treatment-module-subscription';
import { User } from './user';
export interface ClinicConnectionTargetClinicData {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    creator?: User;
    identityCode?: string;
    clinicIdentityCodeAlgorithm?: IdentityCodeAlgorithm;
    gender?: Gender;
    patientNumber?: string;
    localeId?: string;
    icd10Code?: string;
    moduleId?: string;
    subscriptions?: TreatmentModuleSubscription[];
    questionnaireTemplateId?: string;
    treatmentStart?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
