import { SchedulingIntervalType } from './scheduling-interval-type';
import { SchedulingTriggerType } from './scheduling-trigger-type';
export interface QuestionnaireSchedulingEntry {
    orderIndex?: number;
    interval?: number;
    intervalUnit?: SchedulingIntervalType;
    triggerType?: SchedulingTriggerType;
    repeating?: boolean;
    repeatCount?: number;
    repeatType?: SchedulingIntervalType;
    questionnaireType?: any;
}
