// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.Status

export enum Status {
    PENDING = 'pending',
    READY = 'ready',
    PROCESSING = 'processing',
    FAILED = 'failed',
}

export const AllStatus = [Status.PENDING, Status.READY, Status.PROCESSING, Status.FAILED];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
