// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.SmartSymptomInquiryProgress

export enum SmartSymptomInquiryProgress {
    PENDING = 'pending',
    SELECTION = 'selection',
    SELECTIONDONE = 'selectionDone',
    INLOOP = 'inLoop',
    LOOPDONE = 'loopDone',
    FINISHED = 'finished',
    SKIPPED = 'skipped',
    STARTED = 'started',
}

export const AllSmartSymptomInquiryProgress = [
    SmartSymptomInquiryProgress.PENDING,
    SmartSymptomInquiryProgress.SELECTION,
    SmartSymptomInquiryProgress.SELECTIONDONE,
    SmartSymptomInquiryProgress.INLOOP,
    SmartSymptomInquiryProgress.LOOPDONE,
    SmartSymptomInquiryProgress.FINISHED,
    SmartSymptomInquiryProgress.SKIPPED,
    SmartSymptomInquiryProgress.STARTED,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
