// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.SymptomGroup

import { Symptom } from './symptom';
import { SymptomGradeInformation } from './symptom-grade-information';
import { SymptomType } from './symptom-type';
export interface SymptomGroup {
    symptomType?: SymptomType;
    symptoms?: Symptom[];
    grading?: SymptomGradeInformation;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
