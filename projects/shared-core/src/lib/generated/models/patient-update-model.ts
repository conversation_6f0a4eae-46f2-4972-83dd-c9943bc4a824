// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.PatientUpdateModel

import { CareProvider } from './care-provider';
import { ClinicSite } from './clinic-site';
import { EmailProblemType } from './email-problem-type';
import { Gender } from './gender';
import { PatientStatus } from './patient-status';
import { PhoneNumberType } from './phone-number-type';
import { UserStatus } from './user-status';
export interface PatientUpdateModel {
    patientId?: string;
    userId?: string;
    identityCode?: string;
    firstName?: string;
    lastName?: string;
    emailAddress?: string;
    phoneNumber?: string;
    phoneNumberType1?: PhoneNumberType;
    phoneNumber2?: string;
    phoneNumberType2?: PhoneNumberType;
    phoneNumber3?: string;
    phoneNumberType3?: PhoneNumberType;
    hipaaCode?: string;
    medicalRecordNumber?: string;
    address?: string;
    city?: string;
    zipCode?: string;
    state?: string;
    clinicSite?: ClinicSite;
    primaryProvider?: CareProvider;
    localeId?: string;
    status?: UserStatus;
    statusChangedDate?: Date | number | string;
    accountLockedBy?: string;
    gender?: Gender;
    birthDate?: Date | number | string;
    emailProblemDetails?: string;
    emailProblemType?: EmailProblemType;
    emailProblemTime?: Date | number | string;
    statuses?: PatientStatus[];
    nurseControlled?: boolean;
    initialCareTeamId?: string;
    declinedNoonaInvite?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
