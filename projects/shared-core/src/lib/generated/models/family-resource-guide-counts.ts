// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.patient.status.analytics.FamilyResourceGuideCounts

export interface FamilyResourceGuideCounts {
    sentByClinicCount?: number;
    sentByCareNavigatorCount?: number;
    declinedCount?: number;
    changedSentByClinic?: number;
    changedSentByCareTeam?: number;
    changedDeclinedCount?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
