// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.security.model.LoginToken

import { User } from './user';
export interface LoginToken {
    loginTokenId?: string;
    token?: string;
    keyWalletKey?: string;
    user?: User;
    rememberMe?: boolean;
    limited?: boolean;
    created?: Date | number | string;
    refreshed?: Date | number | string;
    usedByPatient?: boolean;
    authenticatedMobile?: boolean;
    apiLoginToken?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
