// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.dto.CcdPatientDto

import { CcdOrganizationDto } from './ccd-organization-dto';
export interface CcdPatientDto {
    ids?: string[];
    completeName?: string;
    address?: string;
    contacts?: string[];
    birthDate?: string;
    gender?: string;
    maritalStatus?: string;
    ethnicity?: string;
    race?: string;
    language?: string;
    providerOrg?: CcdOrganizationDto;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
