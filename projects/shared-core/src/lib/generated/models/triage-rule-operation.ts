// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.triage.model.TriageRuleOperation

import { TriageRule } from './triage-rule';
import { TriageRuleOperationType } from './triage-rule-operation-type';
import { TriageSymptom } from './triage-symptom';
export interface TriageRuleOperation {
    id?: string;
    triageRuleOperation?: TriageRuleOperation;
    triageSymptoms?: TriageSymptom[];
    triageRuleOperations?: TriageRuleOperation[];
    operation?: TriageRuleOperationType;
    triageRule?: TriageRule;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
