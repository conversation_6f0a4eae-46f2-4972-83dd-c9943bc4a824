// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.processing.model.CsvError

import { CsvErrorParameter } from './csv-error-parameter';
export interface CsvError {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    line?: number;
    col?: number;
    errorMessage?: string;
    parameters?: CsvErrorParameter[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
