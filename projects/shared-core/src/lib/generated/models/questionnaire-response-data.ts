// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.api.dtos.provideranalytics.QuestionnaireResponseData

export interface QuestionnaireResponseData {
    year?: number;
    month?: number;
    category?: string;
    type?: string;
    answered?: boolean;
    count?: number;
    careTeam?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
