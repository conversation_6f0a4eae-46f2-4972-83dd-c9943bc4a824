// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.patient.PatientContactInformation

import { PhoneNumberType } from './phone-number-type';
export interface PatientContactInformation {
    id?: string;
    firstName?: string;
    lastName?: string;
    assignee?: string;
    assignTime?: Date | number | string;
    phoneNumber?: string;
    phoneNumberType1?: PhoneNumberType;
    identityCode?: string;
    nurseControlled?: boolean;
    hipaaCode?: string;
    medicalRecordNumber?: string;
    birthDate?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
