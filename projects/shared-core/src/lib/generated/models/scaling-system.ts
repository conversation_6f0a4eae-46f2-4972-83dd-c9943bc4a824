// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.scaling.ScalingSystem

export enum ScalingSystem {
    VAS = 'vas',
    ESAS = 'esas',
    CTCAE = 'ctcae',
    ECOG = 'ecog',
}

export const AllScalingSystem = [ScalingSystem.VAS, ScalingSystem.ESAS, ScalingSystem.CTCAE, ScalingSystem.ECOG];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
