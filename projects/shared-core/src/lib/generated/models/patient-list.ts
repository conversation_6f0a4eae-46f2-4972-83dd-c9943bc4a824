// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.PatientList

import { PatientUpdateModel } from './patient-update-model';
export interface PatientList {
    pages?: number;
    itemsPerPage?: number;
    patientInformationList?: PatientUpdateModel[];
    patientCount?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
