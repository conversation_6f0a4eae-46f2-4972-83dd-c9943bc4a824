// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.PatientValue

import { PatientValueType } from './patient-value-type';
export interface PatientValue {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    inputDate?: Date | number | string;
    type?: PatientValueType;
    unitKey?: string;
    value?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
