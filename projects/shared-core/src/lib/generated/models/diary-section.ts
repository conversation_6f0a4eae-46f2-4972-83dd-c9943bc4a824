// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.DiarySection

import { FollowUp } from './follow-up';
import { Questionary } from './questionary';
import { QuestionaryType } from './questionary-type';
import { QuestionnaireGroup } from './questionnaire-group';
import { Symptom } from './symptom';
import { SymptomGroup } from './symptom-group';
import { SymptomReportModel } from './symptom-report-model';
import { SymptomType } from './symptom-type';
export interface DiarySection {
    followUps?: FollowUp[];
    symptoms?: Symptom[];
    questionaries?: Questionary[];
    symptomReports?: SymptomReportModel[];
    scheduledAeqSendingDates?: Date[] | number[] | string[];
    scheduledQuestionnaireSendingDates?: Date[] | number[] | string[];
    symptomGroups?: { [key in SymptomType]: SymptomGroup };
    questionnaireGroups?: { [key in QuestionaryType]: QuestionnaireGroup };
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
