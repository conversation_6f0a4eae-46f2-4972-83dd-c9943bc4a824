// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.diarytimeline.event.TimelineEvent

import { PhotoGroup } from './photo-group';
import { TimelineEventType } from './timeline-event-type';
import { TreatmentPhase } from './treatment-phase';
export interface TimelineEvent {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    eventStart?: Date | number | string;
    eventEnd?: Date | number | string;
    title?: string;
    location?: string;
    text?: string;
    type?: TimelineEventType;
    sendReminder?: boolean;
    treatmentPhase?: TreatmentPhase;
    photo?: PhotoGroup;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
