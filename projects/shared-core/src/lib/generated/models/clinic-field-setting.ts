// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.ClinicFieldSetting

import { SettingFieldType } from './setting-field-type';
export interface ClinicFieldSetting {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    fieldType?: SettingFieldType;
    mandatory?: boolean;
    enabled?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
