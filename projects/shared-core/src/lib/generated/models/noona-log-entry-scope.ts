// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.audit.model.NoonaLogEntryScope

export enum NoonaLogEntryScope {
    GENERAL = 'general',
    PHI = 'phi',
    NOONA = 'noona',
}

export const AllNoonaLogEntryScope = [NoonaLogEntryScope.GENERAL, NoonaLogEntryScope.PHI, NoonaLogEntryScope.NOONA];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
