// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.medical.Medication

import { Code } from './code';
import { MedicationUnit } from './medication-unit';
export interface Medication {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    activeSubstanceCode?: Code;
    unit?: MedicationUnit;
    amount?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
