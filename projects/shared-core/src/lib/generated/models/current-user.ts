// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.CurrentUser

import { CareTeam } from './care-team';
import { Gender } from './gender';
import { Language } from './language';
import { NotificationMethod } from './notification-method';
import { PatientProfile } from './patient-profile';
import { SmartSymptomFrequency } from './smart-symptom-frequency';
export interface CurrentUser {
    id?: string;
    firstName?: string;
    lastName?: string;
    userRoles?: string[];
    email?: string;
    phoneNumber?: string;
    locked?: boolean;
    visitCount?: number;
    passwordExpirationDate?: Date | number | string;
    enabledLanguages?: Language[];
    notificationMethod?: NotificationMethod;
    consultationNotificationsEnabled?: boolean;
    careTeamNotificationsEnabled?: boolean;
    unsubscribedCareTeams?: CareTeam[];
    smartSymptomFrequency?: SmartSymptomFrequency;
    twoFactorAuthentication?: boolean;
    patientProfile?: PatientProfile;
    gender?: Gender;
    birthDate?: Date | number | string;
    identityCode?: string;
    externalId?: string;
    gaId?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
