// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.PhotoMetadata

import { Patient } from './patient';
import { PhotoGroup } from './photo-group';
import { PhotoScale } from './photo-scale';
import { Status } from './status';
export interface PhotoMetadata {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    patient?: Patient;
    uploaded?: Date | number | string;
    originalPhotoId?: string;
    scale?: PhotoScale;
    photoGroup?: PhotoGroup;
    width?: number;
    height?: number;
    status?: Status;
    contentSize?: number;
    removed?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
