// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.api.dtos.clinicdataexport.ClinicDataExportRequestStatus

export enum ClinicDataExportRequestStatus {
    ACCEPTED = 'accepted',
    PENDING = 'pending',
}

export const AllClinicDataExportRequestStatus = [
    ClinicDataExportRequestStatus.ACCEPTED,
    ClinicDataExportRequestStatus.PENDING,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
