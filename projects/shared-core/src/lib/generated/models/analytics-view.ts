// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.analytics.model.reporting.AnalyticsView

import { CareTeamWeekView } from './care-team-week-view';
import { CountWeekView } from './count-week-view';
import { MessageWeekView } from './message-week-view';
import { PortalUsageCounts } from './portal-usage-counts';
import { QuestionnaireWeekView } from './questionnaire-week-view';
import { StanfordPatientStatusWeekView } from './stanford-patient-status-week-view';
import { SymptomReportWeekView } from './symptom-report-week-view';
import { TotalPatientWeekView } from './total-patient-week-view';
export interface AnalyticsView {
    careTeamWeeks?: { [key in string]: CareTeamWeekView[] };
    clinicMessageWeeks?: { [key in string]: MessageWeekView[] };
    patientMessageWeeks?: { [key in string]: MessageWeekView[] };
    patientDiaryWeeks?: { [key in string]: CountWeekView[] };
    questionnaireWeeks?: { [key in string]: QuestionnaireWeekView[] };
    symptomReportWeeks?: SymptomReportWeekView[];
    stanfordPatientStatusWeeks?: { [key in string]: StanfordPatientStatusWeekView[] };
    clinicPatientCounts?: TotalPatientWeekView[];
    clinicPortalUsageCount?: PortalUsageCounts;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
