// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.templates.questionnaires.MessageSchedulingEntry

import { LocalizedMessageContent } from './localized-message-content';
import { SchedulingIntervalType } from './scheduling-interval-type';
import { SchedulingTriggerType } from './scheduling-trigger-type';
import { TopicType } from './topic-type';
export interface MessageSchedulingEntry {
    orderIndex?: number;
    interval?: number;
    intervalUnit?: SchedulingIntervalType;
    triggerType?: SchedulingTriggerType;
    repeating?: boolean;
    repeatCount?: number;
    repeatType?: SchedulingIntervalType;
    topicType?: TopicType;
    messages?: LocalizedMessageContent[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
