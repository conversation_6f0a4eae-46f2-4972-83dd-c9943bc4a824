// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.security.model.UserClinic

import { AuthenticationType } from './authentication-type';
import { ClinicLanguage } from './clinic-language';
export interface UserClinic {
    clinicId?: string;
    name?: string;
    authenticationType?: AuthenticationType;
    rememberLoginEnabled?: boolean;
    rememberPasswordEnabled?: boolean;
    loginTokenLifetime?: number;
    clinicLanguage?: ClinicLanguage;
    customerId?: string;
    enabledForUser?: boolean;
    enabled?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
