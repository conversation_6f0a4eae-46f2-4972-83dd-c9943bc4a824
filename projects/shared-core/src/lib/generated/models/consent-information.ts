// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.ConsentInformation

export interface ConsentInformation {
    consentId?: string;
    content?: string;
    isMandatory?: boolean;
    signatureRequired?: boolean;
    neededSignature?: string;
    mandatory?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
