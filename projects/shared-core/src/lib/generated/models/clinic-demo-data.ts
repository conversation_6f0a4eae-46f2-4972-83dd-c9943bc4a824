// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.ClinicDemoData

export enum ClinicDemoData {
    DEFAULT = 'default',
    PERFORMANCE_TEST = 'performance-test',
}

export const AllClinicDemoData = [ClinicDemoData.DEFAULT, ClinicDemoData.PERFORMANCE_TEST];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
