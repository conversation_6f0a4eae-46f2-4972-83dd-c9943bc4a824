// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.content.model.CustomerCustomization

import { CustomerNameCustomization } from './customer-name-customization';
export interface CustomerCustomization {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    customerNameCustomizations?: CustomerNameCustomization[];
    customBrandingColor?: string;
    customBrandingLogo?: string;
    customBrandingName?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
