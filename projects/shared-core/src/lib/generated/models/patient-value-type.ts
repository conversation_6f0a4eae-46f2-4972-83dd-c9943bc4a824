// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.PatientValueType

export enum PatientValueType {
    WEIGHT = 'weight',
    DISTRESS = 'distress',
    NO_SYMPTOMS = 'noSymptoms',
}

export const AllPatientValueType = [PatientValueType.WEIGHT, PatientValueType.DISTRESS, PatientValueType.NO_SYMPTOMS];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
