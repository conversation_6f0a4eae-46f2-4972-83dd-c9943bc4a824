// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.units.MeasurementSystem

import { HeightUnit } from './height-unit';
import { WeightUnit } from './weight-unit';
export enum MeasurementSystem {
    METRIC = 'metric',
    IMPERIAL = 'imperial',
}

export const AllMeasurementSystem = [MeasurementSystem.METRIC, MeasurementSystem.IMPERIAL];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
