// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.PatientProfile

import { CancerType } from './cancer-type';
import { TreatmentPhase } from './treatment-phase';
export interface PatientProfile {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    treatmentPhase?: TreatmentPhase;
    cancerType?: CancerType;
    selectedClinic?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
