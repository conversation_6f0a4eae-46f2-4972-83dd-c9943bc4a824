// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.content.model.AcceptedDataExportRequest

import { CareTeam } from './care-team';
import { Code } from './code';
import { Customer } from './customer';
import { DataExportDataType } from './data-export-data-type';
import { DataExportExportType } from './data-export-export-type';
import { TreatmentModuleType } from './treatment-module-type';
import { User } from './user';
export interface AcceptedDataExportRequest {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    customer?: Customer;
    requesterUserId?: string;
    exportType?: DataExportExportType;
    includedPatientIds?: string[];
    includedDataTypes?: DataExportDataType[];
    ssnIncluded?: boolean;
    mrnIncluded?: boolean;
    patientNameIncluded?: boolean;
    requestDate?: Date | number | string;
    treatmentModuleTypes?: TreatmentModuleType[];
    careTeams?: CareTeam[];
    codes?: Code[];
    description?: string;
    approver?: User;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
