// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.ccd.CcdLogType

export enum CcdLogType {
    VIEW = 'view',
    DOWNLOAD = 'download',
    SEND = 'send',
    SEND_UNENCRYPTED = 'sendUnencrypted',
}

export const AllCcdLogType = [CcdLogType.VIEW, CcdLogType.DOWNLOAD, CcdLogType.SEND, CcdLogType.SEND_UNENCRYPTED];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
