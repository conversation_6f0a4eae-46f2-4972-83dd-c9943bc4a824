// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.WellnessDaily

export interface WellnessDaily {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    calorieConsumptionActive?: number;
    calorieConsumptionBMR?: number;
    calorieConsumptionTotal?: number;
    steps?: number;
    bloodPressure?: number;
    height?: number;
    weightFatFree?: number;
    weightFat?: number;
    weightTotal?: number;
    sleepTime?: number;
    sleepEfficiency?: number;
    time?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
