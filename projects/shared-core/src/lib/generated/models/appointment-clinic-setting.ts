// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.integration.AppointmentClinicSetting

export interface AppointmentClinicSetting {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    sendable?: boolean;
    appointmentType?: string;
    scheduledType?: string;
    sentDays?: number;
    visibleToOnlyActivePatients?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
