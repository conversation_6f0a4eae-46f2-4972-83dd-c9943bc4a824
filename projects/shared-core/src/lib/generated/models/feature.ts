// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.Feature

export enum Feature {
    WELLNESS = 'wellness',
    AUTOMATIC_SELF_CARE_INSTRUCTION_ADVICE = 'automatic-self-care-instruction-advice',
}

export const AllFeature = [Feature.WELLNESS, Feature.AUTOMATIC_SELF_CARE_INSTRUCTION_ADVICE];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
