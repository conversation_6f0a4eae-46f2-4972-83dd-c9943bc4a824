// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.SymptomReportPair

import { SaveSymptomResult } from './save-symptom-result';
import { SymptomReport } from './symptom-report';
export interface SymptomReportPair {
    symptomReport?: SymptomReport;
    symptomResultList?: SaveSymptomResult[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
