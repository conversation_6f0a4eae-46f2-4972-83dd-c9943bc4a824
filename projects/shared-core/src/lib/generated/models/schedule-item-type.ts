// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.questionnaire.ScheduleItemType

export enum ScheduleItemType {
    SYMPTOM_INQUIRY = 'symptomInquiry',
    QUESTIONNAIRE_INQUIRY = 'questionnaireInquiry',
    SCHEDULED_MESSAGE = 'scheduledMessage',
}

export const AllScheduleItemType = [
    ScheduleItemType.SYMPTOM_INQUIRY,
    ScheduleItemType.QUESTIONNAIRE_INQUIRY,
    ScheduleItemType.SCHEDULED_MESSAGE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
