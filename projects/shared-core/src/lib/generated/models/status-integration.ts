// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.integration.StatusIntegration

export interface StatusIntegration {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    endpointUrl?: string;
    clientId?: string;
    clientSecret?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
