// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.SchedulingIntervalType

export enum SchedulingIntervalType {
    DAYS = 'days',
    WEEKS = 'weeks',
    MONTHS = 'months',
    YEARS = 'years',
    CYCLES = 'cycles',
}

export const AllSchedulingIntervalType = [
    SchedulingIntervalType.DAYS,
    SchedulingIntervalType.WEEKS,
    SchedulingIntervalType.MONTHS,
    SchedulingIntervalType.YEARS,
    SchedulingIntervalType.CYCLES,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
