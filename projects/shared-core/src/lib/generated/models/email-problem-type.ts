// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.treatment.EmailProblemType

export enum EmailProblemType {
    UNKNOWN = 'unknown',
    PERMANENT_GENERAL = 'permanent-general',
    PERMANENT_NOEMAIL = 'permanent-noemail',
    PERMANENT_SUPPRESSED = 'permanent-suppressed',
    TEMPORARY_GENERAL = 'temporary-general',
    TEMPORARY_CONTENT = 'temporary-content',
    COMPLAINT = 'complaint',
}

export const AllEmailProblemType = [
    EmailProblemType.UNKNOWN,
    EmailProblemType.PERMANENT_GENERAL,
    EmailProblemType.PERMANENT_NOEMAIL,
    EmailProblemType.PERMANENT_SUPPRESSED,
    EmailProblemType.TEMPORARY_GENERAL,
    EmailProblemType.TEMPORARY_CONTENT,
    EmailProblemType.COMPLAINT,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
