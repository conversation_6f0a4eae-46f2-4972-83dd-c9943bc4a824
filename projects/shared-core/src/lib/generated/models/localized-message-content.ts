// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.templates.questionnaires.LocalizedMessageContent

import { Language } from './language';
export interface LocalizedMessageContent {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    locale?: Language;
    body?: string;
    title?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
