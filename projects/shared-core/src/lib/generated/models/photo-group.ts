// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.PhotoGroup

import { Patient } from './patient';
import { Status } from './status';
export interface PhotoGroup {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    patient?: Patient;
    description?: string;
    status?: Status;
    submitted?: Date | number | string;
    photosToRemove?: string[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
