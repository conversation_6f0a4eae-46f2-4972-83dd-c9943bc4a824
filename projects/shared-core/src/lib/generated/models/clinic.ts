// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.Clinic

import { AppointmentClinicSetting } from './appointment-clinic-setting';
import { Article } from './article';
import { AuthenticationType } from './authentication-type';
import { CaseStatus } from './case-status';
import { CaseTypeSetting } from './case-type-setting';
import { CcdApiSetting } from './ccd-api-setting';
import { ClinicDemoData } from './clinic-demo-data';
import { DatalakeConfiguration } from './datalake-configuration';
import { DateFormattingPattern } from './date-formatting-pattern';
import { IdentityCodeAlgorithm } from './identity-code-algorithm';
import { Language } from './language';
import { MeasurementSystem } from './measurement-system';
import { PatientStatusType } from './patient-status-type';
import { QuestionarySettings } from './questionary-settings';
import { SmartOnFhirConfiguration } from './smart-on-fhir-configuration';
import { StatusIntegration } from './status-integration';
import { TopicType } from './topic-type';
import { TreatmentModule } from './treatment-module';
import { TreatmentUnit } from './treatment-unit';
export interface Clinic {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    logo?: string;
    photo?: string;
    name?: string;
    streetAddress?: string;
    zipCode?: string;
    city?: string;
    phoneNumber?: string;
    emailAddress?: string;
    webSiteUrl?: string;
    responseQueueTimeTargetMillis?: number;
    responseWorkTimeTargetMillis?: number;
    responseCountPerCaseTarget?: number;
    caseLifeTimeTargetMillis?: number;
    frontPageArticle?: Article;
    patientAuthenticationType?: AuthenticationType;
    patientPasswordTokenLifeTime?: number;
    patientLoginTokenSessionLifeTime?: number;
    patientLoginTokenPersistentLifeTime?: number;
    clinicUserAuthenticationType?: AuthenticationType;
    clinicUserPasswordTokenLifeTime?: number;
    clinicUserLoginTokenSessionLifeTime?: number;
    clinicUserLoginTokenPersistentLifeTime?: number;
    clinicIdentityCodeAlgorithm?: IdentityCodeAlgorithm;
    languages?: Language[];
    primaryLanguage?: Language;
    passwordReset?: boolean;
    patientFeedback?: boolean;
    dePersonalizedDataCollection?: boolean;
    dePersonalizedCollectionConsentMandatory?: boolean;
    requireSignature?: boolean;
    patientsHasToAcceptTermsAndConditions?: boolean;
    patientHasToAcceptServiceConsent?: boolean;
    termsAndConditionModificationDate?: Date | number | string;
    dataCollectionModificationDate?: Date | number | string;
    serviceConsentModificationDate?: Date | number | string;
    requirePatientTwoFactorConfirmation?: boolean;
    smsNotificationsEnabled?: boolean;
    smsInvitationsEnabled?: boolean;
    integrationEnabled?: boolean;
    statusChangeIntegrationEnabled?: boolean;
    educationEnabled?: boolean;
    labResultsEnabled?: boolean;
    labResultNotificationsEnabled?: boolean;
    labResultNotifyOncePerDayEnabled?: boolean;
    ccdApiSetting?: CcdApiSetting;
    statusIntegration?: StatusIntegration;
    appointmentClinicSettings?: AppointmentClinicSetting[];
    smsInvitationDelay?: number;
    smsInvitationLifeTime?: number;
    smsReminderDelay?: number;
    gatherMedicationInformation?: boolean;
    askNextOfKin?: boolean;
    patientMessagingEnabled?: boolean;
    generalQuestionsEnabled?: boolean;
    contactPatientEnabled?: boolean;
    dateFormattingPattern?: DateFormattingPattern;
    measurementSystem?: MeasurementSystem;
    treatmentModules?: TreatmentModule[];
    directQuestionnaireLinkEnabled?: boolean;
    aeqExpirationTime?: number;
    aeqAdditionalQuestions?: boolean;
    caseManagementEnabled?: boolean;
    timeZone?: string;
    wipeData?: boolean;
    oneTimeWipe?: Date | number | string;
    lastWipe?: Date | number | string;
    clinicConnection?: boolean;
    demoData?: ClinicDemoData;
    consultationEnabled?: boolean;
    enabled?: boolean;
    demoDataUploaded?: Date | number | string;
    clinicUserNotificationsEnabled?: boolean;
    phoneNumberRegion?: string;
    smartSymptomFollowUpEnabled?: boolean;
    customBrandingEnabled?: boolean;
    customizedName?: boolean;
    enabledNonClinicalTopicTypes?: TopicType[];
    questionarySettings?: QuestionarySettings[];
    caseTypeSettings?: CaseTypeSetting[];
    enabledCaseStatuses?: CaseStatus[];
    registerDataFileEnabled?: boolean;
    symptomInquiriesEnabled?: boolean;
    showingOfLastNameEnabled?: boolean;
    termsOfUseVisible?: boolean;
    selfSignupEnabled?: boolean;
    trackedStatuses?: PatientStatusType[];
    nurseCanControlPatient?: boolean;
    analyticsViewForClinicUsers?: boolean;
    dataLakeIntegrationEnabled?: boolean;
    latestDataLakeRun?: Date | number | string;
    availableTreatmentUnits?: TreatmentUnit[];
    clinicianIdentityCodeEnabled?: boolean;
    analyticsViewForNoonaUsers?: boolean;
    fullAeqReportEnabled?: boolean;
    reportIntegrationEnabled?: boolean;
    caseOutcomeEnabled?: boolean;
    caseEscalationEnabled?: boolean;
    caseDelayReasonEnabled?: boolean;
    ssnPrimaryIdentifier?: boolean;
    clinicianExternalIdEnabled?: boolean;
    singleSignOnVerificationEndpoint?: string;
    medicalRecordsVisible?: boolean;
    upcomingAppointmentsVisbileForPatients?: boolean;
    mapAppointmentValuesFromIntegration?: boolean;
    ignoreNonMappedAppointments?: boolean;
    azureAdTenantId?: string;
    pcsnCode?: string;
    varianTenantId?: string;
    varianTenantName?: string;
    providerAnalyticsVisibleForAllowedClinicUsers?: boolean;
    shortName?: string;
    smartOnFhirConfiguration?: SmartOnFhirConfiguration;
    datalakeConfiguration?: DatalakeConfiguration;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
