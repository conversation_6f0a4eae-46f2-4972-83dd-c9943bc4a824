// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.AutoSubmitStatus

export enum AutoSubmitStatus {
    NONE = '0',
    PRIORITY = '1',
    SUBSEQUENT = '2',
}

export const AllAutoSubmitStatus = [AutoSubmitStatus.NONE, AutoSubmitStatus.PRIORITY, AutoSubmitStatus.SUBSEQUENT];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
