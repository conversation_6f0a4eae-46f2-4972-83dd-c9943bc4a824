// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.templates.questionnaires.SchedulingEntry

import { SchedulingIntervalType } from './scheduling-interval-type';
import { SchedulingTriggerType } from './scheduling-trigger-type';
export interface SchedulingEntry {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    orderIndex?: number;
    interval?: number;
    intervalUnit?: SchedulingIntervalType;
    triggerType?: SchedulingTriggerType;
    repeating?: boolean;
    repeatCount?: number;
    repeatType?: SchedulingIntervalType;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
