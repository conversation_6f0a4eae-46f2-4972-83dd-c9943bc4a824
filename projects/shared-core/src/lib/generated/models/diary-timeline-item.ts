// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.diarytimeline.timelineintem.DiaryTimelineItem

import { DiaryTimelineItemType } from './diary-timeline-item-type';
export interface DiaryTimelineItem {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    date?: Date | number | string;
    dayStart?: Date | number | string;
    text?: string;
    originIds?: string[];
    type?: DiaryTimelineItemType;
    extraData?: any;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
