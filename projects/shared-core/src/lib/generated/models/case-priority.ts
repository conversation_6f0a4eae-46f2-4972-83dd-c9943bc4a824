// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CasePriority

export enum CasePriority {
    CRITICAL = 'critical',
    HIGH = 'high',
    MEDIUM = 'medium',
    LOW = 'low',
    NO_SYMPTOM = 'noSymptom',
    NO_ANSWER = 'noAnswer',
    CRITICAL_ESCALATED = 'criticalEscalated',
    DECLINED = 'declined',
}

export const AllCasePriority = [
    CasePriority.CRITICAL,
    CasePriority.HIGH,
    CasePriority.MEDIUM,
    CasePriority.LOW,
    CasePriority.NO_SYMPTOM,
    CasePriority.NO_ANSWER,
    CasePriority.CRITICAL_ESCALATED,
    CasePriority.DECLINED,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
