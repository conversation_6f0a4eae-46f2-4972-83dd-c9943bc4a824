// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.content.model.CustomerNameCustomization

import { Language } from './language';
export interface CustomerNameCustomization {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    locale?: Language;
    key?: string;
    value?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
