// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.symptoms.SymptomReport

import { Message } from './message';
import { Symptom } from './symptom';
export interface SymptomReport {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    symptoms?: Symptom[];
    answerDate?: Date | number | string;
    message?: Message;
    symptomReportPriority?: number;
    additionalQuestions?: string;
    nextOfKinAnswer?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
