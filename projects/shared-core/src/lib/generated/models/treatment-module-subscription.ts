// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.treatment.TreatmentModuleSubscription

import { SubscriberType } from './subscriber-type';
import { TreatmentModuleSubscriptionType } from './treatment-module-subscription-type';
export interface TreatmentModuleSubscription {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    contactTypes?: TreatmentModuleSubscriptionType[];
    subscriberType?: SubscriberType;
    subscriberId?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
