// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.clinicconnection.models.datatransfer.ConnectionRequestList

import { ConnectionRequestListData } from './connection-request-list-data';
export interface ConnectionRequestList {
    pages?: number;
    requestCount?: number;
    itemsPerPage?: number;
    data?: ConnectionRequestListData[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
