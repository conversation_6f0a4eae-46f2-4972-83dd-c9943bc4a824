// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.clinicconnection.models.entities.ClinicConnectionRequest

import { ClinicConnectionRequestStatus } from './clinic-connection-request-status';
import { ClinicConnectionTargetClinicData } from './clinic-connection-target-clinic-data';
export interface ClinicConnectionRequest {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    workerMessage?: string;
    status?: ClinicConnectionRequestStatus;
    targetCustomerId?: string;
    targetCustomerInfo?: string;
    patientConfirmed?: boolean;
    patientStatementDate?: Date | number | string;
    ownerCustomerConfirmed?: boolean;
    ownerCustomerId?: string;
    ownerStatementDate?: Date | number | string;
    targetCustomerConfirmed?: boolean;
    targetStatementDate?: Date | number | string;
    targetClinicDataId?: string;
    targetClinicData?: ClinicConnectionTargetClinicData;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
