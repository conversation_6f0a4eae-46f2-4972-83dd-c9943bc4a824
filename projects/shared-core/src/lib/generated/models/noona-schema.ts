// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.NoonaSchema

import { CodeDefinition } from './code-definition';
import { Condition } from './condition';
import { Feature } from './feature';
import { Form } from './form';
import { FormDefinition } from './form-definition';
import { ListEntry } from './list-entry';
export interface NoonaSchema {
    selectLists?: { [key in string]: CodeDefinition[] };
    selectListOptions?: { [key in string]: { [key in string]: string }[] };
    scalingInformations?: { [key in string]: { [key in string]: string } };
    forms?: { [key in string]: FormDefinition };
    formEngineForms?: { [key in string]: Form };
    attributeLists?: { [key in string]: string[] };
    photoFields?: { [key in string]: string[] };
    translationPrefixes?: { [key in string]: { [key in string]: string } };
    enums?: { [key in string]: { [key in string]: string } };
    bodyDiagrams?: { [key in string]: string };
    enabledFeatures?: Feature[];
    generalSelectLists?: { [key in string]: ListEntry[] };
    validationPatterns?: { [key in string]: string };
    fieldValueConditions?: { [key in string]: Condition };
    fieldValueStaticConditions?: { [key in string]: Condition };
    dateFormattingPatterns?: { [key in string]: { [key in string]: string } };
    inputGroups?: { [key in string]: { [key in string]: string } };
    inputGroupTranslations?: { [key in string]: { [key in string]: string } };
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
