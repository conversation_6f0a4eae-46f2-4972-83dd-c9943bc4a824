// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.PatientConsentType

export enum PatientConsentType {
    TERMS_OF_USE = 'termsAndConditions',
    DE_IDENTIFIED_DATA_USAGE = 'useOfPatientInformation',
    SERVICE_CONSENT = 'serviceConsent',
}

export const AllPatientConsentType = [
    PatientConsentType.TERMS_OF_USE,
    PatientConsentType.DE_IDENTIFIED_DATA_USAGE,
    PatientConsentType.SERVICE_CONSENT,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
