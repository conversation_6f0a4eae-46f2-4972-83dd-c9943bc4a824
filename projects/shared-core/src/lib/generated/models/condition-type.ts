// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.schema.form.condition.ConditionType

export enum ConditionType {
    AND = 'and',
    OR = 'or',
    BOOLEAN_TRUE = 'booleanTrue',
    BOOLEAN_FALSE = 'booleanFalse',
    FIELD = 'field',
    STATIC = 'static',
    FORM_VARIABLE = 'formVariable',
}

export const AllConditionType = [
    ConditionType.AND,
    ConditionType.OR,
    ConditionType.BOOLEAN_TRUE,
    ConditionType.BOOLEAN_FALSE,
    ConditionType.FIELD,
    ConditionType.STATIC,
    ConditionType.FORM_VARIABLE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
