// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.triage.enums.TriageGradingValueType

export enum TriageGradingValueType {
    RANGE_MAX = 'RANGE_MAX',
    RANGE_MIN = 'RANGE_MIN',
    LEVEL_VALUE = 'LEVEL_VALUE',
}

export const AllTriageGradingValueType = [
    TriageGradingValueType.RANGE_MAX,
    TriageGradingValueType.RANGE_MIN,
    TriageGradingValueType.LEVEL_VALUE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
