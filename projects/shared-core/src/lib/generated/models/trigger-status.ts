// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.worker.model.TriggerStatus

export enum TriggerStatus {
    PENDING = 'PENDING',
    PROCESSING = 'PROCESSING',
    FAILED = 'FAILED',
    READY = 'READY',
    INVALID = 'INVALID',
}

export const AllTriggerStatus = [
    TriggerStatus.PENDING,
    TriggerStatus.PROCESSING,
    TriggerStatus.FAILED,
    TriggerStatus.READY,
    TriggerStatus.INVALID,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
