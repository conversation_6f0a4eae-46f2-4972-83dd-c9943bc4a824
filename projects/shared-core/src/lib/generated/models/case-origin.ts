// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseOrigin

export enum CaseOrigin {
    PHONE = 'phone',
    IN_PERSON = 'inPerson',
    PATIENT_INITIATED_CALL = 'patientInitiatedCall',
    OTHER_INITIATED_CALL = 'otherInitiatedCall',
    PATIENT_INITIATED_MESSAGE = 'patientInitiatedMessage',
    SCHEDULED = 'scheduled',
}

export const AllCaseOrigin = [
    CaseOrigin.PHONE,
    CaseOrigin.IN_PERSON,
    CaseOrigin.PATIENT_INITIATED_CALL,
    CaseOrigin.OTHER_INITIATED_CALL,
    CaseOrigin.PATIENT_INITIATED_MESSAGE,
    CaseOrigin.SCHEDULED,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
