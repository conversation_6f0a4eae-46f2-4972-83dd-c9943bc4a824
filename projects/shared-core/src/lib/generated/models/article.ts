// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.content.model.Article

import { Link } from './link';
export interface Article {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    locale?: string;
    key?: string;
    title?: string;
    body?: string;
    links?: Link[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
