// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.dto.CcdSummaryDto

import { CcdPatientDto } from './ccd-patient-dto';
import { CcdSectionDto } from './ccd-section-dto';
export interface CcdSummaryDto {
    patients?: CcdPatientDto[];
    sections?: CcdSectionDto[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
