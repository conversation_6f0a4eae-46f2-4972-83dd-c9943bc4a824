// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.security.model.Customer

import { Group } from './group';
export interface Customer {
    customerId?: string;
    adminGroup?: Group;
    memberGroup?: Group;
    firstName?: string;
    lastName?: string;
    emailAddress?: string;
    phoneNumber?: string;
    company?: boolean;
    companyName?: string;
    companyCode?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
