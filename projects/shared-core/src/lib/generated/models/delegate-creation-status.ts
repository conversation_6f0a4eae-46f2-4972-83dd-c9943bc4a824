// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientdelegate.DelegateCreationStatus

import { DelegateValidationResult } from './delegate-validation-result';
import { PatientDelegateStatus } from './patient-delegate-status';
export interface DelegateCreationStatus {
    id?: string;
    status?: PatientDelegateStatus;
    validation?: DelegateValidationResult;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
