// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.ccd.api.CcdApiSetting

import { ApiStrategy } from './api-strategy';
import { AuthType } from './auth-type';
export interface CcdApiSetting {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    password?: string;
    apiStrategy?: ApiStrategy;
    endpointUrl?: string;
    authType?: AuthType;
    username?: string;
    useMedOncologyData?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
