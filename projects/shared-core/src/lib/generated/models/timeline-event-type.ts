// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.diarytimeline.event.TimelineEventType

export enum TimelineEventType {
    UNKNOWN = 'unknown',
    TREATMENT_VISIT = 'treatmentVisit',
    CLINIC_APPOINTMENT = 'clinicAppointment',
    LABORATORY_TEST = 'laboratoryTest',
    IMAGING_EXAMINATION = 'imagingExamination',
    SURGERY_DAY = 'surgeryDay',
    DIAGNOSIS_DAY = 'diagnosisDay',
    RADIATION_THERAPY_VISIT = 'radiationTherapyVisit',
}

export const AllTimelineEventType = [
    TimelineEventType.UNKNOWN,
    TimelineEventType.TREATMENT_VISIT,
    TimelineEventType.CLINIC_APPOINTMENT,
    TimelineEventType.LABORATORY_TEST,
    TimelineEventType.IMAGING_EXAMINATION,
    TimelineEventType.SURGERY_DAY,
    TimelineEventType.DIAGNOSIS_DAY,
    TimelineEventType.RADIATION_THERAPY_VISIT,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
