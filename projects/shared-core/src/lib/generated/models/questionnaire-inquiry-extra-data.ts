// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.diarytimeline.timelineintem.extradata.QuestionnaireInquiryExtraData

import { InquiryStatus } from './inquiry-status';
export interface QuestionnaireInquiryExtraData {
    messageId?: string;
    status?: InquiryStatus;
    type?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
