// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.SupportMessage

import { FeedbackMethod } from './feedback-method';
import { SupportMessageType } from './support-message-type';
export interface SupportMessage {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    title?: string;
    content?: string;
    contactAllowed?: boolean;
    read?: boolean;
    firstMessage?: SupportMessage;
    lastRequest?: boolean;
    lastResponse?: boolean;
    type?: SupportMessageType;
    messageDate?: Date | number | string;
    rating?: number;
    feedbackMethod?: FeedbackMethod;
    nativeApp?: boolean;
    patientMessage?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
