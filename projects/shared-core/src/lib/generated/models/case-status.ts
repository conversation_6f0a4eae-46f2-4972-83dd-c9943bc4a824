// Auto generated by ad<PERSON><PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseStatus

export enum CaseStatus {
    OPEN = 'open',
    IN_PROGRESS = 'inProgress',
    CLOSED = 'closed',
    CONSULTATION_OPEN = 'consultationOpen',
    CONSULTATION_ANSWERED = 'consultationAnswered',
    WAITING_FOR_PATIENT = 'waitingForPatient',
    CLINIC_REPLIED = 'clinicReplied',
    NEW = 'new',
    AWAITING_CALLBACK = 'awaitingCallback',
    AWAITING_PHYSICIAN_RESPONSE = 'awaitingPhysicianResponse',
    AWAITING_TRIAGE_FOLLOW_UP_CALL = 'awaitingTriageFollowUpCall',
    SENT_TO_VOICEMAIL = 'sentToVoicemail',
    LIVE_TRANSFER = 'liveTransfer',
    CALLBACK_REQUESTED = 'callbackRequested',
    PARK_PHARMACY = 'parkPharmacy',
    GYN = 'gyn',
    OTHER = 'other',
    SENT_TO_CLINIC = 'sentToClinic',
    PROCESSED = 'processed',
    REMINDER = 'reminder',
    EXPIRED = 'expired',
    DECLINED = 'declined',
    DELETED = 'deleted',
}

export const AllCaseStatus = [
    CaseStatus.OPEN,
    CaseStatus.IN_PROGRESS,
    CaseStatus.CLOSED,
    CaseStatus.CONSULTATION_OPEN,
    CaseStatus.CONSULTATION_ANSWERED,
    CaseStatus.WAITING_FOR_PATIENT,
    CaseStatus.CLINIC_REPLIED,
    CaseStatus.NEW,
    CaseStatus.AWAITING_CALLBACK,
    CaseStatus.AWAITING_PHYSICIAN_RESPONSE,
    CaseStatus.AWAITING_TRIAGE_FOLLOW_UP_CALL,
    CaseStatus.SENT_TO_VOICEMAIL,
    CaseStatus.LIVE_TRANSFER,
    CaseStatus.CALLBACK_REQUESTED,
    CaseStatus.PARK_PHARMACY,
    CaseStatus.GYN,
    CaseStatus.OTHER,
    CaseStatus.SENT_TO_CLINIC,
    CaseStatus.PROCESSED,
    CaseStatus.REMINDER,
    CaseStatus.EXPIRED,
    CaseStatus.DECLINED,
    CaseStatus.DELETED,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
