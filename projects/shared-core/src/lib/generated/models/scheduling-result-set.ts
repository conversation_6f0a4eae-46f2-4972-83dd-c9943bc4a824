// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.SchedulingResultSet

import { QuestionnaireInquiry } from './questionnaire-inquiry';
import { ScheduledMessage } from './scheduled-message';
import { SymptomInquiry } from './symptom-inquiry';
export interface SchedulingResultSet {
    symptomInquiries?: SymptomInquiry[];
    questionnaireInquiries?: QuestionnaireInquiry[];
    scheduledMessages?: ScheduledMessage[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
