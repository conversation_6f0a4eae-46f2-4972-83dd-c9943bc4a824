// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.search.patient.PatientSearchInformation

export interface PatientSearchInformation {
    id?: string;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    phoneNumber2?: string;
    phoneNumber3?: string;
    identityCode?: string;
    medicalRecordNumber?: string;
    address?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    hipaaCode?: string;
    birthDate?: Date | number | string;
    clinicSite?: string;
    primaryProvider?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
