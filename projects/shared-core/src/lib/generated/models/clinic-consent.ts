// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.ClinicConsent

import { ConsentContent } from './consent-content';
import { PatientConsentType } from './patient-consent-type';
export interface ClinicConsent {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    revision?: number;
    consentType?: PatientConsentType;
    userReApprovalRequired?: boolean;
    contentChecksum?: string;
    contents?: ConsentContent[];
    modifierName?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
