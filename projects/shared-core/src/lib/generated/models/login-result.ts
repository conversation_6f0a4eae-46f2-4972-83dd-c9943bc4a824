// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.security.model.LoginResult

import { RequiredConsentInformation } from './required-consent-information';
export interface LoginResult {
    loginToken?: string;
    userId?: string;
    userName?: string;
    error?: string;
    forcePasswordChange?: boolean;
    requiredConsents?: RequiredConsentInformation[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
