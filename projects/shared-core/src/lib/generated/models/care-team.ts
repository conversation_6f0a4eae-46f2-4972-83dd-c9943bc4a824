// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.CareTeam

import { TreatmentUnit } from './treatment-unit';
import { UranusIntegrationView } from './uranus-integration-view';
import { User } from './user';
export interface CareTeam {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    name?: string;
    users?: User[];
    removed?: boolean;
    uranusIntegrationView?: UranusIntegrationView;
    treatmentUnit?: TreatmentUnit;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
