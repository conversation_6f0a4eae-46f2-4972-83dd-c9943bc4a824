// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.PatientCard

import { CareTeam } from './care-team';
import { CaseStatus } from './case-status';
import { CaseType } from './case-type';
import { Patient } from './patient';
import { QuestionaryType } from './questionary-type';
import { User } from './user';
export interface PatientCard {
    patient?: Patient;
    priority?: number;
    submitDate?: Date | number | string;
    assignee?: User;
    assigned?: Date | number | string;
    lastSender?: User;
    lastAdviser?: User;
    consultedUser?: User;
    careTeams?: string[];
    consultedCareTeam?: CareTeam;
    consultingUser?: User;
    consultingCareTeams?: string[];
    treatmentDate?: Date | number | string;
    handled?: boolean;
    handler?: User;
    isSymptomReport?: boolean;
    caseType?: CaseType;
    isQuestionnaireReport?: boolean;
    isNote?: boolean;
    caseId?: string;
    questionnaireType?: QuestionaryType;
    closedDate?: Date | number | string;
    status?: CaseStatus;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
