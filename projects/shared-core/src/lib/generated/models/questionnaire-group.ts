// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.QuestionnaireGroup

import { Questionary } from './questionary';
import { QuestionaryType } from './questionary-type';
import { QuestionnaireScore } from './questionnaire-score';
export interface QuestionnaireGroup {
    questionnaireType?: QuestionaryType;
    questionnaires?: Questionary[];
    score?: QuestionnaireScore;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
