// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.responses.CheckPhoneNumberResponse

export enum CheckPhoneNumberResponse {
    VALID = 'valid',
    INVALID = 'invalid',
    DUPLICATE = 'duplicate',
}

export const AllCheckPhoneNumberResponse = [
    CheckPhoneNumberResponse.VALID,
    CheckPhoneNumberResponse.INVALID,
    CheckPhoneNumberResponse.DUPLICATE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
