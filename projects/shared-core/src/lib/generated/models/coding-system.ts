// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.medical.CodingSystem

export enum CodingSystem {
    DIAGNOSIS = '1.2.246.537.6.1',
    PROCEDURE = '1.2.246.537.6.2.2007',
    N_STAGE = 'n-stage',
    M_STAGE = 'm-stage',
    T_STAGE = 't-stage',
    HISTOLOGICAL_TYPE = 'histological-type',
    HISTOLOGICAL_GRADE = 'histological-grade',
    HER_2_STATUS_IHC = 'her-2-status-ihc',
    HER_2_STATUS_ISH = 'her-2-status-ish',
    MENO_PAUSE_STATUS = 'meno-pause-status',
    LEFT_RIGHT_SIDE = 'value',
    ACTIVE_SUBSTANCE = 'active-substance',
}

export const AllCodingSystem = [
    CodingSystem.DIAGNOSIS,
    CodingSystem.PROCEDURE,
    CodingSystem.N_STAGE,
    CodingSystem.M_STAGE,
    CodingSystem.T_STAGE,
    CodingSystem.HISTOLOGICAL_TYPE,
    CodingSystem.HISTOLOGICAL_GRADE,
    CodingSystem.HER_2_STATUS_IHC,
    CodingSystem.HER_2_STATUS_ISH,
    CodingSystem.MENO_PAUSE_STATUS,
    CodingSystem.LEFT_RIGHT_SIDE,
    CodingSystem.ACTIVE_SUBSTANCE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
