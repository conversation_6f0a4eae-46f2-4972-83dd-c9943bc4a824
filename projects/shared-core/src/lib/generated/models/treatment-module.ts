import { TreatmentModuleCategory } from './treatment-module-category';
import { TreatmentModuleControlType } from './treatment-module-control-type';
import { TreatmentModuleType } from './treatment-module-type';
export interface TreatmentModule {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    symptomTypes?: any[];
    treatmentModuleCategory?: TreatmentModuleCategory;
    treatmentModuleType?: TreatmentModuleType;
    collectLineOfTreatmentInformation?: boolean;
    treatmentModuleControlType?: TreatmentModuleControlType;
}
