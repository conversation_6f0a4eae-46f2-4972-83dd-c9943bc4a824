// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.medical.MedicationUnit

export enum MedicationUnit {
    MILLIGRAM = 'mg',
    INTERNATIONAL_UNIT = 'iu',
    MICROGRAM = 'mcg',
    MILLILITER = 'ml',
    OTHER = 'other',
}

export const AllMedicationUnit = [
    MedicationUnit.MILLIGRAM,
    MedicationUnit.INTERNATIONAL_UNIT,
    MedicationUnit.MICROGRAM,
    MedicationUnit.MILLILITER,
    MedicationUnit.OTHER,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
