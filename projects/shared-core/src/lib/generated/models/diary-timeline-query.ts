// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.diarytimeline.query.DiaryTimelineQuery

import { DiaryTimelineItemType } from './diary-timeline-item-type';
export interface DiaryTimelineQuery {
    referenceDate?: Date | number | string;
    nbrDaysInFuture?: number;
    nbrDaysInPast?: number;
    filterTypes?: DiaryTimelineItemType[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
