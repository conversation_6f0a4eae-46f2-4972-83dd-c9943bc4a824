// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.security.model.UserRole

export enum UserRole {
    NURSE = 'NURSE',
    PATIENT = 'PATIENT',
    SUPPORTER = 'SUPPORTER',
    CLIN<PERSON>_ADMINISTRATOR = '<PERSON><PERSON><PERSON><PERSON>_ADMINISTRATOR',
    SYSTEM_ADMINISTRATOR = 'SYSTEM_ADMINISTRATOR',
    SYSTEM = 'SYSTEM',
    REMOTE_SYSTEM = 'REMOTE_SYSTEM',
    CLINIC_MANAGER = 'CLINIC_MANAGER',
    LIMITED_PATIENT = 'LIMITED_PATIENT',
    NOONA_ADMINISTRATOR = 'NOONA_ADMINISTRATOR',
    EHR_SYSTEM = 'EHR_SYSTEM',
    DATA_EXPORT_REQUESTER = 'DATA_EXPORT_REQUESTER',
    SAM = 'SAM',
    INTEGRATION_READ = 'INTEGRATION_READ',
    INTEGRATION_WRITE = 'INTEGRATION_WRITE',
    PATIENT_DELEGATE = 'PATIENT_DELEGATE',
}

export const AllUserRole = [
    UserRole.NURSE,
    UserRole.PATIENT,
    UserRole.SUPPORTER,
    UserRole.CLINIC_ADMINISTRATOR,
    UserRole.SYSTEM_ADMINISTRATOR,
    UserRole.SYSTEM,
    UserRole.REMOTE_SYSTEM,
    UserRole.CLINIC_MANAGER,
    UserRole.LIMITED_PATIENT,
    UserRole.NOONA_ADMINISTRATOR,
    UserRole.EHR_SYSTEM,
    UserRole.DATA_EXPORT_REQUESTER,
    UserRole.SAM,
    UserRole.INTEGRATION_READ,
    UserRole.INTEGRATION_WRITE,
    UserRole.PATIENT_DELEGATE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
