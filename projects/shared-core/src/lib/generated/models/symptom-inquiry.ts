import { InquiryStatus } from './inquiry-status';
import { InquiryType } from './inquiry-type';
import { Medication } from './medication';
import { PatientTreatmentModule } from './patient-treatment-module';
import { User } from './user';
export interface SymptomInquiry {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    sendingDate?: Date | number | string;
    treatmentDate?: Date | number | string;
    status?: InquiryStatus;
    medication?: Medication;
    symptomTypes?: any[];
    patientTreatmentModule?: PatientTreatmentModule;
    type?: InquiryType;
    subInquiry?: boolean;
    generateSubInquiries?: boolean;
    expiredByDate?: Date | number | string;
    expiredByNurse?: User;
    appointmentType?: string;
    compositeStatus?: InquiryStatus;
}
