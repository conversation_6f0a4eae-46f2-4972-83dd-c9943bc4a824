// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.medical.CodeDefinition

import { CodingSystem } from './coding-system';
export interface CodeDefinition {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    codingSystem?: CodingSystem;
    key?: string;
    label_fi_FI?: string;
    label_en_GB?: string;
    label_sv_FI?: string;
    label_no_NO?: string;
    label_da_DK?: string;
    label_de_DE?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
