// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.medical.symptom.TriageInstruction

import { SaveSymptomResult } from './save-symptom-result';
import { TriageHomecareInstruction } from './triage-homecare-instruction';
export interface TriageInstruction {
    alerts?: SaveSymptomResult[];
    careInstructions?: TriageHomecareInstruction[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
