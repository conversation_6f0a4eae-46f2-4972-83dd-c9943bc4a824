// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.symptoms.InquiryType

export enum InquiryType {
    BASELINE = 'baseline',
    TREATMENT_VISIT = 'treatmentVisit',
    CLINIC_APPOINTMENT = 'clinicAppointment',
    FOLLOWUP = 'followUp',
    TO_POST_TREATMENT = 'toPostTreatment',
}

export const AllInquiryType = [
    InquiryType.BASELINE,
    InquiryType.TREATMENT_VISIT,
    InquiryType.CLINIC_APPOINTMENT,
    InquiryType.FOLLOWUP,
    InquiryType.TO_POST_TREATMENT,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
