// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.library.LibraryContentCategory

export enum LibraryContentCategory {
    EDUCATION = 'education',
    MEDICATION = 'medication',
}

export const AllLibraryContentCategory = [LibraryContentCategory.EDUCATION, LibraryContentCategory.MEDICATION];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
