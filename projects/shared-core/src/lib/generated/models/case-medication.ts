// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseMedication

import { CareProvider } from './care-provider';
import { Case } from './case';
import { MedicationCategory } from './medication-category';
import { MedicationUnit } from './medication-unit';
import { Patient } from './patient';
export interface CaseMedication {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    patient?: Patient;
    case_?: Case;
    prescriber?: CareProvider;
    medicationCategory?: MedicationCategory;
    refillCount?: number;
    doseAmount?: number;
    doseUnit?: MedicationUnit;
    dosingInstructions?: string;
    name?: string;
    narcoticsChecked?: boolean;
    requiresIntervention?: boolean;
    needsPTLabWork?: boolean;
    needsPTOfficeVisit?: boolean;
    needsChemicalScreening?: boolean;
    needsPriorAuthorization?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
