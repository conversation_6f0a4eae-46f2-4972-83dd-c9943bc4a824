// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.PhotoScale

export enum PhotoScale {
    THUMBNAIL = 'thumbnail',
    SMALL = 'small',
    LARGE = 'large',
    EXTRA_LARGE = 'extraLarge',
    ORIGINAL = 'original',
}

export const AllPhotoScale = [
    PhotoScale.THUMBNAIL,
    PhotoScale.SMALL,
    PhotoScale.LARGE,
    PhotoScale.EXTRA_LARGE,
    PhotoScale.ORIGINAL,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
