// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.Language

export enum Language {
  EN_GB = 'en_GB',
  FI_FI = 'fi_FI',
  SV_FI = 'sv_FI',
  NO_NO = 'no_NO',
  DE_DE = 'de_DE',
  FR_FR = 'fr_FR',
  ES_ES = 'es_ES',
  IT_IT = 'it_IT',
  PT_PT = 'pt_PT',
  NL_NL = 'nl_NL',
  TR_TR = 'tr_TR',
  PL_PL = 'pl_PL'
}

export const AllLanguage = [
  Language.EN_GB,
  Language.FI_FI,
  Language.SV_FI,
  Language.NO_NO,
  Language.DE_DE,
  Language.FR_FR,
  Language.ES_ES,
  Language.IT_IT,
  Language.PT_PT,
  Language.NL_NL,
  Language.TR_TR,
  Language.PL_PL
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
