// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.analytics.model.reporting.QuestionnaireCounts

export interface QuestionnaireCounts {
    totalSentCount?: number;
    changedSentCount?: number;
    totalAnsweredCount?: number;
    changedAnsweredCount?: number;
    averageScore?: number;
    changeAverageScore?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
