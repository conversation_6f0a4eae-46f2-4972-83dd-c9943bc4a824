// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.search.index.IndexFields.PatientIndexField

export enum PatientIndexField {
    LAST_NAME = 'lastName',
    FIRST_NAME = 'firstName',
    BIRTH_DATE = 'birthDate',
    IDENTITY_CODE = 'identityCode',
    MEDICAL_RECORD_NUMBER = 'medicalRecordNumber',
}

export const AllPatientIndexField = [
    PatientIndexField.LAST_NAME,
    PatientIndexField.FIRST_NAME,
    PatientIndexField.BIRTH_DATE,
    PatientIndexField.IDENTITY_CODE,
    PatientIndexField.MEDICAL_RECORD_NUMBER,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
