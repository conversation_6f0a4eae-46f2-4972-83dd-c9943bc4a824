// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.clinicconnection.models.datatransfer.ConnectionRequestPatientView

import { ClinicConnectionRequestStatus } from './clinic-connection-request-status';
export interface ConnectionRequestPatientView {
    status?: ClinicConnectionRequestStatus;
    targetCustomerId?: string;
    targetCustomerInfo?: string;
    requestId?: string;
    workerMessage?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
