// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.integration.ClinicLinkingContext

import { UserRole } from './user-role';
export enum ClinicLinkingContext {
    BMS_STUDY = 'bmsstudy1',
}

export const AllClinicLinkingContext = [ClinicLinkingContext.BMS_STUDY];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
