// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.rule.SymptomPrioritisationResult

import { AdviceLink } from './advice-link';
import { MatchingAutomaticAdvice } from './matching-automatic-advice';
export interface SymptomPrioritisationResult {
    priority?: number;
    advices?: MatchingAutomaticAdvice[];
    adviceLink?: AdviceLink;
    showPriorityModal?: boolean;
    modalContent?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
