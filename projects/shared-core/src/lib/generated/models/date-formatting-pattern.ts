// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.units.DateFormattingPattern

export enum DateFormattingPattern {
    DOTTED_DMY = 'dottedDMY',
    SLASHED_MDY = 'slashedMDY',
    DASHED_YMD = 'dashedYMD',
    SLASHED_DMY = 'slashedDMY',
}

export const AllDateFormattingPattern = [
    DateFormattingPattern.DOTTED_DMY,
    DateFormattingPattern.SLASHED_MDY,
    DateFormattingPattern.DASHED_YMD,
    DateFormattingPattern.SLASHED_DMY,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
