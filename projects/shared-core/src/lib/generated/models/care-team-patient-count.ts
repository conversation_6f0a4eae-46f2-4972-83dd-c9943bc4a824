// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.CareTeamPatientCount

import { CareTeam } from './care-team';
export interface CareTeamPatientCount {
    careTeam?: CareTeam;
    activePatientCount?: number;
    userActivePatientCounts?: { [key in string]: number };
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
