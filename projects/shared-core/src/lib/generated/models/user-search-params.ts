// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.UserSearchParams

import { UserStatus } from './user-status';
export interface UserSearchParams {
    pageSize?: number;
    pageNumber?: number;
    userStatuses?: UserStatus[];
    careTeamIds?: string[];
    hasEmailProblem?: boolean;
    includeAllPatients?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
