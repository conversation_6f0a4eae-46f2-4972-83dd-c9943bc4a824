// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.triage.model.TriageNoteTranslation

import { Language } from './language';
import { TriageNote } from './triage-note';
export interface TriageNoteTranslation {
    id?: string;
    triageNote?: TriageNote;
    language?: Language;
    translation?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
