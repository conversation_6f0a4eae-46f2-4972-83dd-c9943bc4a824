// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.security.model.AuthenticationType

export enum AuthenticationType {
    PASSWORD = 'password',
    PASSWORD_AND_TUPAS = 'password-and-tupas',
    PASSWORD_AND_VETUMA = 'password-and-vetuma',
    PASSWORD_AND_MOBILE = 'password-and-mobile',
}

export const AllAuthenticationType = [
    AuthenticationType.PASSWORD,
    AuthenticationType.PASSWORD_AND_TUPAS,
    AuthenticationType.PASSWORD_AND_VETUMA,
    AuthenticationType.PASSWORD_AND_MOBILE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
