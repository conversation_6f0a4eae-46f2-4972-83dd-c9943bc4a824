// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.clinicconnection.models.datatransfer.ConnectionRequestListData

import { ClinicConnectionRequestStatus } from './clinic-connection-request-status';
export interface ConnectionRequestListData {
    requestId?: string;
    phoneNumber?: string;
    identityCode?: string;
    status?: ClinicConnectionRequestStatus;
    ownerConfirmed?: boolean;
    patientConfirmed?: boolean;
    targetConfirmed?: boolean;
    workerMessage?: string;
    modified?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
