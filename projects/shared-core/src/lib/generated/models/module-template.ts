// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.templates.questionnaires.ModuleTemplate

import { MessageSchedulingEntry } from './message-scheduling-entry';
import { QuestionnaireSchedulingEntry } from './questionnaire-scheduling-entry';
import { SymptomSchedulingEntry } from './symptom-scheduling-entry';
import { TreatmentModuleSubscription } from './treatment-module-subscription';
export interface ModuleTemplate {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    treatmentModule?: any;
    subscriptions?: TreatmentModuleSubscription[];
    questionnaireEntries?: QuestionnaireSchedulingEntry[];
    entries?: SymptomSchedulingEntry[];
    messageEntries?: MessageSchedulingEntry[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
