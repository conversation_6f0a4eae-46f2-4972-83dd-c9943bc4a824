// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.Patient

import { CareProvider } from './care-provider';
import { ClinicSite } from './clinic-site';
import { Gender } from './gender';
import { PatientStatus } from './patient-status';
import { PhoneNumberType } from './phone-number-type';
export interface Patient {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    identityCode?: string;
    firstName?: string;
    lastName?: string;
    emailAddress?: string;
    phoneNumber?: string;
    phoneNumberType1?: PhoneNumberType;
    phoneNumber2?: string;
    phoneNumberType2?: PhoneNumberType;
    phoneNumber3?: string;
    phoneNumberType3?: PhoneNumberType;
    gender?: Gender;
    diseasesAndOtherMedication?: string;
    activationDate?: Date | number | string;
    statuses?: PatientStatus[];
    clinicSite?: ClinicSite;
    primaryProvider?: CareProvider;
    hipaaCode?: string;
    medicalRecordNumber?: string;
    address?: string;
    city?: string;
    zipCode?: string;
    state?: string;
    nurseControlled?: boolean;
    declinedNoonaInvite?: boolean;
    deletionRequested?: boolean;
    initialCareTeamId?: string;
    externalLinkingInfo?: { [key in string]: string };
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
