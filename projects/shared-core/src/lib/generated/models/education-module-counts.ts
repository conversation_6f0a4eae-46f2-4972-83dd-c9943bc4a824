// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.patient.status.analytics.EducationModuleCounts

export interface EducationModuleCounts {
    module1Count?: number;
    module2Count?: number;
    module3Count?: number;
    changedModule1Count?: number;
    changedModule2Count?: number;
    changedModule3Count?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
