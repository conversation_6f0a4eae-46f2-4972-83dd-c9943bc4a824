// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.ReportStyle

export enum ReportStyle {
    COMPACT_AEQ = 'compactAeq',
    COMPACT_SYMPTOM = 'compactSymptom',
    COMPACT_SYMPTOM_NO_MESSAGES = 'compactSymptomNoMessages',
    COMPACT_OTHER_MESSAGE = 'compactOtherMessage',
    COMPACT_QUESTIONNAIRE = 'compactQuestionnaire',
    FULL_AEQ = 'fullAeq',
    FULL_AEQ_SYMPTOM = 'fullAeqSymptom',
    TNONC_DISTRESS_QUESTIONNAIRE = 'tnOncDistress',
}

export const AllReportStyle = [
    ReportStyle.COMPACT_AEQ,
    ReportStyle.COMPACT_SYMPTOM,
    ReportStyle.COMPACT_SYMPTOM_NO_MESSAGES,
    ReportStyle.COMPACT_OTHER_MESSAGE,
    ReportStyle.COMPACT_QUESTIONNAIRE,
    ReportStyle.FULL_AEQ,
    ReportStyle.FULL_AEQ_SYMPTOM,
    ReportStyle.TNONC_DISTRESS_QUESTIONNAIRE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
