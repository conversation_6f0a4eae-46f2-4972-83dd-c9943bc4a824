// Auto generated by ad<PERSON><PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.rule.model.AutomaticAdvice

export enum AutomaticAdvice {
    MILD_UNCLEAR = 'mild-unclear',
    MILD_UNRELATED = 'mild-unrelated',
    NORMAL = 'normal',
    URGENT = 'urgent',
    EMERGENCY = 'emergency',
    MOOD02AML03_MENTAL_WELLBEING_INSTRUCTIONS = 'mood02aml03-mental-wellbeing-instructions',
    LESION03AML01_RADIATION_TREATMENT_INSTRUCTIONS = 'lesion03aml01-radiation-treatment-instructions',
    DEPRECATED01 = 'deprecated01',
    MENOPAUSE01AML04_MENOPAUSE_INSTRUCTIONS = 'menopause01aml04-menopause-instructions',
    MENTAL01AML03_MENTAL_WELLBEING_INSTRUCTIONS = 'mental01aml03-mental-wellbeing-instructions',
    SEMI_EMERGENCY = 'semi-emergency',
    MENOPAUSE02AMC05_MENOPAUSE_MEDICATION_INSTRUCTIONS = 'menopause02amc05-menopause-medication-instructions',
    HEADACHE08AMC10_MILD_HEADCHE_INSTRUCTIONS = 'headache08amc10-mild-headche-instructions',
    PAIN05AMC03_MILD_PAIN_INSTRUCTIONS = 'pain05amc03-mild-pain-instructions',
    PAIN06AML02_HORMONE_TREATMENT_INSTRUCTIONS = 'pain06aml02-hormone-treatment-instructions',
    BREATH02AMC07_SHORT_TERM_COUGH = 'breath02amc07-short-term-cough',
    JOINT03AMC04_MILD_JOINT_PAIN_MEDICATION_INSTRUCTIONS = 'joint03amc04-mild-joint-pain-medication-instructions',
    JOINTAML02_HORMONE_TREATMENT_INSTRUCTIONS = 'jointaml02-hormone-treatment-instructions',
    DEPRECATED02 = 'deprecated02',
    JOINT06AMX__JOINT_SYMPTOMS_HORMONAL_TREATMENT_INSTRUCTIONS = 'joint06amx--joint-symptoms-hormonal-treatment-instructions',
    MILD_NO_LINK = 'mild-no-link',
    LESION02AMC01_BREAST_INFLAMMATION_EMERGENCY_INSTRUCTIONS = 'lesion02amc01-breast-inflammation-emergency-instructions',
    SWELLING03AMC02_ERYSIPELAS_EMERGENCY_INSTRUCTIONS = 'swelling03amc02-erysipelas-emergency-instructions',
    ABDOMEN02AMC09_SEVERE_ABDOMINAL_PAIN_INSTRUCTIONS = 'abdomen02amc09-severe-abdominal-pain-instructions',
    SWELLING04AMC11_POSSIBLE_ERYSIPELAS_EMERGENCY_WITH_REDNESS_INSTRUCTIONS = 'swelling04amc11-possible-erysipelas-emergency-with-redness-instructions',
    SWELLING05AMC12_POSSIBLE_ERYSIPELAS_EMERGENCY_WITH_FEVER_INSTRUCTIONS = 'swelling05amc12-possible-erysipelas-emergency-with-fever-instructions',
    CYTOSTATICGENERAL_AMC_OtherSymptom01 = 'cytostaticgeneral-amc-othersymptom01',
    CYTOSTATICGENERALAMCDiarrhea01Dehydration = 'cytostaticgeneralamcdiarrhea01dehydration',
    CYTOSTATICGENERALAMCFever01Infection = 'cytostaticgeneralamcfever01infection',
    IMMUNOLOGICTREATMENTSAMCFever01Infection = 'immunologictreatmentsamcfever01infection',
    IMMUNOLOGICTREATMENTSAMCFever02Infection = 'immunologictreatmentsamcfever02infection',
    CYTOSTATICGENERALAMCInfection01Fever = 'cytostaticgeneralamcinfection01fever',
    IMMUNOLOGICTREATMENTSAMCInfection01Inflammation = 'immunologictreatmentsamcinfection01inflammation',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic01Colitis = 'immunologictreatmentsamcabdomenimmunologic01colitis',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic02Colitis = 'immunologictreatmentsamcabdomenimmunologic02colitis',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic03Pancreatis = 'immunologictreatmentsamcabdomenimmunologic03pancreatis',
    SURGERYBREASTAMCBleedingWound01SevereBleeding = 'surgerybreastamcbleedingwound01severebleeding',
    SURGERYBREASTAMCBleedingWound02BleedingWithHighFever = 'surgerybreastamcbleedingwound02bleedingwithhighfever',
    SURGERYBREASTAMCBleedingWound04BleedingWithPain = 'surgerybreastamcbleedingwound04bleedingwithpain',
    SURGERYBREASTAMCBleedingWound05BleedingWithSeverePain = 'surgerybreastamcbleedingwound05bleedingwithseverepain',
    SURGERYBREASTAMCBleedingWound06InternalBleeding = 'surgerybreastamcbleedingwound06internalbleeding',
    CYTOSTATICGENERALAMCBoneOrMusclePain01Pain = 'cytostaticgeneralamcboneormusclepain01pain',
    SURGERYBREASTAMCDiarrhea01Dehydration = 'surgerybreastamcdiarrhea01dehydration',
    SURGERYBREASTAMCDiarrhea02Dehydration = 'surgerybreastamcdiarrhea02dehydration',
    IMMUNOLOGICTREATMENTSAMCEyeSymptoms01Hepatitis = 'immunologictreatmentsamceyesymptoms01hepatitis',
    IMMUNOLOGICTREATMENTSAMCEyeSymptoms02Iritis = 'immunologictreatmentsamceyesymptoms02iritis',
    IMMUNOLOGICTREATMENTSAMCFatigueAt01Infection = 'immunologictreatmentsamcfatigueat01infection',
    SURGERYBREASTAMCFever01HighFever = 'surgerybreastamcfever01highfever',
    SURGERYBREASTAMCInfectedWound01Fever = 'surgerybreastamcinfectedwound01fever',
    SURGERYBREASTAMCInfectedWound02Redness = 'surgerybreastamcinfectedwound02redness',
    SURGERYBREASTAMCInfectedWound03Redness = 'surgerybreastamcinfectedwound03redness',
    SURGERYBREASTAMCInfectedWound04Bleeding = 'surgerybreastamcinfectedwound04bleeding',
    CYTOSTATICGENERALAMCMucositisOral01Dehydration = 'cytostaticgeneralamcmucositisoral01dehydration',
    CYTOSTATICGENERALAMCNausea01Dehydration = 'cytostaticgeneralamcnausea01dehydration',
    IMMUNOLOGICTREATMENTSAMCNausea01Dehydration = 'immunologictreatmentsamcnausea01dehydration',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness01GuillanBarre = 'immunologictreatmentsamcneuropathyormuscleweakness01guillanbarre',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness02AutoImmune = 'immunologictreatmentsamcneuropathyormuscleweakness02autoimmune',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness03AutoImmune = 'immunologictreatmentsamcneuropathyormuscleweakness03autoimmune',
    CYTOSTATICGENERALAMCOtherSymptom01 = 'cytostaticgeneralamcothersymptom01',
    SURGERYBREASTAMCPain01 = 'surgerybreastamcpain01',
    SURGERYBREASTAMCPain02 = 'surgerybreastamcpain02',
    IMMUNOLOGICTREATMENTSAMCPainImmunologic01Pain = 'immunologictreatmentsamcpainimmunologic01pain',
    IMMUNOLOGICTREATMENTSAMCPainImmunologic02Stroke = 'immunologictreatmentsamcpainimmunologic02stroke',
    IMMUNOLOGICTREATMENTSAMCRashImmunologic01SkinReaction = 'immunologictreatmentsamcrashimmunologic01skinreaction',
    IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic01Embolia = 'immunologictreatmentsamcrespiratoryimmunologic01embolia',
    IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic02Embolia = 'immunologictreatmentsamcrespiratoryimmunologic02embolia',
    SURGERYBREASTAMCSHORTNESSOFBREATH02Embolia = 'surgerybreastamcshortnessofbreath02embolia',
    SURGERYBREASTAMCSHORTNESSOFBREATH03ChestPain = 'surgerybreastamcshortnessofbreath03chestpain',
    SURGERYBREASTAMCSHORTNESSOFBREATH04HighFever = 'surgerybreastamcshortnessofbreath04highfever',
    SURGERYBREASTAMCSurgeryDrain01SeverePain = 'surgerybreastamcsurgerydrain01severepain',
    SURGERYBREASTAMCSurgeryDrain02MoreSeverePain = 'surgerybreastamcsurgerydrain02moreseverepain',
    SURGERYBREASTAMCSurgeryDrain03Bleeding = 'surgerybreastamcsurgerydrain03bleeding',
    SURGERYBREASTAMCSurgeryDrain04Redness = 'surgerybreastamcsurgerydrain04redness',
    SURGERYBREASTAMCSurgeryDrain05HighFever = 'surgerybreastamcsurgerydrain05highfever',
    SURGERYBREASTAMCSurgeryDrain07SwollenWound = 'surgerybreastamcsurgerydrain07swollenwound',
    SURGERYBREASTAMCSurgeryDrain08SwollenWoundWithBleeding = 'surgerybreastamcsurgerydrain08swollenwoundwithbleeding',
    IMMUNOLOGICTREATMENTSAMCVertigo01Stroke = 'immunologictreatmentsamcvertigo01stroke',
    IMMUNOLOGICTREATMENTSAMCVertigo02NeurologicImpairment = 'immunologictreatmentsamcvertigo02neurologicimpairment',
    SURGERYBREASTAMCWoundAppearance01InternalBleeding = 'surgerybreastamcwoundappearance01internalbleeding',
    EATINGPROBLEMSAMC01Dehydration = 'eatingproblemsamc01dehydration',
    IMMUNOLOGICTREATMENTSAMCInfection05Fever = 'immunologictreatmentsamcinfection05fever',
    IMMUNOLOGICTREATMENTSAMCInfection06Fever = 'immunologictreatmentsamcinfection06fever',
    SURGERYBREASTAMCWoundAppearance02Paracentesis = 'surgerybreastamcwoundappearance02paracentesis',
    RASH03 = 'rash03',
    RESPIRATORY16Fever = 'respiratory16fever',
    AMCabdominal11obstruction = 'amcabdominal11obstruction',
    AMCabdominal27Fever = 'amcabdominal27fever',
    MYELOMAAMCFever01Infection = 'myelomaamcfever01infection',
    MYELOMAAMCCirculatory01Embolia = 'myelomaamccirculatory01embolia',
    MYELOMAAMCCirculatory02Embolia = 'myelomaamccirculatory02embolia',
    EATINGPROBLMESORMOUTHSYMPTOMS01Dehydration = 'eatingproblmesormouthsymptoms01dehydration',
    INFECTIONORFEVER04Infection = 'infectionorfever04infection',
    LYMPHOMARECOVERYMCNNausea01Dehydraion = 'lymphomarecoverymcnnausea01dehydraion',
    MELANOMA_RECOVERYAMCInfection08Fever = 'melanoma-recoveryamcinfection08fever',
    CYTOSTATICGENERALAMCFever001Infection = 'cytostaticgeneralamcfever001infection',
    CYTOSTATICGENERALAMCNausea001Dehydration = 'cytostaticgeneralamcnausea001dehydration',
    LYMPHOMARECOVERYMCNNausea001Dehydraion = 'lymphomarecoverymcnnausea001dehydraion',
    IMMUNOLOGICTREATMENTSAMCFatigueAt001Infection = 'immunologictreatmentsamcfatigueat001infection',
    CYTOSTATICGENERALAMCOtherSymptom001 = 'cytostaticgeneralamcothersymptom001',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic001Colitis = 'immunologictreatmentsamcabdomenimmunologic001colitis',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic002Colitis = 'immunologictreatmentsamcabdomenimmunologic002colitis',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic003Pancreatis = 'immunologictreatmentsamcabdomenimmunologic003pancreatis',
    ABDOMEN002AMC009_SEVERE_ABDOMINAL_PAIN_INSTRUCTIONS = 'abdomen002amc009-severe-abdominal-pain-instructions',
    CYTOSTATICGENERALAMCDiarrhea001Dehydration = 'cytostaticgeneralamcdiarrhea001dehydration',
    SURGERYBREASTAMCDiarrhea002Dehydration = 'surgerybreastamcdiarrhea002dehydration',
    AMCabdominal011obstruction = 'amcabdominal011obstruction',
    AMCabdominal027Fever = 'amcabdominal027fever',
    EATINGPROBLMESORMOUTHSYMPTOMS001Dehydration = 'eatingproblmesormouthsymptoms001dehydration',
    IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic001Embolia = 'immunologictreatmentsamcrespiratoryimmunologic001embolia',
    IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic002Embolia = 'immunologictreatmentsamcrespiratoryimmunologic002embolia',
    SURGERYBREASTAMCSHORTNESSOFBREATH002Embolia = 'surgerybreastamcshortnessofbreath002embolia',
    SURGERYBREASTAMCSHORTNESSOFBREATH004HighFever = 'surgerybreastamcshortnessofbreath004highfever',
    RESPIRATORY016Fever = 'respiratory016fever',
    SURGERYBREASTAMCPain001 = 'surgerybreastamcpain001',
    SURGERYBREASTAMCPain002 = 'surgerybreastamcpain002',
    CYTOSTATICGENERALAMCBoneOrMusclePain001Pain = 'cytostaticgeneralamcboneormusclepain001pain',
    IMMUNOLOGICTREATMENTSAMCPainImmunologic002Stroke = 'immunologictreatmentsamcpainimmunologic002stroke',
    IMMUNOLOGICTREATMENTSAMCRashImmunologic001SkinReaction = 'immunologictreatmentsamcrashimmunologic001skinreaction',
    RASH003 = 'rash003',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness001GuillanBarre = 'immunologictreatmentsamcneuropathyormuscleweakness001guillanbarre',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness002AutoImmune = 'immunologictreatmentsamcneuropathyormuscleweakness002autoimmune',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness003AutoImmune = 'immunologictreatmentsamcneuropathyormuscleweakness003autoimmune',
    SURGERYBREASTAMCSHORTNESSOFBREATH04HighFeverSE = 'surgerybreastamcshortnessofbreath04highfeverse',
}

export const AllAutomaticAdvice = [
    AutomaticAdvice.MILD_UNCLEAR,
    AutomaticAdvice.MILD_UNRELATED,
    AutomaticAdvice.NORMAL,
    AutomaticAdvice.URGENT,
    AutomaticAdvice.EMERGENCY,
    AutomaticAdvice.MOOD02AML03_MENTAL_WELLBEING_INSTRUCTIONS,
    AutomaticAdvice.LESION03AML01_RADIATION_TREATMENT_INSTRUCTIONS,
    AutomaticAdvice.DEPRECATED01,
    AutomaticAdvice.MENOPAUSE01AML04_MENOPAUSE_INSTRUCTIONS,
    AutomaticAdvice.MENTAL01AML03_MENTAL_WELLBEING_INSTRUCTIONS,
    AutomaticAdvice.SEMI_EMERGENCY,
    AutomaticAdvice.MENOPAUSE02AMC05_MENOPAUSE_MEDICATION_INSTRUCTIONS,
    AutomaticAdvice.HEADACHE08AMC10_MILD_HEADCHE_INSTRUCTIONS,
    AutomaticAdvice.PAIN05AMC03_MILD_PAIN_INSTRUCTIONS,
    AutomaticAdvice.PAIN06AML02_HORMONE_TREATMENT_INSTRUCTIONS,
    AutomaticAdvice.BREATH02AMC07_SHORT_TERM_COUGH,
    AutomaticAdvice.JOINT03AMC04_MILD_JOINT_PAIN_MEDICATION_INSTRUCTIONS,
    AutomaticAdvice.JOINTAML02_HORMONE_TREATMENT_INSTRUCTIONS,
    AutomaticAdvice.DEPRECATED02,
    AutomaticAdvice.JOINT06AMX__JOINT_SYMPTOMS_HORMONAL_TREATMENT_INSTRUCTIONS,
    AutomaticAdvice.MILD_NO_LINK,
    AutomaticAdvice.LESION02AMC01_BREAST_INFLAMMATION_EMERGENCY_INSTRUCTIONS,
    AutomaticAdvice.SWELLING03AMC02_ERYSIPELAS_EMERGENCY_INSTRUCTIONS,
    AutomaticAdvice.ABDOMEN02AMC09_SEVERE_ABDOMINAL_PAIN_INSTRUCTIONS,
    AutomaticAdvice.SWELLING04AMC11_POSSIBLE_ERYSIPELAS_EMERGENCY_WITH_REDNESS_INSTRUCTIONS,
    AutomaticAdvice.SWELLING05AMC12_POSSIBLE_ERYSIPELAS_EMERGENCY_WITH_FEVER_INSTRUCTIONS,
    AutomaticAdvice.CYTOSTATICGENERAL_AMC_OtherSymptom01,
    AutomaticAdvice.CYTOSTATICGENERALAMCDiarrhea01Dehydration,
    AutomaticAdvice.CYTOSTATICGENERALAMCFever01Infection,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCFever01Infection,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCFever02Infection,
    AutomaticAdvice.CYTOSTATICGENERALAMCInfection01Fever,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCInfection01Inflammation,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic01Colitis,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic02Colitis,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic03Pancreatis,
    AutomaticAdvice.SURGERYBREASTAMCBleedingWound01SevereBleeding,
    AutomaticAdvice.SURGERYBREASTAMCBleedingWound02BleedingWithHighFever,
    AutomaticAdvice.SURGERYBREASTAMCBleedingWound04BleedingWithPain,
    AutomaticAdvice.SURGERYBREASTAMCBleedingWound05BleedingWithSeverePain,
    AutomaticAdvice.SURGERYBREASTAMCBleedingWound06InternalBleeding,
    AutomaticAdvice.CYTOSTATICGENERALAMCBoneOrMusclePain01Pain,
    AutomaticAdvice.SURGERYBREASTAMCDiarrhea01Dehydration,
    AutomaticAdvice.SURGERYBREASTAMCDiarrhea02Dehydration,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCEyeSymptoms01Hepatitis,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCEyeSymptoms02Iritis,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCFatigueAt01Infection,
    AutomaticAdvice.SURGERYBREASTAMCFever01HighFever,
    AutomaticAdvice.SURGERYBREASTAMCInfectedWound01Fever,
    AutomaticAdvice.SURGERYBREASTAMCInfectedWound02Redness,
    AutomaticAdvice.SURGERYBREASTAMCInfectedWound03Redness,
    AutomaticAdvice.SURGERYBREASTAMCInfectedWound04Bleeding,
    AutomaticAdvice.CYTOSTATICGENERALAMCMucositisOral01Dehydration,
    AutomaticAdvice.CYTOSTATICGENERALAMCNausea01Dehydration,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCNausea01Dehydration,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness01GuillanBarre,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness02AutoImmune,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness03AutoImmune,
    AutomaticAdvice.CYTOSTATICGENERALAMCOtherSymptom01,
    AutomaticAdvice.SURGERYBREASTAMCPain01,
    AutomaticAdvice.SURGERYBREASTAMCPain02,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCPainImmunologic01Pain,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCPainImmunologic02Stroke,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCRashImmunologic01SkinReaction,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic01Embolia,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic02Embolia,
    AutomaticAdvice.SURGERYBREASTAMCSHORTNESSOFBREATH02Embolia,
    AutomaticAdvice.SURGERYBREASTAMCSHORTNESSOFBREATH03ChestPain,
    AutomaticAdvice.SURGERYBREASTAMCSHORTNESSOFBREATH04HighFever,
    AutomaticAdvice.SURGERYBREASTAMCSurgeryDrain01SeverePain,
    AutomaticAdvice.SURGERYBREASTAMCSurgeryDrain02MoreSeverePain,
    AutomaticAdvice.SURGERYBREASTAMCSurgeryDrain03Bleeding,
    AutomaticAdvice.SURGERYBREASTAMCSurgeryDrain04Redness,
    AutomaticAdvice.SURGERYBREASTAMCSurgeryDrain05HighFever,
    AutomaticAdvice.SURGERYBREASTAMCSurgeryDrain07SwollenWound,
    AutomaticAdvice.SURGERYBREASTAMCSurgeryDrain08SwollenWoundWithBleeding,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCVertigo01Stroke,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCVertigo02NeurologicImpairment,
    AutomaticAdvice.SURGERYBREASTAMCWoundAppearance01InternalBleeding,
    AutomaticAdvice.EATINGPROBLEMSAMC01Dehydration,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCInfection05Fever,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCInfection06Fever,
    AutomaticAdvice.SURGERYBREASTAMCWoundAppearance02Paracentesis,
    AutomaticAdvice.RASH03,
    AutomaticAdvice.RESPIRATORY16Fever,
    AutomaticAdvice.AMCabdominal11obstruction,
    AutomaticAdvice.AMCabdominal27Fever,
    AutomaticAdvice.MYELOMAAMCFever01Infection,
    AutomaticAdvice.MYELOMAAMCCirculatory01Embolia,
    AutomaticAdvice.MYELOMAAMCCirculatory02Embolia,
    AutomaticAdvice.EATINGPROBLMESORMOUTHSYMPTOMS01Dehydration,
    AutomaticAdvice.INFECTIONORFEVER04Infection,
    AutomaticAdvice.LYMPHOMARECOVERYMCNNausea01Dehydraion,
    AutomaticAdvice.MELANOMA_RECOVERYAMCInfection08Fever,
    AutomaticAdvice.CYTOSTATICGENERALAMCFever001Infection,
    AutomaticAdvice.CYTOSTATICGENERALAMCNausea001Dehydration,
    AutomaticAdvice.LYMPHOMARECOVERYMCNNausea001Dehydraion,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCFatigueAt001Infection,
    AutomaticAdvice.CYTOSTATICGENERALAMCOtherSymptom001,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic001Colitis,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic002Colitis,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic003Pancreatis,
    AutomaticAdvice.ABDOMEN002AMC009_SEVERE_ABDOMINAL_PAIN_INSTRUCTIONS,
    AutomaticAdvice.CYTOSTATICGENERALAMCDiarrhea001Dehydration,
    AutomaticAdvice.SURGERYBREASTAMCDiarrhea002Dehydration,
    AutomaticAdvice.AMCabdominal011obstruction,
    AutomaticAdvice.AMCabdominal027Fever,
    AutomaticAdvice.EATINGPROBLMESORMOUTHSYMPTOMS001Dehydration,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic001Embolia,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic002Embolia,
    AutomaticAdvice.SURGERYBREASTAMCSHORTNESSOFBREATH002Embolia,
    AutomaticAdvice.SURGERYBREASTAMCSHORTNESSOFBREATH004HighFever,
    AutomaticAdvice.RESPIRATORY016Fever,
    AutomaticAdvice.SURGERYBREASTAMCPain001,
    AutomaticAdvice.SURGERYBREASTAMCPain002,
    AutomaticAdvice.CYTOSTATICGENERALAMCBoneOrMusclePain001Pain,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCPainImmunologic002Stroke,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCRashImmunologic001SkinReaction,
    AutomaticAdvice.RASH003,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness001GuillanBarre,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness002AutoImmune,
    AutomaticAdvice.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness003AutoImmune,
    AutomaticAdvice.SURGERYBREASTAMCSHORTNESSOFBREATH04HighFeverSE
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
