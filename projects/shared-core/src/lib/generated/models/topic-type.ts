// Auto generated by ad<PERSON><PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.TopicType

export enum TopicType {
    NONE = 0,
    TREATMENT = 1,
    MEDICATION = 2,
    PHYSIOTHERAPY = 3,
    TREATMENT_SIDE_EFFECT = 4,
    FOLLOW_UP_PERIOD = 5,
    OTHER_ISSUE = 6,
    SYMPTOM_SUBMIT = 7,
    FOLLOW_UP_SUBMIT = 8,
    EXAMINATION = 9,
    INVITATION = 10,
    INSTRUCTION = 11,
    MONITOR = 12,
    COMPLETE_SYMPTOM = 13,
    NURSE_REPLY = 14,
    PATIENT_REPLY = 15,
    RESEARCH_RESULTS = 16,
    PATIENT_FOLLOW_UP_SUBMIT = 17,
    NURSE_QUESTION = 18,
    AUTOMATED_ANSWER = 19,
    AUTOMATED_ANSWER_INFORMATION = 20,
    CLOSE_CASE = 21,
    VALIDATE_AUTOMATED_ANSWER = 22,
    QUESTIONARY = 23,
    QUESTIONARY_REQUEST = 24,
    QUESTIONARY_ANSWER = 25,
    PATIENT_FEEDBACK = 26,
    SYMPTOM_REPORT_SUBMIT = 27,
    SYMPTOM_INQUIRY_REQUEST = 28,
    CONSULTATION_REQUEST = 29,
    CONSULTATION_REPLY = 30,
    CONSULTATION_CANCEL = 31,
    ANNOUNCEMENT = 32,
    TRANSPORTATION_LODGING = 33,
    HOME_LIFE_NEEDS = 34,
    CARE_TEAM_COMMUNICATION = 35,
    COMMUNICATION_WITH_FAMILY = 36,
    SUPPORTIVE_CARE = 37,
    PATIENT_EDUCATION = 38,
    APPEARANCE_OR_SELF_IMAGE = 39,
    MEDICAL_RECORDS = 40,
    CLINIC_PREPARATION = 41,
    SCHEDULING = 42,
    INSURANCE_AUTHORIZATION_BILLING = 43,
    FINANCIAL_ASSISTANCE = 44,
    ANXIETY_FEAR_DEPRESSION = 45,
    INTERNAL_NOTE = 46,
    CONNECTION_ACCEPTED = 47,
    CONNECTION_REJECTED = 48,
    QUESTIONARY_ALERT = 49,
    EDUCATION_BINDER = 50,
    LAB_RESULT = 51,
    MEDICATION_REFILL = 52,
    CONTACT_INFO_UPDATE = 53,
    NOONA_APP_RELATED_QUESTION = 54,
    RESULTS_OR_LABS = 55,
}

export const AllTopicType = [
    TopicType.NONE,
    TopicType.TREATMENT,
    TopicType.MEDICATION,
    TopicType.PHYSIOTHERAPY,
    TopicType.TREATMENT_SIDE_EFFECT,
    TopicType.FOLLOW_UP_PERIOD,
    TopicType.OTHER_ISSUE,
    TopicType.SYMPTOM_SUBMIT,
    TopicType.FOLLOW_UP_SUBMIT,
    TopicType.EXAMINATION,
    TopicType.INVITATION,
    TopicType.INSTRUCTION,
    TopicType.MONITOR,
    TopicType.COMPLETE_SYMPTOM,
    TopicType.NURSE_REPLY,
    TopicType.PATIENT_REPLY,
    TopicType.RESEARCH_RESULTS,
    TopicType.PATIENT_FOLLOW_UP_SUBMIT,
    TopicType.NURSE_QUESTION,
    TopicType.AUTOMATED_ANSWER,
    TopicType.AUTOMATED_ANSWER_INFORMATION,
    TopicType.CLOSE_CASE,
    TopicType.VALIDATE_AUTOMATED_ANSWER,
    TopicType.QUESTIONARY,
    TopicType.QUESTIONARY_REQUEST,
    TopicType.QUESTIONARY_ANSWER,
    TopicType.PATIENT_FEEDBACK,
    TopicType.SYMPTOM_REPORT_SUBMIT,
    TopicType.SYMPTOM_INQUIRY_REQUEST,
    TopicType.CONSULTATION_REQUEST,
    TopicType.CONSULTATION_REPLY,
    TopicType.CONSULTATION_CANCEL,
    TopicType.ANNOUNCEMENT,
    TopicType.TRANSPORTATION_LODGING,
    TopicType.HOME_LIFE_NEEDS,
    TopicType.CARE_TEAM_COMMUNICATION,
    TopicType.COMMUNICATION_WITH_FAMILY,
    TopicType.SUPPORTIVE_CARE,
    TopicType.PATIENT_EDUCATION,
    TopicType.APPEARANCE_OR_SELF_IMAGE,
    TopicType.MEDICAL_RECORDS,
    TopicType.CLINIC_PREPARATION,
    TopicType.SCHEDULING,
    TopicType.INSURANCE_AUTHORIZATION_BILLING,
    TopicType.FINANCIAL_ASSISTANCE,
    TopicType.ANXIETY_FEAR_DEPRESSION,
    TopicType.INTERNAL_NOTE,
    TopicType.CONNECTION_ACCEPTED,
    TopicType.CONNECTION_REJECTED,
    TopicType.QUESTIONARY_ALERT,
    TopicType.EDUCATION_BINDER,
    TopicType.LAB_RESULT,
    TopicType.MEDICATION_REFILL,
    TopicType.CONTACT_INFO_UPDATE,
    TopicType.NOONA_APP_RELATED_QUESTION,
    TopicType.RESULTS_OR_LABS,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
