// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.audit.model.NoonaLogEntry

import { NoonaLogEntryAction } from './noona-log-entry-action';
import { NoonaLogEntryScope } from './noona-log-entry-scope';
export interface NoonaLogEntry {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    actionDate?: Date | number | string;
    entryScope?: NoonaLogEntryScope;
    action?: NoonaLogEntryAction;
    targetRoles?: string[];
    userEmail?: string;
    targetIdentifier?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
