// Auto generated by ad<PERSON><PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.report.model.TransactionType

import { ProcessType } from './process-type';
export enum TransactionType {
    INSTRUCTION_REQUEST = 'INSTRUCTION_REQUEST',
    INSTRUCTION_RESPONSE = 'INSTRUCTION_RESPONSE',
    FOLLOW_INVITE = 'FOLLOW_INVITE',
    FOLLOW_RESULT = 'FOLLOW_RESULT',
    TEST_INVITE = 'TEST_INVITE',
    TEST_RESULT = 'TEST_RESULT',
    EXAMINATION_INVITE = 'EXAMINATION_INVITE',
    EXAMINATION_RESULT = 'EXAMINATION_RESULT',
    PATIENT_ADD = 'PATIENT_ADD',
    PATIENT_LOGIN_SUCCESS = 'PATIENT_LOGIN_SUCCESS',
    PATIENT_SYMPTOM_ADD = 'PATIENT_SYMPTOM_ADD',
    PATIENT_CASE_CLOSE = 'PATIENT_CASE_CLOSE',
    PATIENT_CASE_OPEN = 'PATIENT_CASE_OPEN',
    CASE_CLOSE = 'CASE_CLOSE',
    QUESTION_RESPONSE = 'QUESTION_RESPONSE',
    PATIENT_INVITATION = 'PATIENT_INVITATION',
    PATIENT_INVITATION_SENT = 'PATIENT_INVITATION_SENT',
    PATIENT_INVITATION_REMINDER = 'PATIENT_INVITATION_REMINDER',
    PATIENT_INVITATION_ACCEPTED = 'PATIENT_INVITATION_ACCEPTED',
    PATIENT_INVITATION_DECLINED = 'PATIENT_INVITATION_DECLINED',
    PATIENT_INVITATION_HANDLING_WARNING = 'PATIENT_INVITATION_HANDLING_WARNING',
    PATIENT_INVITATION_INFORMATION_RECEIVED = 'PATIENT_INVITATION_INFORMATION_RECEIVED',
    PATIENT_INVITATION_LOGIN_REMINDER_SENT = 'PATIENT_INVITATION_LOGIN_REMINDER_SENT',
    PATIENT_INVITATION_EXPIRED = 'PATIENT_INVITATION_EXPIRED',
    PATIENT_ACCOUNT_ACTIVATE = 'PATIENT_ACCOUNT_ACTIVATE',
    PATIENT_ACCOUNT_LOCKED_BY_NURSE = 'PATIENT_ACCOUNT_LOCKED_BY_NURSE',
    PATIENT_ACCOUNT_UNLOCKED_BY_NURSE = 'PATIENT_ACCOUNT_UNLOCKED_BY_NURSE',
    PATIENT_PASSWORD_RESET_REQUEST = 'PATIENT_PASSWORD_RESET_REQUEST',
    PATIENT_PASSWORD_RESET_REQUEST_LOCKED = 'PATIENT_PASSWORD_RESET_REQUEST_LOCKED',
    PATIENT_PASSWORD_RESET_CONFIRM = 'PATIENT_PASSWORD_RESET_CONFIRM',
    SYMPTOM_INQUIRY_SENT = 'SYMPTOM_INQUIRY_SENT',
    SYMPTOM_INQUIRY_OPENED = 'SYMPTOM_INQUIRY_OPENED',
    SYMPTOM_INQUIRY_SUBMITTED = 'SYMPTOM_INQUIRY_SUBMITTED',
    SYMPTOM_INQUIRY_SCHEDULED = 'SYMPTOM_INQUIRY_SCHEDULED',
    SYMPTOM_INQUIRY_READ = 'SYMPTOM_INQUIRY_READ',
    SYMPTOM_INQUIRY_ANSWER_CLOSED = 'SYMPTOM_INQUIRY_ANSWER_CLOSED',
    PATIENT_ACCOUNT_ACTIVATED_BY_NURSE = 'PATIENT_ACCOUNT_ACTIVATED_BY_NURSE',
    PATIENT_READ_MESSAGE = 'PATIENT_READ_MESSAGE',
    PATIENT_LOGIN_TUPAS_ENTERED = 'PATIENT_LOGIN_TUPAS_ENTERED',
    PATIENT_LOGIN_TUPAS_FAILURE = 'PATIENT_LOGIN_TUPAS_FAILURE',
    PATIENT_LOGIN_TUPAS_SUCCESS = 'PATIENT_LOGIN_TUPAS_SUCCESS',
    PATIENT_LOGIN_FAILURE_LOCKED_OUT = 'PATIENT_LOGIN_FAILURE_LOCKED_OUT',
    PATIENT_LOGIN_FAILURE_WRONG_PASSWORD = 'PATIENT_LOGIN_FAILURE_WRONG_PASSWORD',
    PATIENT_LOGIN_FAILURE_NO_SUCH_EMAIL = 'PATIENT_LOGIN_FAILURE_NO_SUCH_EMAIL',
    PATIENT_LOGIN_FAILURE_OTHER = 'PATIENT_LOGIN_FAILURE_OTHER',
    PATIENT_LOGIN_FAILURE_WRONG_ID_CODE = 'PATIENT_LOGIN_FAILURE_WRONG_ID_CODE',
    PATIENT_LOGOUT = 'PATIENT_LOGOUT',
    QUESTIONNAIRE_REMINDER = 'QUESTIONNAIRE_REMINDER',
    QUESTIONNAIRE_SENT = 'QUESTIONNAIRE_SENT',
    SYMPTOM_INQUIRY_REMINDER_SENT = 'SYMPTOM_INQUIRY_REMINDER_SENT',
    NURSE_OPENS_WORK_QUEUE_OPEN_CASES = 'NURSE_OPENS_WORK_QUEUE_OPEN_CASES',
    NURSE_OPENS_WORK_QUEUE_UNVALIDATED_AUTOMATIC_MESSAGES = 'NURSE_OPENS_WORK_QUEUE_UNVALIDATED_AUTOMATIC_MESSAGES',
    NURSE_OPENS_WORK_QUEUE_ARCHIVE = 'NURSE_OPENS_WORK_QUEUE_ARCHIVE',
    NURSE_OPENS_WORK_QUEUE_SYMPTOM_REPORT_MESSAGES = 'NURSE_OPENS_WORK_QUEUE_SYMPTOM_REPORT_MESSAGES',
    NURSE_OPENS_SYMPTOM_ENTRY = 'NURSE_OPENS_SYMPTOM_ENTRY',
    NURSE_USES_CLIPBOARD_COPY_TEXT = 'NURSE_USES_CLIPBOARD_COPY_TEXT',
    PATIENT_PASSWORD_CHANGE_SUCCESS = 'PATIENT_PASSWORD_CHANGE_SUCCESS',
    QUESTIONNAIRE_OPENED = 'QUESTIONNAIRE_OPENED',
    TRIGGER_AEQ_PDF_ADDED = 'TRIGGER_AEQ_PDF_ADDED',
    CONSULTATION_REQUEST_SYMPTOM_USER = 'CONSULTATION_REQUEST_SYMPTOM_USER',
    CONSULTATION_REQUEST_SYMPTOM_CARE_TEAM = 'CONSULTATION_REQUEST_SYMPTOM_CARE_TEAM',
    CONSULTATION_REQUEST_AEQ_USER = 'CONSULTATION_REQUEST_AEQ_USER',
    CONSULTATION_REQUEST_AEQ_CARE_TEAM = 'CONSULTATION_REQUEST_AEQ_CARE_TEAM',
    CONSULTATION_CLOSED_AEQ = 'CONSULTATION_CLOSED_AEQ',
    CONSULTATION_CLOSED_SYMPTOM = 'CONSULTATION_CLOSED_SYMPTOM',
    CONSULTATION_CANCELLED_SYMPTOM = 'CONSULTATION_CANCELLED_SYMPTOM',
    CONSULTATION_CANCELLED_AEQ = 'CONSULTATION_CANCELLED_AEQ',
    CONSULTATION_REPLY = 'CONSULTATION_REPLY',
    PHOTO_UPLOAD_DESKTOP = 'PHOTO_UPLOAD_DESKTOP',
    PHOTO_UPLOAD_MOBILE = 'PHOTO_UPLOAD_MOBILE',
    PHOTO_SCALING_WARNING = 'PHOTO_SCALING_WARNING',
    PHOTO_UPLOAD_CLIENT_WARNING_DESKTOP = 'PHOTO_UPLOAD_CLIENT_WARNING_DESKTOP',
    PHOTO_UPLOAD_CLIENT_WARNING_MOBILE = 'PHOTO_UPLOAD_CLIENT_WARNING_MOBILE',
    PHOTO_DELETE = 'PHOTO_DELETE',
    PHOTO_OPEN_BY_CLINIC_USER = 'PHOTO_OPEN_BY_CLINIC_USER',
    LOGIN_TO_DISABLED_CLINIC = 'LOGIN_TO_DISABLED_CLINIC',
    NURSE_COPY_TO_CLIPBOARD_MESSAGE = 'NURSE_COPY_TO_CLIPBOARD_MESSAGE',
    NURSE_COPY_TO_CLIPBOARD_AEQ = 'NURSE_COPY_TO_CLIPBOARD_AEQ',
    NURSE_COPY_TO_CLIPBOARD_DIARY_ENTRY = 'NURSE_COPY_TO_CLIPBOARD_DIARY_ENTRY',
    TRIGGER_SYMPTOM_PDF_ADDED = 'TRIGGER_SYMPTOM_PDF_ADDED',
    TRIGGER_MESSAGE_PDF_ADDED = 'TRIGGER_MESSAGE_PDF_ADDED',
    NURSE_DIRECT_MESSAGE_TO_PATIENT = 'NURSE_DIRECT_MESSAGE_TO_PATIENT',
    PATIENT_ACTIVATION_WELCOME_NEXT = 'PATIENT_ACTIVATION_WELCOME_NEXT',
    PATIENT_ACTIVATION_EMAIL_NEXT = 'PATIENT_ACTIVATION_EMAIL_NEXT',
    PATIENT_ACTIVATION_EMAIL_FAILURE_INVALID_EMAIL = 'PATIENT_ACTIVATION_EMAIL_FAILURE_INVALID_EMAIL',
    PATIENT_ACTIVATION_EMAIL_FAILURE_INVALID_CONFIRM = 'PATIENT_ACTIVATION_EMAIL_FAILURE_INVALID_CONFIRM',
    PATIENT_ACTIVATION_PASSWORD_FAILURE = 'PATIENT_ACTIVATION_PASSWORD_FAILURE',
    PATIENT_ACTIVATION_PASSWORD_AND_EMAIL_UPDATED = 'PATIENT_ACTIVATION_PASSWORD_AND_EMAIL_UPDATED',
    PATIENT_ACTIVATION_COMPLETE_NEXT = 'PATIENT_ACTIVATION_COMPLETE_NEXT',
    PATIENT_ACTIVATION_REMEMBER_ME_YES = 'PATIENT_ACTIVATION_REMEMBER_ME_YES',
    PATIENT_ACTIVATION_REMEMBER_ME_NO = 'PATIENT_ACTIVATION_REMEMBER_ME_NO',
    PATIENT_ACTIVATION_CLINIC_INFORMATION_ENTER = 'PATIENT_ACTIVATION_CLINIC_INFORMATION_ENTER',
    PATIENT_ACTIVATION_CLINIC_INFORMATION_NEXT = 'PATIENT_ACTIVATION_CLINIC_INFORMATION_NEXT',
    PATIENT_ACTIVATION_EXPIRED_EMAIL_SENT = 'PATIENT_ACTIVATION_EXPIRED_EMAIL_SENT',
    PATIENT_ACTIVATION_EXPIRED_SMS_SENT = 'PATIENT_ACTIVATION_EXPIRED_SMS_SENT',
    PATIENT_ACTIVATION_PASSWORD_UPDATED = 'PATIENT_ACTIVATION_PASSWORD_UPDATED',
    PATIENT_ACTIVATION_TERMS_AND_CONDITIONS_NEXT = 'PATIENT_ACTIVATION_TERMS_AND_CONDITIONS_NEXT',
    PATIENT_ACTIVATION_PATIENT_CONSENT_NEXT = 'PATIENT_ACTIVATION_PATIENT_CONSENT_NEXT',
    PATIENT_VALUE_ADD = 'PATIENT_VALUE_ADD',
    PATIENT_WELLNESS_INQUIRY_SENT = 'PATIENT_WELLNESS_INQUIRY_SENT',
    PATIENT_VALUE_ADD_WEIGHT = 'PATIENT_VALUE_ADD_WEIGHT',
    PATIENT_VALUE_ADD_DISTRESS = 'PATIENT_VALUE_ADD_DISTRESS',
    PATIENT_VALUE_ADD_NO_SYMPTOM = 'PATIENT_VALUE_ADD_NO_SYMPTOM',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_SENT = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_SENT',
    PATIENT_INVITATION_UNEXPECTED_WARNING = 'PATIENT_INVITATION_UNEXPECTED_WARNING',
    LOGIN_NATIVE_PIN_CODE_SET = 'LOGIN_NATIVE_PIN_CODE_SET',
    LOGIN_NATIVE_PIN_CODE_SKIPPED = 'LOGIN_NATIVE_PIN_CODE_SKIPPED',
    PATIENT_DIARY_ADD_ANOTHER_ENTRY = 'PATIENT_DIARY_ADD_ANOTHER_ENTRY',
    PATIENT_DIARY_ADD_VIEW_DIARY_FROM_WELLNESS = 'PATIENT_DIARY_ADD_VIEW_DIARY_FROM_WELLNESS',
    PATIENT_DIARY_ADD_ANOTHER_SYMPTOM = 'PATIENT_DIARY_ADD_ANOTHER_SYMPTOM',
    PATIENT_DIARY_VIEW_DIARY_FROM_SYMPTOMS = 'PATIENT_DIARY_VIEW_DIARY_FROM_SYMPTOMS',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_SENT_SYMPTOM = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_SENT_SYMPTOM',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_SENT_ACTIONLESS = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_SENT_ACTIONLESS',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_OPEN_SYMPTOM = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_OPEN_SYMPTOM',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_OPEN_ACTIONLESS = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_OPEN_ACTIONLESS',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_SYMPTOM_SYMPTOM = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_SYMPTOM_SYMPTOM',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_SYMPTOM_ACTIONLESS = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_SYMPTOM_ACTIONLESS',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_WEIGHT_SYMPTOM = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_WEIGHT_SYMPTOM',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_WEIGHT_ACTIONLESS = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_WEIGHT_ACTIONLESS',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_ANXIETY_SYMPTOM = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_ANXIETY_SYMPTOM',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_ANXIETY_ACTIONLESS = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_ANXIETY_ACTIONLESS',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_NO_SYMPTOM_SYMPTOM = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_NO_SYMPTOM_SYMPTOM',
    PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_NO_SYMPTOM_ACTIONLESS = 'PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_NO_SYMPTOM_ACTIONLESS',
    LOGIN_NATIVE_PIN_CODE_FAILURE = 'LOGIN_NATIVE_PIN_CODE_FAILURE',
    LOGIN_NATIVE_PIN_CODE_RESET = 'LOGIN_NATIVE_PIN_CODE_RESET',
    SUPPORT_RESPONSE_CHAT_IN_ACTIVE = 'SUPPORT_RESPONSE_CHAT_IN_ACTIVE',
    USER_TWO_FACTOR_AUTHENTICATION_REQUIRED = 'USER_TWO_FACTOR_AUTHENTICATION_REQUIRED',
    USER_TWO_FACTOR_AUTHENTICATION_SUCCESS = 'USER_TWO_FACTOR_AUTHENTICATION_SUCCESS',
    SCHEDULED_MESSAGE_SENT = 'SCHEDULED_MESSAGE_SENT',
    SCHEDULED_MESSAGE_READ = 'SCHEDULED_MESSAGE_READ',
    SMART_HP_TRACKING_FRONTEND_SHOWED = 'SMART_HP_TRACKING_FRONTEND_SHOWED',
    SMART_HP_TRACKING_FRONTEND_STARTED = 'SMART_HP_TRACKING_FRONTEND_STARTED',
    SMART_HP_TRACKING_FRONTEND_CONTINUED = 'SMART_HP_TRACKING_FRONTEND_CONTINUED',
    SMART_HP_TRACKING_FRONTEND_DROPOUT = 'SMART_HP_TRACKING_FRONTEND_DROPOUT',
    SMART_HP_TRACKING_FRONTEND_SKIPPED = 'SMART_HP_TRACKING_FRONTEND_SKIPPED',
    SMART_HP_TRACKING_FRONTEND_FINISHED = 'SMART_HP_TRACKING_FRONTEND_FINISHED',
    SMART_HP_TRACKING_FRONTEND_SELECTION_SHOWN = 'SMART_HP_TRACKING_FRONTEND_SELECTION_SHOWN',
    SMART_HP_TRACKING_FRONTEND_SELECTION_NONE = 'SMART_HP_TRACKING_FRONTEND_SELECTION_NONE',
    SMART_HP_TRACKING_FRONTEND_SELECTION_DONE = 'SMART_HP_TRACKING_FRONTEND_SELECTION_DONE',
    SMART_HP_TRACKING_FRONTEND_SYMPTOM_CHANGED = 'SMART_HP_TRACKING_FRONTEND_SYMPTOM_CHANGED',
    SMART_HP_TRACKING_FRONTEND_SYMPTOM_STAYED = 'SMART_HP_TRACKING_FRONTEND_SYMPTOM_STAYED',
    SMART_HP_TRACKING_FRONTEND_SYMPTOM_ALERT = 'SMART_HP_TRACKING_FRONTEND_SYMPTOM_ALERT',
    SMART_HP_TRACKING_FRONTEND_FOLLOWUP_SHOWED = 'SMART_HP_TRACKING_FRONTEND_FOLLOWUP_SHOWED',
    SMART_HP_TRACKING_FRONTEND_FOLLOWUP_OTHER_SYMPTOMS = 'SMART_HP_TRACKING_FRONTEND_FOLLOWUP_OTHER_SYMPTOMS',
    SMART_HP_TRACKING_FRONTEND_FOLLOWUP_NOOTHER_SYMPTOMS = 'SMART_HP_TRACKING_FRONTEND_FOLLOWUP_NOOTHER_SYMPTOMS',
    SMART_HP_TRACKING_FRONTEND_FOLLOWUP_SYMPTOMFREE = 'SMART_HP_TRACKING_FRONTEND_FOLLOWUP_SYMPTOMFREE',
    PATIENT_ACTIVATION_SERVICE_CONSENT_NEXT = 'PATIENT_ACTIVATION_SERVICE_CONSENT_NEXT',
    NURSE_COPY_TO_CLIPBOARD_QUESTIONNAIRE = 'NURSE_COPY_TO_CLIPBOARD_QUESTIONNAIRE',
    INITIALIZE_INTEGRATION_PATIENT = 'INITIALIZE_INTEGRATION_PATIENT',
    INVITE_INTEGRATION_PATIENT = 'INVITE_INTEGRATION_PATIENT',
    ACTIVATE_INTEGRATION_PATIENT = 'ACTIVATE_INTEGRATION_PATIENT',
    TRIGGER_DATA_EXTRACT_PDF_ADDED = 'TRIGGER_DATA_EXTRACT_PDF_ADDED',
    CLINIC_CONNECTION_REQUEST_CREATED = 'CLINIC_CONNECTION_REQUEST_CREATED',
    CLINIC_CONNECTION_REQUEST_CLINIC_CANCELED = 'CLINIC_CONNECTION_REQUEST_CLINIC_CANCELED',
    CLINIC_CONNECTION_REQUEST_PATIENT_REJECTED = 'CLINIC_CONNECTION_REQUEST_PATIENT_REJECTED',
    CLINIC_CONNECTION_REQUEST_PATIENT_ACCEPTED = 'CLINIC_CONNECTION_REQUEST_PATIENT_ACCEPTED',
    SMART_HP_TRACKING_FRONTEND_STARTED_FROM_NOTIFICATION = 'SMART_HP_TRACKING_FRONTEND_STARTED_FROM_NOTIFICATION',
    PATIENT_SIGNUP_PASSWORD_FAILURE = 'PATIENT_SIGNUP_PASSWORD_FAILURE',
    PATIENT_SIGNUP_REMEMBER_ME_YES = 'PATIENT_SIGNUP_REMEMBER_ME_YES',
    PATIENT_SIGNUP_REMEMBER_ME_NO = 'PATIENT_SIGNUP_REMEMBER_ME_NO',
    PATIENT_SIGNUP_WELCOME_NEXT = 'PATIENT_SIGNUP_WELCOME_NEXT',
    PATIENT_TIMELINE_EVENT_ADD = 'PATIENT_TIMELINE_EVENT_ADD',
    PATIENT_TIMELINE_NOTE_ADD = 'PATIENT_TIMELINE_NOTE_ADD',
    PATIENT_QUESTIONNAIRE_SUBMITTED = 'PATIENT_QUESTIONNAIRE_SUBMITTED',
    PATIENT_QUESTIONNAIRE_READ = 'PATIENT_QUESTIONNAIRE_READ',
    PATIENT_REREAD_MESSAGE = 'PATIENT_REREAD_MESSAGE',
    CLINIC_CONNECTION_REQUEST_RETRY = 'CLINIC_CONNECTION_REQUEST_RETRY',
    PATIENT_TIMELINE_EVENT_OPEN = 'PATIENT_TIMELINE_EVENT_OPEN',
    PATIENT_TIMELINE_EVENT_EDIT = 'PATIENT_TIMELINE_EVENT_EDIT',
    PATIENT_TIMELINE_EVENT_SAVE = 'PATIENT_TIMELINE_EVENT_SAVE',
    PATIENT_TIMELINE_NOTE_OPEN = 'PATIENT_TIMELINE_NOTE_OPEN',
    PATIENT_TIMELINE_NOTE_EDIT = 'PATIENT_TIMELINE_NOTE_EDIT',
    PATIENT_TIMELINE_WEEKLYSUMMARY_NOTE_OPEN = 'PATIENT_TIMELINE_WEEKLYSUMMARY_NOTE_OPEN',
    PATIENT_TIMELINE_SYMPTOM_OPEN = 'PATIENT_TIMELINE_SYMPTOM_OPEN',
    PATIENT_TIMELINE_QUESTIONNAIRE_OPEN = 'PATIENT_TIMELINE_QUESTIONNAIRE_OPEN',
    PATIENT_TIMELINE_SYMPTOM_INQUIRY_OPEN = 'PATIENT_TIMELINE_SYMPTOM_INQUIRY_OPEN',
    PATIENT_TIMELINE_SYMPTOM_INQUIRY_ANSWER_OPEN = 'PATIENT_TIMELINE_SYMPTOM_INQUIRY_ANSWER_OPEN',
    PATIENT_TIMELINE_QUESTIONNAIRE_INQUIRY_OPEN = 'PATIENT_TIMELINE_QUESTIONNAIRE_INQUIRY_OPEN',
    PATIENT_TIMELINE_QUESTIONNAIRE_INQUIRY_ANSWER_OPEN = 'PATIENT_TIMELINE_QUESTIONNAIRE_INQUIRY_ANSWER_OPEN',
    PATIENT_TIMELINE_UPCOMING_EVENTS_EXPAND = 'PATIENT_TIMELINE_UPCOMING_EVENTS_EXPAND',
    PATIENT_CONSENT_SHOWN = 'PATIENT_CONSENT_SHOWN',
    NURSE_OPENS_WORK_QUEUE_UNANSWERED_INQUIRIES = 'NURSE_OPENS_WORK_QUEUE_UNANSWERED_INQUIRIES',
    ADD_INTEGRATION_PATIENT_WITH_MODULE = 'ADD_INTEGRATION_PATIENT_WITH_MODULE',
    UPDATE_INTEGRATION_PATIENT_WITH_MODULE = 'UPDATE_INTEGRATION_PATIENT_WITH_MODULE',
    CLINIC_MANAGER_DELETED_CASE = 'CLINIC_MANAGER_DELETED_CASE',
    TRIGGER_CASE_PDF_ADDED = 'TRIGGER_CASE_PDF_ADDED',
    DECLINED_TO_ANSWER = 'DECLINED_TO_ANSWER',
    TRIGGER_QOL_QUESTIONNAIRE_PDF_ADDED = 'TRIGGER_QOL_QUESTIONNAIRE_PDF_ADDED',
    ADD_INTEGRATION_DISTRESS_SCREENING = 'ADD_INTEGRATION_DISTRESS_SCREENING',
    REMOVE_INTEGRATION_DISTRESS_SCREENING = 'REMOVE_INTEGRATION_DISTRESS_SCREENING',
    TRIGGER_CCD_DOWNLOAD_ZIP = 'TRIGGER_CCD_DOWNLOAD_ZIP',
    PATIENT_DELEGATE_ADD = 'PATIENT_DELEGATE_ADD',
    PATIENT_DELEGATE_ACTIVATION_REMEMBER_ME_YES = 'PATIENT_DELEGATE_ACTIVATION_REMEMBER_ME_YES',
    PATIENT_DELEGATE_ACTIVATION_REMEMBER_ME_NO = 'PATIENT_DELEGATE_ACTIVATION_REMEMBER_ME_NO',
    PATIENT_DELEGATE_ACTIVATION_PASSWORD_FAILURE = 'PATIENT_DELEGATE_ACTIVATION_PASSWORD_FAILURE',
    PATIENT_DELEGATE_ACTIVATION_WELCOME_NEXT = 'PATIENT_DELEGATE_ACTIVATION_WELCOME_NEXT',
    PATIENT_DELEGATE_ACTIVATION_COMPLETE_NEXT = 'PATIENT_DELEGATE_ACTIVATION_COMPLETE_NEXT',
    PATIENT_DELEGATE_PASSWORD_RESET_REQUEST = 'PATIENT_DELEGATE_PASSWORD_RESET_REQUEST',
    PATIENT_LIBRARY_LATEST_LAB_REPORT_OPEN = 'PATIENT_LIBRARY_LATEST_LAB_REPORT_OPEN',
    PATIENT_LAB_REPORT_OBSERVATION_OPEN = 'PATIENT_LAB_REPORT_OBSERVATION_OPEN',
    ADD_INTEGRATION_APPOINTMENT = 'ADD_INTEGRATION_APPOINTMENT',
    REMOVE_INTEGRATION_APPOINTMENT = 'REMOVE_INTEGRATION_APPOINTMENT',
    LAB_REPORT_ADDED = 'LAB_REPORT_ADDED',
    LAB_REPORT_UPDATED = 'LAB_REPORT_UPDATED',
    PATIENT_TIMELINE_APPOINTMENT_OPEN = 'PATIENT_TIMELINE_APPOINTMENT_OPEN',
    ADD_INTEGRATION_QUESTIONNAIRE_INQUIRY = 'ADD_INTEGRATION_QUESTIONNAIRE_INQUIRY',
    ADD_INTEGRATION_SYMPTOM_INQUIRY = 'ADD_INTEGRATION_SYMPTOM_INQUIRY',
    ADD_INTEGRATION_TIMELINE = 'ADD_INTEGRATION_TIMELINE',
    REMOVE_INTEGRATION_QUESTIONNAIRE_INQUIRY = 'REMOVE_INTEGRATION_QUESTIONNAIRE_INQUIRY',
    REMOVE_INTEGRATION_SYMPTOM_INQUIRY = 'REMOVE_INTEGRATION_SYMPTOM_INQUIRY',
    REMOVE_INTEGRATION_TIMELINE = 'REMOVE_INTEGRATION_TIMELINE',
    LAB_REPORT_DELETED = 'LAB_REPORT_DELETED',
    QUESTIONARY_START_ANSWER_FROM_PATIENT_APP = 'QUESTIONARY_START_ANSWER_FROM_PATIENT_APP',
    SYMPTOMINQURY_START_ANSWER_FROM_PATIENT_APP = 'SYMPTOMINQURY_START_ANSWER_FROM_PATIENT_APP',
    QUESTIONARY_START_ANSWER_FROM_CLINIC_APP_BY_PATIENT = 'QUESTIONARY_START_ANSWER_FROM_CLINIC_APP_BY_PATIENT',
    SYMPTOMINQURY_START_ANSWER_FROM_CLINIC_APP_BY_NURSE = 'SYMPTOMINQURY_START_ANSWER_FROM_CLINIC_APP_BY_NURSE',
    QUESTIONARY_START_ANSWER_FROM_CLINIC_APP_BY_NURSE = 'QUESTIONARY_START_ANSWER_FROM_CLINIC_APP_BY_NURSE',
}

export const AllTransactionType = [
    TransactionType.INSTRUCTION_REQUEST,
    TransactionType.INSTRUCTION_RESPONSE,
    TransactionType.FOLLOW_INVITE,
    TransactionType.FOLLOW_RESULT,
    TransactionType.TEST_INVITE,
    TransactionType.TEST_RESULT,
    TransactionType.EXAMINATION_INVITE,
    TransactionType.EXAMINATION_RESULT,
    TransactionType.PATIENT_ADD,
    TransactionType.PATIENT_LOGIN_SUCCESS,
    TransactionType.PATIENT_SYMPTOM_ADD,
    TransactionType.PATIENT_CASE_CLOSE,
    TransactionType.PATIENT_CASE_OPEN,
    TransactionType.CASE_CLOSE,
    TransactionType.QUESTION_RESPONSE,
    TransactionType.PATIENT_INVITATION,
    TransactionType.PATIENT_INVITATION_SENT,
    TransactionType.PATIENT_INVITATION_REMINDER,
    TransactionType.PATIENT_INVITATION_ACCEPTED,
    TransactionType.PATIENT_INVITATION_DECLINED,
    TransactionType.PATIENT_INVITATION_HANDLING_WARNING,
    TransactionType.PATIENT_INVITATION_INFORMATION_RECEIVED,
    TransactionType.PATIENT_INVITATION_LOGIN_REMINDER_SENT,
    TransactionType.PATIENT_INVITATION_EXPIRED,
    TransactionType.PATIENT_ACCOUNT_ACTIVATE,
    TransactionType.PATIENT_ACCOUNT_LOCKED_BY_NURSE,
    TransactionType.PATIENT_ACCOUNT_UNLOCKED_BY_NURSE,
    TransactionType.PATIENT_PASSWORD_RESET_REQUEST,
    TransactionType.PATIENT_PASSWORD_RESET_REQUEST_LOCKED,
    TransactionType.PATIENT_PASSWORD_RESET_CONFIRM,
    TransactionType.SYMPTOM_INQUIRY_SENT,
    TransactionType.SYMPTOM_INQUIRY_OPENED,
    TransactionType.SYMPTOM_INQUIRY_SUBMITTED,
    TransactionType.SYMPTOM_INQUIRY_SCHEDULED,
    TransactionType.SYMPTOM_INQUIRY_READ,
    TransactionType.SYMPTOM_INQUIRY_ANSWER_CLOSED,
    TransactionType.PATIENT_ACCOUNT_ACTIVATED_BY_NURSE,
    TransactionType.PATIENT_READ_MESSAGE,
    TransactionType.PATIENT_LOGIN_TUPAS_ENTERED,
    TransactionType.PATIENT_LOGIN_TUPAS_FAILURE,
    TransactionType.PATIENT_LOGIN_TUPAS_SUCCESS,
    TransactionType.PATIENT_LOGIN_FAILURE_LOCKED_OUT,
    TransactionType.PATIENT_LOGIN_FAILURE_WRONG_PASSWORD,
    TransactionType.PATIENT_LOGIN_FAILURE_NO_SUCH_EMAIL,
    TransactionType.PATIENT_LOGIN_FAILURE_OTHER,
    TransactionType.PATIENT_LOGIN_FAILURE_WRONG_ID_CODE,
    TransactionType.PATIENT_LOGOUT,
    TransactionType.QUESTIONNAIRE_REMINDER,
    TransactionType.QUESTIONNAIRE_SENT,
    TransactionType.SYMPTOM_INQUIRY_REMINDER_SENT,
    TransactionType.NURSE_OPENS_WORK_QUEUE_OPEN_CASES,
    TransactionType.NURSE_OPENS_WORK_QUEUE_UNVALIDATED_AUTOMATIC_MESSAGES,
    TransactionType.NURSE_OPENS_WORK_QUEUE_ARCHIVE,
    TransactionType.NURSE_OPENS_WORK_QUEUE_SYMPTOM_REPORT_MESSAGES,
    TransactionType.NURSE_OPENS_SYMPTOM_ENTRY,
    TransactionType.NURSE_USES_CLIPBOARD_COPY_TEXT,
    TransactionType.PATIENT_PASSWORD_CHANGE_SUCCESS,
    TransactionType.QUESTIONNAIRE_OPENED,
    TransactionType.TRIGGER_AEQ_PDF_ADDED,
    TransactionType.CONSULTATION_REQUEST_SYMPTOM_USER,
    TransactionType.CONSULTATION_REQUEST_SYMPTOM_CARE_TEAM,
    TransactionType.CONSULTATION_REQUEST_AEQ_USER,
    TransactionType.CONSULTATION_REQUEST_AEQ_CARE_TEAM,
    TransactionType.CONSULTATION_CLOSED_AEQ,
    TransactionType.CONSULTATION_CLOSED_SYMPTOM,
    TransactionType.CONSULTATION_CANCELLED_SYMPTOM,
    TransactionType.CONSULTATION_CANCELLED_AEQ,
    TransactionType.CONSULTATION_REPLY,
    TransactionType.PHOTO_UPLOAD_DESKTOP,
    TransactionType.PHOTO_UPLOAD_MOBILE,
    TransactionType.PHOTO_SCALING_WARNING,
    TransactionType.PHOTO_UPLOAD_CLIENT_WARNING_DESKTOP,
    TransactionType.PHOTO_UPLOAD_CLIENT_WARNING_MOBILE,
    TransactionType.PHOTO_DELETE,
    TransactionType.PHOTO_OPEN_BY_CLINIC_USER,
    TransactionType.LOGIN_TO_DISABLED_CLINIC,
    TransactionType.NURSE_COPY_TO_CLIPBOARD_MESSAGE,
    TransactionType.NURSE_COPY_TO_CLIPBOARD_AEQ,
    TransactionType.NURSE_COPY_TO_CLIPBOARD_DIARY_ENTRY,
    TransactionType.TRIGGER_SYMPTOM_PDF_ADDED,
    TransactionType.TRIGGER_MESSAGE_PDF_ADDED,
    TransactionType.NURSE_DIRECT_MESSAGE_TO_PATIENT,
    TransactionType.PATIENT_ACTIVATION_WELCOME_NEXT,
    TransactionType.PATIENT_ACTIVATION_EMAIL_NEXT,
    TransactionType.PATIENT_ACTIVATION_EMAIL_FAILURE_INVALID_EMAIL,
    TransactionType.PATIENT_ACTIVATION_EMAIL_FAILURE_INVALID_CONFIRM,
    TransactionType.PATIENT_ACTIVATION_PASSWORD_FAILURE,
    TransactionType.PATIENT_ACTIVATION_PASSWORD_AND_EMAIL_UPDATED,
    TransactionType.PATIENT_ACTIVATION_COMPLETE_NEXT,
    TransactionType.PATIENT_ACTIVATION_REMEMBER_ME_YES,
    TransactionType.PATIENT_ACTIVATION_REMEMBER_ME_NO,
    TransactionType.PATIENT_ACTIVATION_CLINIC_INFORMATION_ENTER,
    TransactionType.PATIENT_ACTIVATION_CLINIC_INFORMATION_NEXT,
    TransactionType.PATIENT_ACTIVATION_EXPIRED_EMAIL_SENT,
    TransactionType.PATIENT_ACTIVATION_EXPIRED_SMS_SENT,
    TransactionType.PATIENT_ACTIVATION_PASSWORD_UPDATED,
    TransactionType.PATIENT_ACTIVATION_TERMS_AND_CONDITIONS_NEXT,
    TransactionType.PATIENT_ACTIVATION_PATIENT_CONSENT_NEXT,
    TransactionType.PATIENT_VALUE_ADD,
    TransactionType.PATIENT_WELLNESS_INQUIRY_SENT,
    TransactionType.PATIENT_VALUE_ADD_WEIGHT,
    TransactionType.PATIENT_VALUE_ADD_DISTRESS,
    TransactionType.PATIENT_VALUE_ADD_NO_SYMPTOM,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_SENT,
    TransactionType.PATIENT_INVITATION_UNEXPECTED_WARNING,
    TransactionType.LOGIN_NATIVE_PIN_CODE_SET,
    TransactionType.LOGIN_NATIVE_PIN_CODE_SKIPPED,
    TransactionType.PATIENT_DIARY_ADD_ANOTHER_ENTRY,
    TransactionType.PATIENT_DIARY_ADD_VIEW_DIARY_FROM_WELLNESS,
    TransactionType.PATIENT_DIARY_ADD_ANOTHER_SYMPTOM,
    TransactionType.PATIENT_DIARY_VIEW_DIARY_FROM_SYMPTOMS,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_SENT_SYMPTOM,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_SENT_ACTIONLESS,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_OPEN_SYMPTOM,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_OPEN_ACTIONLESS,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_SYMPTOM_SYMPTOM,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_SYMPTOM_ACTIONLESS,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_WEIGHT_SYMPTOM,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_WEIGHT_ACTIONLESS,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_ANXIETY_SYMPTOM,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_ANXIETY_ACTIONLESS,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_NO_SYMPTOM_SYMPTOM,
    TransactionType.PATIENT_SMART_SYMPTOM_NOTIFICATION_TRACK_NO_SYMPTOM_ACTIONLESS,
    TransactionType.LOGIN_NATIVE_PIN_CODE_FAILURE,
    TransactionType.LOGIN_NATIVE_PIN_CODE_RESET,
    TransactionType.SUPPORT_RESPONSE_CHAT_IN_ACTIVE,
    TransactionType.USER_TWO_FACTOR_AUTHENTICATION_REQUIRED,
    TransactionType.USER_TWO_FACTOR_AUTHENTICATION_SUCCESS,
    TransactionType.SCHEDULED_MESSAGE_SENT,
    TransactionType.SCHEDULED_MESSAGE_READ,
    TransactionType.SMART_HP_TRACKING_FRONTEND_SHOWED,
    TransactionType.SMART_HP_TRACKING_FRONTEND_STARTED,
    TransactionType.SMART_HP_TRACKING_FRONTEND_CONTINUED,
    TransactionType.SMART_HP_TRACKING_FRONTEND_DROPOUT,
    TransactionType.SMART_HP_TRACKING_FRONTEND_SKIPPED,
    TransactionType.SMART_HP_TRACKING_FRONTEND_FINISHED,
    TransactionType.SMART_HP_TRACKING_FRONTEND_SELECTION_SHOWN,
    TransactionType.SMART_HP_TRACKING_FRONTEND_SELECTION_NONE,
    TransactionType.SMART_HP_TRACKING_FRONTEND_SELECTION_DONE,
    TransactionType.SMART_HP_TRACKING_FRONTEND_SYMPTOM_CHANGED,
    TransactionType.SMART_HP_TRACKING_FRONTEND_SYMPTOM_STAYED,
    TransactionType.SMART_HP_TRACKING_FRONTEND_SYMPTOM_ALERT,
    TransactionType.SMART_HP_TRACKING_FRONTEND_FOLLOWUP_SHOWED,
    TransactionType.SMART_HP_TRACKING_FRONTEND_FOLLOWUP_OTHER_SYMPTOMS,
    TransactionType.SMART_HP_TRACKING_FRONTEND_FOLLOWUP_NOOTHER_SYMPTOMS,
    TransactionType.SMART_HP_TRACKING_FRONTEND_FOLLOWUP_SYMPTOMFREE,
    TransactionType.PATIENT_ACTIVATION_SERVICE_CONSENT_NEXT,
    TransactionType.NURSE_COPY_TO_CLIPBOARD_QUESTIONNAIRE,
    TransactionType.INITIALIZE_INTEGRATION_PATIENT,
    TransactionType.INVITE_INTEGRATION_PATIENT,
    TransactionType.ACTIVATE_INTEGRATION_PATIENT,
    TransactionType.TRIGGER_DATA_EXTRACT_PDF_ADDED,
    TransactionType.CLINIC_CONNECTION_REQUEST_CREATED,
    TransactionType.CLINIC_CONNECTION_REQUEST_CLINIC_CANCELED,
    TransactionType.CLINIC_CONNECTION_REQUEST_PATIENT_REJECTED,
    TransactionType.CLINIC_CONNECTION_REQUEST_PATIENT_ACCEPTED,
    TransactionType.SMART_HP_TRACKING_FRONTEND_STARTED_FROM_NOTIFICATION,
    TransactionType.PATIENT_SIGNUP_PASSWORD_FAILURE,
    TransactionType.PATIENT_SIGNUP_REMEMBER_ME_YES,
    TransactionType.PATIENT_SIGNUP_REMEMBER_ME_NO,
    TransactionType.PATIENT_SIGNUP_WELCOME_NEXT,
    TransactionType.PATIENT_TIMELINE_EVENT_ADD,
    TransactionType.PATIENT_TIMELINE_NOTE_ADD,
    TransactionType.PATIENT_QUESTIONNAIRE_SUBMITTED,
    TransactionType.PATIENT_QUESTIONNAIRE_READ,
    TransactionType.PATIENT_REREAD_MESSAGE,
    TransactionType.CLINIC_CONNECTION_REQUEST_RETRY,
    TransactionType.PATIENT_TIMELINE_EVENT_OPEN,
    TransactionType.PATIENT_TIMELINE_EVENT_EDIT,
    TransactionType.PATIENT_TIMELINE_EVENT_SAVE,
    TransactionType.PATIENT_TIMELINE_NOTE_OPEN,
    TransactionType.PATIENT_TIMELINE_NOTE_EDIT,
    TransactionType.PATIENT_TIMELINE_WEEKLYSUMMARY_NOTE_OPEN,
    TransactionType.PATIENT_TIMELINE_SYMPTOM_OPEN,
    TransactionType.PATIENT_TIMELINE_QUESTIONNAIRE_OPEN,
    TransactionType.PATIENT_TIMELINE_SYMPTOM_INQUIRY_OPEN,
    TransactionType.PATIENT_TIMELINE_SYMPTOM_INQUIRY_ANSWER_OPEN,
    TransactionType.PATIENT_TIMELINE_QUESTIONNAIRE_INQUIRY_OPEN,
    TransactionType.PATIENT_TIMELINE_QUESTIONNAIRE_INQUIRY_ANSWER_OPEN,
    TransactionType.PATIENT_TIMELINE_UPCOMING_EVENTS_EXPAND,
    TransactionType.PATIENT_CONSENT_SHOWN,
    TransactionType.NURSE_OPENS_WORK_QUEUE_UNANSWERED_INQUIRIES,
    TransactionType.ADD_INTEGRATION_PATIENT_WITH_MODULE,
    TransactionType.UPDATE_INTEGRATION_PATIENT_WITH_MODULE,
    TransactionType.CLINIC_MANAGER_DELETED_CASE,
    TransactionType.TRIGGER_CASE_PDF_ADDED,
    TransactionType.DECLINED_TO_ANSWER,
    TransactionType.TRIGGER_QOL_QUESTIONNAIRE_PDF_ADDED,
    TransactionType.ADD_INTEGRATION_DISTRESS_SCREENING,
    TransactionType.REMOVE_INTEGRATION_DISTRESS_SCREENING,
    TransactionType.TRIGGER_CCD_DOWNLOAD_ZIP,
    TransactionType.PATIENT_DELEGATE_ADD,
    TransactionType.PATIENT_DELEGATE_ACTIVATION_REMEMBER_ME_YES,
    TransactionType.PATIENT_DELEGATE_ACTIVATION_REMEMBER_ME_NO,
    TransactionType.PATIENT_DELEGATE_ACTIVATION_PASSWORD_FAILURE,
    TransactionType.PATIENT_DELEGATE_ACTIVATION_WELCOME_NEXT,
    TransactionType.PATIENT_DELEGATE_ACTIVATION_COMPLETE_NEXT,
    TransactionType.PATIENT_DELEGATE_PASSWORD_RESET_REQUEST,
    TransactionType.PATIENT_LIBRARY_LATEST_LAB_REPORT_OPEN,
    TransactionType.PATIENT_LAB_REPORT_OBSERVATION_OPEN,
    TransactionType.ADD_INTEGRATION_APPOINTMENT,
    TransactionType.REMOVE_INTEGRATION_APPOINTMENT,
    TransactionType.LAB_REPORT_ADDED,
    TransactionType.LAB_REPORT_UPDATED,
    TransactionType.PATIENT_TIMELINE_APPOINTMENT_OPEN,
    TransactionType.ADD_INTEGRATION_QUESTIONNAIRE_INQUIRY,
    TransactionType.ADD_INTEGRATION_SYMPTOM_INQUIRY,
    TransactionType.ADD_INTEGRATION_TIMELINE,
    TransactionType.REMOVE_INTEGRATION_QUESTIONNAIRE_INQUIRY,
    TransactionType.REMOVE_INTEGRATION_SYMPTOM_INQUIRY,
    TransactionType.REMOVE_INTEGRATION_TIMELINE,
    TransactionType.LAB_REPORT_DELETED,
    TransactionType.QUESTIONARY_START_ANSWER_FROM_PATIENT_APP,
    TransactionType.SYMPTOMINQURY_START_ANSWER_FROM_PATIENT_APP,
    TransactionType.QUESTIONARY_START_ANSWER_FROM_CLINIC_APP_BY_PATIENT,
    TransactionType.SYMPTOMINQURY_START_ANSWER_FROM_CLINIC_APP_BY_NURSE,
    TransactionType.QUESTIONARY_START_ANSWER_FROM_CLINIC_APP_BY_NURSE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
