// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.content.model.EnvironmentLocalisationType

export enum EnvironmentLocalisationType {
    NURSE_SUPPORT_INFORMATION = 'nurseSupportInformation',
}

export const AllEnvironmentLocalisationType = [EnvironmentLocalisationType.NURSE_SUPPORT_INFORMATION];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
