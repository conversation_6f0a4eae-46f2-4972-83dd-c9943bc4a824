// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.labsview.dto.ReportDto

import { ObservationDto } from './observation-dto';
export interface ReportDto {
    reportId?: string;
    reportName?: string;
    observations?: ObservationDto[];
    visitDate?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
