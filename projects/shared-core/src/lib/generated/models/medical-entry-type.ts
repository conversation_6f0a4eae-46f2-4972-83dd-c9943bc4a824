// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.medical.MedicalEntryType

export enum MedicalEntryType {
    OTHER = 'other',
    BREAST_CANCER_DIAGNOSIS = 'breast-cancer-diagnosis',
    BREAST_CANCER_PROCEDURE = 'breast-cancer-procedure',
    BREAST_CANCER_TUMOR = 'breast-cancer-tumor',
}

export const AllMedicalEntryType = [
    MedicalEntryType.OTHER,
    MedicalEntryType.BREAST_CANCER_DIAGNOSIS,
    MedicalEntryType.BREAST_CANCER_PROCEDURE,
    MedicalEntryType.BREAST_CANCER_TUMOR,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
