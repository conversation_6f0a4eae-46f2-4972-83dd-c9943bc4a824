// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.medical.Code

import { CodingSystem } from './coding-system';
export interface Code {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    codingSystem?: CodingSystem;
    key?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
