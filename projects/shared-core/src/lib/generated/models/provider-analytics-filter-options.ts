// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.api.dtos.provideranalytics.ProviderAnalyticsFilterOptions

import { ProviderAnalyticsCareTeamOption } from './provider-analytics-care-team-option';
import { QuestionnaireFilterOption } from './questionnaire-filter-option';
export interface ProviderAnalyticsFilterOptions {
    startYear?: number;
    careTeams?: ProviderAnalyticsCareTeamOption[];
    caseStatuses?: string[];
    caseTypes?: string[];
    questionnaires?: QuestionnaireFilterOption[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
