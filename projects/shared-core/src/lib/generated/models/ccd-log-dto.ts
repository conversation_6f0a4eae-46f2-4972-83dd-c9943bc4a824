// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.dto.CcdLogDto

import { ApiStrategy } from './api-strategy';
import { CcdLogType } from './ccd-log-type';
export interface CcdLogDto {
    id?: string;
    actorName?: string;
    ccdLogType?: CcdLogType;
    timestamp?: Date | number | string;
    sentTo?: string;
    startDate?: Date | number | string;
    endDate?: Date | number | string;
    contents?: string[];
    apiStrategy?: ApiStrategy;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
