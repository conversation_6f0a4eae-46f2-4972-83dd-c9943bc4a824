// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.DataExportDataType

export enum DataExportDataType {
    PATIENTS = 'patients',
    SYMPTOMS = 'symptoms',
    QUESTIONNAIRES = 'questionnaires',
    MESSAGES = 'messages',
    DIARYENTRIES = 'diaryentries',
    TIMELINEEVENTS = 'timelineevents',
    CASES = 'cases',
}

export const AllDataExportDataType = [
    DataExportDataType.PATIENTS,
    DataExportDataType.SYMPTOMS,
    DataExportDataType.QUESTIONNAIRES,
    DataExportDataType.MESSAGES,
    DataExportDataType.DIARYENTRIES,
    DataExportDataType.TIMELINEEVENTS,
    DataExportDataType.CASES,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
