// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.WorkQueueFilter.WorkQueueFilterType

export enum WorkQueueFilterType {
    CARE_TEAM = 'careTeam',
    NURSE = 'nurse',
    OWN_PATIENTS = 'ownPatients',
    ALL_PATIENTS = 'allPatients',
    UNHANDLED_PATIENTS = 'unhandledPatients',
}

export const AllWorkQueueFilterType = [
    WorkQueueFilterType.CARE_TEAM,
    WorkQueueFilterType.NURSE,
    WorkQueueFilterType.OWN_PATIENTS,
    WorkQueueFilterType.ALL_PATIENTS,
    WorkQueueFilterType.UNHANDLED_PATIENTS,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
