// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.treatment.TreatmentModuleType

export enum TreatmentModuleType {
    BREAST_RECOVERY = 'breastRecovery',
    CYTOSTATICS_GENERAL = 'cytostaticsGeneral',
    CYTOSTATICS_KIDNEY = 'cytostaticsKidney',
    CYTOSTATICS_UROLOGIC = 'cytostaticsUrologic',
    HORMONAL_TREATMENTS_UROLOGIC = 'hormonalTreatmentsUrologic',
    IMMUNOLOGIC_TREATMENTS = 'immunologicTreatments',
    MEDICAL_TREATMENTS_GASTROINTESTINAL = 'medicalTreatmentsGastrointestinal',
    RADIOTHERAPY_BREAST = 'radiotherapyBreast',
    RADIOTHERAPY_PELVIS_AREA = 'radiotherapyPelvisArea',
    SURGERY_BREAST = 'surgeryBreast',
    GYNECOLOGIC_RECOVERY = 'gynecologicRecovery',
    SARCOMA_RECOVERY = 'sarcomaRecovery',
    YOUNG_RECOVERY = 'youngRecovery',
    PALLIATIVE_TREATMENT = 'palliativeTreatment',
    TESTICLE_RECOVERY = 'testicleRecovery',
    LYMPHOMA_RECOVERY = 'lymphomaRecovery',
    MYELOMA = 'myeloma',
    BOWEL_RECOVERY = 'bowelRecovery',
    IO_MELANOMA = 'ioMelanoma',
    CYTOSTATICS_MELANOMA_LYMPHOMA = 'cytostaticsMelanomaLymphoma',
    MELANOMA_RECOVERY = 'melanomaRecovery',
    GI_SURGERY = 'giSurgery',
    BREAST_HORMONAL_COMBINATION = 'breastHormonalCombination',
    IO_CHEMO_COMBINATION = 'ioChemoCombination',
    LUNG_RECOVERY = 'lungRecovery',
    ACUTE_LEUKAEMIA_MDS = 'acuteLeukaemiaMds',
    RADIOTHERAPY_CHEST = 'radiotherapyChest',
    CLL_TREATMENT = 'cllTreatment',
    LYMPHOMA_TREATMENT = 'lymphomaTreatment',
    RADIOTHERAPY_HEAD_NECK = 'radiotherapyHeadNeck',
    CHEMO_GENERAL = 'chemoGeneral',
    HAEMATOLOGY_GENERAL = 'haematologyGeneral',
    GYNECOLOGIC_SURGERY = 'gynecologicSurgery',
    BLADDER_CANCER_SPECIFIC = 'bladderCancerSpecific',
    KIDNEY_CANCER_SPECIFIC = 'kidneyCancerSpecific',
    BREAST_CANCER_SPECIFIC = 'breastCancerSpecific',
    HEAD_NECK_RECOVERY = 'headNeckRecovery',
    PROSTATE_CANCER_SPECIFIC = 'prostateCancerSpecific',
    RADIOTHERAPY_BONE = 'radiotherapyBone',
    COLON_CANCER_SPECIFIC = 'colonCancerSpecific',
    RECTUM_ANUS_CANCER_SPECIFIC = 'rectumAnusCancerSpecific',
    LYMPHOMA_SPECIFIC = 'lymphomaSpecific',
    SKIN_CANCER_SPECIFIC = 'skinCancerSpecific',
    TAXANES_TREATMENT = 'taxanesTreatment',
    TAMOXIFENE_TREATMENT = 'tamoxifeneTreatment',
    AI_TREATMENT = 'aiTreatment',
    HERTWO_TREATMENT = 'hertwoTreatment',
    IO_CHEMO_COMBO_GROUP = 'ioChemoComboGroup',
    UROLOGIC_RECOVERY = 'urologicRecovery',
    UROLOGIC_SURGERY = 'urologicSurgery',
    SARCOMA_SURGERY = 'sarcomaSurgery',
    IO_CHEMO_SECOND_COMBO_GROUP = 'ioChemoSecondComboGroup',
    GENERIC_RADIATION_THERAPY = 'genericRadiationTherapy',
}

export const AllTreatmentModuleType = [
    TreatmentModuleType.BREAST_RECOVERY,
    TreatmentModuleType.CYTOSTATICS_GENERAL,
    TreatmentModuleType.CYTOSTATICS_KIDNEY,
    TreatmentModuleType.CYTOSTATICS_UROLOGIC,
    TreatmentModuleType.HORMONAL_TREATMENTS_UROLOGIC,
    TreatmentModuleType.IMMUNOLOGIC_TREATMENTS,
    TreatmentModuleType.MEDICAL_TREATMENTS_GASTROINTESTINAL,
    TreatmentModuleType.RADIOTHERAPY_BREAST,
    TreatmentModuleType.RADIOTHERAPY_PELVIS_AREA,
    TreatmentModuleType.SURGERY_BREAST,
    TreatmentModuleType.GYNECOLOGIC_RECOVERY,
    TreatmentModuleType.SARCOMA_RECOVERY,
    TreatmentModuleType.YOUNG_RECOVERY,
    TreatmentModuleType.PALLIATIVE_TREATMENT,
    TreatmentModuleType.TESTICLE_RECOVERY,
    TreatmentModuleType.LYMPHOMA_RECOVERY,
    TreatmentModuleType.MYELOMA,
    TreatmentModuleType.BOWEL_RECOVERY,
    TreatmentModuleType.IO_MELANOMA,
    TreatmentModuleType.CYTOSTATICS_MELANOMA_LYMPHOMA,
    TreatmentModuleType.MELANOMA_RECOVERY,
    TreatmentModuleType.GI_SURGERY,
    TreatmentModuleType.BREAST_HORMONAL_COMBINATION,
    TreatmentModuleType.IO_CHEMO_COMBINATION,
    TreatmentModuleType.LUNG_RECOVERY,
    TreatmentModuleType.ACUTE_LEUKAEMIA_MDS,
    TreatmentModuleType.RADIOTHERAPY_CHEST,
    TreatmentModuleType.CLL_TREATMENT,
    TreatmentModuleType.LYMPHOMA_TREATMENT,
    TreatmentModuleType.RADIOTHERAPY_HEAD_NECK,
    TreatmentModuleType.CHEMO_GENERAL,
    TreatmentModuleType.HAEMATOLOGY_GENERAL,
    TreatmentModuleType.GYNECOLOGIC_SURGERY,
    TreatmentModuleType.BLADDER_CANCER_SPECIFIC,
    TreatmentModuleType.KIDNEY_CANCER_SPECIFIC,
    TreatmentModuleType.BREAST_CANCER_SPECIFIC,
    TreatmentModuleType.HEAD_NECK_RECOVERY,
    TreatmentModuleType.PROSTATE_CANCER_SPECIFIC,
    TreatmentModuleType.RADIOTHERAPY_BONE,
    TreatmentModuleType.COLON_CANCER_SPECIFIC,
    TreatmentModuleType.RECTUM_ANUS_CANCER_SPECIFIC,
    TreatmentModuleType.LYMPHOMA_SPECIFIC,
    TreatmentModuleType.SKIN_CANCER_SPECIFIC,
    TreatmentModuleType.TAXANES_TREATMENT,
    TreatmentModuleType.TAMOXIFENE_TREATMENT,
    TreatmentModuleType.AI_TREATMENT,
    TreatmentModuleType.HERTWO_TREATMENT,
    TreatmentModuleType.IO_CHEMO_COMBO_GROUP,
    TreatmentModuleType.UROLOGIC_RECOVERY,
    TreatmentModuleType.UROLOGIC_SURGERY,
    TreatmentModuleType.SARCOMA_SURGERY,
    TreatmentModuleType.IO_CHEMO_SECOND_COMBO_GROUP,
    TreatmentModuleType.GENERIC_RADIATION_THERAPY,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
