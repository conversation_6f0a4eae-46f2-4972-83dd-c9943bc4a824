// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.notification.model.NotificationType

export enum NotificationType {
    AEQ = 'aeq',
    AEQ_BASELINE = 'aeqBaseline',
    AEQ_FOLLOW_UP = 'aeqFollowUp',
    AEQ_REMINDER = 'aeqReminder',
    AEQ_REMINDER_BASELINE = 'aeqReminderBaseline',
    AEQ_REMINDER_FOLLOW_UP = 'aeqReminderFollowUp',
    QOL = 'qol',
    QOL_REMINDER = 'qolReminder',
    NEW_MESSAGE = 'newMessage',
    ANNOUNCEMENT = 'announcement',
    SMART_FOLLOW_UP = 'smartFollowUp',
    AEQ_HANDLED = 'aeqHandled',
    CONSULTATION_AEQ = 'consultationAeq',
    CONSULTATION_QUESTION = 'consultationQuestion',
    SUPPORT_FEEDBACK_REPLY = 'supportFeedbackReply',
    SUPPORT_CHAT_REPLY = 'supportChatReply',
    SCHEDULED_MESSAGE = 'scheduledMessage',
    NCCN = 'nccn',
    NCCN_REMINDER = 'nccnReminder',
    DISTRESS_FOLLOW_UP = 'distressFollowUp',
    DISTRESS_FOLLOW_UP_REMINDER = 'distressFollowUpReminder',
    HNQOL = 'hnqol',
    HNQOL_REMINDER = 'hnqolReminder',
    SMART_FOLLOW_UP_STANDALONE = 'smartFollowUpStandalone',
    CLINIC_CONNECTION_REQUEST = 'clinicConnectionRequest',
    CLINIC_CONNECTION_FINISHED = 'clinicConnectionFinished',
    FACT_GOG_NTX = 'factGogNtx',
    FACT_GOG_NTX_REMINDER = 'factGogNtxReminder',
    BOUNCE_TIPI = 'bounceTipi',
    BOUNCE_TIPI_REMINDER = 'bounceTipiReminder',
    BOUNCE_CERQ = 'bounceCerq',
    BOUNCE_CERQ_REMINDER = 'bounceCerqReminder',
    BOUNCE_LOT_R = 'bounceLotR',
    BOUNCE_LOT_R_REMINDER = 'bounceLotRReminder',
    BOUNCE_EORTC_QLQ_BR_23 = 'bounceEortcQlqBr23',
    BOUNCE_EORTC_QLQ_BR_23_REMINDER = 'bounceEortcQlqBr23Reminder',
    BOUNCE_MAAS = 'bounceMaas',
    BOUNCE_MAAS_REMINDER = 'bounceMaasReminder',
    BOUNCE_HADS = 'bounceHads',
    BOUNCE_HADS_REMINDER = 'bounceHadsReminder',
    BOUNCE_PANAS = 'bouncePanas',
    BOUNCE_PANAS_REMINDER = 'bouncePanasReminder',
    BOUNCE_MMOS_SS = 'bounceMmosSs',
    BOUNCE_MMOS_SS_REMINDER = 'bounceMmosSsReminder',
    BOUNCE_FCRI_SF = 'bounceFcriSf',
    BOUNCE_FCRI_SF_REMINDER = 'bounceFcriSfReminder',
    BOUNCE_EORTC_QLQ_C_30 = 'bounceEortcQlqC30',
    BOUNCE_EORTC_QLQ_C_30_REMINDER = 'bounceEortcQlqC30Reminder',
    BOUNCE_RNLE = 'bounceRnle',
    BOUNCE_RNLE_REMINDER = 'bounceRnleReminder',
    GENERAL_SELF_EFFICACY_ITEM = 'generalSelfEfficacyItem',
    GENERAL_SELF_EFFICACY_ITEM_REMINDER = 'generalSelfEfficacyItemReminder',
    BOUNCE_SD_LIFESTYLE_MO = 'bounceSdLifestyleMo',
    BOUNCE_SD_LIFESTYLE_MO_REMINDER = 'bounceSdLifestyleMoReminder',
    CD_RISC = 'cdRisc',
    CD_RISC_REMINDER = 'cdRiscReminder',
    NCCN_DISTRESS_THERMO = 'nccnDistressThermo',
    NCCN_DISTRESS_THERMO_REMINDER = 'nccnDistressThermoReminder',
    SOC_13 = 'soc13',
    SOC_13_REMINDER = 'soc13Reminder',
    PACT = 'pact',
    PACT_REMINDER = 'pactReminder',
    RCAC = 'rcac',
    RCAC_REMINDER = 'rcacReminder',
    CBI_B = 'cbiB',
    CBI_B_REMINDER = 'cbiBReminder',
    IEPSS = 'iepss',
    IEPSS_REMINDER = 'iepssReminder',
    BOUNCE_SD_LIFESTYLE_MO_PT = 'bounceSdLifestyleMoPt',
    BOUNCE_SD_LIFESTYLE_MO_PT_REMINDER = 'bounceSdLifestyleMoPtReminder',
    TENONC_DISTRESS_BASELINE = 'tenoncDistressBaseline',
    TENONC_DISTRESS_BASELINE_REMINDER = 'tenoncDistressBaselineReminder',
    TENONC_DISTRESS_AFTERBASELINE = 'tenoncDistressAfterbaseline',
    TENONC_DISTRESS_AFTERBASELINE_REMINDER = 'tenoncDistressAfterbaselineReminder',
    SPIRITUALITY_COPING = 'spiritualityCoping',
    SPIRITUALITY_COPING_REMINDER = 'spiritualityCopingReminder',
    FARE = 'fare',
    FARE_REMINDER = 'fareReminder',
    MINI_MAC = 'miniMac',
    MINI_MAC_REMINDER = 'miniMacReminder',
    WHAT_HAS_DONE_TO_COPE = 'whatHasDoneToCope',
    WHAT_HAS_DONE_TO_COPE_REMINDER = 'whatHasDoneToCopeReminder',
    PTGI = 'ptgi',
    PTGI_REMINDER = 'ptgiReminder',
    BOUNCE_SD_LIFESTYLE_M_3 = 'bounceSdLifestyleM3',
    BOUNCE_SD_LIFESTYLE_M_3_REMINDER = 'bounceSdLifestyleM3Reminder',
    ADHERENCE_TO_MEDICAL_ADVICE_MOS = 'adherenceToMedicalAdviceMos',
    ADHERENCE_TO_MEDICAL_ADVICE_MOS_REMINDER = 'adherenceToMedicalAdviceMosReminder',
    IPQ = 'ipq',
    IPQ_REMINDER = 'ipqReminder',
    B_IPQ = 'bIpq',
    B_IPQ_REMINDER = 'bIpqReminder',
    RECIL_RELATED_EVENTS = 'recilRelatedEvents',
    RECIL_RELATED_EVENTS_REMINDER = 'recilRelatedEventsReminder',
    PCL_5 = 'pcl5',
    PCL_5_REMINDER = 'pcl5Reminder',
    HOW_MUCH_BACK_TOYOURSELF = 'howMuchBackToyourself',
    HOW_MUCH_BACK_TOYOURSELF_REMINDER = 'howMuchBackToyourselfReminder',
    BOUNCE_PA_CA_PA_DA_APRVD = 'bouncePaCaPaDaAprvd',
    BOUNCE_PA_CA_PA_DA_APRVD_REMINDER = 'bouncePaCaPaDaAprvdReminder',
    PHQ_2 = 'phq2',
    PHQ_2_REMINDER = 'phq2Reminder',
    BOUNCE_SD_LIFESTYLE_M_18_HE_AR = 'bounceSdLifestyleM18HeAr',
    BOUNCE_SD_LIFESTYLE_M_18_HE_AR_REMINDER = 'bounceSdLifestyleM18HeArReminder',
    BOUNCE_SD_LIFESTYLE_M_18_PT = 'bounceSdLifestyleM18Pt',
    BOUNCE_SD_LIFESTYLE_M_18_PT_REMINDER = 'bounceSdLifestyleM18PtReminder',
    TREATMENT_XP_MICHI = 'treatmentXpMichi',
    TREATMENT_XP_MICHI_REMINDER = 'treatmentXpMichiReminder',
    BOUNCE_SD_LIFESTYLE_M_9 = 'bounceSdLifestyleM9',
    BOUNCE_SD_LIFESTYLE_M_9_REMINDER = 'bounceSdLifestyleM9Reminder',
    BOUNCE_SD_LIFESTYLE_M_18_ORIG = 'bounceSdLifestyleM18Orig',
    BOUNCE_SD_LIFESTYLE_M_18_ORIG_REMINDER = 'bounceSdLifestyleM18OrigReminder',
    BOUNCE_SD_LIFESTYLE_M_15 = 'bounceSdLifestyleM15',
    BOUNCE_SD_LIFESTYLE_M_15_REMINDER = 'bounceSdLifestyleM15Reminder',
    BOUNCE_SD_LIFESTYLE_M_6_M_12 = 'bounceSdLifestyleM6M12',
    BOUNCE_SD_LIFESTYLE_M_6_M_12_REMINDER = 'bounceSdLifestyleM6M12Reminder',
    BOUNCE_SD_LIFESTYLE_MO_HE_AR = 'bounceSdLifestyleMoHeAr',
    BOUNCE_SD_LIFESTYLE_MO_HE_AR_REMINDER = 'bounceSdLifestyleMoHeArReminder',
    EPRO_PATIENT_SATISFACTION = 'eproPatientSatisfaction',
    EPRO_PATIENT_SATISFACTION_REMINDER = 'eproPatientSatisfactionReminder',
    BOUNCE_SD_LIFESTYLE_M_6_M_12_PT = 'bounceSdLifestyleM6M12Pt',
    BOUNCE_SD_LIFESTYLE_M_6_M_12_PT_REMINDER = 'bounceSdLifestyleM6M12PtReminder',
    BOUNCE_PA_CA_PA_DA_HUS = 'bouncePaCaPaDaHus',
    BOUNCE_PA_CA_PA_DA_HUS_REMINDER = 'bouncePaCaPaDaHusReminder',
    POST_TREATMENT_STATUS_CHECK = 'postTreatmentStatusCheck',
    POST_TREATMENT_STATUS_CHECK_REMINDER = 'postTreatmentStatusCheckReminder',
    MULTIPLE_QOLS = 'multipleQols',
    MULTIPLE_QOLS_REMINDER = 'multipleQolsReminder',
    EDUCATION_BINDER = 'educationBinder',
    FACT_LYM_VERFOUR_MOD = 'factLymVerfourMod',
    FACT_LYM_VERFOUR_MOD_REMINDER = 'factLymVerfourModReminder',
    MANDA_EORTC_QLQ_C_30 = 'mandaEortcQlqC30',
    MANDA_EORTC_QLQ_C_30_REMINDER = 'mandaEortcQlqC30Reminder',
    G_EIGHT_FRAILTY = 'gEightFrailty',
    G_EIGHT_FRAILTY_REMINDER = 'gEightFrailtyReminder',
    COVID = 'covid',
    COVID_REMINDER = 'covidReminder',
    LAB_RESULT = 'labResult',
    FACT_HN_VERSION_FOUR = 'factHnVersionFour',
    FACT_HN_VERSION_FOUR_REMINDER = 'factHnVersionFourReminder',
    IPSS = 'ipss',
    IPSS_REMINDER = 'ipssReminder',
    EPIC_SF_6 = 'epicSf6',
    EPIC_SF_6_REMINDER = 'epicSf6Reminder',
    FACT_G_VERSION_FOUR = 'factGVersionFour',
    FACT_G_VERSION_FOUR_REMINDER = 'factGVersionFourReminder',
    ZRTI = 'zrti',
    ZRTI_REMINDER = 'zrtiReminder',
    ORAL_MED_COMPLIANCE = 'oralMedCompliance',
    ORAL_MED_COMPLIANCE_REMINDER = 'oralMedComplianceReminder',
    EPIC_26 = 'epic26',
    EPIC_26_REMINDER = 'epic26Reminder',
    BOUNCE_COMPLIANCE = 'bounceCompliance',
    BOUNCE_COMPLIANCE_REMINDER = 'bounceComplianceReminder',
    COVID_CCMB = 'covidCcmb',
    COVID_CCMB_REMINDER = 'covidCcmbReminder',
    TAYS_EPIC_26 = 'taysEpic26',
    TAYS_EPIC_26_REMINDER = 'taysEpic26Reminder',
    IIEF_5 = 'iief5',
    IIEF_5_REMINDER = 'iief5Reminder',
    ICIQ_INCONTINENCE = 'iciqIncontinence',
    ICIQ_INCONTINENCE_REMINDER = 'iciqIncontinenceReminder',
}

export const AllNotificationType = [
    NotificationType.AEQ,
    NotificationType.AEQ_BASELINE,
    NotificationType.AEQ_FOLLOW_UP,
    NotificationType.AEQ_REMINDER,
    NotificationType.AEQ_REMINDER_BASELINE,
    NotificationType.AEQ_REMINDER_FOLLOW_UP,
    NotificationType.QOL,
    NotificationType.QOL_REMINDER,
    NotificationType.NEW_MESSAGE,
    NotificationType.ANNOUNCEMENT,
    NotificationType.SMART_FOLLOW_UP,
    NotificationType.AEQ_HANDLED,
    NotificationType.CONSULTATION_AEQ,
    NotificationType.CONSULTATION_QUESTION,
    NotificationType.SUPPORT_FEEDBACK_REPLY,
    NotificationType.SUPPORT_CHAT_REPLY,
    NotificationType.SCHEDULED_MESSAGE,
    NotificationType.NCCN,
    NotificationType.NCCN_REMINDER,
    NotificationType.DISTRESS_FOLLOW_UP,
    NotificationType.DISTRESS_FOLLOW_UP_REMINDER,
    NotificationType.HNQOL,
    NotificationType.HNQOL_REMINDER,
    NotificationType.SMART_FOLLOW_UP_STANDALONE,
    NotificationType.CLINIC_CONNECTION_REQUEST,
    NotificationType.CLINIC_CONNECTION_FINISHED,
    NotificationType.FACT_GOG_NTX,
    NotificationType.FACT_GOG_NTX_REMINDER,
    NotificationType.BOUNCE_TIPI,
    NotificationType.BOUNCE_TIPI_REMINDER,
    NotificationType.BOUNCE_CERQ,
    NotificationType.BOUNCE_CERQ_REMINDER,
    NotificationType.BOUNCE_LOT_R,
    NotificationType.BOUNCE_LOT_R_REMINDER,
    NotificationType.BOUNCE_EORTC_QLQ_BR_23,
    NotificationType.BOUNCE_EORTC_QLQ_BR_23_REMINDER,
    NotificationType.BOUNCE_MAAS,
    NotificationType.BOUNCE_MAAS_REMINDER,
    NotificationType.BOUNCE_HADS,
    NotificationType.BOUNCE_HADS_REMINDER,
    NotificationType.BOUNCE_PANAS,
    NotificationType.BOUNCE_PANAS_REMINDER,
    NotificationType.BOUNCE_MMOS_SS,
    NotificationType.BOUNCE_MMOS_SS_REMINDER,
    NotificationType.BOUNCE_FCRI_SF,
    NotificationType.BOUNCE_FCRI_SF_REMINDER,
    NotificationType.BOUNCE_EORTC_QLQ_C_30,
    NotificationType.BOUNCE_EORTC_QLQ_C_30_REMINDER,
    NotificationType.BOUNCE_RNLE,
    NotificationType.BOUNCE_RNLE_REMINDER,
    NotificationType.GENERAL_SELF_EFFICACY_ITEM,
    NotificationType.GENERAL_SELF_EFFICACY_ITEM_REMINDER,
    NotificationType.BOUNCE_SD_LIFESTYLE_MO,
    NotificationType.BOUNCE_SD_LIFESTYLE_MO_REMINDER,
    NotificationType.CD_RISC,
    NotificationType.CD_RISC_REMINDER,
    NotificationType.NCCN_DISTRESS_THERMO,
    NotificationType.NCCN_DISTRESS_THERMO_REMINDER,
    NotificationType.SOC_13,
    NotificationType.SOC_13_REMINDER,
    NotificationType.PACT,
    NotificationType.PACT_REMINDER,
    NotificationType.RCAC,
    NotificationType.RCAC_REMINDER,
    NotificationType.CBI_B,
    NotificationType.CBI_B_REMINDER,
    NotificationType.IEPSS,
    NotificationType.IEPSS_REMINDER,
    NotificationType.BOUNCE_SD_LIFESTYLE_MO_PT,
    NotificationType.BOUNCE_SD_LIFESTYLE_MO_PT_REMINDER,
    NotificationType.TENONC_DISTRESS_BASELINE,
    NotificationType.TENONC_DISTRESS_BASELINE_REMINDER,
    NotificationType.TENONC_DISTRESS_AFTERBASELINE,
    NotificationType.TENONC_DISTRESS_AFTERBASELINE_REMINDER,
    NotificationType.SPIRITUALITY_COPING,
    NotificationType.SPIRITUALITY_COPING_REMINDER,
    NotificationType.FARE,
    NotificationType.FARE_REMINDER,
    NotificationType.MINI_MAC,
    NotificationType.MINI_MAC_REMINDER,
    NotificationType.WHAT_HAS_DONE_TO_COPE,
    NotificationType.WHAT_HAS_DONE_TO_COPE_REMINDER,
    NotificationType.PTGI,
    NotificationType.PTGI_REMINDER,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_3,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_3_REMINDER,
    NotificationType.ADHERENCE_TO_MEDICAL_ADVICE_MOS,
    NotificationType.ADHERENCE_TO_MEDICAL_ADVICE_MOS_REMINDER,
    NotificationType.IPQ,
    NotificationType.IPQ_REMINDER,
    NotificationType.B_IPQ,
    NotificationType.B_IPQ_REMINDER,
    NotificationType.RECIL_RELATED_EVENTS,
    NotificationType.RECIL_RELATED_EVENTS_REMINDER,
    NotificationType.PCL_5,
    NotificationType.PCL_5_REMINDER,
    NotificationType.HOW_MUCH_BACK_TOYOURSELF,
    NotificationType.HOW_MUCH_BACK_TOYOURSELF_REMINDER,
    NotificationType.BOUNCE_PA_CA_PA_DA_APRVD,
    NotificationType.BOUNCE_PA_CA_PA_DA_APRVD_REMINDER,
    NotificationType.PHQ_2,
    NotificationType.PHQ_2_REMINDER,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_18_HE_AR,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_18_HE_AR_REMINDER,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_18_PT,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_18_PT_REMINDER,
    NotificationType.TREATMENT_XP_MICHI,
    NotificationType.TREATMENT_XP_MICHI_REMINDER,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_9,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_9_REMINDER,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_18_ORIG,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_18_ORIG_REMINDER,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_15,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_15_REMINDER,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_6_M_12,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_6_M_12_REMINDER,
    NotificationType.BOUNCE_SD_LIFESTYLE_MO_HE_AR,
    NotificationType.BOUNCE_SD_LIFESTYLE_MO_HE_AR_REMINDER,
    NotificationType.EPRO_PATIENT_SATISFACTION,
    NotificationType.EPRO_PATIENT_SATISFACTION_REMINDER,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_6_M_12_PT,
    NotificationType.BOUNCE_SD_LIFESTYLE_M_6_M_12_PT_REMINDER,
    NotificationType.BOUNCE_PA_CA_PA_DA_HUS,
    NotificationType.BOUNCE_PA_CA_PA_DA_HUS_REMINDER,
    NotificationType.POST_TREATMENT_STATUS_CHECK,
    NotificationType.POST_TREATMENT_STATUS_CHECK_REMINDER,
    NotificationType.MULTIPLE_QOLS,
    NotificationType.MULTIPLE_QOLS_REMINDER,
    NotificationType.EDUCATION_BINDER,
    NotificationType.FACT_LYM_VERFOUR_MOD,
    NotificationType.FACT_LYM_VERFOUR_MOD_REMINDER,
    NotificationType.MANDA_EORTC_QLQ_C_30,
    NotificationType.MANDA_EORTC_QLQ_C_30_REMINDER,
    NotificationType.G_EIGHT_FRAILTY,
    NotificationType.G_EIGHT_FRAILTY_REMINDER,
    NotificationType.COVID,
    NotificationType.COVID_REMINDER,
    NotificationType.LAB_RESULT,
    NotificationType.FACT_HN_VERSION_FOUR,
    NotificationType.FACT_HN_VERSION_FOUR_REMINDER,
    NotificationType.IPSS,
    NotificationType.IPSS_REMINDER,
    NotificationType.EPIC_SF_6,
    NotificationType.EPIC_SF_6_REMINDER,
    NotificationType.FACT_G_VERSION_FOUR,
    NotificationType.FACT_G_VERSION_FOUR_REMINDER,
    NotificationType.ZRTI,
    NotificationType.ZRTI_REMINDER,
    NotificationType.ORAL_MED_COMPLIANCE,
    NotificationType.ORAL_MED_COMPLIANCE_REMINDER,
    NotificationType.EPIC_26,
    NotificationType.EPIC_26_REMINDER,
    NotificationType.BOUNCE_COMPLIANCE,
    NotificationType.BOUNCE_COMPLIANCE_REMINDER,
    NotificationType.COVID_CCMB,
    NotificationType.COVID_CCMB_REMINDER,
    NotificationType.TAYS_EPIC_26,
    NotificationType.TAYS_EPIC_26_REMINDER,
    NotificationType.IIEF_5,
    NotificationType.IIEF_5_REMINDER,
    NotificationType.ICIQ_INCONTINENCE,
    NotificationType.ICIQ_INCONTINENCE_REMINDER,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
