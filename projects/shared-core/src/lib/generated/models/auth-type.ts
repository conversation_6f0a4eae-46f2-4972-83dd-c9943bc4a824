// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.security.AuthType

export enum AuthType {
    NO_AUTH = 'noAuth',
    BASIC = 'basic',
    BEARER = 'bearer',
    DIGEST = 'digest',
    OAUTH_1 = 'oauth1',
    OAUTH_2 = 'oauth2',
}

export const AllAuthType = [
    AuthType.NO_AUTH,
    AuthType.BASIC,
    AuthType.BEARER,
    AuthType.DIGEST,
    AuthType.OAUTH_1,
    AuthType.OAUTH_2,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
