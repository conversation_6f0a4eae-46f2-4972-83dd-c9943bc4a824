// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.analytics.model.reporting.PortalUsageCounts

import { PortalUsageView } from './portal-usage-view';
import { ProviderPortalUsageView } from './provider-portal-usage-view';
export interface PortalUsageCounts {
    providerUsage?: ProviderPortalUsageView[];
    patientAnalytics?: PortalUsageView[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
