// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.patient.status.analytics.MyHealthStatusCounts

export interface MyHealthStatusCounts {
    activeCount?: number;
    offeredCount?: number;
    declinedCount?: number;
    changedActiveCount?: number;
    changedOfferedCount?: number;
    changedDeclinedCount?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
