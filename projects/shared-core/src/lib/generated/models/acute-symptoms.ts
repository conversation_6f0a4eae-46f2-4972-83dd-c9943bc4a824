// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.AcuteSymptoms

export enum AcuteSymptoms {
  CHEST_PAIN = 'chestPain',
  INTRACTABLE_PAIN = 'intractablePain',
  HIGH_GRADE_FEVER = 'highGradeFever',
  TROUBLE_BREATHING = 'troubleBreathing',
  CURRENTLY_BLEEDING = 'currentlyBleeding',
  CHANGE_IN_MENTAL_STATUS = 'changeInMentalStatus',
  ASKING_ABOUT_GOING_TO_ER = 'askingAboutGoingToEr'
}

export const AllAcuteSymptoms = [
  AcuteSymptoms.CHEST_PAIN,
  AcuteSymptoms.INTRACTABLE_PAIN,
  AcuteSymptoms.HIGH_GRADE_FEVER,
  AcuteSymptoms.TROUBLE_BREATHING,
  AcuteSymptoms.CURRENTLY_BLEEDING,
  AcuteSymptoms.CHANGE_IN_MENTAL_STATUS,
  AcuteSymptoms.ASKING_ABOUT_GOING_TO_ER
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
