// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.ConsentContent

import { Language } from './language';
export interface ConsentContent {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    locale?: Language;
    content?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
