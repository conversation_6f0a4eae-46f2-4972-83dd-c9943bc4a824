import { InquiryStatus } from './inquiry-status';
import { PatientTreatmentModule } from './patient-treatment-module';
import { User } from './user';
export interface QuestionnaireInquiry {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    sendingDate?: Date | number | string;
    treatmentDate?: Date | number | string;
    patientTreatmentModule?: PatientTreatmentModule;
    type?: any;
    status?: InquiryStatus;
    expiredByDate?: Date | number | string;
    expiredByNurse?: User;
    appointmentType?: string;
    compositeStatus?: InquiryStatus;
}
