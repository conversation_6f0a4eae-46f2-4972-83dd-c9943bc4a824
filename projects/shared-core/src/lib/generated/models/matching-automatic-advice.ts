// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.rule.model.MatchingAutomaticAdvice

import { AutomaticAdvice } from './automatic-advice';
export interface MatchingAutomaticAdvice {
    automaticAdvice?: AutomaticAdvice;
    matchingRuleName?: string;
    matchingRuleId?: string;
    description?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
