// Auto generated by ad<PERSON><PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.rule.model.AutomaticAdviceType

export enum AutomaticAdviceType {
    MILD_UNCLEAR = 'mild-unclear',
    MILD_UNRELATED = 'mild-unrelated',
    MILD_RELATED = 'mild-related',
    NORMAL = 'normal',
    URGENT = 'urgent',
    SEMI_EMERGENCY = 'semi-emergency',
    EMERGENCY = 'emergency',
    MENOPAUSE02AMC05_MENOPAUSE_MEDICATION_INSTRUCTIONS = 'menopause02amc05-menopause-medication-instructions',
    PAIN05AMC03_MILD_PAIN_INSTRUCTIONS = 'pain05amc03-mild-pain-instructions',
    BREATH02AMC07_SHORT_TERM_COUGH = 'breath02amc07-short-term-cough',
    HEADACHE08AMC10_MILD_HEADACHE_INSTRUCTIONS = 'headache08amc10-mild-headache-instructions',
    MILD_NO_LINK = 'mild-no-link',
    LESION02AMC01_BREAST_INFLAMMATION_EMERGENCY_INSTRUCTIONS = 'lesion02amc01-breast-inflammation-emergency-instructions',
    SWELLING03AMC02_ERYSIPELAS_EMERGENCY_INSTRUCTIONS = 'swelling03amc02-erysipelas-emergency-instructions',
    JOINT03AMC04_MILD_JOINT_PAIN_MEDICATION_INSTRUCTIONS = 'joint03amc04-mild-joint-pain-medication-instructions',
    ABDOMEN02AMC09_SEVERE_ABDOMINAL_PAIN_INSTRUCTIONS = 'abdomen02amc09-severe-abdominal-pain-instructions',
    SWELLING04AMC11_POSSIBLE_ERYSIPELAS_EMERGENCY_WITH_REDNESS_INSTRUCTIONS = 'swelling04amc11-possible-erysipelas-emergency-with-redness-instructions',
    SWELLING05AMC12_POSSIBLE_ERYSIPELAS_EMERGENCY_WITH_FEVER_INSTRUCTIONS = 'swelling05amc12-possible-erysipelas-emergency-with-fever-instructions',
    CYTOSTATICGENERAL_AMC_OtherSymptom01 = 'cytostaticgeneral-amc-othersymptom01',
    CYTOSTATICGENERALAMCDiarrhea01Dehydration = 'cytostaticgeneralamcdiarrhea01dehydration',
    CYTOSTATICGENERALAMCFever01Infection = 'cytostaticgeneralamcfever01infection',
    IMMUNOLOGICTREATMENTSAMCFever01Infection = 'immunologictreatmentsamcfever01infection',
    IMMUNOLOGICTREATMENTSAMCFever02Infection = 'immunologictreatmentsamcfever02infection',
    CYTOSTATICGENERALAMCInfection01Fever = 'cytostaticgeneralamcinfection01fever',
    IMMUNOLOGICTREATMENTSAMCInfection01Inflammation = 'immunologictreatmentsamcinfection01inflammation',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic01Colitis = 'immunologictreatmentsamcabdomenimmunologic01colitis',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic02Colitis = 'immunologictreatmentsamcabdomenimmunologic02colitis',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic03Pancreatis = 'immunologictreatmentsamcabdomenimmunologic03pancreatis',
    SURGERYBREASTAMCBleedingWound01SevereBleeding = 'surgerybreastamcbleedingwound01severebleeding',
    SURGERYBREASTAMCBleedingWound02BleedingWithHighFever = 'surgerybreastamcbleedingwound02bleedingwithhighfever',
    SURGERYBREASTAMCBleedingWound04BleedingWithPain = 'surgerybreastamcbleedingwound04bleedingwithpain',
    SURGERYBREASTAMCBleedingWound05BleedingWithSeverePain = 'surgerybreastamcbleedingwound05bleedingwithseverepain',
    SURGERYBREASTAMCBleedingWound06InternalBleeding = 'surgerybreastamcbleedingwound06internalbleeding',
    CYTOSTATICGENERALAMCBoneOrMusclePain01Pain = 'cytostaticgeneralamcboneormusclepain01pain',
    SURGERYBREASTAMCDiarrhea01Dehydration = 'surgerybreastamcdiarrhea01dehydration',
    SURGERYBREASTAMCDiarrhea02Dehydration = 'surgerybreastamcdiarrhea02dehydration',
    IMMUNOLOGICTREATMENTSAMCEyeSymptoms01Hepatitis = 'immunologictreatmentsamceyesymptoms01hepatitis',
    IMMUNOLOGICTREATMENTSAMCEyeSymptoms02Iritis = 'immunologictreatmentsamceyesymptoms02iritis',
    IMMUNOLOGICTREATMENTSAMCFatigueAt01Infection = 'immunologictreatmentsamcfatigueat01infection',
    SURGERYBREASTAMCFever01HighFever = 'surgerybreastamcfever01highfever',
    SURGERYBREASTAMCInfectedWound01Fever = 'surgerybreastamcinfectedwound01fever',
    SURGERYBREASTAMCInfectedWound02Redness = 'surgerybreastamcinfectedwound02redness',
    SURGERYBREASTAMCInfectedWound03Redness = 'surgerybreastamcinfectedwound03redness',
    SURGERYBREASTAMCInfectedWound04Bleeding = 'surgerybreastamcinfectedwound04bleeding',
    CYTOSTATICGENERALAMCMucositisOral01Dehydration = 'cytostaticgeneralamcmucositisoral01dehydration',
    CYTOSTATICGENERALAMCNausea01Dehydration = 'cytostaticgeneralamcnausea01dehydration',
    IMMUNOLOGICTREATMENTSAMCNausea01Dehydration = 'immunologictreatmentsamcnausea01dehydration',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness01GuillanBarre = 'immunologictreatmentsamcneuropathyormuscleweakness01guillanbarre',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness02AutoImmune = 'immunologictreatmentsamcneuropathyormuscleweakness02autoimmune',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness03AutoImmune = 'immunologictreatmentsamcneuropathyormuscleweakness03autoimmune',
    CYTOSTATICGENERALAMCOtherSymptom01 = 'cytostaticgeneralamcothersymptom01',
    SURGERYBREASTAMCPain01 = 'surgerybreastamcpain01',
    SURGERYBREASTAMCPain02 = 'surgerybreastamcpain02',
    IMMUNOLOGICTREATMENTSAMCPainImmunologic01Pain = 'immunologictreatmentsamcpainimmunologic01pain',
    IMMUNOLOGICTREATMENTSAMCPainImmunologic02Stroke = 'immunologictreatmentsamcpainimmunologic02stroke',
    IMMUNOLOGICTREATMENTSAMCRashImmunologic01SkinReaction = 'immunologictreatmentsamcrashimmunologic01skinreaction',
    IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic01Embolia = 'immunologictreatmentsamcrespiratoryimmunologic01embolia',
    IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic02Embolia = 'immunologictreatmentsamcrespiratoryimmunologic02embolia',
    SURGERYBREASTAMCSHORTNESSOFBREATH02Embolia = 'surgerybreastamcshortnessofbreath02embolia',
    SURGERYBREASTAMCSHORTNESSOFBREATH03ChestPain = 'surgerybreastamcshortnessofbreath03chestpain',
    SURGERYBREASTAMCSHORTNESSOFBREATH04HighFever = 'surgerybreastamcshortnessofbreath04highfever',
    SURGERYBREASTAMCSurgeryDrain01SeverePain = 'surgerybreastamcsurgerydrain01severepain',
    SURGERYBREASTAMCSurgeryDrain02MoreSeverePain = 'surgerybreastamcsurgerydrain02moreseverepain',
    SURGERYBREASTAMCSurgeryDrain03Bleeding = 'surgerybreastamcsurgerydrain03bleeding',
    SURGERYBREASTAMCSurgeryDrain04Redness = 'surgerybreastamcsurgerydrain04redness',
    SURGERYBREASTAMCSurgeryDrain05HighFever = 'surgerybreastamcsurgerydrain05highfever',
    SURGERYBREASTAMCSurgeryDrain07SwollenWound = 'surgerybreastamcsurgerydrain07swollenwound',
    SURGERYBREASTAMCSurgeryDrain08SwollenWoundWithBleeding = 'surgerybreastamcsurgerydrain08swollenwoundwithbleeding',
    IMMUNOLOGICTREATMENTSAMCVertigo01Stroke = 'immunologictreatmentsamcvertigo01stroke',
    IMMUNOLOGICTREATMENTSAMCVertigo02NeurologicImpairment = 'immunologictreatmentsamcvertigo02neurologicimpairment',
    SURGERYBREASTAMCWoundAppearance01InternalBleeding = 'surgerybreastamcwoundappearance01internalbleeding',
    EATINGPROBLEMSAMC01Dehydration = 'eatingproblemsamc01dehydration',
    IMMUNOLOGICTREATMENTSAMCInfection05Fever = 'immunologictreatmentsamcinfection05fever',
    IMMUNOLOGICTREATMENTSAMCInfection06Fever = 'immunologictreatmentsamcinfection06fever',
    SURGERYBREASTAMCWoundAppearance02Paracentesis = 'surgerybreastamcwoundappearance02paracentesis',
    RASH03 = 'rash03',
    RESPIRATORY16Fever = 'respiratory16fever',
    AMCabdominal11obstruction = 'amcabdominal11obstruction',
    AMCabdominal27Fever = 'amcabdominal27fever',
    MYELOMAAMCFever01Infection = 'myelomaamcfever01infection',
    MYELOMAAMCCirculatory01Embolia = 'myelomaamccirculatory01embolia',
    MYELOMAAMCCirculatory02Embolia = 'myelomaamccirculatory02embolia',
    EATINGPROBLMESORMOUTHSYMPTOMS01Dehydration = 'eatingproblmesormouthsymptoms01dehydration',
    INFECTIONORFEVER04Infection = 'infectionorfever04infection',
    LYMPHOMARECOVERYMCNNausea01Dehydraion = 'lymphomarecoverymcnnausea01dehydraion',
    MELANOMA_RECOVERYAMCInfection08Fever = 'melanoma-recoveryamcinfection08fever',
    CYTOSTATICGENERALAMCFever001Infection = 'cytostaticgeneralamcfever001infection',
    CYTOSTATICGENERALAMCNausea001Dehydration = 'cytostaticgeneralamcnausea001dehydration',
    LYMPHOMARECOVERYMCNNausea001Dehydraion = 'lymphomarecoverymcnnausea001dehydraion',
    IMMUNOLOGICTREATMENTSAMCFatigueAt001Infection = 'immunologictreatmentsamcfatigueat001infection',
    CYTOSTATICGENERALAMCOtherSymptom001 = 'cytostaticgeneralamcothersymptom001',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic001Colitis = 'immunologictreatmentsamcabdomenimmunologic001colitis',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic002Colitis = 'immunologictreatmentsamcabdomenimmunologic002colitis',
    IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic003Pancreatis = 'immunologictreatmentsamcabdomenimmunologic003pancreatis',
    ABDOMEN002AMC009_SEVERE_ABDOMINAL_PAIN_INSTRUCTIONS = 'abdomen002amc009-severe-abdominal-pain-instructions',
    CYTOSTATICGENERALAMCDiarrhea001Dehydration = 'cytostaticgeneralamcdiarrhea001dehydration',
    SURGERYBREASTAMCDiarrhea002Dehydration = 'surgerybreastamcdiarrhea002dehydration',
    AMCabdominal011obstruction = 'amcabdominal011obstruction',
    AMCabdominal027Fever = 'amcabdominal027fever',
    EATINGPROBLMESORMOUTHSYMPTOMS001Dehydration = 'eatingproblmesormouthsymptoms001dehydration',
    IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic001Embolia = 'immunologictreatmentsamcrespiratoryimmunologic001embolia',
    IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic002Embolia = 'immunologictreatmentsamcrespiratoryimmunologic002embolia',
    SURGERYBREASTAMCSHORTNESSOFBREATH002Embolia = 'surgerybreastamcshortnessofbreath002embolia',
    SURGERYBREASTAMCSHORTNESSOFBREATH004HighFever = 'surgerybreastamcshortnessofbreath004highfever',
    RESPIRATORY016Fever = 'respiratory016fever',
    SURGERYBREASTAMCPain001 = 'surgerybreastamcpain001',
    SURGERYBREASTAMCPain002 = 'surgerybreastamcpain002',
    CYTOSTATICGENERALAMCBoneOrMusclePain001Pain = 'cytostaticgeneralamcboneormusclepain001pain',
    IMMUNOLOGICTREATMENTSAMCPainImmunologic002Stroke = 'immunologictreatmentsamcpainimmunologic002stroke',
    IMMUNOLOGICTREATMENTSAMCRashImmunologic001SkinReaction = 'immunologictreatmentsamcrashimmunologic001skinreaction',
    RASH003 = 'rash003',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness001GuillanBarre = 'immunologictreatmentsamcneuropathyormuscleweakness001guillanbarre',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness002AutoImmune = 'immunologictreatmentsamcneuropathyormuscleweakness002autoimmune',
    IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness003AutoImmune = 'immunologictreatmentsamcneuropathyormuscleweakness003autoimmune',
    SURGERYBREASTAMCSHORTNESSOFBREATH04HighFeverSE = 'surgerybreastamcshortnessofbreath04highfeverse',
}

export const AllAutomaticAdviceType = [
    AutomaticAdviceType.MILD_UNCLEAR,
    AutomaticAdviceType.MILD_UNRELATED,
    AutomaticAdviceType.MILD_RELATED,
    AutomaticAdviceType.NORMAL,
    AutomaticAdviceType.URGENT,
    AutomaticAdviceType.SEMI_EMERGENCY,
    AutomaticAdviceType.EMERGENCY,
    AutomaticAdviceType.MENOPAUSE02AMC05_MENOPAUSE_MEDICATION_INSTRUCTIONS,
    AutomaticAdviceType.PAIN05AMC03_MILD_PAIN_INSTRUCTIONS,
    AutomaticAdviceType.BREATH02AMC07_SHORT_TERM_COUGH,
    AutomaticAdviceType.HEADACHE08AMC10_MILD_HEADACHE_INSTRUCTIONS,
    AutomaticAdviceType.MILD_NO_LINK,
    AutomaticAdviceType.LESION02AMC01_BREAST_INFLAMMATION_EMERGENCY_INSTRUCTIONS,
    AutomaticAdviceType.SWELLING03AMC02_ERYSIPELAS_EMERGENCY_INSTRUCTIONS,
    AutomaticAdviceType.JOINT03AMC04_MILD_JOINT_PAIN_MEDICATION_INSTRUCTIONS,
    AutomaticAdviceType.ABDOMEN02AMC09_SEVERE_ABDOMINAL_PAIN_INSTRUCTIONS,
    AutomaticAdviceType.SWELLING04AMC11_POSSIBLE_ERYSIPELAS_EMERGENCY_WITH_REDNESS_INSTRUCTIONS,
    AutomaticAdviceType.SWELLING05AMC12_POSSIBLE_ERYSIPELAS_EMERGENCY_WITH_FEVER_INSTRUCTIONS,
    AutomaticAdviceType.CYTOSTATICGENERAL_AMC_OtherSymptom01,
    AutomaticAdviceType.CYTOSTATICGENERALAMCDiarrhea01Dehydration,
    AutomaticAdviceType.CYTOSTATICGENERALAMCFever01Infection,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCFever01Infection,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCFever02Infection,
    AutomaticAdviceType.CYTOSTATICGENERALAMCInfection01Fever,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCInfection01Inflammation,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic01Colitis,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic02Colitis,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic03Pancreatis,
    AutomaticAdviceType.SURGERYBREASTAMCBleedingWound01SevereBleeding,
    AutomaticAdviceType.SURGERYBREASTAMCBleedingWound02BleedingWithHighFever,
    AutomaticAdviceType.SURGERYBREASTAMCBleedingWound04BleedingWithPain,
    AutomaticAdviceType.SURGERYBREASTAMCBleedingWound05BleedingWithSeverePain,
    AutomaticAdviceType.SURGERYBREASTAMCBleedingWound06InternalBleeding,
    AutomaticAdviceType.CYTOSTATICGENERALAMCBoneOrMusclePain01Pain,
    AutomaticAdviceType.SURGERYBREASTAMCDiarrhea01Dehydration,
    AutomaticAdviceType.SURGERYBREASTAMCDiarrhea02Dehydration,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCEyeSymptoms01Hepatitis,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCEyeSymptoms02Iritis,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCFatigueAt01Infection,
    AutomaticAdviceType.SURGERYBREASTAMCFever01HighFever,
    AutomaticAdviceType.SURGERYBREASTAMCInfectedWound01Fever,
    AutomaticAdviceType.SURGERYBREASTAMCInfectedWound02Redness,
    AutomaticAdviceType.SURGERYBREASTAMCInfectedWound03Redness,
    AutomaticAdviceType.SURGERYBREASTAMCInfectedWound04Bleeding,
    AutomaticAdviceType.CYTOSTATICGENERALAMCMucositisOral01Dehydration,
    AutomaticAdviceType.CYTOSTATICGENERALAMCNausea01Dehydration,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCNausea01Dehydration,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness01GuillanBarre,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness02AutoImmune,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness03AutoImmune,
    AutomaticAdviceType.CYTOSTATICGENERALAMCOtherSymptom01,
    AutomaticAdviceType.SURGERYBREASTAMCPain01,
    AutomaticAdviceType.SURGERYBREASTAMCPain02,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCPainImmunologic01Pain,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCPainImmunologic02Stroke,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCRashImmunologic01SkinReaction,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic01Embolia,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic02Embolia,
    AutomaticAdviceType.SURGERYBREASTAMCSHORTNESSOFBREATH02Embolia,
    AutomaticAdviceType.SURGERYBREASTAMCSHORTNESSOFBREATH03ChestPain,
    AutomaticAdviceType.SURGERYBREASTAMCSHORTNESSOFBREATH04HighFever,
    AutomaticAdviceType.SURGERYBREASTAMCSurgeryDrain01SeverePain,
    AutomaticAdviceType.SURGERYBREASTAMCSurgeryDrain02MoreSeverePain,
    AutomaticAdviceType.SURGERYBREASTAMCSurgeryDrain03Bleeding,
    AutomaticAdviceType.SURGERYBREASTAMCSurgeryDrain04Redness,
    AutomaticAdviceType.SURGERYBREASTAMCSurgeryDrain05HighFever,
    AutomaticAdviceType.SURGERYBREASTAMCSurgeryDrain07SwollenWound,
    AutomaticAdviceType.SURGERYBREASTAMCSurgeryDrain08SwollenWoundWithBleeding,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCVertigo01Stroke,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCVertigo02NeurologicImpairment,
    AutomaticAdviceType.SURGERYBREASTAMCWoundAppearance01InternalBleeding,
    AutomaticAdviceType.EATINGPROBLEMSAMC01Dehydration,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCInfection05Fever,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCInfection06Fever,
    AutomaticAdviceType.SURGERYBREASTAMCWoundAppearance02Paracentesis,
    AutomaticAdviceType.RASH03,
    AutomaticAdviceType.RESPIRATORY16Fever,
    AutomaticAdviceType.AMCabdominal11obstruction,
    AutomaticAdviceType.AMCabdominal27Fever,
    AutomaticAdviceType.MYELOMAAMCFever01Infection,
    AutomaticAdviceType.MYELOMAAMCCirculatory01Embolia,
    AutomaticAdviceType.MYELOMAAMCCirculatory02Embolia,
    AutomaticAdviceType.EATINGPROBLMESORMOUTHSYMPTOMS01Dehydration,
    AutomaticAdviceType.INFECTIONORFEVER04Infection,
    AutomaticAdviceType.LYMPHOMARECOVERYMCNNausea01Dehydraion,
    AutomaticAdviceType.MELANOMA_RECOVERYAMCInfection08Fever,
    AutomaticAdviceType.CYTOSTATICGENERALAMCFever001Infection,
    AutomaticAdviceType.CYTOSTATICGENERALAMCNausea001Dehydration,
    AutomaticAdviceType.LYMPHOMARECOVERYMCNNausea001Dehydraion,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCFatigueAt001Infection,
    AutomaticAdviceType.CYTOSTATICGENERALAMCOtherSymptom001,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic001Colitis,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic002Colitis,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCAbdomenImmunologic003Pancreatis,
    AutomaticAdviceType.ABDOMEN002AMC009_SEVERE_ABDOMINAL_PAIN_INSTRUCTIONS,
    AutomaticAdviceType.CYTOSTATICGENERALAMCDiarrhea001Dehydration,
    AutomaticAdviceType.SURGERYBREASTAMCDiarrhea002Dehydration,
    AutomaticAdviceType.AMCabdominal011obstruction,
    AutomaticAdviceType.AMCabdominal027Fever,
    AutomaticAdviceType.EATINGPROBLMESORMOUTHSYMPTOMS001Dehydration,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic001Embolia,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCRespiratoryImmunologic002Embolia,
    AutomaticAdviceType.SURGERYBREASTAMCSHORTNESSOFBREATH002Embolia,
    AutomaticAdviceType.SURGERYBREASTAMCSHORTNESSOFBREATH004HighFever,
    AutomaticAdviceType.RESPIRATORY016Fever,
    AutomaticAdviceType.SURGERYBREASTAMCPain001,
    AutomaticAdviceType.SURGERYBREASTAMCPain002,
    AutomaticAdviceType.CYTOSTATICGENERALAMCBoneOrMusclePain001Pain,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCPainImmunologic002Stroke,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCRashImmunologic001SkinReaction,
    AutomaticAdviceType.RASH003,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness001GuillanBarre,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness002AutoImmune,
    AutomaticAdviceType.IMMUNOLOGICTREATMENTSAMCNeuropathyOrMuscleWeakness003AutoImmune,
    AutomaticAdviceType.SURGERYBREASTAMCSHORTNESSOFBREATH04HighFeverSE
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
