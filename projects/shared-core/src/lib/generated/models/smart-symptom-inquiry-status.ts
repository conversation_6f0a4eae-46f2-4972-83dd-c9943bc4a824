// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.SmartSymptomInquiryStatus

import { SmartSymptomInquiryProgress } from './smart-symptom-inquiry-progress';
import { Symptom } from './symptom';
export interface SmartSymptomInquiryStatus {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    lastUpdated?: Date | number | string;
    pendingSymptoms?: Symptom[];
    progress?: SmartSymptomInquiryProgress;
    noPendingSymptomSelected?: boolean;
    wasSymptomFree?: boolean;
    active?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
