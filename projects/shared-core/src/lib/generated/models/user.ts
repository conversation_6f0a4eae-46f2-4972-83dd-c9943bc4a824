// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.security.model.User

export interface User {
    userId?: string;
    emailAddress?: string;
    emailAddressValidated?: boolean;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    failedLoginCount?: number;
    lockedOut?: boolean;
    passwordExpirationDate?: Date | number | string;
    created?: Date | number | string;
    modified?: Date | number | string;
    fullName?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
