// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.api.dtos.provideranalytics.ProviderAnalyticsFilters

export interface ProviderAnalyticsFilters {
    year?: number;
    month?: number;
    careTeam?: string;
    caseStatus?: string;
    caseType?: string;
    questionnaire?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
