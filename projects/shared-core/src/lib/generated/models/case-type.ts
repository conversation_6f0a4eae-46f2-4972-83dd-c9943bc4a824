// Auto generated by ad<PERSON><PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseType

export enum CaseType {
    SYMPTOM = 'symptom',
    CONTACT_WITHOUT_SYMPTOM = 'contactWithoutSymptom',
    SYMPTOM_REPORT = 'symptomReport',
    QUESTIONNAIRE = 'questionnaire',
    FOLLOW_UP = 'followUp',
    NOTE = 'note',
    SYMPTOM_MANAGEMENT = 'symptomManagement',
    CHEMO_TX_QUESTIONS = 'chemoTxQuestions',
    MEDICATION_INSTRUCTIONS = 'medicationInstructions',
    LAB_QUESTIONS_OR_TEST_RESULTS = 'labQuestionsOrTestResults',
    APPOINTMENT_QUESTIONS = 'appointmentQuestions',
    INSURANCE_QUESTIONS = 'insuranceQuestions',
    MEDICATION_REFILL = 'medicationRefill',
    PAPERWORK = 'paperwork',
    PRETEST_INSTRUCTIONS = 'pretestInstructions',
    REFERRALS = 'referrals',
    OTHER = 'other',
    PATIENT_INBOX_NOTIFICATION = 'patientInboxNotification',
    PRACTICAL_PROBLEMS = 'practicalProblems',
    SYMPTOM_FROM_QUESTIONNAIRE = 'symptomFromQuestionnaire',
    MEDICAL_RECORDS = 'medicalRecords',
    TREATMENT_SCHEDULING = 'treatmentScheduling',
    INTERPERSONAL_SAFETY = 'interpersonalSafety',
}

export const AllCaseType = [
    CaseType.SYMPTOM,
    CaseType.CONTACT_WITHOUT_SYMPTOM,
    CaseType.SYMPTOM_REPORT,
    CaseType.QUESTIONNAIRE,
    CaseType.FOLLOW_UP,
    CaseType.NOTE,
    CaseType.SYMPTOM_MANAGEMENT,
    CaseType.CHEMO_TX_QUESTIONS,
    CaseType.MEDICATION_INSTRUCTIONS,
    CaseType.LAB_QUESTIONS_OR_TEST_RESULTS,
    CaseType.APPOINTMENT_QUESTIONS,
    CaseType.INSURANCE_QUESTIONS,
    CaseType.MEDICATION_REFILL,
    CaseType.PAPERWORK,
    CaseType.PRETEST_INSTRUCTIONS,
    CaseType.REFERRALS,
    CaseType.OTHER,
    CaseType.PATIENT_INBOX_NOTIFICATION,
    CaseType.PRACTICAL_PROBLEMS,
    CaseType.SYMPTOM_FROM_QUESTIONNAIRE,
    CaseType.MEDICAL_RECORDS,
    CaseType.TREATMENT_SCHEDULING,
    CaseType.INTERPERSONAL_SAFETY,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
