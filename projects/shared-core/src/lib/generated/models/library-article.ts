// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.library.dto.LibraryArticle

export interface LibraryArticle {
    id?: string;
    language?: string;
    label?: string;
    contentOrigin?: string;
    contentType?: string;
    category?: string;
    topic?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
