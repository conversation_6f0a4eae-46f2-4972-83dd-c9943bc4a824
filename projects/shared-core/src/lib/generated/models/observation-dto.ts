// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.labsview.dto.ObservationDto

import { ReferenceRangeDto } from './reference-range-dto';
export interface ObservationDto {
    observationId?: string;
    observationName?: string;
    unit?: string;
    value?: string;
    valueString?: string;
    referenceRange?: ReferenceRangeDto;
    interpretation?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
