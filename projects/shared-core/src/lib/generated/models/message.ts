// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.Message

import { CareTeam } from './care-team';
import { MessageType } from './message-type';
import { QuestionnaireInquiry } from './questionnaire-inquiry';
import { ScheduledMessage } from './scheduled-message';
import { SymptomInquiry } from './symptom-inquiry';
import { TopicType } from './topic-type';
import { User } from './user';
export interface Message {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    sender?: User;
    title?: string;
    content?: string;
    read?: boolean;
    firstMessage?: Message;
    lastRequest?: boolean;
    lastResponse?: boolean;
    type?: MessageType;
    messageDate?: Date | number | string;
    topicType?: TopicType;
    linkOne?: string;
    validated?: boolean;
    questionnaireInquiry?: QuestionnaireInquiry;
    getScheduledMessage?: ScheduledMessage;
    questionaryAnswered?: boolean;
    yesNoStatus?: boolean;
    totalHandlingTime?: number;
    instructionsSufficient?: boolean;
    instructionsRating?: number;
    lastAdviser?: User;
    messageReadMoment?: Date | number | string;
    symptomReportId?: string;
    symptomInquiry?: SymptomInquiry;
    automaticRuleId?: string;
    consultedUser?: User;
    consultedCareTeam?: CareTeam;
    consultingUser?: User;
    consultingTeamIds?: string[];
    reminderDate?: Date | number | string;
    senderNoona?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
