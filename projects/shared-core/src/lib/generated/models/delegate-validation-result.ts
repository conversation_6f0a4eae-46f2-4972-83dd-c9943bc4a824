// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.DelegateValidationResult

export enum DelegateValidationResult {
    OK = 'ok',
    MISSING_FIELD = 'missingField',
    INVALID_EMAIL = 'invalidEmail',
    UNKNOWN_ERROR = 'unknownError',
}

export const AllDelegateValidationResult = [
    DelegateValidationResult.OK,
    DelegateValidationResult.MISSING_FIELD,
    DelegateValidationResult.INVALID_EMAIL,
    DelegateValidationResult.UNKNOWN_ERROR,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
