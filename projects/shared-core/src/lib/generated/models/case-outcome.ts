// Auto generated by ad<PERSON><PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseOutcome

export enum CaseOutcome {
    PROVIDER_CONSULTED = 'providerConsulted',
    ADDITIONAL_FOLLOW_UP_REQUIRED = 'additionalFollowUpRequired',
    HOSPITAL_ADMIT = 'hospitalAdmit',
    RESOLVED_ON_PHONE = 'resolvedOnPhone',
    SCHEDULED_VISIT = 'scheduledVisit',
    SENT_PT_TO_ER_OR_HOSP = 'sentPtToErOrHosp',
    NON_CLINICAL_CALL = 'nonClinicalCall',
    COVID_19 = 'covid19',
    LEFT_MESSAGE = 'leftMessage',
    UNABLE_TO_CONNECT = 'unableToConnect',
    NEEDS_FOLLOW_UP = 'needsFollowUp',
    NO_FOLLOW_UP_NEEDED = 'noFollowUpNeeded',
}

export const AllCaseOutcome = [
    CaseOutcome.PROVIDER_CONSULTED,
    CaseOutcome.ADDITIONAL_FOLLOW_UP_REQUIRED,
    CaseOutcome.HOSPITAL_ADMIT,
    CaseOutcome.RESOLVED_ON_PHONE,
    CaseOutcome.SCHEDULED_VISIT,
    CaseOutcome.SENT_PT_TO_ER_OR_HOSP,
    CaseOutcome.NON_CLINICAL_CALL,
    CaseOutcome.COVID_19,
    CaseOutcome.LEFT_MESSAGE,
    CaseOutcome.UNABLE_TO_CONNECT,
    CaseOutcome.NEEDS_FOLLOW_UP,
    CaseOutcome.NO_FOLLOW_UP_NEEDED,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
