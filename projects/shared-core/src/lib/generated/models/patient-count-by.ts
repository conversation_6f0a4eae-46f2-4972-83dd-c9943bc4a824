// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.PatientCountBy

export enum PatientCountBy {
    ACTIVE_MESSAGES = 'activeMessages',
    SYMPTOM_INQUIRIES = 'symptomInquiries',
    UNVALIDATED_AUTOMATIC_MESSAGES = 'unvalidatedAutomaticMessages',
    RECEIVED_MESSAGES = 'receivedMessages',
}

export const AllPatientCountBy = [
    PatientCountBy.ACTIVE_MESSAGES,
    PatientCountBy.SYMPTOM_INQUIRIES,
    PatientCountBy.UNVALIDATED_AUTOMATIC_MESSAGES,
    PatientCountBy.RECEIVED_MESSAGES,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
