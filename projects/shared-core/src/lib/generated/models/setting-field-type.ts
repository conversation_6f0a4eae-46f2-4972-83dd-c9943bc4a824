// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.SettingFieldType

export enum SettingFieldType {
    PATIENT_MEDICAL_RECORD_NUMBER = 'medicalRecordNumber',
    PATIENT_HIPAA_CODE = 'hipaaCode',
    PATIENT_PHONE_NUMBER2 = 'phoneNumber2',
    PATIENT_PHONE_NUMBER3 = 'phoneNumber3',
    PATIENT_ADDRESS = 'address',
    PATIENT_ZIP_CODE = 'zipCode',
    PATIENT_CITY = 'city',
    PATIENT_STATE = 'state',
    PATIENT_PRIMARY_PROVIDER = 'primaryProvider',
    PATIENT_CLINIC_SITE = 'clinicSite',
}

export const AllSettingFieldType = [
    SettingFieldType.PATIENT_MEDICAL_RECORD_NUMBER,
    SettingFieldType.PATIENT_HIPAA_CODE,
    SettingFieldType.PATIENT_PHONE_NUMBER2,
    SettingFieldType.PATIENT_PHONE_NUMBER3,
    SettingFieldType.PATIENT_ADDRESS,
    SettingFieldType.PATIENT_ZIP_CODE,
    SettingFieldType.PATIENT_CITY,
    SettingFieldType.PATIENT_STATE,
    SettingFieldType.PATIENT_PRIMARY_PROVIDER,
    SettingFieldType.PATIENT_CLINIC_SITE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
