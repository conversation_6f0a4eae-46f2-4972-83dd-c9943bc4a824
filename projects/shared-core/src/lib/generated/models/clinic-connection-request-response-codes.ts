// Auto generated by ad<PERSON><PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.clinicconnection.models.enums.ClinicConnectionRequestResponseCodes

export enum ClinicConnectionRequestResponseCodes {
    NO_PATIENT = 'nopatient',
    NOT_UNIQUE_PHONE_NUMBER = 'notuniquephonenumber',
    MISSING_TREATMENT_DATA = 'missingtreatmentdata',
    NUMBER_CODE_MISSMATCH = 'numbercodemissmatch',
    EXISTING_REQUESTS = 'existingrequests',
    NO_REQUEST = 'norequest',
    PATIENT_CHANGED = 'patientchanged',
    ALREADY_PATIENT = 'alreadypatient',
    INVALID_IDENTITY_CODE = 'invalididentitycode',
}

export const AllClinicConnectionRequestResponseCodes = [
    ClinicConnectionRequestResponseCodes.NO_PATIENT,
    ClinicConnectionRequestResponseCodes.NOT_UNIQUE_PHONE_NUMBER,
    ClinicConnectionRequestResponseCodes.MISSING_TREATMENT_DATA,
    ClinicConnectionRequestResponseCodes.NUMBER_CODE_MISSMATCH,
    ClinicConnectionRequestResponseCodes.EXISTING_REQUESTS,
    ClinicConnectionRequestResponseCodes.NO_REQUEST,
    ClinicConnectionRequestResponseCodes.PATIENT_CHANGED,
    ClinicConnectionRequestResponseCodes.ALREADY_PATIENT,
    ClinicConnectionRequestResponseCodes.INVALID_IDENTITY_CODE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
