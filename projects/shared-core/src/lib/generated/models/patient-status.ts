// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.patient.PatientStatus

import { PatientStatusType } from './patient-status-type';
export interface PatientStatus {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    type?: PatientStatusType;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
