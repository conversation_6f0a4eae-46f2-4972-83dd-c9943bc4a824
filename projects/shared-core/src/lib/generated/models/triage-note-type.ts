// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.triage.enums.TriageNoteType

export enum TriageNoteType {
    CLINIC_USER = 'CLINIC_USER',
    CASE_NOTE = 'CASE_NOTE',
    PATIENT = 'PATIENT',
}

export const AllTriageNoteType = [TriageNoteType.CLINIC_USER, TriageNoteType.CASE_NOTE, TriageNoteType.PATIENT];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
