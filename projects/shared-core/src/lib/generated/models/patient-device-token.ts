// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.PatientDeviceToken

import { DeviceType } from './device-type';
import { LoginToken } from './login-token';
export interface PatientDeviceToken {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    deviceType?: DeviceType;
    deviceId?: string;
    deviceLabel?: string;
    deviceToken?: string;
    loginToken?: LoginToken;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
