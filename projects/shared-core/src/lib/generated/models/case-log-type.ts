// Auto generated by ad<PERSON><PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseLogType

export enum CaseLogType {
    CASE_CREATED = 'caseCreated',
    SYMPTOM_ADDED = 'symptomAdded',
    SYMPTOM_REMOVED = 'symptomRemoved',
    SYMPTOM_MODIFIED = 'symptomModified',
    CONTENT_REMOVED = 'contentRemoved',
    CONTENT_ADDED = 'contentAdded',
    CONTENT_MODIFIED = 'contentModified',
    MEDICATION_REFILL_ADDED = 'medicationRefillAdded',
    MEDICATION_REFILL_MODIFIED = 'medicationRefillModified',
    CONSULTATION_REQUESTED = 'consultationRequested',
    CONSULTATION_RESPONDED = 'consultationResponded',
    CONSULTATION_CANCELLED = 'consultationCancelled',
    NOTE_ADDED = 'noteAdded',
    NOTE_EDITED = 'noteEdited',
    CASE_CLOSED = 'caseClosed',
    INTEGRATION_CONTENT_ARCHIVED = 'integrationContentArchived',
    EXPORTED_PDF = 'exportedPdf',
    EXPORTED_TEXT = 'exportedText',
    SYMPTOM_OPENED = 'symptomOpened',
    SYMPTOM_OPENED_FOR_PATIENT = 'symptomOpenedForPatient',
    QUESTIONNAIRE_OPENED = 'questionnaireOpened',
    QUESTIONNAIRE_OPENED_FOR_PATIENT = 'questionnaireOpenedForPatient',
}

export const AllCaseLogType = [
    CaseLogType.CASE_CREATED,
    CaseLogType.SYMPTOM_ADDED,
    CaseLogType.SYMPTOM_REMOVED,
    CaseLogType.SYMPTOM_MODIFIED,
    CaseLogType.CONTENT_REMOVED,
    CaseLogType.CONTENT_ADDED,
    CaseLogType.CONTENT_MODIFIED,
    CaseLogType.MEDICATION_REFILL_ADDED,
    CaseLogType.MEDICATION_REFILL_MODIFIED,
    CaseLogType.CONSULTATION_REQUESTED,
    CaseLogType.CONSULTATION_RESPONDED,
    CaseLogType.CONSULTATION_CANCELLED,
    CaseLogType.NOTE_ADDED,
    CaseLogType.NOTE_EDITED,
    CaseLogType.CASE_CLOSED,
    CaseLogType.INTEGRATION_CONTENT_ARCHIVED,
    CaseLogType.EXPORTED_PDF,
    CaseLogType.EXPORTED_TEXT,
    CaseLogType.SYMPTOM_OPENED,
    CaseLogType.SYMPTOM_OPENED_FOR_PATIENT,
    CaseLogType.QUESTIONNAIRE_OPENED,
    CaseLogType.QUESTIONNAIRE_OPENED_FOR_PATIENT,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
