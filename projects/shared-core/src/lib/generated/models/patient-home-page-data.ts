// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.PatientHomePageData

import { Clinic } from './clinic';
import { Notification } from './notification';
import { RequiredConsentInformation } from './required-consent-information';
export interface PatientHomePageData {
    newNotificationCount?: number;
    newMessageCount?: number;
    newSupportMessageCount?: number;
    connectionRequests?: number;
    notifications?: Notification[];
    clinic?: Clinic;
    requiredConsents?: RequiredConsentInformation[];
    lastNotificationRead?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
