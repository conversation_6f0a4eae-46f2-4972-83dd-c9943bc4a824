// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.MedicationCategory

export enum MedicationCategory {
    ORAL_ONCOLYTIC = 'oralOncolytic',
    CONTROLLED_SUBSTANCE_1_2 = 'controlledSubstance12',
    OTHER_RETAIL = 'otherRetail',
}

export const AllMedicationCategory = [
    MedicationCategory.ORAL_ONCOLYTIC,
    MedicationCategory.CONTROLLED_SUBSTANCE_1_2,
    MedicationCategory.OTHER_RETAIL,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
