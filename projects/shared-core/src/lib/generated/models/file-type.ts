// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.FileType

export enum FileType {
    AEQ_REPORT = 0,
    SYMPTOM_PDF = 1,
    MESSAGE_PDF = 2,
    DATA_EXPORT = 3,
    <PERSON><PERSON><PERSON><PERSON>_DATA_EXPORT = 4,
    PATIENT_DATA_EXPORT = 5,
    CASE_PDF = 6,
    QOL_QUESTIONARY_PDF = 7,
    CCD_ZIP = 8,
}

export const AllFileType = [
    FileType.AEQ_REPORT,
    FileType.SYMPTOM_PDF,
    FileType.MESSAGE_PDF,
    FileType.DATA_EXPORT,
    FileType.CLINIC_DATA_EXPORT,
    FileType.PATIENT_DATA_EXPORT,
    FileType.CASE_PDF,
    FileType.QOL_QUESTIONARY_PDF,
    FileType.CCD_ZIP,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
