// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.ActiveConsentsModel

import { ClinicConsent } from './clinic-consent';
import { PatientConsentType } from './patient-consent-type';
export interface ActiveConsentsModel {
    activeConsents?: { [key in PatientConsentType]: ClinicConsent };
    mandatoryness?: { [key in PatientConsentType]: boolean };
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
