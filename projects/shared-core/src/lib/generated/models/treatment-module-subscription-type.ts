// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.treatment.TreatmentModuleSubscriptionType

export enum TreatmentModuleSubscriptionType {
    SYMPTOM_REPORT = 'symptomReport',
    CONTACT_CLINIC = 'contactClinic',
}

export const AllTreatmentModuleSubscriptionType = [
    TreatmentModuleSubscriptionType.SYMPTOM_REPORT,
    TreatmentModuleSubscriptionType.CONTACT_CLINIC,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
