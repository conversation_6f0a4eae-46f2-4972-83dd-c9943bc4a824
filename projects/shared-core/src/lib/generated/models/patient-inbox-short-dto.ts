// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.dto.PatientInboxShortDto

import { CaseType } from './case-type';
import { FirstMessage } from './first-message';
import { InquiryStatus } from './inquiry-status';
import { MessageType } from './message-type';
import { NotificationType } from './notification-type';
import { QuestionaryType } from './questionary-type';
import { SupportMessageType } from './support-message-type';
import { TopicType } from './topic-type';
import { User } from './user';
export interface PatientInboxShortDto {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    messageDate?: Date | number | string;
    title?: string;
    stype?: SupportMessageType;
    mtype?: MessageType;
    ntype?: NotificationType;
    otherTitle?: string;
    topicType?: TopicType;
    sender?: User;
    read?: boolean;
    removed?: boolean;
    caseId?: string;
    caseType?: CaseType;
    inquiryStatus?: InquiryStatus;
    questionnaireInquiryType?: QuestionaryType;
    firstMessage?: FirstMessage;
    patientMessage?: boolean;
    messageId?: string;
    supportMessage?: boolean;
    messageTitle?: string;
    senderNoona?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
