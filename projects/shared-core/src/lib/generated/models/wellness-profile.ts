// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.WellnessProfile

export interface WellnessProfile {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    w2eUserName?: string;
    w2eAccessTokenExpires?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
