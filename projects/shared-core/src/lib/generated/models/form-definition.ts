// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.schema.diagnosis.FormDefinition

import { FormItemDefinition } from './form-item-definition';
export interface FormDefinition {
    name?: string;
    formOptions?: { [key in string]: string };
    fields?: FormItemDefinition[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
