// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.Workflow

import { FollowUp } from './follow-up';
import { Message } from './message';
import { Patient } from './patient';
import { Questionary } from './questionary';
import { Symptom } from './symptom';
import { SymptomReport } from './symptom-report';
import { User } from './user';
import { WorkflowState } from './workflow-state';
export interface Workflow {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    patient?: Patient;
    symptoms?: Symptom[];
    symptomReports?: SymptomReport[];
    questionaries?: Questionary[];
    messages?: Message[];
    followUps?: FollowUp[];
    started?: Date | number | string;
    completed?: Date | number | string;
    lastSubmitted?: Date | number | string;
    lastResponded?: Date | number | string;
    state?: WorkflowState;
    waitingDiagnosis?: boolean;
    details?: string;
    assigned?: Date | number | string;
    assignee?: User;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
