// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.analytics.model.reporting.MessageWeekView

import { MessageCounts } from './message-counts';
export interface MessageWeekView {
    careTeamName?: string;
    firstDay?: Date | number | string;
    lastDay?: Date | number | string;
    counts?: { [key in string]: MessageCounts };
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
