// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseDelayReason

export enum CaseDelayReason {
    CANNOT_REACH_OR_WAITING_PATIENT_CALLBACK = 'cannotReachOrWaitingPatientCallback',
    HIGH_CALL_VOLUME = 'highCallVolume',
    LOWER_PRIORITY = 'lowerPriority',
    NO_RESPONSE_FROM_PROVIDER = 'noResponseFromProvider',
    TECHNOLOGY_ISSUE = 'technologyIssue',
    RESEARCH_DRIVEN_DELAY = 'researchDrivenDelay',
    AFTER_HOURS = 'afterHours',
}

export const AllCaseDelayReason = [
    CaseDelayReason.CANNOT_REACH_OR_WAITING_PATIENT_CALLBACK,
    CaseDelayReason.HIGH_CALL_VOLUME,
    CaseDelayReason.LOWER_PRIORITY,
    CaseDelayReason.NO_RESPONSE_FROM_PROVIDER,
    CaseDelayReason.TECHNOLOGY_ISSUE,
    CaseDelayReason.RESEARCH_DRIVEN_DELAY,
    CaseDelayReason.AFTER_HOURS,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
