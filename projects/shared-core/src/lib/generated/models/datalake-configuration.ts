// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.integration.DatalakeConfiguration

export interface DatalakeConfiguration {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    accountName?: string;
    sastoken?: string;
    publicKey?: string;
    containerName?: string;
    encryptionKeyId?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
