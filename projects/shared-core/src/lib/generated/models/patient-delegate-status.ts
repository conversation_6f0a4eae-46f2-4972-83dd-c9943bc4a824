// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientdelegate.PatientDelegateStatus

export enum PatientDelegateStatus {
    UNDEFINED = 0,
    NOT_CREATED = 1,
    ACTIVE = 2,
    INVITATION_SENT = 3,
    INVITATION_REMINDER_SENT = 4,
}

export const AllPatientDelegateStatus = [
    PatientDelegateStatus.UNDEFINED,
    PatientDelegateStatus.NOT_CREATED,
    PatientDelegateStatus.ACTIVE,
    PatientDelegateStatus.INVITATION_SENT,
    PatientDelegateStatus.INVITATION_REMINDER_SENT,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
