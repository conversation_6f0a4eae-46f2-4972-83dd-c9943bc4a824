// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.integration.SmartOnFhirConfiguration

export interface SmartOnFhirConfiguration {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    smartServer?: string;
    patientAppClientId?: string;
    patientAppFhirApi?: string;
    patientIdentifierToUse?: string;
    patientIdentifierField?: string;
    patientLandingPage?: string;
    providerAppClientId?: string;
    providerAppFhirApi?: string;
    providerIdentifierToUse?: string;
    providerIdentifierField?: string;
    providerLandingPage?: string;
    patientIdentifierToUseForProviderApp?: string;
    patientIdentifierFieldForProviderApp?: string;
    providerLandingPageForPatient?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
