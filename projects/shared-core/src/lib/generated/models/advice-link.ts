// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.rule.model.AdviceLink

import { AutomaticAdviceType } from './automatic-advice-type';
export interface AdviceLink {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    type?: AutomaticAdviceType;
    key?: string;
    url_fi_FI?: string;
    url_sv_FI?: string;
    url_en_GB?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
