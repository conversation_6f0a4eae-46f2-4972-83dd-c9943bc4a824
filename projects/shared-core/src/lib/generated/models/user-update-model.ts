// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.UserUpdateModel

import { CareTeam } from './care-team';
import { Gender } from './gender';
import { NotificationMethod } from './notification-method';
import { PatientProfile } from './patient-profile';
import { SmartSymptomFrequency } from './smart-symptom-frequency';
export interface UserUpdateModel {
    id?: string;
    firstName?: string;
    lastName?: string;
    emailAddress?: string;
    phoneNumber?: string;
    localeId?: string;
    notificationMethod?: NotificationMethod;
    consultationNotificationsEnabled?: boolean;
    careTeamNotificationsEnabled?: boolean;
    unsubscribedCareTeams?: CareTeam[];
    smartSymptomFrequency?: SmartSymptomFrequency;
    twoFactorAuthentication?: boolean;
    patientProfile?: PatientProfile;
    gender?: Gender;
    birthDate?: Date | number | string;
    identityCode?: string;
    externalId?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
