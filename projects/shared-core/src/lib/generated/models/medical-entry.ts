// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.medical.MedicalEntry

import { MedicalEntryType } from './medical-entry-type';
export interface MedicalEntry {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    type?: MedicalEntryType;
    beginDate?: Date | number | string;
    endDate?: Date | number | string;
    author?: string;
    children?: MedicalEntry[];
    primaryDiagnosis?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
