// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.triage.model.TriageSymptom

import { SymptomType } from './symptom-type';
import { TriageRuleOperation } from './triage-rule-operation';
import { TriageSymptomGrading } from './triage-symptom-grading';
export interface TriageSymptom {
    id?: string;
    symptom?: SymptomType;
    triageRuleOperation?: TriageRuleOperation;
    gradings?: TriageSymptomGrading[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
