// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.content.model.EnvironmentLocalisation

import { EnvironmentLocalisationType } from './environment-localisation-type';
import { Language } from './language';
export interface EnvironmentLocalisation {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    type?: EnvironmentLocalisationType;
    locale?: Language;
    content?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
