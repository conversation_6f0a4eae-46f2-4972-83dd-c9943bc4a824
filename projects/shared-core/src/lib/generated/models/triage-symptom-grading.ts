// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.triage.model.TriageSymptomGrading

import { ScalingSystem } from './scaling-system';
import { TriageGradingValueType } from './triage-grading-value-type';
import { TriageSymptom } from './triage-symptom';
export interface TriageSymptomGrading {
    id?: string;
    triageSymptom?: TriageSymptom;
    subSymptom?: string;
    grading?: ScalingSystem;
    value?: number;
    fraction?: number;
    valueType?: TriageGradingValueType;
    inclusive?: boolean;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
