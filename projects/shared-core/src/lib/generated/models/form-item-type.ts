// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.schema.form.FormItemType

export enum FormItemType {
    ROW = 'rowItem',
    SECTION = 'section',
    TEXT = 'textLineField',
    TEXTAREA = 'textareaField',
    EMAIL = 'emailField',
    PHONE_NUMBER = 'phoneNumberField',
    URL = 'urlField',
    DATE = 'dateField',
    INTEGER = 'integerField',
    DOUBLE = 'doubleField',
    CHECKBOX = 'checkboxList',
    RADIO = 'radioList',
    SELECT = 'selectList',
    SLIDER = 'sliderItem',
    PAIN_POINTER = 'painPointerList',
    SUB_HEADER = 'subHeader',
    TEXT_BLOCK = 'textBlock',
    WIZARD_SECTION = 'wizardSection',
    DATE_RANGE = 'dateRangeItem',
    PHOTO = 'photoItem',
    TOGGLE = 'toggleItem',
    INPUT_GROUP = 'inputGroup',
    EARLIER_SYMPTOM = 'earlierSymptom',
    YES_NO_DATE = 'yesNoDateComponent',
    MULTISELECT = 'multiselectList',
    MEASUREMENT = 'measurementComponent',
    CHECKBOX_WITH_EXTRA_FIELDS = 'checkboxListWithExtraFields',
    QUESTION_GROUP = 'questionGroup',
    RADIO_WITH_EXTRA_FIELDS = 'radioListWithExtraFields',
    SELECT_WITH_RADIO = 'selectListWithRadio',
    EVENT_MODAL = 'eventModal',
    NUMERIC_FIELD = 'numericField',
    TEXT_INPUT_FIELD = 'textInputField',
}

export const AllFormItemType = [
    FormItemType.ROW,
    FormItemType.SECTION,
    FormItemType.TEXT,
    FormItemType.TEXTAREA,
    FormItemType.EMAIL,
    FormItemType.PHONE_NUMBER,
    FormItemType.URL,
    FormItemType.DATE,
    FormItemType.INTEGER,
    FormItemType.DOUBLE,
    FormItemType.CHECKBOX,
    FormItemType.RADIO,
    FormItemType.SELECT,
    FormItemType.SLIDER,
    FormItemType.PAIN_POINTER,
    FormItemType.SUB_HEADER,
    FormItemType.TEXT_BLOCK,
    FormItemType.WIZARD_SECTION,
    FormItemType.DATE_RANGE,
    FormItemType.PHOTO,
    FormItemType.TOGGLE,
    FormItemType.INPUT_GROUP,
    FormItemType.EARLIER_SYMPTOM,
    FormItemType.YES_NO_DATE,
    FormItemType.MULTISELECT,
    FormItemType.MEASUREMENT,
    FormItemType.CHECKBOX_WITH_EXTRA_FIELDS,
    FormItemType.QUESTION_GROUP,
    FormItemType.RADIO_WITH_EXTRA_FIELDS,
    FormItemType.SELECT_WITH_RADIO,
    FormItemType.EVENT_MODAL,
    FormItemType.NUMERIC_FIELD,
    FormItemType.TEXT_INPUT_FIELD,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
