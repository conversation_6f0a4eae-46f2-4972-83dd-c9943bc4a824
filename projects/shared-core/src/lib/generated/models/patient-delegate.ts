// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientdelegate.PatientDelegate

import { Patient } from './patient';
import { PatientDelegateStatus } from './patient-delegate-status';
export interface PatientDelegate {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    patient?: Patient;
    emailAddress?: string;
    firstName?: string;
    lastName?: string;
    status?: PatientDelegateStatus;
    fullName?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
