// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.triage.model.TriageNote

import { TriageNoteTranslation } from './triage-note-translation';
import { TriageNoteType } from './triage-note-type';
import { TriageRuleNote } from './triage-rule-note';
export interface TriageNote {
    id?: string;
    noteType?: TriageNoteType;
    triageRuleNotes?: TriageRuleNote[];
    triageNoteTranslations?: TriageNoteTranslation[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
