// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.diarytimeline.query.DiaryTimelineQueryResult

import { DiaryTimelineDay } from './diary-timeline-day';
import { DiaryTimelineQuery } from './diary-timeline-query';
export interface DiaryTimelineQueryResult {
    referenceDay?: DiaryTimelineDay;
    daysInFuture?: DiaryTimelineDay[];
    daysInPast?: DiaryTimelineDay[];
    query?: DiaryTimelineQuery;
    restDaysInFuture?: number;
    restDaysInPast?: number;
    clinicTimezone?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
