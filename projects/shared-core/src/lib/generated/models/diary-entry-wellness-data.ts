// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.diarytimeline.diaryentry.DiaryEntryWellnessData

export interface DiaryEntryWellnessData {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    energy?: number;
    appetite?: number;
    optimism?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
