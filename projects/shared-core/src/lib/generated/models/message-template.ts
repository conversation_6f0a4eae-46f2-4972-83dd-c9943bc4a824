// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.MessageTemplate

import { CareTeam } from './care-team';
import { ClinicLocalisation } from './clinic-localisation';
import { TopicType } from './topic-type';
export interface MessageTemplate {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    lastEditorFirstName?: string;
    lastEditorLastName?: string;
    lastEdited?: Date | number | string;
    title?: string;
    selectedTeams?: CareTeam[];
    contents?: ClinicLocalisation[];
    englishContent?: string;
    finnishContent?: string;
    swedishContent?: string;
    danishContent?: string;
    norwegianContent?: string;
    defaultForTopicType?: TopicType;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
