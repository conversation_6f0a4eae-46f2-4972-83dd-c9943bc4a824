// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.audit.model.NoonaLogEntryAction

export enum NoonaLogEntryAction {
    USER_ADD = 'userAdd',
    VIEW = 'view',
    USER_EDIT = 'userEdit',
    USER_LINK = 'userLink',
    CASE_DELETE = 'caseDelete',
}

export const AllNoonaLogEntryAction = [
    NoonaLogEntryAction.USER_ADD,
    NoonaLogEntryAction.VIEW,
    NoonaLogEntryAction.USER_EDIT,
    NoonaLogEntryAction.USER_LINK,
    NoonaLogEntryAction.CASE_DELETE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
