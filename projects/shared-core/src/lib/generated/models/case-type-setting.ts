// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.CaseTypeSetting

import { CasePriority } from './case-priority';
import { CaseType } from './case-type';
export interface CaseTypeSetting {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    caseType?: CaseType;
    enabled?: boolean;
    basePriority?: CasePriority;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
