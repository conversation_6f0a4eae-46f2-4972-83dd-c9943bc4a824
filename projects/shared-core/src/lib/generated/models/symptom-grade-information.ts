// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.SymptomGradeInformation

import { FieldGrading } from './field-grading';
export interface SymptomGradeInformation {
    maxGrade?: FieldGrading;
    minGrade?: FieldGrading;
    averageGrade?: number;
    symptomaticDays?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
