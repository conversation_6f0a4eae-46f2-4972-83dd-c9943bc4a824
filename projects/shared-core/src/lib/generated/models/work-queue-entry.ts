// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.workqueue.WorkQueueEntry

import { CasePriority } from './case-priority';
import { CaseStatus } from './case-status';
import { CaseType } from './case-type';
export interface WorkQueueEntry {
    id?: string;
    priority?: CasePriority;
    name?: string;
    medicalRecordNumber?: string;
    identityCode?: string;
    caseId?: string;
    caseType?: CaseType;
    created?: Date | number | string;
    status?: CaseStatus;
    careTeams?: string[];
    responsibleUsers?: string[];
    assignee?: string;
    escalated?: boolean;
    nurseControlled?: boolean;
    messageId?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
