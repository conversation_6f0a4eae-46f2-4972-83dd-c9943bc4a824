// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.api.dtos.provideranalytics.DelayedCasesData

export interface DelayedCasesData {
    id?: string;
    patientId?: string;
    date?: Date | number | string;
    contactType?: string;
    caseType?: string;
    careTeam?: string;
    assignee?: string;
    delayReason?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
