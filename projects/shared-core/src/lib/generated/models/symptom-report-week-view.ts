// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.analytics.model.reporting.SymptomReportWeekView

export interface SymptomReportWeekView {
    firstDay?: Date | number | string;
    lastDay?: Date | number | string;
    sentCount?: number;
    changedSentCount?: number;
    answeredCount?: number;
    answeredChange?: number;
    answeredPerc?: number;
    answeredPercChange?: number;
    expiredCount?: number;
    changedExpireCount?: number;
    pendingCount?: number;
    changedPendingCount?: number;
    nurseAnsweredForNonProxyCount?: number;
    nurseAnsweredForNonProxyChange?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
