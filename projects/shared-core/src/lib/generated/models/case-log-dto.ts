// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.dto.CaseLogDto

import { CaseLogType } from './case-log-type';
export interface CaseLogDto {
    caseId?: string;
    message?: string;
    actorName?: string;
    caseLogType?: CaseLogType;
    timestamp?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
