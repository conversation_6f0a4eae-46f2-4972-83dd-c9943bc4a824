// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.CareProvider

export interface CareProvider {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    firstName?: string;
    lastName?: string;
    middleName?: string;
    suffix?: string;
    enabled?: boolean;
    fullName?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
