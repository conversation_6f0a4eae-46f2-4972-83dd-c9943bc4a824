// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.PhoneNumberType

export enum PhoneNumberType {
    MOBILE = 'mobile',
    WORK = 'work',
    HOME = 'home',
}

export const AllPhoneNumberType = [PhoneNumberType.MOBILE, PhoneNumberType.WORK, PhoneNumberType.HOME];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
