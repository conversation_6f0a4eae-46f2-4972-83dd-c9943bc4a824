// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.api.model.UserModel

import { CareTeam } from './care-team';
export interface UserModel {
    id?: string;
    firstName?: string;
    lastName?: string;
    emailAddress?: string;
    phoneNumber?: string;
    locked?: boolean;
    roles?: string[];
    careTeams?: CareTeam[];
    identityCode?: string;
    externalId?: string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
