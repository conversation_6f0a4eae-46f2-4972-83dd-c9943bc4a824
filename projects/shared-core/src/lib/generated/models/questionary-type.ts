// Auto generated by adel<PERSON>.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.QuestionaryType

export enum QuestionaryType {
    EXAMPLE = 'questionaryExample',
    QOL15_D = 'qol15D',
    DISTRESS_AND_PROBLEM_LIST = 'distressAndProblemList',
    DISTRESS_FOLLOW_UP = 'distressFollowUp',
    HNQOL = 'hnqol',
    FACT_GOG_NTX = 'factGogNtx',
    BOUNCE_TIPI = 'bounceTipi',
    BOUNCE_CERQ = 'bounceCerq',
    BOUNCE_LOT_R = 'bounceLotR',
    BOUNCE_EORTC_QLQ_BR_23 = 'bounceEortcQlqBr23',
    BOUNCE_MAAS = 'bounceMaas',
    BOUNCE_HADS = 'bounceHads',
    BOUNCE_PANAS = 'bouncePanas',
    BOUNCE_MMOS_SS = 'bounceMmosSs',
    BOUNCE_FCRI_SF = 'bounceFcriSf',
    BOUNCE_EORTC_QLQ_C_30 = 'bounceEortcQlqC30',
    BOUNCE_RNLE = 'bounceRnle',
    GENERAL_SELF_EFFICACY_ITEM = 'generalSelfEfficacyItem',
    BOUNCE_SD_LIFESTYLE_MO = 'bounceSdLifestyleMo',
    CD_RISC = 'cdRisc',
    NCCN_DISTRESS_THERMO = 'nccnDistressThermo',
    SOC_13 = 'soc13',
    PACT = 'pact',
    RCAC = 'rcac',
    CBI_B = 'cbiB',
    IEPSS = 'iepss',
    BOUNCE_SD_LIFESTYLE_MO_PT = 'bounceSdLifestyleMoPt',
    TENONC_DISTRESS_BASELINE = 'tenoncDistressBaseline',
    TENONC_DISTRESS_AFTERBASELINE = 'tenoncDistressAfterbaseline',
    SPIRITUALITY_COPING = 'spiritualityCoping',
    FARE = 'fare',
    MINI_MAC = 'miniMac',
    WHAT_HAS_DONE_TO_COPE = 'whatHasDoneToCope',
    PTGI = 'ptgi',
    BOUNCE_SD_LIFESTYLE_M_3 = 'bounceSdLifestyleM3',
    ADHERENCE_TO_MEDICAL_ADVICE_MOS = 'adherenceToMedicalAdviceMos',
    IPQ = 'ipq',
    B_IPQ = 'bIpq',
    RECIL_RELATED_EVENTS = 'recilRelatedEvents',
    PCL_5 = 'pcl5',
    HOW_MUCH_BACK_TOYOURSELF = 'howMuchBackToyourself',
    PHQ_2 = 'phq2',
    EPRO_PATIENT_SATISFACTION = 'eproPatientSatisfaction',
    BOUNCE_PA_CA_PA_DA_APRVD = 'bouncePaCaPaDaAprvd',
    BOUNCE_SD_LIFESTYLE_M_18_HE_AR = 'bounceSdLifestyleM18HeAr',
    BOUNCE_SD_LIFESTYLE_M_18_PT = 'bounceSdLifestyleM18Pt',
    TREATMENT_XP_MICHI = 'treatmentXpMichi',
    BOUNCE_SD_LIFESTYLE_M_9 = 'bounceSdLifestyleM9',
    BOUNCE_SD_LIFESTYLE_M_18_ORIG = 'bounceSdLifestyleM18Orig',
    BOUNCE_SD_LIFESTYLE_M_15 = 'bounceSdLifestyleM15',
    BOUNCE_SD_LIFESTYLE_M_6_M_12 = 'bounceSdLifestyleM6M12',
    BOUNCE_SD_LIFESTYLE_MO_HE_AR = 'bounceSdLifestyleMoHeAr',
    BOUNCE_SD_LIFESTYLE_M_6_M_12_PT = 'bounceSdLifestyleM6M12Pt',
    BOUNCE_PA_CA_PA_DA_HUS = 'bouncePaCaPaDaHus',
    POST_TREATMENT_STATUS_CHECK = 'postTreatmentStatusCheck',
    FACT_LYM_VERFOUR_MOD = 'factLymVerfourMod',
    MANDA_EORTC_QLQ_C_30 = 'mandaEortcQlqC30',
    G_EIGHT_FRAILTY = 'gEightFrailty',
    COVID = 'covid',
    FACT_HN_VERSION_FOUR = 'factHnVersionFour',
    IPSS = 'ipss',
    EPIC_SF_6 = 'epicSf6',
    FACT_G_VERSION_FOUR = 'factGVersionFour',
    ZRTI = 'zrti',
    ORAL_MED_COMPLIANCE = 'oralMedCompliance',
    EPIC_26 = 'epic26',
    BOUNCE_COMPLIANCE = 'bounceCompliance',
    COVID_CCMB = 'covidCcmb',
    TAYS_EPIC_26 = 'taysEpic26',
    IIEF_5 = 'iief5',
    ICIQ_INCONTINENCE = 'iciqIncontinence',
    OFF_EORTC_QLQ_C_30 = 'offEortcQlqC30',
    OFF_EORTC_QLQ_BR_23 = 'offEortcQlqBr23',
    EDMONTON_SYM_ASSESS = 'edmontonSymAssess',
    CPC = 'cpc',
    BPI_SF = 'bpiSf',
    CARTI_SSAT = 'cartiSsat',
    EQ_5D_3L = 'eq5d3l',
    HUMANA_CONSENT = 'humanaConsent',
    PARK_PHARMACY_MED_ADHERENCE = 'parkPharmacyMedAdherence',
    ESAS_HN_MODIFIED = 'esasHnModified',
    SATISFACTION_SURVEY_QUESTIONNAIRE = 'satisfactionSurveyQuestionnaire',
    BREAST_Q_V_2_CORECHECK_ADFX = 'breastQV2CorecheckAdfx',
    BREAST_Q_V_2_CORESCALE_SATIBRST = 'breastQV2CorescaleSatibrst',
    BREAST_Q_V_2_BCT_SATIBRST = 'breastQV2BctSatibrst',
    CCMB_COMBINATION_FORM_CPASS = 'ccmbCombinationFormCpass',
    PHQ_9 = 'phq9',
    EQ_5D_5L = 'eq5d5l',
}

export const AllQuestionaryType = [
    QuestionaryType.EXAMPLE,
    QuestionaryType.QOL15_D,
    QuestionaryType.DISTRESS_AND_PROBLEM_LIST,
    QuestionaryType.DISTRESS_FOLLOW_UP,
    QuestionaryType.HNQOL,
    QuestionaryType.FACT_GOG_NTX,
    QuestionaryType.BOUNCE_TIPI,
    QuestionaryType.BOUNCE_CERQ,
    QuestionaryType.BOUNCE_LOT_R,
    QuestionaryType.BOUNCE_EORTC_QLQ_BR_23,
    QuestionaryType.BOUNCE_MAAS,
    QuestionaryType.BOUNCE_HADS,
    QuestionaryType.BOUNCE_PANAS,
    QuestionaryType.BOUNCE_MMOS_SS,
    QuestionaryType.BOUNCE_FCRI_SF,
    QuestionaryType.BOUNCE_EORTC_QLQ_C_30,
    QuestionaryType.BOUNCE_RNLE,
    QuestionaryType.GENERAL_SELF_EFFICACY_ITEM,
    QuestionaryType.BOUNCE_SD_LIFESTYLE_MO,
    QuestionaryType.CD_RISC,
    QuestionaryType.NCCN_DISTRESS_THERMO,
    QuestionaryType.SOC_13,
    QuestionaryType.PACT,
    QuestionaryType.RCAC,
    QuestionaryType.CBI_B,
    QuestionaryType.IEPSS,
    QuestionaryType.BOUNCE_SD_LIFESTYLE_MO_PT,
    QuestionaryType.TENONC_DISTRESS_BASELINE,
    QuestionaryType.TENONC_DISTRESS_AFTERBASELINE,
    QuestionaryType.SPIRITUALITY_COPING,
    QuestionaryType.FARE,
    QuestionaryType.MINI_MAC,
    QuestionaryType.WHAT_HAS_DONE_TO_COPE,
    QuestionaryType.PTGI,
    QuestionaryType.BOUNCE_SD_LIFESTYLE_M_3,
    QuestionaryType.ADHERENCE_TO_MEDICAL_ADVICE_MOS,
    QuestionaryType.IPQ,
    QuestionaryType.B_IPQ,
    QuestionaryType.RECIL_RELATED_EVENTS,
    QuestionaryType.PCL_5,
    QuestionaryType.HOW_MUCH_BACK_TOYOURSELF,
    QuestionaryType.PHQ_2,
    QuestionaryType.EPRO_PATIENT_SATISFACTION,
    QuestionaryType.BOUNCE_PA_CA_PA_DA_APRVD,
    QuestionaryType.BOUNCE_SD_LIFESTYLE_M_18_HE_AR,
    QuestionaryType.BOUNCE_SD_LIFESTYLE_M_18_PT,
    QuestionaryType.TREATMENT_XP_MICHI,
    QuestionaryType.BOUNCE_SD_LIFESTYLE_M_9,
    QuestionaryType.BOUNCE_SD_LIFESTYLE_M_18_ORIG,
    QuestionaryType.BOUNCE_SD_LIFESTYLE_M_15,
    QuestionaryType.BOUNCE_SD_LIFESTYLE_M_6_M_12,
    QuestionaryType.BOUNCE_SD_LIFESTYLE_MO_HE_AR,
    QuestionaryType.BOUNCE_SD_LIFESTYLE_M_6_M_12_PT,
    QuestionaryType.BOUNCE_PA_CA_PA_DA_HUS,
    QuestionaryType.POST_TREATMENT_STATUS_CHECK,
    QuestionaryType.FACT_LYM_VERFOUR_MOD,
    QuestionaryType.MANDA_EORTC_QLQ_C_30,
    QuestionaryType.G_EIGHT_FRAILTY,
    QuestionaryType.COVID,
    QuestionaryType.FACT_HN_VERSION_FOUR,
    QuestionaryType.IPSS,
    QuestionaryType.EPIC_SF_6,
    QuestionaryType.FACT_G_VERSION_FOUR,
    QuestionaryType.ZRTI,
    QuestionaryType.ORAL_MED_COMPLIANCE,
    QuestionaryType.EPIC_26,
    QuestionaryType.BOUNCE_COMPLIANCE,
    QuestionaryType.COVID_CCMB,
    QuestionaryType.TAYS_EPIC_26,
    QuestionaryType.IIEF_5,
    QuestionaryType.ICIQ_INCONTINENCE,
    QuestionaryType.OFF_EORTC_QLQ_C_30,
    QuestionaryType.OFF_EORTC_QLQ_BR_23,
    QuestionaryType.EDMONTON_SYM_ASSESS,
    QuestionaryType.CPC,
    QuestionaryType.BPI_SF,
    QuestionaryType.CARTI_SSAT,
    QuestionaryType.EQ_5D_3L,
    QuestionaryType.HUMANA_CONSENT,
    QuestionaryType.PARK_PHARMACY_MED_ADHERENCE,
    QuestionaryType.ESAS_HN_MODIFIED,
    QuestionaryType.SATISFACTION_SURVEY_QUESTIONNAIRE,
    QuestionaryType.BREAST_Q_V_2_CORECHECK_ADFX,
    QuestionaryType.BREAST_Q_V_2_CORESCALE_SATIBRST,
    QuestionaryType.BREAST_Q_V_2_BCT_SATIBRST,
    QuestionaryType.CCMB_COMBINATION_FORM_CPASS,
    QuestionaryType.PHQ_9,
    QuestionaryType.EQ_5D_5L,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
