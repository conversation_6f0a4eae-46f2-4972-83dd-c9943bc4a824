// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.patientcase.Case

import { AcuteSymptoms } from './acute-symptoms';
import { CaseDelayReason } from './case-delay-reason';
import { CaseOrigin } from './case-origin';
import { CaseOutcome } from './case-outcome';
import { CasePriority } from './case-priority';
import { CaseStatus } from './case-status';
import { CaseType } from './case-type';
import { Customer } from './customer';
import { Patient } from './patient';
export interface Case {
    id?: string;
    created?: Date | number | string;
    modified?: Date | number | string;
    patient?: Patient;
    customer?: Customer;
    caseType?: CaseType;
    relatedCaseId?: string;
    messages?: string[];
    priority?: CasePriority;
    status?: CaseStatus;
    processed?: boolean;
    assignee?: string;
    itemId?: string;
    consultedTeam?: string;
    consultedUser?: string;
    consultingUser?: string;
    consultingTeams?: string[];
    nextAutoEscalation?: Date | number | string;
    escalated?: boolean;
    careTeams?: string[];
    responsibleUsers?: string[];
    caseDate?: Date | number | string;
    submitTime?: Date | number | string;
    description?: string;
    caseOrigin?: CaseOrigin;
    acuteSymptoms?: AcuteSymptoms[];
    caseOutcomes?: CaseOutcome[];
    caseDelayReason?: CaseDelayReason;
    notes?: string;
    hipaaVerified?: boolean;
    patientVoiceUnderstanding?: boolean;
    caseNotes?: string[];
    linkedInstructions?: string[];
    reminder?: boolean;
    visibleForPatient?: boolean;
    secretKeyId?: string;
    assigneeName?: string;
    closedDate?: Date | number | string;
    publicDate?: Date | number | string;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
