// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.treatment.TreatmentPhase

export enum TreatmentPhase {
    UNKNOWN = 'unknown',
    BEFORE = 'before',
    SURGERY = 'surgery',
    MEDICAL_TREATMENTS = 'medicalTreatments',
    RADIATION_THERAPY = 'radiationTherapy',
    FOLLOWUP_PHASE = 'followUpPhase',
}

export const AllTreatmentPhase = [
    TreatmentPhase.UNKNOWN,
    TreatmentPhase.BEFORE,
    TreatmentPhase.SURGERY,
    TreatmentPhase.MEDICAL_TREATMENTS,
    TreatmentPhase.RADIATION_THERAPY,
    TreatmentPhase.FOLLOWUP_PHASE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
