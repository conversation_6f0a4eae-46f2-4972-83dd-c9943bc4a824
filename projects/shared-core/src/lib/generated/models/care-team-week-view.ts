// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.analytics.model.reporting.CareTeamWeekView

export interface CareTeamWeekView {
    careTeam?: string;
    firstDay?: Date | number | string;
    lastDay?: Date | number | string;
    activeCount?: number;
    changedActiveCount?: number;
    closedCount?: number;
    changedClosedCount?: number;
    invitedCount?: number;
    changedInvitedCount?: number;
    nurseProxyCount?: number;
    changedNurseProxyCount?: number;
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
