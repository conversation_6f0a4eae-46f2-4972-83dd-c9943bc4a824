// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.symptoms.InquiryStatus

export enum InquiryStatus {
    SCHEDULED = 'scheduled',
    SENT = 'sent',
    ANSWERED = 'answered',
    REMINDER = 'reminder',
    REVIEWED = 'reviewed',
    EXPIRED = 'expired',
    WEEKLYSENT = 'weeklysent',
    WEEKLYANSWERED = 'weeklyanswered',
    SKIPPED = 'skipped',
    READ = 'read',
    FILLED_BY_CLINIC = 'filledByClinic',
    REVIEWED_FILLED_BY_CLINIC = 'reviewedFilledByClinic',
    DECLINED = 'declined',
    SCHEDULED_ACTIVE = 'scheduledActive',
    SKIPPED_ACTIVE = 'skippedActive',
}

export const AllInquiryStatus = [
    InquiryStatus.SCHEDULED,
    InquiryStatus.SENT,
    InquiryStatus.ANSWERED,
    InquiryStatus.REMINDER,
    InquiryStatus.REVIEWED,
    InquiryStatus.EXPIRED,
    InquiryStatus.WEEKLYSENT,
    InquiryStatus.WEEKLYANSWERED,
    InquiryStatus.SKIPPED,
    InquiryStatus.READ,
    InquiryStatus.FILLED_BY_CLINIC,
    InquiryStatus.REVIEWED_FILLED_BY_CLINIC,
    InquiryStatus.DECLINED,
    InquiryStatus.SCHEDULED_ACTIVE,
    InquiryStatus.SKIPPED_ACTIVE,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
