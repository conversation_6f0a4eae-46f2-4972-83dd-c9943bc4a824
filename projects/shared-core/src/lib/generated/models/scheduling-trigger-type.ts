// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.SchedulingTriggerType

export enum SchedulingTriggerType {
    PATIENT_ADD = 'patientAdd',
    TREATMENT_BEFORE_START_DATE = 'beforeTreatmentStartDate',
    TREATMENT_AFTER_START_DATE = 'afterTreatmentStartDate',
    TREATMENT_AFTER_PREVIOUS_REVIEW = 'afterPreviousReview',
}

export const AllSchedulingTriggerType = [
    SchedulingTriggerType.PATIENT_ADD,
    SchedulingTriggerType.TREATMENT_BEFORE_START_DATE,
    SchedulingTriggerType.TREATMENT_AFTER_START_DATE,
    SchedulingTriggerType.TREATMENT_AFTER_PREVIOUS_REVIEW,
];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
