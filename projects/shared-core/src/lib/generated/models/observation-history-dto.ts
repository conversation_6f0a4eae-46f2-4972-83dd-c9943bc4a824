// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  noona.service.health.model.labsview.dto.ObservationHistoryDto

import { ChartDataDto } from './chart-data-dto';
import { DataPointDto } from './data-point-dto';
import { ReferenceRangeDto } from './reference-range-dto';
export interface ObservationHistoryDto {
    observationName?: string;
    referenceRange?: ReferenceRangeDto;
    unit?: string;
    description?: string;
    dataPointsCount?: number;
    chartData?: ChartDataDto;
    dataPoints?: DataPointDto[];
}

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
