// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
// From source:  adelmann.service.health.model.treatment.TreatmentModuleControlType

export enum TreatmentModuleControlType {
    TREATMENT = 'treatment',
    REVIEW = 'review',
}

export const AllTreatmentModuleControlType = [TreatmentModuleControlType.TREATMENT, TreatmentModuleControlType.REVIEW];

// Auto generated by adelmann.util.codegenerator.TypescriptModelGenerator
