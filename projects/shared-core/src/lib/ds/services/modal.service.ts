import { ComponentFactory, ComponentFactoryResolver, Inject, Injectable, Injector, ViewContainerRef } from '@angular/core';
import { Modal, ModalAction, ModalComponent } from '../components/modal/modal.component';
import { DsDomService } from './dom.service';
import { ButtonSize } from '../components/button/button.component';

export interface ModalRef {
  instance?: ModalComponent | null;
  childInstance?: any | null;
}

export class ModalActionInit implements ModalAction {
  closeOnClick: boolean = true;
  id: string = '';
  size?: ButtonSize;
}

@Injectable({
  providedIn: 'root'
})
export class DsModalService {
  constructor(
    @Inject(ComponentFactoryResolver) private componentFactoryResolver: ComponentFactoryResolver,
    private domService: DsDomService
  ) {}

  openPopup(target: ViewContainerRef, modal: Modal): ModalRef {
    const componentFactory = this.componentFactoryResolver.resolveComponentFactory(ModalComponent);
    const childComponentRef = this.createChildComponent(modal.childComponent);

    // set initial visibility
    modal.isVisible = false;

    // set initial actions if not defined
    if (!modal.backdropAction) {
      modal.backdropAction = new ModalActionInit();
    }
    if (!modal.closeAction) {
      modal.closeAction = new ModalActionInit();
    }

    // if appendToBody is set, inject the componentFactory, modal and childComponentRef to dom via domService
    if (modal.appendToBody) {
      return this.domService.appendComponentToBody(componentFactory, modal, childComponentRef);
    }

    // otherwise use possible router outlets or inject without modifying the dom
    const componentRef = target.createComponent(componentFactory);

    componentRef.instance.modal = Object.assign(modal, componentRef);
    componentRef.instance.childComponentRef = childComponentRef;

    return {
      instance: componentRef.instance,
      childInstance: childComponentRef && childComponentRef.instance
    };
  }

  private createChildComponent(childComponent: any) {
    if (!childComponent) {
      return null;
    }

    const childFactory: ComponentFactory<any> = this.componentFactoryResolver.resolveComponentFactory(childComponent);

    return childFactory.create(Injector.NULL);
  }
}
