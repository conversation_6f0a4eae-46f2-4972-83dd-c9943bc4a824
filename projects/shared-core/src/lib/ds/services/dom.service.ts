import { Injectable, Injector, EmbeddedViewRef, ApplicationRef, ComponentFactory, ComponentRef } from '@angular/core';
import { Modal } from '../components/modal/modal.component';

@Injectable()
export class DsDomService {
    constructor(private appRef: ApplicationRef, private injector: Injector) {}

    appendComponentToBody(componentFactory: ComponentFactory<any>, modal: Modal, childComponentRef: ComponentRef<any>) {
        // create a component reference from the inherited factory component
        const componentRef: any = componentFactory.create(this.injector);

        // set modal & childComponent instances
        componentRef.instance.modal = Object.assign(modal, {
            type: '',
            componentRef,
        });
        componentRef.instance.childComponentRef = childComponentRef;

        // attach component to the appRef so that it's inside the ng component tree
        this.appRef.attachView(componentRef.hostView);

        // get DOM element from component
        const domElem = (componentRef.hostView as EmbeddedViewRef<any>).rootNodes[0] as HTMLElement;

        // append DOM element to the body
        document.body.appendChild(domElem);

        return {
            instance: componentRef.instance,
            childInstance: childComponentRef && childComponentRef.instance,
        };

        // destroying the componentRef is taken care by childComponentRef.destroy() via modal.component.ts
    }
}
