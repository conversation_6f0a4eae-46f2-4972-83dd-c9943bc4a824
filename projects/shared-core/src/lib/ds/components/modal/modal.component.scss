@import '../../../../styles/ds-variables.scss';
@import '../../../../styles/ds-mixins';

ds-modal {
  display: flex;
  align-items: var(--modal-host-align-items);
  justify-content: var(--modal-host-justify-content);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: var(--z-modal);
  @media screen and ($screen-from-s) {
    @include container-padding-all();
  }
}
.ds-modal__backdrop {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--modal-backdrop-background-color);
  -webkit-tap-highlight-color: transparent;
}
.ds-modal__window {
  position: relative;
  background-color: var(--modal-window-background-color);
  z-index: 2;
  box-shadow: var(--modal-window-box-shadow-size) var(--modal-window-box-shadow-color);
  max-width: 100%;
  max-height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  @media screen and ($screen-to-s) {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  @media screen and ($screen-from-s) {
    border-radius: var(--modal-window-border-radius);
    width: 100%;
    min-width: var(--modal-window-min-width);
    max-width: var(--modal-window-max-width);
  }
}
.ds-modal__content {
  position: relative;
  padding: var(--modal-content-padding-top) var(--modal-content-padding-right) var(--modal-content-padding-bottom)
    var(--modal-content-padding-left);
  @media screen and ($screen-to-s) {
    flex: 1 1 auto;
  }
}
.ds-modal__footer {
  padding: var(--modal-content-padding);
  vertical-align: bottom;
}
.ds-modal__close {
  --button-primary-regular-height: var(--spacing-xl);
  fill: var(--color-grey-40);
  position: absolute;
  top: var(--modal-close-button-top);
  right: var(--modal-close-button-right);
  z-index: var(--modal-close-button-z-index);
  @media screen and ($screen-to-s) {
    position: fixed;
    border-radius: 50%;
    box-shadow: var(--modal-close-button-box-shadow);
    background-color: var(--modal-window-background-color);
    z-index: 1;
  }
  &:active,
  &:hover {
    fill: var(--color-primary);
  }
}
.ds-modal__buttons-wrapper {
  padding: var(--modal-footer-padding-top) var(--modal-footer-padding-right) var(--modal-footer-padding-bottom)
    var(--modal-footer-padding-left);
  @media screen and ($screen-from-s) {
    flex-wrap: wrap;
  }
}

@media screen and ($screen-to-s) {
  .modal-button {
    margin: 0 !important;
  }
}
.ds-modal__window--fixed-footer {
  display: flex;
  flex-direction: column;
  .ds-modal__content {
    overflow-y: auto;
  }
  .ds-modal__buttons-wrapper {
    --modal-footer-padding-top: var(--spacing-xs);
    --modal-footer-padding-bottom: var(--spacing-xs);

    background-color: var(--modal-window-background-color);
    flex-direction: row;
    justify-content: space-between;
    flex-shrink: 0;
    box-shadow: 0 -1px 5px 0 rgba(0, 0, 0, 0.1);
    @media screen and ($screen-from-s) {
      --modal-footer-padding-top: var(--spacing-m);
      --modal-footer-padding-bottom: var(--spacing-m);
      box-shadow: 0 0 4px rgba(0, 0, 0, 0.15);
    }
    .ds-button {
      fill: var(--color-primary);
      &--disabled {
        fill: var(--color-grey-lighten-1);
      }
    }
    .ds-button--secondary {
      flex: 1 1 auto;
    }
  }
}
