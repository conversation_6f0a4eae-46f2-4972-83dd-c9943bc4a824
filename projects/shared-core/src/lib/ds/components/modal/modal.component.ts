import { animate, state, style, transition, trigger } from '@angular/animations';
import {
  ChangeDetectionStrategy,
  Component,
  ComponentRef,
  EventEmitter,
  HostBinding,
  HostListener,
  OnInit,
  Output,
  ViewChild,
  ViewContainerRef,
  ViewEncapsulation
} from '@angular/core';
import { ButtonIconPosition, ButtonSize, ButtonVariation } from '../button/button.component';

export interface ModalInit {
  modalInit(modal: Modal): void;
}

export interface Modal {
  id?: string;
  class?: string;
  type?: string;
  variation?: string;
  appendToBody?: boolean;
  hideCloseButton?: boolean;
  hideBackdrop?: boolean;
  label?: string;
  header?: string;
  message?: string;
  footer?: string;
  errorMessage?: string;
  content?: any;
  childComponent?: any;
  componentRef?: ComponentRef<ModalComponent>;
  hideFooter?: boolean;
  fixedFooter?: boolean;
  dynamicContent?: boolean;
  isVisible?: boolean;
  backdropAction?: ModalAction;
  cancelAction?: ModalAction;
  primaryAction?: ModalAction;
  secondaryAction?: ModalAction;
  closeAction?: ModalAction;
  customActions?: ModalAction[];
}

export interface ModalAction {
  ariaLabel?: string;
  closeOnClick?: boolean;
  id?: string;
  isDisabled?: boolean;
  isLoading?: boolean;
  label?: string;
  variation?: ButtonVariation;
  class?: string;
  icon?: string;
  iconHeight?: number;
  iconLibrarySpriteFile?: boolean;
  iconPosition?: ButtonIconPosition;
  iconSize?: number;
  iconSpriteFile?: string;
  iconWidth?: number;
  isAutofocus?: boolean;
  loadingDashColor?: string;
  loadingShadowColor?: string;
  loadingSize?: number;
  size?: ButtonSize;
  tabindex?: number;
  onClick?: () => void;
}

@Component({
  selector: 'ds-modal',
  exportAs: 'modal',
  templateUrl: './modal.component.html',
  styleUrls: ['./modal.component.scss'],
  animations: [
    trigger('backdrop', [
      state('void', style({ opacity: 0 })),
      state('false', style({ opacity: 0 })),
      state('true', style({ opacity: 1 })),
      transition('false <=> true', animate('300ms ease'))
    ]),
    trigger('modal', [
      state('void', style({ opacity: 0 })),
      state('true', style({ opacity: 1, transform: 'translateY(0)' })),
      state('false', style({ opacity: 0, transform: 'translateY(2rem)' })),
      transition('false => true', animate('250ms ease')),
      transition('true => false', animate('150ms ease'))
    ])
  ],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ModalComponent implements OnInit {
  modal: Modal;
  childComponentRef: ComponentRef<any>;
  @Output() actionClick = new EventEmitter<Event>();
  @Output() backdropClick = new EventEmitter<Event>();
  @Output() cancelButtonClick = new EventEmitter<Event>();
  @Output() primaryButtonClick = new EventEmitter<Event>();
  @Output() secondaryButtonClick = new EventEmitter<Event>();
  @Output() closeClick = new EventEmitter<Event>();
  @Output() closed = new EventEmitter<Modal>();

  @HostBinding('class') get hostClasses() {
    return [
      'ds-modal',
      this.modal && this.modal.type ? 'ds-modal--' + this.modal.type : 'ds-modal--dialog',
      this.modal && this.modal.variation ? 'ds-modal--' + this.modal.variation : 'ds-modal--desktop',
      this.modal.class
    ].join(' ');
  }

  @HostBinding('id') get hostId() {
    return this.modal.id || 'modal';
  }

  @ViewChild('dynamicComponentTarget', { read: ViewContainerRef, static: true }) public dynamicComponentTarget: ViewContainerRef;

  @HostListener('document:keydown.escape', ['$event']) escapeKeyHandler(_event: KeyboardEvent) {
    this.closeModal();
  }

  constructor() {}

  ngOnInit() {
    document.body.classList.add('modal-active');

    if (!this.modal.dynamicContent) {
      this.modal.isVisible = true;
    }

    if (this.modal.childComponent && this.childComponentRef) {
      this.dynamicComponentTarget.insert(this.childComponentRef.hostView);

      if (this.childComponentRef.instance.modalInit) {
        this.childComponentRef.instance.modalInit(this.modal);
      }
    }
  }

  onCancelClick(e: Event) {
    this.cancelButtonClick.emit(e);
    if (typeof this.modal.cancelAction.onClick === 'function') {
      this.modal.cancelAction.onClick();
    }
    if (this.modal.cancelAction.closeOnClick) {
      this.closeModal();
    }
  }

  onPrimaryClick(e: Event) {
    this.primaryButtonClick.emit(e);
    if (typeof this.modal.primaryAction.onClick === 'function') {
      this.modal.primaryAction.onClick();
    }
    if (this.modal.primaryAction.closeOnClick) {
      this.closeModal();
    }
  }

  onSecondaryClick(e: Event) {
    this.secondaryButtonClick.emit(e);
    if (typeof this.modal.secondaryAction.onClick === 'function') {
      this.modal.secondaryAction.onClick();
    }
    if (this.modal.secondaryAction.closeOnClick) {
      this.closeModal();
    }
  }

  onBackdropClick(e: Event) {
    this.backdropClick.emit(e);
    if (typeof this.modal.backdropAction.onClick === 'function') {
      this.modal.backdropAction.onClick();
    }
    if (this.modal.backdropAction.closeOnClick) {
      this.closeModal();
    }
  }

  onCloseClick(e: Event) {
    this.closeClick.emit(e);
    if (typeof this.modal.closeAction.onClick === 'function') {
      this.modal.closeAction.onClick();
    }
    if (this.modal.closeAction.closeOnClick) {
      this.closeModal();
    }
  }

  onActionClick(e: Event, i: number) {
    this.actionClick.emit(e);
    if (typeof this.modal.customActions[i].onClick === 'function') {
      this.modal.customActions[i].onClick();
    }
    if (this.modal.customActions[i].closeOnClick) {
      this.closeModal();
    }
  }

  closeModal() {
    this.modal.isVisible = false;
    // setTimeout required for animations
    setTimeout(() => {
      document.body.classList.remove('modal-active');
      this.closed.emit(this.modal);
      if (this.modal.componentRef) {
        this.modal.componentRef.destroy();
      }
    }, 500);
  }
}
