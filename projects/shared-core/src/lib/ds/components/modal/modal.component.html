<!-- TODO: NOONA-25712 Fix modal a11y -->
<div class="ds-modal__backdrop" (click)="onBackdropClick($event)" [@backdrop]="modal.isVisible"></div>
<div class="ds-modal__window" [class.ds-modal__window--fixed-footer]="modal.fixedFooter" [@modal]="modal.isVisible">
  <div class="ds-modal__content">
    <ds-button
      *ngIf="!modal.hideCloseButton"
      class="ds-modal__close"
      variation="icon"
      icon="icon-close-thick"
      [iconSize]="12"
      [ariaLabel]="modal.closeAction.ariaLabel"
      [id]="modal.closeAction.id ? modal.closeAction.id : modal.id + '-button-close'"
      [iconLibrarySpriteFile]="true"
      (buttonClick)="onCloseClick($event)"
    ></ds-button>
    <h1 *ngIf="modal.header" class="ds-modal__header mb-m">{{ modal.header }}</h1>
    <p *ngIf="modal.message" class="mb-m">{{ modal.message }}</p>
    <ng-content select="[modal-body]"></ng-content>
    <ng-container #dynamicComponentTarget></ng-container>
  </div>
  <div *ngIf="!modal.hideFooter" class="ds-modal__buttons-wrapper flex flex--column-to-s flex--align-items-center-from-s">
    <footer *ngIf="modal.footer" class="ds-modal__footer">{{ modal.footer }}</footer>

    <ng-container *ngIf="modal.customActions; then customModalActions; else defaultModalActions"></ng-container>
  </div>
</div>

<ng-template #defaultModalActions>
  <ds-button
    *ngIf="modal.cancelAction"
    class="modal-button modal-button--cancel"
    [variation]="modal.cancelAction.variation ? modal.cancelAction.variation : 'link'"
    [size]="modal.cancelAction.size ? modal.cancelAction.size : 'regular'"
    [ariaLabel]="modal.cancelAction.ariaLabel"
    [id]="modal.cancelAction.id ? modal.cancelAction.id : modal.id + '-button-cancel'"
    [isDisabled]="modal.cancelAction.isDisabled"
    (buttonClick)="onCancelClick($event)"
    >{{ modal.cancelAction.label }}</ds-button
  >
  <ds-button
    *ngIf="modal.secondaryAction"
    class="modal-button modal-button--secondary ml-auto"
    [variation]="modal.secondaryAction.variation ? modal.secondaryAction.variation : 'secondary'"
    [size]="modal.secondaryAction.size ? modal.secondaryAction.size : 'regular'"
    [ariaLabel]="modal.secondaryAction.ariaLabel"
    [id]="modal.secondaryAction.id ? modal.secondaryAction.id : modal.id + '-button-secondary'"
    [isDisabled]="modal.secondaryAction.isDisabled"
    (buttonClick)="onSecondaryClick($event)"
    >{{ modal.secondaryAction.label }}</ds-button
  >
  <ds-button
    *ngIf="modal.primaryAction"
    class="modal-button modal-button--primary {{ modal.secondaryAction ? 'ml-m' : null }} {{ !modal.secondaryAction ? 'ml-auto' : null }}"
    [variation]="modal.primaryAction.variation ? modal.primaryAction.variation : 'primary'"
    [size]="modal.primaryAction.size ? modal.primaryAction.size : 'regular'"
    [ariaLabel]="modal.primaryAction.ariaLabel"
    [id]="modal.primaryAction.id ? modal.primaryAction.id : modal.id + '-button-primary'"
    [isDisabled]="modal.primaryAction.isDisabled"
    [isLoading]="modal.primaryAction.isLoading"
    (buttonClick)="onPrimaryClick($event)"
    >{{ modal.primaryAction.label }}</ds-button
  >
</ng-template>

<ng-template #customModalActions>
  <ds-button
    *ngFor="let action of modal.customActions; let index = index"
    [ariaLabel]="action.ariaLabel"
    [id]="action.id"
    [isDisabled]="action.isDisabled"
    [isLoading]="action.isLoading"
    [label]="action.label"
    [variation]="action.variation"
    [class]="action.class"
    [icon]="action.icon"
    [iconHeight]="action.iconHeight"
    [iconLibrarySpriteFile]="action.iconLibrarySpriteFile"
    [iconPosition]="action.iconPosition"
    [iconSize]="action.iconSize"
    [iconSpriteFile]="action.iconSpriteFile"
    [iconWidth]="action.iconWidth"
    [isAutofocus]="action.isAutofocus"
    [loadingDashColor]="action.loadingDashColor"
    [loadingShadowColor]="action.loadingShadowColor"
    [loadingSize]="action.loadingSize"
    [size]="action.size ? action.size : 'regular'"
    [tabindex]="action.tabindex"
    (buttonClick)="onActionClick($event, index)"
  ></ds-button>
</ng-template>
