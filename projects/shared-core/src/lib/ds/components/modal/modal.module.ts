import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModalComponent } from './modal.component';
import { DsButtonModule } from '../button/button.module';
import { DsModalService } from '../../services/modal.service';
import { DsDomService } from '../../services/dom.service';

@NgModule({
    imports: [CommonModule, DsButtonModule],
    declarations: [ModalComponent],
    exports: [ModalComponent],
})
export class DsModalModule {
    static forRoot(): ModuleWithProviders<DsModalModule> {
        return {
            ngModule: DsModalModule,
            providers: [DsModalService, DsDomService],
        };
    }
}
