import { Component, EventEmitter, HostBinding, HostListener, Input, Output, ViewChild } from '@angular/core';

export interface ActionsDropdownOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  id?: string;
}

export interface ActionsDropdownOptionClickEvent {
  option: ActionsDropdownOption;
  index: number;
  event?: Event;
}

@Component({
  selector: 'ds-actions-dropdown',
  templateUrl: './actions-dropdown.component.html',
  styleUrls: ['./actions-dropdown.component.scss'],
  host: { class: 'ds-actions-dropdown' }
})
export class ActionsDropdownComponent {
  @HostBinding('class') get hostClasses() {
    return ['ds-actions-dropdown', 'ds-actions-dropdown--' + (this.optionsVisible ? 'open' : 'closed'), this.class].join(' ');
  }
  @Input() class: string;
  @Input() id: string;
  @Input() toggleIcon = 'icon-ellipsis';
  @Input() toggleIconSize = 22;
  @Input() toggleIconLibrarySpriteFile = true;
  @Input() options: ActionsDropdownOption[] = [];
  @Input() set optionsVisible(value: boolean) {
    if (value !== undefined && value !== this._optionsVisible) {
      this._optionsVisible = value;
      this.visibilityChange.emit(value);
    }
  }
  get optionsVisible() {
    return this._optionsVisible;
  }
  @Input() toggleAriaLabel: string;
  @Output() optionClick = new EventEmitter<ActionsDropdownOptionClickEvent>();
  @Output() toggleClick = new EventEmitter<any>();
  @Output() visibilityChange = new EventEmitter<any>();
  @ViewChild('optionsList', { static: true }) optionsElement;
  _optionsVisible = false;

  @HostListener('document:click', ['$event.target']) clickOutside(targetElement) {
    if (this._optionsVisible && !this.optionsElement.nativeElement.contains(targetElement)) {
      this.optionsVisible = false;
    }
  }

  onOptionClick(option: ActionsDropdownOption, index: number, event: Event) {
    this.optionsVisible = false;
    this.optionClick.emit({ option, index, event });
  }

  onToggleClick(e: Event) {
    this.optionsVisible = !this.optionsVisible;
    this.toggleClick.emit(e);
  }
}
