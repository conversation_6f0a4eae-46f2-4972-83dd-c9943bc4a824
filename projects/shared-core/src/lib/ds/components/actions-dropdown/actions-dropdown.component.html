<ds-button
  class="actions__toggle"
  variation="icon"
  [attr.id]="id + '-button'"
  [icon]="toggleIcon"
  [iconSize]="toggleIconSize"
  [ariaLabel]="toggleAriaLabel"
  [iconLibrarySpriteFile]="toggleIconLibrarySpriteFile"
  [attr.aria-expanded]="_optionsVisible"
  [attr.aria-haspopup]="'menu'"
  [attr.aria-controls]="id + '-menu'"
  (buttonClick)="onToggleClick($event)"
></ds-button>
<ul
  #optionsList
  [attr.id]="id + '-menu'"
  class="actions__dropdown"
  [class.actions__dropdown--open]="_optionsVisible"
  [attr.aria-hidden]="_optionsVisible ? null : true"
  [tabindex]="_optionsVisible ? null : -1"
>
  <li
    *ngFor="let option of options; let index = index"
    class="option"
    [attr.id]="option.id ? option.id : (id ? id : 'actions-dropdown') + '-option-' + index"
  >
    <button
      type="button"
      class="option__label"
      (click)="onOptionClick(option, index, $event)"
      [disabled]="option.disabled"
      [attr.aria-hidden]="_optionsVisible ? null : true"
      [tabindex]="_optionsVisible ? null : -1"
    >
      {{ option.label }}
    </button>
  </li>
</ul>
