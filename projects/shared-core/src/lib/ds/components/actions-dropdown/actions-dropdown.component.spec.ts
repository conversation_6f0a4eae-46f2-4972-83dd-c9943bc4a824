import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActionsDropdownComponent, ActionsDropdownOption } from './actions-dropdown.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('ActionsDropdownComponent', () => {
  let component: ActionsDropdownComponent;
  let fixture: ComponentFixture<ActionsDropdownComponent>;

  const mockOptions: ActionsDropdownOption[] = [
    { value: 'edit', label: 'Edit' },
    { value: 'delete', label: 'Delete' },
    { value: 'duplicate', label: 'Duplicate', disabled: true }
  ];

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ActionsDropdownComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(ActionsDropdownComponent);
    component = fixture.componentInstance;
    component.options = mockOptions;

    component.optionsElement = {
      nativeElement: document.createElement('div')
    };

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component._optionsVisible).toBeFalsy();
    expect(component.toggleIcon).toBe('icon-ellipsis');
    expect(component.toggleIconSize).toBe(22);
    expect(component.toggleIconLibrarySpriteFile).toBeTruthy();
    expect(component.options).toEqual(mockOptions);
  });

  it('should set and get optionsVisible property', () => {
    const visibilityChangeSpy = jest.spyOn(component.visibilityChange, 'emit');

    expect(component.optionsVisible).toBeFalsy();

    component.optionsVisible = true;

    expect(component._optionsVisible).toBeTruthy();
    expect(visibilityChangeSpy).toHaveBeenCalledWith(true);

    component.optionsVisible = false;

    expect(component._optionsVisible).toBeFalsy();
    expect(visibilityChangeSpy).toHaveBeenCalledWith(false);

    visibilityChangeSpy.mockClear();
    component.optionsVisible = false;

    expect(visibilityChangeSpy).not.toHaveBeenCalled();
  });

  it('should handle toggle click and emit event', () => {
    const toggleClickSpy = jest.spyOn(component.toggleClick, 'emit');
    const eventMock = new MouseEvent('click');

    // Start with closed dropdown
    component._optionsVisible = false;

    component.onToggleClick(eventMock);

    expect(component._optionsVisible).toBeTruthy();
    expect(toggleClickSpy).toHaveBeenCalledWith(eventMock);
  });

  it('should handle option click and emit event', () => {
    const optionClickSpy = jest.spyOn(component.optionClick, 'emit');
    const eventMock = new MouseEvent('click');
    const option = mockOptions[0];
    const index = 0;

    component._optionsVisible = true;

    component.onOptionClick(option, index, eventMock);

    expect(component._optionsVisible).toBeFalsy();
    expect(optionClickSpy).toHaveBeenCalledWith({
      option,
      index,
      event: eventMock
    });
  });

  it('should close options when clicking outside', () => {
    component._optionsVisible = true;
    const outsideElement = document.createElement('div');

    jest.spyOn(component.optionsElement.nativeElement, 'contains').mockReturnValue(false);

    component.clickOutside(outsideElement);

    expect(component._optionsVisible).toBeFalsy();
  });

  it('should not close options when clicking inside', () => {
    component._optionsVisible = true;
    const insideElement = document.createElement('div');

    jest.spyOn(component.optionsElement.nativeElement, 'contains').mockReturnValue(true);

    component.clickOutside(insideElement);

    expect(component._optionsVisible).toBeTruthy();
  });

  it('should apply host classes based on state', () => {
    component._optionsVisible = false;
    component.class = 'custom-class';

    expect(component.hostClasses).toContain('ds-actions-dropdown');
    expect(component.hostClasses).toContain('ds-actions-dropdown--closed');
    expect(component.hostClasses).toContain('custom-class');

    component._optionsVisible = true;

    expect(component.hostClasses).toContain('ds-actions-dropdown');
    expect(component.hostClasses).toContain('ds-actions-dropdown--open');
    expect(component.hostClasses).toContain('custom-class');
  });
});
