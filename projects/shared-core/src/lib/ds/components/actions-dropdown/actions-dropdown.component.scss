:host {
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: var(--button-primary-small-height);
  width: var(--button-primary-small-height);

  &.ds-actions-dropdown--open {
    z-index: 1;
  }
}

.ds-button.actions__toggle {
  --button-primary-regular-height: var(--button-primary-small-height);
  position: relative;
  border-radius: var(--actions-dropdown-toggle-border-radius);
  background-color: var(--actions-dropdown-toggle-background);
  box-shadow: var(--actions-dropdown-toggle-box-shadow);
  cursor: pointer;
  fill: var(--actions-dropdown-toggle-fill);
  transition: box-shadow 150ms, background-color 150ms;

  &:hover {
    fill: var(--actions-dropdown-toggle-fill-hover);
    box-shadow: var(--actions-dropdown-toggle-box-shadow-hover);
    background-color: var(--actions-dropdown-toggle-background-hover);
  }

  &.ds-button--focused {
    background-color: var(--actions-dropdown-toggle-background-focus);
  }
}

:host.ds-actions-dropdown--open .ds-button.actions__toggle {
  background-color: var(--actions-dropdown-toggle-background-active);
}

.actions__dropdown {
  position: absolute;
  background-color: var(--dropdown-option-background-color);
  border: var(--form-element-border-width) solid var(--form-element-border-color);
  border-radius: var(--actions-dropdown-options-border-radius);
  list-style: none;
  right: 0;
  padding: 0px;
  top: calc(100% - 8px);
  opacity: 0;
  pointer-events: none;
  transition: opacity 150ms, padding 200ms, top 200ms;

  .option {
    opacity: 0;
    line-height: 0px;
    white-space: nowrap;
    padding: 0;
    color: var(--actions-dropdown-option-color);
    transition: opacity 150ms, line-height 200ms, color 150ms;
    cursor: pointer;
    outline: none;
    overflow: hidden;

    button {
      height: auto;
      width: 100%;
      line-height: var(--actions-dropdown-option-height);
      text-align: left;
      padding: 0 var(--actions-dropdown-option-horizontal-padding);
      cursor: pointer;
    }

    button:hover,
    button:focus {
      color: var(--actions-dropdown-option-color-hover);
      text-decoration: underline;
      background: var(--dropdown-option-background-color-hover);
    }

    button:disabled {
      pointer-events: none;
      color: var(--form-element-color-disabled);
    }
  }
}

.actions__dropdown--open {
  opacity: 1;
  pointer-events: all;
  box-shadow: var(--actions-dropdown-options-box-shadow);
  padding: var(--spacing-xs) 0;
  top: calc(100%);

  .option {
    opacity: 1;
  }
}
