import { Component, EventEmitter, HostBinding, Input, Output } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';

export interface CheckboxChangeEvent {
    checked: boolean;
    checkbox: CheckboxComponent;
}

@Component({
    selector: 'ds-checkbox',
    templateUrl: 'checkbox.component.html',
    styleUrls: ['./checkbox.component.scss'],
})
export class CheckboxComponent {
    @HostBinding('id') @Input() id: string = 'ds-checkbox__' + Math.random().toString(36).substr(2, 9);
    @HostBinding('class') get hostClasses() {
        return ['ds-checkbox', this.class].join(' ');
    }
    @HostBinding('class.ds-checkbox--checked') get checkedClass() {
        if (this.parentForm && this.parentForm.controls[this.name]) {
            return this.parentForm.controls[this.name].value;
        } else {
            return this.checked;
        }
    }
    @Input() checked = false;
    @HostBinding('class.ds-checkbox--disabled') get disabledClass() {
        if (this.parentForm && this.parentForm.controls[this.name]) {
            return this.parentForm.controls[this.name].disabled;
        } else {
            return this.disabled;
        }
    }
    @Input() disabled = false;
    @HostBinding('class.ds-checkbox--required') get requiredClass() {
        if (this.parentForm && this.parentForm.controls[this.name]) {
            return this.parentForm.controls[this.name].errors && this.parentForm.controls[this.name].errors.required;
        } else {
            return this.required;
        }
    }
    @Input() required = false;
    @HostBinding('class.ds-checkbox--focused') focused = false;
    @Input() ariaLabel: string | null = null;
    @Input() ariaLabelledBy: string | null = null;
    @Input() parentForm: UntypedFormGroup;
    @Input() class: string;
    @Input() name: string;
    @Input() labelPosition = 'after';
    @Output() checkboxChange = new EventEmitter<CheckboxChangeEvent>();
    @Output() focusChange = new EventEmitter<boolean>();

    toggle() {
        if (this.disabled) {
            return;
        }

        this.checked = !this.checked;
        this.checkboxChange.emit({ checked: this.checked, checkbox: this });
    }

    onChange(event: Event) {
        event.stopPropagation();
    }

    onClick(event: Event) {
        event.stopPropagation();
        this.toggle();
    }

    onBlur() {
        this.focused = false;
        this.focusChange.emit(this.focused);
    }

    onFocus() {
        this.focused = !this.focused;
    }
}
