:root {
    display: inline-flex;
    flex-flow: row nowrap;
    padding: var(--checkbox-wrapper-padding-vertical) var(--checkbox-wrapper-padding-horizontal);
}
.ds-checkbox__input {
    position: absolute;
    left: 0;
    opacity: 0;
    width: 0;
    height: 0;
    margin: 0;
    border: none;
    overflow: hidden;
    z-index: -1;
    pointer-events: none;
}
.ds-checkbox__box {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: var(--checkbox-width);
    height: var(--checkbox-width);
    min-width: var(--checkbox-width);
    margin: 0;
    background-color: var(--checkbox-background);
    border-radius: var(--checkbox-border-radius);
    box-shadow: 0 0 0 var(--checkbox-border-width) var(--checkbox-border-color) inset;
    transition: box-shadow 150ms, background-color 150ms;
    -webkit-tap-highlight-color: transparent;
    will-change: background-color, box-shadow;
}
:host.ds-checkbox--checked .ds-checkbox__box {
    background-color: var(--checkbox-background-checked);
    box-shadow: 0 0 0 var(--checkbox-border-width) var(--checkbox-border-color-checked) inset;
}
:host.ds-checkbox--focused:not(.ds-checkbox--checked) .ds-checkbox__box {
    box-shadow: 0 0 0 var(--checkbox-border-width) var(--checkbox-border-color-checked) inset;
}
:host.ds-checkbox--focused.ds-checkbox--checked .ds-checkbox__box {
    box-shadow: 0 0 0 var(--checkbox-border-width) var(--checkbox-border-color-checked) inset;
}
:host.ds-checkbox--disabled .ds-checkbox__box {
    background-color: var(--checkbox-background-disabled);
    box-shadow: 0 0 0 var(--checkbox-border-width) var(--checkbox-border-color-disabled) inset;
}
:host.ds-checkbox--disabled.ds-checkbox--checked .ds-checkbox__box {
    background-color: var(--checkbox-background-disabled-checked);
}
.ds-checkbox__icon {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    stroke: var(--form-element-check-icon-color);
    stroke-linecap: square;
    stroke-width: 2px;
    stroke-dasharray: 14;
    stroke-dashoffset: 14;
    fill: none;
}
:host.ds-checkbox--checked .ds-checkbox__icon {
    stroke-dashoffset: 0;
    transition: stroke-dashoffset 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.ds-checkbox__label {
    display: flex;
    cursor: pointer;
    color: var(--checkbox-label-color);
    font-weight: var(--checkbox-label-font-weight);
    line-height: var(--checkbox-label-line-height);
    transition: color 150ms;
}
:host:hover .ds-checkbox__label {
    color: var(--checkbox-label-color-hover);
}
:host.ds-checkbox--checked .ds-checkbox__label {
    color: var(--checkbox-label-color-checked);
}
:host.ds-checkbox--disabled .ds-checkbox__label {
    color: var(--checkbox-label-color-disabled);
}
:host.ds-checkbox--required label:after {
    color: var(--label-required-indicator-color);
    content: '*';
    margin-left: var(--spacing-xs);
}
.ds-checkbox__content {
    margin-left: var(--checkbox-label-margin);
    &:empty {
        display: none;
    }
}
