<ng-container *ngIf="parentForm; then reactiveForm; else templateForm"></ng-container>

<ng-template #reactiveForm>
  <fieldset [formGroup]="parentForm">
    <input
      class="ds-checkbox__input"
      type="checkbox"
      [formControlName]="name"
      [attr.id]="id + '-input'"
      [attr.name]="name"
      [attr.aria-label]="ariaLabel"
      [attr.aria-labelledby]="ariaLabelledBy"
      (focus)="onFocus()"
      (blur)="onFocus()"
    />
    <label class="ds-checkbox__label ds-checkbox__label--{{ labelPosition }}" [attr.for]="id + '-input'">
      <span class="ds-checkbox__box" aria-hidden="true">
        <svg class="ds-checkbox__icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
          <path d="M3.5,7.8l3.2,3.2l5.8-5.8" />
        </svg>
      </span>
      <ng-container *ngTemplateOutlet="labelContent"></ng-container>
    </label>
  </fieldset>
</ng-template>

<ng-template #templateForm>
  <input
    class="ds-checkbox__input"
    type="checkbox"
    [checked]="checked"
    [disabled]="disabled"
    [required]="required"
    [attr.aria-label]="ariaLabel"
    [attr.aria-labelledby]="ariaLabelledBy"
    [attr.aria-required]="required"
    [attr.name]="name"
    [attr.id]="id + '-input'"
    (keyup.space)="onClick($event)"
    (click)="onClick($event)"
    (focus)="onFocus()"
    (blur)="onBlur()"
    (change)="onChange($event)"
  />
  <label class="ds-checkbox__label ds-checkbox__label--{{ labelPosition }}" [attr.for]="id + '-input'">
    <span class="ds-checkbox__box" aria-hidden="true">
      <svg class="ds-checkbox__icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">
        <path d="M3.5,7.8l3.2,3.2l5.8-5.8" />
      </svg>
    </span>
    <ng-container *ngTemplateOutlet="labelContent"></ng-container>
  </label>
</ng-template>

<ng-template #labelContent>
  <span class="ds-checkbox__content">
    <ng-content></ng-content>
  </span>
</ng-template>
