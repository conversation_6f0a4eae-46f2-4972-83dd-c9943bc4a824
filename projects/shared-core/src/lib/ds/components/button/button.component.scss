:host {
  width: var(--button-width);
  min-width: var(--button-min-width);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  position: relative;
  will-change: background, border-color, color, box-shadow, transform;
  transition: background 150ms, border-color 150ms, color 150ms, box-shadow 150ms, transform 150ms;
  user-select: none;
  white-space: nowrap;
  -webkit-tap-highlight-color: transparent;
}

button {
  display: inherit;
  color: inherit;
  font-weight: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
  text-shadow: inherit;
  text-decoration: inherit;
  text-transform: inherit;
  text-align: inherit;
  cursor: inherit;
  border-radius: inherit;
  align-items: inherit;
  justify-content: inherit;
  flex-direction: inherit;
  width: 100%;
  height: 100%;
  -webkit-tap-highlight-color: transparent;
}

.ds-button__content-wrapper {
  display: inherit;
  flex-direction: inherit;
  align-items: inherit;
  justify-content: inherit;
  width: 100%;
  border-radius: inherit;
}

.ds-button__label {
  position: relative;
  display: inline-block;
  will-change: opacity;
  transition: opacity 250ms;
  white-space: pre-wrap;

  &--hidden {
    opacity: 0;
  }
}

button:not(:-moz-focusring):focus > .ds-button__content-wrapper {
  box-shadow: none;
}

button:focus,
.ds-button__content-wrapper:focus {
  outline: none;
}

:host.ds-button--focused .ds-button__content-wrapper {
  box-shadow: var(--form-element-focus-ring-size) transparent;
}

:host.ds-button--regular,
:host.ds-button--regular .ds-button__content-wrapper {
  height: var(--button-primary-regular-height);
}

:host.ds-button--small,
:host.ds-button--small .ds-button__content-wrapper {
  height: var(--button-primary-small-height);
}

:host.ds-button--large,
:host.ds-button--large .ds-button__content-wrapper {
  height: var(--button-primary-large-height);
}

:host.ds-button--primary .ds-button__content-wrapper {
  padding: 0 var(--button-primary-regular-padding);
}

:host.ds-button--primary.ds-button--small .ds-button__content-wrapper {
  padding: 0 var(--button-primary-small-padding);
}

:host.ds-button--primary.ds-button--large .ds-button__content-wrapper {
  padding: 0 var(--button-primary-large-padding);
}

:host.ds-button--primary {
  background: var(--button-primary-background-color);
  border-style: var(--button-primary-border-style);
  border-width: var(--button-primary-border-width);
  border-color: var(--button-primary-border-color);
  border-radius: var(--button-primary-border-radius);
  color: var(--button-primary-color);
  text-shadow: 0 1px var(--button-primary-text-shadow-color);
  font-size: var(--button-primary-regular-font-size);
  text-transform: var(--button-primary-text-transform);
  text-align: var(--button-primary-text-align);
  font-family: var(--button-primary-font-family);
  font-weight: var(--button-primary-font-weight);
  box-shadow: var(--button-primary-box-shadow-size) var(--button-primary-box-shadow-color);
  min-width: var(--button-primary-min-width);
  fill: var(--button-primary-icon-fill);

  &:hover {
    background: var(--button-primary-background-color-hover);
    border-color: var(--button-primary-border-color-hover);
    color: var(--button-primary-color-hover);
    text-shadow: 0 1px var(--button-primary-text-shadow-color-hover);
    box-shadow: var(--button-primary-box-shadow-size) var(--button-primary-box-shadow-color-hover);
    fill: var(--button-primary-icon-fill-hover);
  }

  &.ds-button--focused {
    background: var(--button-primary-background-color-focus);
    border-color: var(--button-primary-border-color-focus);
    color: var(--button-primary-color-focus);
    text-shadow: 0 1px var(--button-primary-text-shadow-color-focus);
    box-shadow: var(--button-primary-box-shadow-size) var(--button-primary-box-shadow-color-focus);
    fill: var(--button-primary-icon-fill-focus);

    .ds-button__content-wrapper {
      box-shadow: var(--form-element-focus-ring-size) var(--form-element-focus-ring-color);
    }
  }

  &.ds-button--active {
    background: var(--button-primary-background-color-active);
    border-color: var(--button-primary-border-color-active);
    color: var(--button-primary-color-active);
    text-shadow: 0 1px var(--button-primary-text-shadow-color-active);
    box-shadow: var(--button-primary-box-shadow-size) var(--button-primary-box-shadow-color-active);
    fill: var(--button-primary-icon-fill-active);
  }

  &.ds-button--disabled {
    background: var(--button-primary-background-color-disabled);
    border-color: var(--button-primary-border-color-disabled);
    color: var(--button-primary-color-disabled);
    text-shadow: 0 1px var(--button-primary-text-shadow-color-disabled);
    box-shadow: var(--button-primary-box-shadow-size) var(--button-primary-box-shadow-color-disabled);
    fill: var(--button-primary-icon-fill-disabled);
  }
}

:host.ds-button--primary.ds-button--small {
  font-size: var(--button-primary-small-font-size);
}

:host.ds-button--primary.ds-button--large {
  font-size: var(--button-primary-large-font-size);
}

:host.ds-button--mobile {
  background: var(--color-brand-1);
  box-shadow: var(--button-primary-box-shadow-size) var(--button-primary-box-shadow-color);

  &:hover {
    background: var(--color-brand-1-lighten-1);
  }

  &.ds-button--focused {
    background: var(--color-brand-1-lighten-1);
  }

  &.ds-button--active {
    background: var(--color-brand-1-darken-1);
  }
}

:host.ds-button--secondary .ds-button__content-wrapper {
  padding: 0 var(--button-secondary-regular-padding);
}

:host.ds-button--secondary.ds-button--small .ds-button__content-wrapper {
  padding: 0 var(--button-secondary-small-padding);
}

:host.ds-button--secondary.ds-button--large .ds-button__content-wrapper {
  padding: 0 var(--button-secondary-large-padding);
}

:host.ds-button--secondary {
  background: var(--button-secondary-background-color);
  border-style: var(--button-secondary-border-style);
  border-width: var(--button-secondary-border-width);
  border-color: var(--button-secondary-border-color);
  border-radius: var(--button-secondary-border-radius);
  color: var(--button-secondary-color);
  text-shadow: 0 1px var(--button-secondary-text-shadow-color);
  font-size: var(--button-secondary-regular-font-size);
  text-transform: var(--button-secondary-text-transform);
  text-align: var(--button-secondary-text-align);
  font-family: var(--button-secondary-font-family);
  font-weight: var(--button-secondary-font-weight);
  box-shadow: var(--button-secondary-box-shadow-size) var(--button-secondary-box-shadow-color);
  min-width: var(--button-secondary-min-width);
  fill: var(--button-secondary-icon-fill);

  &:hover {
    background: var(--button-secondary-background-color-hover);
    border-color: var(--button-secondary-border-color-hover);
    color: var(--button-secondary-color-hover);
    text-shadow: 0 1px var(--button-secondary-text-shadow-color-hover);
    box-shadow: var(--button-secondary-box-shadow-size) var(--button-secondary-box-shadow-color-hover);
    fill: var(--button-secondary-icon-fill-hover);
  }

  &.ds-button--focused {
    background: var(--button-secondary-background-color-focus);
    border-color: var(--button-secondary-border-color-focus);
    color: var(--button-secondary-color-focus);
    text-shadow: 0 1px var(--button-secondary-text-shadow-color-focus);
    box-shadow: var(--button-secondary-box-shadow-size) var(--button-secondary-box-shadow-color-focus);
    fill: var(--button-secondary-icon-fill-focus);

    .ds-button__content-wrapper {
      box-shadow: var(--form-element-focus-ring-size) var(--button-secondary-box-shadow-color-focus);
    }
  }

  &.ds-button--active {
    background: var(--button-secondary-background-color-active);
    border-color: var(--button-secondary-border-color-active);
    color: var(--button-secondary-color-active);
    text-shadow: 0 1px var(--button-secondary-text-shadow-color-active);
    box-shadow: var(--button-secondary-box-shadow-size) var(--button-secondary-box-shadow-color-active);
    fill: var(--button-secondary-icon-fill-active);
  }

  &.ds-button--disabled {
    background: var(--button-secondary-background-color-disabled);
    border-color: var(--button-secondary-border-color-disabled);
    color: var(--button-secondary-color-disabled);
    text-shadow: 0 1px var(--button-secondary-text-shadow-color-disabled);
    box-shadow: var(--button-secondary-box-shadow-size) var(--button-secondary-box-shadow-color-disabled);
    fill: var(--button-secondary-icon-fill-disabled);

    .ds-button__content-wrapper {
      box-shadow: none;
    }
  }
}

:host.ds-button--secondary.ds-button--small {
  font-size: var(--button-secondary-small-font-size);
}

:host.ds-button--secondary.ds-button--large {
  font-size: var(--button-secondary-large-font-size);
}

:host.ds-button--link .ds-button__content-wrapper {
  padding: 0 var(--button-link-regular-padding);
}

:host.ds-button--link.ds-button--small .ds-button__content-wrapper {
  padding: 0 var(--button-link-small-padding);
}

:host.ds-button--link.ds-button--large .ds-button__content-wrapper {
  padding: 0 var(--button-link-large-padding);
}

:host.ds-button--link {
  background: var(--button-link-background-color);
  border-style: var(--button-link-border-style);
  border-width: var(--button-link-border-width);
  border-color: var(--button-link-border-color);
  border-radius: var(--button-link-border-radius);
  color: var(--button-link-color);
  text-shadow: 0 1px var(--button-link-text-shadow-color);
  font-size: var(--button-link-regular-font-size);
  text-decoration: var(--button-link-text-decoration);
  text-transform: var(--button-link-text-transform);
  text-align: var(--button-link-text-align);
  font-family: var(--button-link-font-family);
  font-weight: var(--button-link-font-weight);
  box-shadow: var(--button-link-box-shadow-size) var(--button-link-box-shadow-color);
  min-width: var(--button-link-min-width);
  fill: var(--button-link-icon-fill);

  &:hover {
    background: var(--button-link-background-color-hover);
    border-color: var(--button-link-border-color-hover);
    color: var(--button-link-color-hover);
    text-shadow: 0 1px var(--button-link-text-shadow-color-hover);
    text-decoration: var(--button-link-text-decoration-hover);
    box-shadow: var(--button-link-box-shadow-size) var(--button-link-box-shadow-color-hover);
    fill: var(--button-link-icon-fill-hover);
  }

  &.ds-button--focused {
    background: var(--button-link-background-color-focus);
    border-color: var(--button-link-border-color-focus);
    color: var(--button-link-color-focus);
    text-shadow: 0 1px var(--button-link-text-shadow-color-focus);
    text-decoration: var(--button-link-text-decoration-focus);
    box-shadow: var(--button-link-box-shadow-size) var(--button-link-box-shadow-color-focus);
    fill: var(--button-link-icon-fill-focus);

    .ds-button__content-wrapper {
      box-shadow: none;
    }
  }

  &.ds-button--active {
    background: var(--button-link-background-color-active);
    border-color: var(--button-link-border-color-active);
    color: var(--button-link-color-active);
    text-shadow: 0 1px var(--button-link-text-shadow-color-active);
    box-shadow: var(--button-link-box-shadow-size) var(--button-link-box-shadow-color-active);
    fill: var(--button-link-icon-fill-active);
  }

  &.ds-button--disabled {
    background: var(--button-link-background-color-disabled);
    border-color: var(--button-link-border-color-disabled);
    color: var(--button-link-color-disabled);
    text-shadow: 0 1px var(--button-link-text-shadow-color-disabled);
    box-shadow: var(--button-link-box-shadow-size) var(--button-link-box-shadow-color-disabled);
    fill: var(--button-link-icon-fill-disabled);
  }
}

:host.ds-button--link.ds-button--small {
  font-size: var(--button-link-small-font-size);
}

:host.ds-button--link.ds-button--large {
  font-size: var(--button-link-large-font-size);
}

:host.ds-button--icon.ds-button--regular {
  width: var(--button-primary-regular-height);
  min-width: var(--button-primary-regular-height);
}

:host.ds-button--icon.ds-button--small {
  width: var(--button-primary-small-height);
}

:host.ds-button--icon.ds-button--large {
  width: var(--button-primary-large-height);
}

:host.ds-button--icon .ds-button__content-wrapper {
  padding: 0;
}

:host.ds-button--with-icon .ds-button__icon {
  margin-right: var(--button-icon-margin);
}

:host.ds-button--with-icon.ds-button--icon-position-after .ds-button__icon {
  margin-right: 0;
  margin-left: var(--button-icon-margin);
  order: 1;
}

:host.ds-button--primary.ds-button--with-icon .ds-button__icon ::ng-deep svg {
  filter: drop-shadow(0px 1px 0px var(--button-primary-text-shadow-color));
}

:host.ds-button--primary.ds-button--with-icon.ds-button--disabled .ds-button__icon ::ng-deep svg {
  filter: drop-shadow(0px 1px 0px var(--button-primary-text-shadow-color));
}

button:disabled {
  cursor: not-allowed;

  button {
    pointer-events: none;
  }
}

:host.ds-button--loading {
  cursor: not-allowed;

  button {
    pointer-events: none;
  }
}

.ds-button__loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  pointer-events: none;
  will-change: opacity;
  transition: opacity 250ms;
  --spinner-dash-color: var(--button-loading-dash-color);
  --spinner-shadow-color: var(--button-loading-shadow-color);

  &--active {
    opacity: 1;
  }
}

:host-context(.checkbox-with-button).ds-button--secondary .ds-button__content-wrapper {
  height: 80px;
}
