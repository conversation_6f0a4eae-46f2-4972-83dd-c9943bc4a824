import { Component, Output, EventEmitter, Input, HostBinding } from '@angular/core';

export type ButtonType = 'button' | 'submit' | 'reset';
export type ButtonSize = 'regular' | 'small' | 'large';
export type ButtonVariation = 'primary' | 'secondary' | 'link' | 'icon';
export type ButtonIconPosition = 'before' | 'after';

@Component({
  selector: 'ds-button',
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss']
})
export class ButtonComponent {
  @HostBinding('class') get hostClasses() {
    return [
      'ds-button',
      'ds-button--' + this.variation,
      'ds-button--' + this.size,
      'ds-button--' + this.type,
      'ds-button--icon-position-' + this.iconPosition,
      this.class
    ].join(' ');
  }

  @HostBinding('class.ds-button--with-icon') get iconClass() {
    return this.variation !== 'icon' && this.icon;
  }

  @HostBinding('class.ds-button--disabled') @Input() isDisabled: boolean;
  @HostBinding('class.ds-button--loading') @Input() isLoading: boolean;
  @HostBinding('class.ds-button--loading-error') @Input() isLoadingError: boolean;
  @HostBinding('class.ds-button--loading-succesful') @Input() isLoadingSuccessful: boolean;
  @HostBinding('class.ds-button--focused') isFocused: boolean;
  @HostBinding('class.ds-button--active') isActive: boolean;
  @HostBinding('attr.id') @Input() id: string;
  @Input() ariaLabel: string | null = null;
  @Input() ariaPressed: boolean;
  @Input() class: string;
  @Input() icon: string;
  @Input() iconAriaHidden: boolean;
  @Input() iconAriaLabel: string;
  @Input() iconHeight: number;
  @Input() iconLibrarySpriteFile: boolean;
  @Input() iconPosition: ButtonIconPosition = 'before';
  @Input() iconSize: number;
  @Input() iconSpriteFile: string;
  @Input() iconWidth: number;
  @Input() isAutofocus: boolean;
  @Input() label: string;
  @Input() loadingDashColor: string;
  @Input() loadingShadowColor: string;
  @Input() loadingSize: number;
  @Input() size: ButtonSize = 'regular';
  @Input() tabindex: number;
  @Input() variation: ButtonVariation = 'primary';
  @Input() type: ButtonType = 'button';
  @Input() skipEventStopPropagation: boolean;
  @Output() buttonClick = new EventEmitter<Event>();
  @Output() blurred = new EventEmitter<Event>();

  onClick(event: Event) {
    if (!this.skipEventStopPropagation) {
      event.stopImmediatePropagation();
    }
    this.buttonClick.emit(event);
    this.isFocused = false;
  }

  onFocusIn() {
    this.isFocused = true;
  }

  onFocusOut() {
    this.isFocused = false;
    this.blurred.emit();
  }

  onActiveIn() {
    this.isActive = true;
  }

  onActiveOut() {
    this.isActive = false;
  }
}
