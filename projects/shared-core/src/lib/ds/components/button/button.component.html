<button
  [attr.aria-label]="ariaLabel"
  [attr.aria-pressed]="ariaPressed"
  [attr.tabindex]="tabindex ? tabindex : 0"
  [attr.type]="type"
  [autofocus]="isAutofocus && !isDisabled"
  [disabled]="isDisabled"
  (click)="onClick($event)"
  (focusin)="onFocusIn()"
  (focusout)="onFocusOut(); onActiveOut()"
  (blur)="onFocusOut(); onActiveOut()"
  (mousedown)="onActiveIn()"
  (mouseup)="onActiveOut()"
  (keydown.space)="onActiveIn()"
  (keyup.space)="onActiveOut()"
  (keydown.enter)="onActiveIn()"
  (keyup.enter)="onActiveOut()"
  [attr.aria-busy]="isLoading"
>
  <div class="ds-button__content-wrapper" tabindex="-1">
    <ds-icon
      *ngIf="icon"
      class="ds-button__icon"
      [name]="icon"
      [size]="iconSize ? iconSize : size === 'small' ? '10' : '16'"
      [width]="iconWidth"
      [height]="iconHeight"
      [spriteFile]="iconSpriteFile"
      [librarySpriteFile]="iconLibrarySpriteFile"
      [ariaLabel]="iconAriaLabel"
      [ariaHidden]="iconAriaHidden"
    ></ds-icon>
    <div class="ds-button__label" [class.ds-button__label--hidden]="isLoading || isLoadingSuccessful || isLoadingError">
      <ng-container *ngIf="label">{{ label }}</ng-container>
      <ng-content *ngIf="!label"></ng-content>
    </div>
  </div>
  <div
    *ngIf="isLoading"
    class="ds-button__loading-indicator"
    [class.ds-button__loading-indicator--active]="isLoading || isLoadingSuccessful || isLoadingError"
  >
    <ds-spinner
      class="ds-button__loading-icon"
      [active]="isLoading"
      [size]="loadingSize"
      [dashColor]="loadingDashColor"
      [shadowColor]="loadingShadowColor"
    ></ds-spinner>
  </div>
</button>
