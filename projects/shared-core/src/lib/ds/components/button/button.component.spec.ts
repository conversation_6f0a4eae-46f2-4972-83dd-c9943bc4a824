import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ButtonComponent } from './button.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('ButtonComponent', () => {
  let component: ButtonComponent;
  let fixture: ComponentFixture<ButtonComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ButtonComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(ButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('default values', () => {
    it('should have correct default values', () => {
      expect(component.iconPosition).toBe('before');
      expect(component.size).toBe('regular');
      expect(component.variation).toBe('primary');
      expect(component.type).toBe('button');
    });
  });

  describe('hostClasses binding', () => {
    it('should return correct classes based on component properties', () => {
      component.variation = 'secondary';
      component.size = 'small';
      component.type = 'submit';
      component.iconPosition = 'after';
      component.class = 'custom-class';

      const hostClasses = component.hostClasses;

      expect(hostClasses).toContain('ds-button');
      expect(hostClasses).toContain('ds-button--secondary');
      expect(hostClasses).toContain('ds-button--small');
      expect(hostClasses).toContain('ds-button--submit');
      expect(hostClasses).toContain('ds-button--icon-position-after');
      expect(hostClasses).toContain('custom-class');
    });
  });

  describe('iconClass binding', () => {
    it('should return true when non-icon button has an icon', () => {
      component.variation = 'primary';
      component.icon = 'test-icon';

      expect(component.iconClass).toBeTruthy();
    });

    it('should return false for icon-only button with icon', () => {
      component.variation = 'icon';
      component.icon = 'test-icon';

      expect(component.iconClass).toBeFalsy();
    });

    it('should return false when button has no icon', () => {
      component.variation = 'primary';
      component.icon = undefined;

      expect(component.iconClass).toBeFalsy();
    });
  });

  describe('event handling', () => {
    it('should emit buttonClick event when clicked', () => {
      const eventMock = new MouseEvent('click');
      const stopPropagationSpy = jest.spyOn(eventMock, 'stopImmediatePropagation');
      const buttonClickSpy = jest.spyOn(component.buttonClick, 'emit');

      component.onClick(eventMock);

      expect(stopPropagationSpy).toHaveBeenCalled();
      expect(buttonClickSpy).toHaveBeenCalledWith(eventMock);
      expect(component.isFocused).toBeFalsy();
    });

    it('should not stop propagation when skipEventStopPropagation is true', () => {
      const eventMock = new MouseEvent('click');
      const stopPropagationSpy = jest.spyOn(eventMock, 'stopImmediatePropagation');

      component.skipEventStopPropagation = true;
      component.onClick(eventMock);

      expect(stopPropagationSpy).not.toHaveBeenCalled();
    });

    it('should set focus state when focusing in/out', () => {
      component.onFocusIn();

      expect(component.isFocused).toBeTruthy();

      component.onFocusOut();

      expect(component.isFocused).toBeFalsy();
    });

    it('should emit blurred event when focus out', () => {
      const blurredSpy = jest.spyOn(component.blurred, 'emit');

      component.onFocusOut();

      expect(blurredSpy).toHaveBeenCalled();
    });

    it('should set active state on active in/out', () => {
      component.onActiveIn();

      expect(component.isActive).toBeTruthy();

      component.onActiveOut();

      expect(component.isActive).toBeFalsy();
    });
  });

  describe('property bindings', () => {
    it('should properly bind isDisabled', () => {
      component.isDisabled = true;
      fixture.detectChanges();
      const buttonElement = fixture.nativeElement;

      expect(buttonElement.classList.contains('ds-button--disabled')).toBeTruthy();
    });

    it('should properly bind isLoading', () => {
      component.isLoading = true;
      fixture.detectChanges();
      const buttonElement = fixture.nativeElement;

      expect(buttonElement.classList.contains('ds-button--loading')).toBeTruthy();
    });

    it('should properly bind isLoadingError', () => {
      component.isLoadingError = true;
      fixture.detectChanges();
      const buttonElement = fixture.nativeElement;

      expect(buttonElement.classList.contains('ds-button--loading-error')).toBeTruthy();
    });

    it('should properly bind isLoadingSuccessful', () => {
      component.isLoadingSuccessful = true;
      fixture.detectChanges();
      const buttonElement = fixture.nativeElement;

      expect(buttonElement.classList.contains('ds-button--loading-succesful')).toBeTruthy();
    });
  });

  describe('different variations', () => {
    it('should support primary button variation', () => {
      component.variation = 'primary';
      fixture.detectChanges();

      expect(component.hostClasses).toContain('ds-button--primary');
    });

    it('should support secondary button variation', () => {
      component.variation = 'secondary';
      fixture.detectChanges();

      expect(component.hostClasses).toContain('ds-button--secondary');
    });

    it('should support link button variation', () => {
      component.variation = 'link';
      fixture.detectChanges();

      expect(component.hostClasses).toContain('ds-button--link');
    });

    it('should support icon button variation', () => {
      component.variation = 'icon';
      fixture.detectChanges();

      expect(component.hostClasses).toContain('ds-button--icon');
    });
  });

  describe('different sizes', () => {
    it('should support regular size', () => {
      component.size = 'regular';
      fixture.detectChanges();

      expect(component.hostClasses).toContain('ds-button--regular');
    });

    it('should support small size', () => {
      component.size = 'small';
      fixture.detectChanges();

      expect(component.hostClasses).toContain('ds-button--small');
    });

    it('should support large size', () => {
      component.size = 'large';
      fixture.detectChanges();

      expect(component.hostClasses).toContain('ds-button--large');
    });
  });

  describe('button types', () => {
    it('should support button type', () => {
      component.type = 'button';
      fixture.detectChanges();

      expect(component.hostClasses).toContain('ds-button--button');
    });

    it('should support submit type', () => {
      component.type = 'submit';
      fixture.detectChanges();

      expect(component.hostClasses).toContain('ds-button--submit');
    });

    it('should support reset type', () => {
      component.type = 'reset';
      fixture.detectChanges();

      expect(component.hostClasses).toContain('ds-button--reset');
    });
  });
});
