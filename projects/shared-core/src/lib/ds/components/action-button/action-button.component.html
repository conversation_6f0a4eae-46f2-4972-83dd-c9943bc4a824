<ds-button
  variation="secondary"
  class="action-button"
  (buttonClick)="handleClick()"
>
  <div class="flex flex--align-items-center">
    <ds-icon
      *ngIf="icon"
      class="main-icon"
      [name]="icon"
      size="24"
    ></ds-icon>
    <div class="title">
      <ng-container *ngIf="label">{{ label }}</ng-container>
      <ng-content *ngIf="!label"></ng-content>
    </div>
    <ds-icon
      *ngIf="action === 'link'"
      class="action-icon link"
      name="icon-caret-right"
      [librarySpriteFile]="true"
    ></ds-icon>
    <ds-icon
      *ngIf="action === 'add'"
      class="action-icon add"
      name="icon-plus"
      [librarySpriteFile]="true"
    ></ds-icon>
    <ds-icon
      *ngIf="action === 'message'"
      class="action-icon message"
      name="icon-message"
      [librarySpriteFile]="true"
    ></ds-icon>
  </div>
</ds-button>
