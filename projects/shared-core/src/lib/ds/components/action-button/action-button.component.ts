import { Component, EventEmitter, HostBinding, Input, Output } from '@angular/core';

export enum Action {
    LINK = 'link',
    ADD = 'add',
    MESSAGE = 'message',
}

@Component({
    selector: 'ds-action-button',
    styleUrls: ['./action-button.component.scss'],
    templateUrl: './action-button.component.html',
})
export class ActionButtonComponent {
    @HostBinding('class') get hostClasses() {
        return ['ds-action-button', 'ds-action-button--' + this.action, this.class].join(' ');
    }
    @Input() label: string;
    @Input() icon: string;
    @Input() class: string;
    @Input() action: Action | string = Action.LINK;
    @Output() buttonClick = new EventEmitter<any>();

    public handleClick() {
        this.buttonClick.emit();
    }
}
