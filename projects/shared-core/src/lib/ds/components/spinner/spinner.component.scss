:root {
    display: block;
}
.ds-spinner {
    width: 100%;
    height: 100%;
}
.ds-spinner--active svg {
    will-change: transform;
    animation: rotate 1.5s linear infinite;
}
.ds-spinner--active circle.dash {
    animation: dash 1s ease-in-out infinite;
}

circle.dash {
    stroke: var(--spinner-dash-color);
    stroke-linecap: round;
}

circle.shadow {
    stroke: var(--spinner-shadow-color);
}
