import { Component, OnInit, HostBinding, Input } from '@angular/core';

@Component({
  selector: 'ds-spinner',
  templateUrl: './spinner.component.html',
  styleUrls: ['./spinner.component.scss']
})
export class SpinnerComponent implements OnInit {
  @Input() active: boolean;
  @Input() size: string | number;
  @Input() dashColor: string;
  @Input() shadowColor: string;
  sizes = {
    xl: 48,
    lg: 32,
    md: 24,
    sm: 16,
    xs: 12
  };
  style = {
    width: null,
    height: null
  };
  defaultSize = this.sizes.md;
  @HostBinding('style.height.px') hostHeight: number;
  @HostBinding('style.width.px') hostWidth: number;

  constructor() {}

  ngOnInit() {
    const size = typeof this.size === 'number' ? this.size : parseInt(this.size, 10);
    const width = !isNaN(size) ? size : this.sizes[this.size] ?? this.defaultSize;
    const height = width;

    this.style.width = this.hostHeight = width;
    this.style.height = this.hostWidth = height;
  }
}
