import { NgModule, ModuleWithProviders } from '@angular/core';
import { CommonModule } from '@angular/common';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { IconComponent } from './icon.component';
import { TooltipDirective } from '../../directives/tooltip.directive';
import { DsLibraryConfig, DsConfigService } from '../../services/config.service';

@NgModule({
  declarations: [IconComponent, TooltipDirective],
  exports: [IconComponent, TooltipDirective],
  imports: [CommonModule],
  providers: [provideHttpClient(withInterceptorsFromDi())]
})
export class DsIconModule {
  static forRoot(config: DsLibraryConfig): ModuleWithProviders<DsIconModule> {
    return {
      ngModule: DsIconModule,
      providers: [
        {
          provide: DsConfigService,
          useValue: config
        }
      ]
    };
  }
}
