import { Component, Input, OnInit, HostBinding, Inject } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml } from '@angular/platform-browser';
import { DsConfigService, DsLibraryConfig } from '../../services/config.service';

const DEFAULT_SPRITE_FILE_LOCATION = 'assets/images/sprite.svg';

@Component({
  selector: 'ds-icon',
  templateUrl: './icon.component.html',
  styleUrls: ['./icon.component.scss'],
  host: { class: 'ds-icon' }
})
export class IconComponent implements OnInit {
  @HostBinding('class') get hostClasses() {
    return ['ds-icon', 'ds-icon--' + this.name, this.cssSize ? 'ds-icon--fixed-size' : '', this.class].join(' ');
  }
  @Input() class: string;
  @Input() spriteFile: string;
  @Input() name: string;
  @Input() size: string | number;
  @Input() width: number;
  @Input() height: number;
  @Input() cssSize: boolean;
  @Input() librarySpriteFile: boolean;
  @Input() ariaLabel: string;
  @Input() ariaHidden: boolean;
  sizes = {
    xxl: 48,
    xl: 34,
    l: 24,
    m: 16,
    s: 12,
    xs: 8,
    xxs: 4
  };
  defaultSize = this.sizes.m;
  style = {};
  renderNativeIcons = this.dsConfig.renderNativeIcons;
  nativeIconContent: SafeHtml;
  nativeIconViewBox: string;

  constructor(@Inject(DsConfigService) private dsConfig: DsLibraryConfig, private sanitizer: DomSanitizer) {}

  ngOnInit() {
    if (!this.spriteFile) {
      this.spriteFile = this.dsConfig.pathToProjectSprite ? this.dsConfig.pathToProjectSprite : DEFAULT_SPRITE_FILE_LOCATION;
    }

    if (this.librarySpriteFile) {
      this.spriteFile = this.dsConfig.pathToLibrarySprite;
    }

    if (this.renderNativeIcons) {
      this.prepareNativeIcon();
    }

    if (this.cssSize) {
      return;
    }

    this.applyStyles();
  }

  applyStyles() {
    let width: number | string;
    let height: number | string;

    if (!this.width && !this.height) {
      const size = typeof this.size === 'number' ? this.size : parseInt(this.size, 10);
      width = !isNaN(size) ? size : this.sizes[this.size] ?? this.defaultSize;
      height = width;
    }
    if (this.width) {
      width = this.width;
    }
    if (this.height) {
      height = this.height;
    }

    this.style = {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'width.px': width,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      'height.px': height
    };
  }

  prepareNativeIcon() {
    const originalSvg = document.getElementById(this.name);

    if (!originalSvg || originalSvg.tagName.toLowerCase() !== 'svg') {
      console.warn('%cWarning: ds-icon with id "' + this.name + '" not found, skipping', 'color: red');
      return;
    }

    this.nativeIconViewBox = originalSvg.getAttribute('viewBox');
    this.nativeIconContent = this.sanitizer.bypassSecurityTrustHtml(originalSvg.innerHTML);
  }
}
