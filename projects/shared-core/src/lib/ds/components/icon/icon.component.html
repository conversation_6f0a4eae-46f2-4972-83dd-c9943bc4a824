<ng-container *ngIf="renderNativeIcons; then nativeTemplate; else defaultTemplate"></ng-container>

<ng-template #defaultTemplate>
  <svg xmlns="http://www.w3.org/2000/svg" [ngStyle]="style" [attr.aria-label]="ariaLabel" [attr.aria-hidden]="ariaHidden || !ariaLabel">
    <use [attr.xlink:href]="spriteFile + '#' + name"></use>
  </svg>
</ng-template>

<ng-template #nativeTemplate>
  <svg
    [attr.viewBox]="nativeIconViewBox"
    [innerHtml]="nativeIconContent"
    [ngStyle]="style"
    [attr.aria-label]="ariaLabel"
    [attr.aria-hidden]="ariaHidden || !ariaLabel"
  ></svg>
</ng-template>
