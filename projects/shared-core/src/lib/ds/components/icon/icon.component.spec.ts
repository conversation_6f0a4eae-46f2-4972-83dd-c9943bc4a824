import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IconComponent } from './icon.component';
import { DomSanitizer } from '@angular/platform-browser';
import { DsConfigService } from '../../services/config.service';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('IconComponent', () => {
  let component: IconComponent;
  let fixture: ComponentFixture<IconComponent>;
  let mockDsConfig: any;
  let mockSanitizer: any;

  beforeEach(() => {
    mockDsConfig = {
      renderNativeIcons: false,
      pathToProjectSprite: 'custom-project-sprite.svg',
      pathToLibrarySprite: 'custom-library-sprite.svg'
    };

    mockSanitizer = {
      bypassSecurityTrustHtml: jest.fn().mockReturnValue('sanitized-content')
    };

    TestBed.configureTestingModule({
      declarations: [IconComponent],
      providers: [
        { provide: DsConfigService, useValue: mockDsConfig },
        { provide: DomSanitizer, useValue: mockSanitizer }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(IconComponent);
    component = fixture.componentInstance;
    component.name = 'test-icon';
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('host binding', () => {
    it('should return correct hostClasses', () => {
      component.name = 'user';
      component.class = 'custom-class';

      expect(component.hostClasses).toContain('ds-icon');
      expect(component.hostClasses).toContain('ds-icon--user');
      expect(component.hostClasses).toContain('custom-class');
    });

    it('should include fixed-size class when cssSize is true', () => {
      component.name = 'user';
      component.cssSize = true;

      expect(component.hostClasses).toContain('ds-icon--fixed-size');
    });
  });

  describe('ngOnInit', () => {
    it('should use default sprite file location when not provided', () => {
      mockDsConfig.pathToProjectSprite = undefined;
      component.ngOnInit();

      expect(component.spriteFile).toBe('assets/images/sprite.svg');
    });

    it('should use project sprite file from config when available', () => {
      component.ngOnInit();

      expect(component.spriteFile).toBe('custom-project-sprite.svg');
    });

    it('should use library sprite file when librarySpriteFile is true', () => {
      component.librarySpriteFile = true;
      component.ngOnInit();

      expect(component.spriteFile).toBe('custom-library-sprite.svg');
    });

    it('should use provided sprite file when specified', () => {
      component.spriteFile = 'custom-sprite.svg';
      component.ngOnInit();

      expect(component.spriteFile).toBe('custom-sprite.svg');
    });

    it('should call prepareNativeIcon when renderNativeIcons is true', () => {
      component.renderNativeIcons = true;
      const spy = jest.spyOn(component, 'prepareNativeIcon');

      component.ngOnInit();

      expect(spy).toHaveBeenCalled();
    });

    it('should not call applyStyles when cssSize is true', () => {
      component.cssSize = true;
      const spy = jest.spyOn(component, 'applyStyles');

      component.ngOnInit();

      expect(spy).not.toHaveBeenCalled();
    });

    it('should call applyStyles when cssSize is false', () => {
      component.cssSize = false;
      const spy = jest.spyOn(component, 'applyStyles');

      component.ngOnInit();

      expect(spy).toHaveBeenCalled();
    });
  });

  describe('applyStyles', () => {
    it('should set width and height based on size input with predefined size', () => {
      component.size = 'l';
      component.applyStyles();

      expect(component.style['width.px']).toBe(24);
      expect(component.style['height.px']).toBe(24);
    });

    it('should set width and height based on size input with numeric string', () => {
      component.size = '30';
      component.applyStyles();

      expect(component.style['width.px']).toBe(30);
      expect(component.style['height.px']).toBe(30);
    });

    it('should set width and height based on size input with number', () => {
      component.size = 42;
      component.applyStyles();

      expect(component.style['width.px']).toBe(42);
      expect(component.style['height.px']).toBe(42);
    });

    it('should use default size when size is invalid', () => {
      component.size = 'invalid-size';
      component.applyStyles();

      expect(component.style['width.px']).toBe(16);
      expect(component.style['height.px']).toBe(16);
    });

    it('should use width and height inputs when provided', () => {
      component.width = 50;
      component.height = 60;
      component.applyStyles();

      expect(component.style['width.px']).toBe(50);
      expect(component.style['height.px']).toBe(60);
    });

    it('should prioritize width/height inputs over size input', () => {
      component.size = 'xl';
      component.width = 50;
      component.height = 60;
      component.applyStyles();

      expect(component.style['width.px']).toBe(50);
      expect(component.style['height.px']).toBe(60);
    });
  });

  describe('prepareNativeIcon', () => {
    it('should prepare native icon content when icon exists', () => {
      const svgElement = {
        tagName: 'SVG',
        getAttribute: jest.fn().mockReturnValue('0 0 24 24'),
        innerHTML: '<path d="M10 10"></path>'
      };

      document.getElementById = jest.fn().mockImplementation(id => {
        return id === 'test-icon' ? svgElement : null;
      });

      component.name = 'test-icon';
      component.prepareNativeIcon();

      expect(component.nativeIconViewBox).toBe('0 0 24 24');
      expect(mockSanitizer.bypassSecurityTrustHtml).toHaveBeenCalledWith('<path d="M10 10"></path>');
    });

    it('should log warning when icon does not exist', () => {
      document.getElementById = jest.fn().mockReturnValue(null);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});

      component.name = 'non-existent-icon';
      component.prepareNativeIcon();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Warning: ds-icon with id "non-existent-icon" not found'),
        expect.any(String)
      );
    });

    it('should log warning when element is not an SVG', () => {
      const divElement = {
        tagName: 'DIV'
      };

      document.getElementById = jest.fn().mockReturnValue(divElement);

      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});

      component.name = 'test-icon';
      component.prepareNativeIcon();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Warning: ds-icon with id "test-icon" not found'),
        expect.any(String)
      );
    });
  });
});
