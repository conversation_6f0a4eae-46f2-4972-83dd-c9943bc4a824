.ds-datatable {
    position: relative;
    display: flex;
    flex-direction: column;

    font-size: var(--datatable-font-size);
    font-family: var(--datatable-font-family);
}
.datatable__header-wrapper,
.datatable__body-wrapper,
.datatable__footer-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
}
.datatable__body-wrapper {
    flex: 1;
}
.datatable__header-row,
.datatable__row {
    position: relative;
    display: flex;
}
.datatable__header-cell,
.datatable__cell {
    flex-grow: 1;
    width: 100%;
    outline: none;
    -webkit-tap-highlight-color: transparent;
}
.datatable--2cols {
    .datatable__header-cell,
    .datatable__cell {
        width: 50%;
    }
    .datatable__cell.datatable-indexcell--outside {
        width: 0px;
    }
}
.datatable--3cols {
    .datatable__header-cell,
    .datatable__cell {
        width: calc(100% / 3);
    }
    .datatable__cell.datatable-indexcell--outside {
        width: 0px;
    }
}
.datatable--4cols {
    .datatable__header-cell,
    .datatable__cell {
        width: calc(100% / 4);
    }
    .datatable__cell.datatable-indexcell--outside {
        width: 0px;
    }
}
.datatable--5cols {
    .datatable__header-cell,
    .datatable__cell {
        width: calc(100% / 5);
    }
    .datatable__cell.datatable-indexcell--outside {
        width: 0px;
    }
}
.datatable--6cols {
    .datatable__header-cell,
    .datatable__cell {
        width: calc(100% / 6);
    }
    .datatable-indexcell--outside {
        width: 0px;
    }
}
.datatable--7cols {
    .datatable__header-cell,
    .datatable__cell {
        width: calc(100% / 7);
    }
    .datatable__cell.datatable-indexcell--outside {
        width: 0px;
    }
}
.datatable--8cols {
    .datatable__header-cell,
    .datatable__cell {
        width: calc(100% / 8);
    }
    .datatable__cell.datatable-indexcell--outside {
        width: 0px;
    }
}
.datatable--9cols {
    .datatable__header-cell,
    .datatable__cell {
        width: calc(100% / 9);
    }
    .datatable__cell.datatable-indexcell--outside {
        width: 0px;
    }
}
.datatable--10cols {
    .datatable__header-cell,
    .datatable__cell {
        width: calc(100% / 10);
    }
    .datatable__cell.datatable-indexcell--outside {
        width: 0px;
    }
}
.datatable__header-wrapper {
    background-color: var(--datatable-header-background-color);
    border: var(--datatable-header-border-width) solid var(--datatable-header-border-color);
    border-bottom: 0;
}
.datatable__header {
    margin: var(--spacing-m);
    margin-bottom: 0;
    padding-bottom: var(--spacing-m);
    transition: opacity 300ms;
}
.datatable--is-loading .datatable__header,
.datatable--is-loading .datatable__header-row,
.datatable--is-disabled .datatable__header-row {
    pointer-events: none;
    opacity: 0.5;
}
.datatable__header-row {
    transition: opacity 300ms;
}
.datatable__header-cell {
    position: relative;
    display: flex;
    align-items: center;
    user-select: none;
    padding: var(--datatable-cell-vertical-padding) var(--datatable-cell-horizontal-padding);
    font-weight: var(--datatable-header-cell-font-weight);
    font-size: var(--datatable-header-cell-font-size);
    &--sorted-by,
    &--sortable {
        cursor: pointer;
    }
}
.datatable__header .spacer {
    width: 1px;
    height: 100%;
    background: var(--color-grey-darken-1);
    box-shadow: 1px 0 rgba(255, 255, 255, 0.5);
}
.datatable__column-sort {
    display: flex !important;
    height: auto !important;
    min-height: 100%;
    width: var(--spacing-l);
}
.column-sort-icon {
    margin-left: var(--spacing-xs);
    min-width: 14px;
    height: 14px;
    fill: var(--color-grey-darken-1);
    &--descending {
        --icon-sort-up-fill: var(--color-primary);
    }
    &--ascending {
        --icon-sort-down-fill: var(--color-primary);
    }
}
.datatable__row {
    flex-direction: column;
    background-color: var(--datatable-row-background-color);
    border: var(--datatable-border-width) solid var(--datatable-border-color);
    border-top-width: 0;
    border-bottom-width: var(--datatable-row-border-width);
    border-bottom-color: var(--datatable-row-border-color);
    box-shadow: 0 -1px transparent;
    transition: background-color 150ms, border-color 150ms, box-shadow 150ms;
    &--hide-bottom-border {
        border-bottom-width: 0;
    }
    &:first-of-type {
        box-shadow: none !important;
        border-top-width: var(--datatable-border-width);

        border-top-right-radius: var(--datatable-border-radius);
        border-top-left-radius: var(--datatable-border-radius);
    }
    &:last-of-type {
        border-bottom-color: var(--datatable-border-color);
        border-bottom-right-radius: var(--datatable-border-radius);
        border-bottom-left-radius: var(--datatable-border-radius);
    }
}
.datatable__row:nth-child(even) {
    background-color: var(--datatable-row-even-background-color);
}
.datatable__row:hover {
    border-color: var(--datatable-row-border-color-hover);
    box-shadow: 0 calc(var(--datatable-row-border-width) * -1) var(--datatable-row-border-color-hover);
}
.datatable__row--clickable {
    cursor: pointer;
}
.datatable__cell {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: var(--datatable-cell-vertical-padding) var(--datatable-cell-horizontal-padding);
    transition: background-color 150ms;
}
.datatable__row--selected .datatable__cell {
    background-color: var(--datatable-row-background-color-selected);
}
.column-header-label,
.cell-label {
    display: block;
    white-space nowrap {
        &--wrap-content {
            white-space: normal;
        }
    }
}
.column-header-label {
    font-size: 1.3rem;
    line-height: 1;
}
.datatable__header-cell--select,
.datatable__cell--select {
    max-width: calc(var(--datatable-cell-horizontal-padding) + var(--checkbox-width));
    padding-right: 0 !important;
}
.datatable__header-cell--select .select-all ::ng-deep .ds-checkbox__label {
    padding: 0;
}
.datatable__row-click-trigger {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    cursor: pointer;
    outline: none;
    border: 0;
    background: transparent;
    -webkit-tap-highlight-color: transparent;
}
.datatable__row--fixed-height .datatable__cell {
    height: var(--datatable-fixed-row-height);
}
.datatable__header-cell--no-padding,
.datatable__cell--no-padding {
    padding: 0 !important;
}
.datatable__loading-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
    background: #fff;
    color: var(--text-grey);
    border-left: 1px solid;
    border-bottom: 1px solid;
    border-right: 1px solid;
    border-color: var(--datatable-border-color);
    opacity: 0;
    pointer-events: none;
    transition: opacity 150ms;
    &--visible {
        opacity: 1;
        pointer-events: all;
    }
}
.datatable-indexcell--outside {
    width: 0px;
    padding: 0px;
    justify-content: flex-start;
    padding-top: var(--datatable-cell-horizontal-padding);

    span {
        color: var(--color-grey);
        font-weight: var(--font-weight-semibold);
        position: absolute;
        left: -40px;
    }
}
.pagination {
    position: relative;
    display: flex;
    align-items: center;
    font-size: 1.3rem;
    user-select: none;
    transition: opacity 150ms;
    &--hidden {
        opacity: 0;
        pointer-events: none;
    }
}
.pagination__button {
    fill: var(--datatable-pagination-button-icon-fill);
    padding: 0;
    height: var(--datatable-pagination-button-size);
    width: var(--datatable-pagination-button-size);
}
.pagination__status {
    display: inline-flex;
    transition: opacity 150ms;
}
.pagination--disabled .pagination__status {
    opacity: 0.5;
}
.datatable__footer-wrapper {
    flex-direction: row;
    justify-content: space-between;
    background-color: var(--datatable-footer-background-color);
    border: 1px solid var(--datatable-border-color);
    border-top: 0;
    padding: 0 var(--spacing-xs);
}
.datatable__limit-filter.ng-select {
    padding-right: var(--spacing-xs);
}
.datatable__expanded-content-wrapper {
    z-index: 1;
}
