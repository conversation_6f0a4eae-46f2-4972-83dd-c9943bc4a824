import { NgModule } from '@angular/core';
import { DatePipe, CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataTableColumnDirective } from './datatablecolumn.directive';
import { DataTableComponent } from './datatable.component';
import { DataTablePaginationComponent } from './datatablepagination.component';
import { DataTableRowComponent } from './datatablerow.component';
import { DsButtonModule } from '../button/button.module';
import { DsIconModule } from '../icon/icon.module';
import { DsCheckboxModule } from '../checkbox/checkbox.module';

@NgModule({
    imports: [CommonModule, FormsModule, ReactiveFormsModule, DsButtonModule, DsIconModule, DsCheckboxModule],
    providers: [DatePipe],
    declarations: [DataTableComponent, DataTableColumnDirective, DataTableRowComponent, DataTablePaginationComponent],
    exports: [DataTableComponent, DataTableColumnDirective],
})
export class DsDataTableModule {}
