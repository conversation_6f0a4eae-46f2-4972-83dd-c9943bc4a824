<div class="datatable__header-wrapper">
  <div *ngIf="showHeader" class="datatable__header flex">
    <ng-template [ngTemplateOutlet]="headerTemplate"></ng-template>
  </div>
  <div class="datatable__header-row" [class.datatable__header-row--fixed-height]="fixedRowHeight">
    <div *ngIf="indexColumnVisible && !indexColumnOutsideTable" class="datatable__header-cell datatable__header-cell--index">
      <span [textContent]="indexColumnHeader"></span>
    </div>
    <div
      *ngIf="selectColumnVisible"
      class="datatable__header-cell datatable__header-cell--select datatable__header-cell--show-on-accordion"
    >
      <ds-checkbox [checked]="selectAllCheckbox" (checkboxChange)="onSelectAllChange($event)"></ds-checkbox>
    </div>
    <div
      *ngFor="let column of columns; trackBy: trackColumnByFn.bind(this); let first = first; let last = last"
      class="datatable__header-cell"
      #th
      [class.datatable__header-cell--first]="first"
      [class.datatable__header-cell--last]="last"
      [class.datatable__header-cell--sortable]="column.sortable"
      [class.datatable__header-cell--no-padding]="!column.columnHeaderTemplate && !column.header"
      [class.datatable__header-cell--sorted-by]="column.sortable && sortBy === column.property ? true : false"
      [ngClass]="column.classList"
      [style.flexDirection]="column.flexDirection"
      [style.alignItems]="column.alignItems"
      [style.justifyContent]="column.justifyContent"
      [style.minWidth]="column.minWidth"
      [style.maxWidth]="column.maxWidth"
      [attr.tabindex]="column.sortable ? '0' : null"
      (click)="onHeaderClick(column, $event)"
      (keydown.enter)="onHeaderClick(column, $event)"
      (keydown.space)="onHeaderClick(column, $event)"
    >
      <span *ngIf="!column.columnHeaderTemplate && column.header" class="column-header-label" [textContent]="column.header"></span>
      <ng-template
        *ngIf="column.columnHeaderTemplate"
        [ngTemplateOutlet]="column.columnHeaderTemplate"
        [ngTemplateOutletContext]="{ column: column }"
      ></ng-template>
      <ds-icon
        *ngIf="column.sortable"
        class="column-sort-icon"
        name="icon-sort"
        size="14"
        [librarySpriteFile]="true"
        [ngClass]="{
          'column-sort-icon--descending': !sortAsc && sortBy === column.property,
          'column-sort-icon--ascending': sortAsc && sortBy === column.property
        }"
      ></ds-icon>
    </div>
  </div>
</div>
<div class="datatable__body-wrapper">
  <form *ngIf="!isLoading" novalidate [formGroup]="datatableForm">
    <ng-container *ngFor="let item of items; let index = index; trackBy: trackItemByFn.bind(this)">
      <div
        *ngIf="isVisibleRow(index)"
        datatablerow
        #row
        class="datatable__row"
        [class.datatable__row--selected]="row.isSelected"
        [class.datatable__row--hide-bottom-border]="row.hideBottomBorder"
        [class.datatable__row--fixed-height]="fixedRowHeight"
        [displayParams]="displayParams"
        [columns]="columns"
        [showIndex]="indexColumnVisible"
        [indexOutsideTable]="indexColumnOutsideTable"
        [showPagination]="showPagination"
        [isSelectable]="selectableRows"
        [isExpandable]="expandableRows"
        [defaultExpanded]="expandAll"
        [multiSelect]="multiSelect"
        [action]="actionRows"
        [item]="item"
        [index]="index"
        [parentUid]="uid"
        [parentForm]="datatableForm"
        (selectedChange)="onRowSelectedChange(row)"
        (rowClick)="onRowClick(row)"
      ></div>
    </ng-container>
    <div *ngIf="itemCount === 0 && noDataMessage" class="datatable__row datatable__row--no-data">
      <div class="datatable__cell">{{ noDataMessage }}</div>
    </div>
    <ng-template *ngIf="extraRowTemplate" [ngTemplateOutlet]="extraRowTemplate" [ngTemplateOutletContext]="{}"></ng-template>
  </form>
  <div class="datatable__loading-wrapper" [class.datatable__loading-wrapper--visible]="isLoading">
    {{ loadingMessage }}
  </div>
</div>
