import { Directive, Input, ContentChild, OnInit } from '@angular/core';

@Directive({
    selector: 'datatablecolumn',
})
export class DataTableColumnDirective implements OnInit {
    @Input() class: any;
    @Input() header: string;
    @Input() sortable = false;
    @Input() property: string;
    @Input() minWidth: number | string;
    @Input() maxWidth: number | string;
    @Input() flexDirection: string;
    @Input() justifyContent: string;
    @Input() alignItems: string;
    @Input() visible = true;
    @Input() visibleOverflow: boolean;
    @Input() customSort: (items: any[], sortByAsc: boolean) => any[];
    @ContentChild('datatableColumnCell', { static: true }) columnCellTemplate;
    @ContentChild('datatableColumnHeader', { static: true }) columnHeaderTemplate;
    classList: string;

    ngOnInit() {
        if (this.property) {
            if (/^[a-zA-Z0-9_]+$/.test(this.property)) {
                this.classList = 'column-' + this.property;
            } else {
                this.classList = 'column-' + this.property.replace(/[^a-zA-Z0-9_]/g, '');
            }
        }

        this.classList = [this.class, this.classList].join(' ');
    }
}
