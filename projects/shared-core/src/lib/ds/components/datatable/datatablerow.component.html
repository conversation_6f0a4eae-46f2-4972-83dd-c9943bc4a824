<div class="flex">
  <button *ngIf="action || (isExpandable && !defaultExpanded)" class="datatable__row-click-trigger" (click)="onRowClick($event)"></button>
  <div *ngIf="showIndex" class="datatable__cell datatable__cell--index" [class.datatable-indexcell--outside]="indexOutsideTable">
    <span [textContent]="displayIndex"></span>
  </div>
  <div *ngIf="isSelectable" class="datatable__cell datatable__cell--select">
    <ds-checkbox class="datatable__row-select-checkbox" [checked]="isSelected" (checkboxChange)="onRowSelectChange($event)"></ds-checkbox>
  </div>
  <div
    *ngFor="let column of columns; let idx = index; trackBy: trackByFn; let first = first; let last = last"
    class="datatable__cell datatable__cell--data"
    [class.datatable__cell--first]="first"
    [class.datatable__cell--last]="last"
    [class.datatable__cell--sorted-by]="column.sortable ? (column.property === displayParams.sortBy ? true : false) : false"
    [ngClass]="column.classList"
    [style.flexDirection]="column.flexDirection"
    [style.alignItems]="column.alignItems"
    [style.justifyContent]="column.justifyContent"
    [style.overflow]="column.visibleOverflow ? 'visible' : null"
    [style.minWidth]="column.minWidth"
    [style.maxWidth]="column.maxWidth"
  >
    <div *ngIf="!column.columnCellTemplate" [textContent]="item[column.property]"></div>
    <ng-template
      *ngIf="column.columnCellTemplate"
      [ngTemplateOutlet]="column.columnCellTemplate"
      [ngTemplateOutletContext]="{ column: column, item: item }"
    ></ng-template>
  </div>
</div>
<div *ngIf="isExpandable" class="datatable__expanded-content-wrapper">
  <div *ngIf="rowExpanded()" class="datatable__expanded-content">
    <ng-template [ngTemplateOutlet]="displayParams.expandTemplate" [ngTemplateOutletContext]="{ item: item }"></ng-template>
  </div>
</div>
