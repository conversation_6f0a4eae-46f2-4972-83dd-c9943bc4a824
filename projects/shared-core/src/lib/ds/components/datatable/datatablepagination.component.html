<div class="pagination" [class.pagination--disabled]="displayParams.lastPage === 1">
  <ds-button
    class="pagination__button pagination__button--previous mr-xs"
    [isDisabled]="displayParams.page === 1"
    variation="link"
    size="small"
    (buttonClick)="pageBack()"
    >&lt;</ds-button
  >
  <div class="pagination__status">
    <span class="current mr-xs" [textContent]="displayParams.page"></span>
    <span>/</span>
    <span class="total ml-xs" [textContent]="displayParams.lastPage === 0 ? 1 : displayParams.lastPage"></span>
  </div>
  <ds-button
    class="pagination__button pagination__button--next ml-xs"
    [isDisabled]="displayParams.lastPage < 1 || displayParams.page === displayParams.lastPage"
    variation="link"
    size="small"
    (buttonClick)="pageForward()"
    >&gt;</ds-button
  >
</div>
