import {
    Component,
    Input,
    Output,
    EventEmitter,
    ContentChildren,
    QueryList,
    TemplateRef,
    ContentChild,
    ViewChildren,
    OnInit,
    ViewEncapsulation,
    SimpleChanges,
    ChangeDetectionStrategy,
    OnChanges,
    HostBinding,
} from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { DataTableColumnDirective } from './datatablecolumn.directive';
import { DataTableRowComponent } from './datatablerow.component';
import get from 'lodash/get';

export interface DataTableParams {
    itemCount?: number;
    lastPage?: number;
    limit?: number;
    offset?: number;
    page?: number;
    sortAsc?: boolean;
    sortBy?: string;
    expandTemplate?: any;
    expandedRows?: number[];
}

export interface DatatableSelectedItem {
    item?: any;
}

@Component({
    selector: 'ds-datatable',
    templateUrl: './datatable.component.html',
    styleUrls: ['./datatable.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataTableComponent implements DataTableParams, OnInit, OnChanges {
    @HostBinding('class') get hostClasses() {
        return ['ds-datatable', 'datatable--' + this.columnCount + 'cols', this.class].join(' ');
    }
    @HostBinding('class.datatable--is-loading') @Input() isLoading: boolean;
    @HostBinding('class.datatable--is-disabled') @Input() isDisabled: boolean;
    @HostBinding('class.datatable--selectable-rows') @Input() selectableRows = false;
    @HostBinding('class.datatable--action-rows') @Input() actionRows = false;
    @HostBinding('class.datatable--multi-select') @Input() multiSelect = false;
    @ContentChild('datatableHeader') headerTemplate: TemplateRef<any>;
    @ContentChild('datatableExpand', { static: true }) expandTemplate: TemplateRef<any>;
    @ContentChild('datatableExtraRow') extraRowTemplate: TemplateRef<any>;
    @ViewChildren(DataTableRowComponent) rows: QueryList<DataTableRowComponent>;
    @ContentChildren(DataTableColumnDirective, { descendants: true }) columns: QueryList<DataTableColumnDirective> =
        new QueryList();
    @Input() class: string;
    @Input() itemCount: number;
    @Input() loadingMessage: string;
    @Input() showHeader: boolean;
    @Input() showLimitFilter: boolean;
    @Input() limitFilterLabel: string;
    @Input() showPagination = true;
    @Input() indexColumn = false;
    @Input() indexColumnHeader = '';
    @Input() indexColumnOutsideTable = false;
    @Input() noDataMessage: string;
    @Input() fixedRowHeight = true;
    @Input() trackItemByAttribute: string = null;
    @Input() selectedItems: DatatableSelectedItem[] = [];
    @Input() expandableRows = false;
    @Input() expandAll = false;
    @Input() expandSingleRow = false;
    @Output() reload = new EventEmitter();
    @Output() rowClick = new EventEmitter();
    @Output() selectedRowChange = new EventEmitter();
    _items: object[] = [];
    _sortBy: string;
    _sortAsc = true;
    _offset = 0;
    _limit = 10;
    _displayParams = {} as DataTableParams;
    _selectAllCheckbox = false;
    _expandedRows?: number[] = [];
    uid: string = Math.random().toString(36).substr(2, 9);
    indexColumnVisible: boolean;
    selectColumnVisible: boolean;
    datatableForm: UntypedFormGroup;

    @Input()
    get items() {
        return this._items;
    }
    set items(items: object[]) {
        if (!items || items === this._items) {
            return;
        }
        this._items = items;
        this.onReloadFinished();
    }

    @Input()
    get sortBy() {
        return this._sortBy;
    }
    set sortBy(value) {
        if (value !== this._sortBy) {
            this._sortBy = value;
            this._sortAsc = true;
            this.reloadItems();
        }
    }

    @Input()
    get sortAsc() {
        return this._sortAsc;
    }
    set sortAsc(value) {
        if (value !== this._sortAsc) {
            this._sortAsc = value;
            this.reloadItems();
        }
    }

    @Input()
    get offset() {
        return this._offset;
    }
    set offset(value) {
        if (value !== this._offset) {
            this._offset = value;
            this.reloadItems();
        }
    }

    @Input()
    get limit() {
        return this._limit;
    }
    set limit(value) {
        if (value !== this._limit) {
            this._limit = value;
            this.reloadItems();
        }
    }

    @Input()
    get page() {
        return Math.floor(this.offset / this.limit) + 1;
    }
    set page(value) {
        this.offset = (value - 1) * this.limit;
    }

    get lastPage() {
        return Math.ceil(this.itemCount / this.limit);
    }

    get displayParams() {
        return this._displayParams;
    }

    get columnCount() {
        let count = 0;

        count += this.indexColumnVisible && !this.indexColumnOutsideTable ? 1 : 0;
        count += this.selectColumnVisible ? 1 : 0;

        this.columns.toArray().forEach((column) => {
            count += column.visible ? 1 : 0;
        });

        return count;
    }

    get selectAllCheckbox() {
        return this._selectAllCheckbox;
    }

    set selectAllCheckbox(value) {
        this._selectAllCheckbox = value ? true : false;
        if (typeof value === 'boolean') {
            this.rows.toArray().forEach((row) => {
                if (row.selected !== value) {
                    row.selected = value;
                    this.onRowSelectedChange(row);
                }
            });
        }
    }

    constructor(private formBuilder: UntypedFormBuilder) {}

    ngOnInit() {
        this.initDefaultValues();
        this.formInit();
    }

    ngOnChanges(changes: SimpleChanges): void {
        this.updateDisplayParams();
    }

    formInit() {
        this.datatableForm = this.formBuilder.group({
            selectedRow: [''],
        });
    }

    initDefaultValues() {
        this.indexColumnVisible = this.indexColumn;
        this.selectColumnVisible = this.selectableRows;
    }

    isVisibleRow(index) {
        const from = this.offset;
        const to = from + this.limit;
        const item = this.items[index];

        return index >= from && index < to;
    }

    reloadItems() {
        if (!this.items.length) {
            return;
        }

        this.updateDisplayParams();
        this.sortItems();
        this.reload.emit(this._displayParams);
    }

    sortItems() {
        const theColumn = this.columns.find((column) => {
            return column.property === this.sortBy;
        });
        if (theColumn && theColumn.customSort) {
            this.items = theColumn.customSort(this.items, this.sortAsc);
            return;
        }

        const itemsCopy = [...this.items];

        if (this.items.length && this.sortBy) {
            itemsCopy.sort((a, b) => {
                const aComparable = get(a, this.sortBy);
                const bComparable = get(b, this.sortBy);

                if (typeof aComparable === 'string') {
                    return aComparable.localeCompare(bComparable);
                } else {
                    return aComparable - bComparable;
                }
            });

            this.items = this.sortAsc === false ? itemsCopy : itemsCopy.reverse();
        }
    }

    onHeaderClick(column: DataTableColumnDirective, event: Event) {
        event.preventDefault();
        event.stopPropagation();

        if (column.sortable) {
            this.sortBy = column.property;
            this.sortAsc = !this.sortAsc;
        }
    }

    onRowClick(row) {
        if (this.expandableRows) {
            if (this._expandedRows.indexOf(row.index) > -1) {
                this._expandedRows.splice(this._expandedRows.indexOf(row), 1);
            } else {
                if (this.expandSingleRow) {
                    this._expandedRows.splice(0, this._expandedRows.length);
                }
                this._expandedRows.push(row.index);
                this.updateDisplayParams();
            }
        } else {
            this.rowClick.emit(row.item);
            if (!this.multiSelect) {
                this.rows
                    .toArray()
                    .filter((_row) => {
                        return _row.selected;
                    })
                    .forEach((_row) => {
                        if (_row !== row) {
                            _row.selected = false;
                        }
                    });
            }
        }
    }

    onSelectAllChange(event) {
        this.selectAllCheckbox = event.checked;
    }

    onRowSelectedChange(row) {
        this.selectedRowChange.emit(row);
    }

    onPaginationChange({ offset }) {
        this.selectAllCheckbox = null;
        this.offset = offset;
    }

    trackColumnByFn(index) {
        return index;
    }

    trackItemByFn(index, item) {
        if (!item || !this.trackItemByAttribute || item[this.trackItemByAttribute] === undefined) {
            return item;
        }
        return item[this.trackItemByAttribute];
    }

    private onReloadFinished() {
        this._selectAllCheckbox = false;
    }

    private updateDisplayParams() {
        this._displayParams = {
            lastPage: this.lastPage,
            page: this.page,
            itemCount: this.itemCount,
            sortBy: this.sortBy,
            sortAsc: this.sortAsc,
            offset: this.offset,
            limit: this.limit,
            expandTemplate: this.expandTemplate,
            expandedRows: this._expandedRows,
        };
    }
}
