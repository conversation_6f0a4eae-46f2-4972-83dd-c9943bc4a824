import {
    Component,
    Input,
    Output,
    EventEmitter,
    ViewEncapsulation,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
} from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { DataTableParams } from './datatable.component';

@Component({
    selector: '[datatablerow]',
    templateUrl: './datatablerow.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataTableRowComponent {
    @Input() displayParams: DataTableParams;
    @Input() item: any;
    @Input() index: number;
    @Input() showIndex: boolean;
    @Input() isSelectable: boolean;
    @Input() multiSelect: boolean;
    @Input() showPagination: boolean;
    @Input() columns: any; // FIX
    @Input() action: boolean;
    @Input() parentUid: string;
    @Input() parentForm: UntypedFormGroup;
    @Input() isExpandable: boolean;
    @Input() defaultExpanded: boolean;
    @Input() indexOutsideTable: boolean;
    @Output() selectedChange = new EventEmitter<boolean>();
    @Output() rowClick = new EventEmitter<any>();
    isSelected: boolean;
    isExpanded: boolean;
    uid: string = Math.random().toString(36).substr(2, 9);

    constructor(private cd: ChangeDetectorRef) {}

    get selected() {
        return this.isSelected;
    }

    set selected(selected) {
        if (this.isSelected !== selected) {
            this.isSelected = selected;
            this.cd.markForCheck();
        }
    }
    get hideBottomBorder() {
        return this.item && this.item.hideBottomBorder ? this.item.hideBottomBorder : false;
    }

    onRowSelectChange(event) {
        if (this.selected !== event.checked) {
            this.selected = event.checked;
            this.selectedChange.emit(this.selected);
        }
    }

    get displayIndex() {
        if (this.showPagination) {
            return this.displayParams.offset + this.index + 1;
        } else {
            return this.index + 1;
        }
    }

    onRowClick(e: Event) {
        this.rowClick.emit(e);
    }

    rowExpanded(): boolean {
        return this.defaultExpanded || this.displayParams.expandedRows.indexOf(this.index) > -1;
    }

    trackByFn(index) {
        return index;
    }
}
