import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { DataTableParams } from './datatable.component';

@Component({
    selector: 'ds-datatablepagination',
    templateUrl: './datatablepagination.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataTablePaginationComponent {
    @Input() displayParams: DataTableParams;
    @Output() paginationChange = new EventEmitter<any>();

    constructor() {}

    pageBack() {
        if (this.displayParams.page === 1) {
            this.displayParams.offset = this.displayParams.limit * (this.displayParams.lastPage - 1);
        } else {
            this.displayParams.offset -= this.displayParams.limit;
        }
        this.updatePagination();
    }

    pageForward() {
        if (this.displayParams.page === this.displayParams.lastPage) {
            this.displayParams.offset = 0;
        } else {
            this.displayParams.offset += this.displayParams.limit;
        }
        this.updatePagination();
    }

    updatePagination() {
        this.paginationChange.emit(this.displayParams);
    }
}
