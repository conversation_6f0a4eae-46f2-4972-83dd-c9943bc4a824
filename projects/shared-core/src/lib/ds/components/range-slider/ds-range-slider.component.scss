:host {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;

  &[invert='true'] {
    .slider__wrapper {
      .top-and-bottom-values {
        color: var(--color-grey-darken-5);
      }
      .slider {
        ::ng-deep .ngx-slider {
          .ngx-slider-bar {
            background: linear-gradient(
              to top,
              var(--color-wellbeing-great),
              var(--color-wellbeing-ok) 27%,
              var(--color-severity-low) 51%,
              var(--color-severity-medium) 75%,
              var(--color-severity-high)
            );
          }
        }
      }
    }
  }
}

.slider__wrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: inherit;

  .slider__wrapper--label {
    width: 175px;
    font-size: 1.4rem;
    line-height: 1.5;
    padding: 0;
    text-align: center;
  }

  .top-and-bottom-values {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: 20px 25px 15px 0;
    font-weight: bold;
    color: var(--color-grey-darken-5);
  }

  .slider {
    position: relative;
    height: 400px;
    width: 60px;
    display: flex;

    ::ng-deep .ngx-slider {
      height: 100%;

      .ngx-slider-pointer {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: none !important;
        margin-left: -10px;

        cursor: pointer;
      }

      &.vertical .ngx-slider-bar-wrapper {
        margin: 0 0 0 -22px !important;
      }

      .ngx-slider-bar {
        width: 20px;
        border-radius: 10px;
        background: linear-gradient(
          to bottom,
          var(--color-wellbeing-great),
          var(--color-wellbeing-ok) 27%,
          var(--color-severity-low) 51%,
          var(--color-severity-medium) 75%,
          var(--color-severity-high)
        );
      }
    }
  }
}
