import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DsRangeSliderComponent, rangeSliderColors } from './ds-range-slider.component';
import { MocksModule } from '@shared-core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { I18NPipe } from '../../../pipes/i18n.pipe';
import { DsSliderPointerComponent } from './ds-slider-pointer/ds-slider-pointer.component';

describe('DsRangeSliderComponent', () => {
  let component: DsRangeSliderComponent;
  let fixture: ComponentFixture<DsRangeSliderComponent>;
  let i18nPipeMock: { transform: jest.Mock };

  beforeEach(async () => {
    i18nPipeMock = {
      transform: jest.fn(val => val)
    };

    await TestBed.configureTestingModule({
      imports: [MocksModule, DsSliderPointerComponent],
      declarations: [DsRangeSliderComponent],
      providers: [{ provide: I18NPipe, useValue: i18nPipeMock }],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DsRangeSliderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('mid value', () => {
    it('should display mid value when set to true', () => {
      component.min = 0;
      component.max = 10;
      component.mid = true;

      expect(component.mid).toBe(5);

      fixture.detectChanges();

      const midValueElement = fixture.nativeElement.querySelector('[data-testid="slider-mid-value"]');

      expect(midValueElement).toBeTruthy();
      expect(midValueElement.textContent.trim()).toBe('5');
    });

    it('should set mid value to the provided number', () => {
      component.min = 0;
      component.max = 10;
      component.mid = 3;

      expect(component.mid).toBe(3);

      fixture.detectChanges();

      const midValueElement = fixture.nativeElement.querySelector('[data-testid="slider-mid-value"]');

      expect(midValueElement).toBeTruthy();
      expect(midValueElement.textContent.trim()).toBe('3');
    });

    it('should not display mid value when set to null', () => {
      component.min = 0;
      component.max = 10;
      component.mid = null;

      fixture.detectChanges();

      const midValueElement = fixture.nativeElement.querySelector('[data-testid="slider-mid-value"]');

      expect(midValueElement).toBeFalsy();
    });

    it('should not display mid value when set to false', () => {
      component.min = 0;
      component.max = 10;
      component.mid = false;

      fixture.detectChanges();

      const midValueElement = fixture.nativeElement.querySelector('[data-testid="slider-mid-value"]');

      expect(midValueElement).toBeFalsy();
    });
  });

  describe('value setting', () => {
    it('should set value within bounds', () => {
      component.min = 0;
      component.max = 10;
      component.value = 5;

      expect(component.value).toBe(5);
      expect(component.sliderDisplayValue).toBe('5');
    });

    it('should handle 0 as a valid value', () => {
      component.min = 0;
      component.max = 10;
      component.value = 0;

      expect(component.value).toBe(0);
      expect(component.sliderDisplayValue).toBe('0');
    });

    it('should clamp value to min when below minimum', () => {
      component.min = 5;
      component.max = 10;
      component.setFixedValue(2);

      expect(component.value).toBe(5);
      expect(component.sliderDisplayValue).toBe('5');
    });

    it('should clamp value to max when above maximum', () => {
      component.min = 0;
      component.max = 10;
      component.setFixedValue(15);

      expect(component.value).toBe(10);
      expect(component.sliderDisplayValue).toBe('10');
    });

    it('should display ? if no value is set', () => {
      component.min = 0;
      component.max = 10;

      expect(component.value).toBe(5);
      expect(component.sliderDisplayValue).toBe('?');
    });

    it('should emit valueChange event when valid value is set', () => {
      jest.spyOn(component.valueChange, 'emit');
      component.min = 0;
      component.max = 10;
      component.value = 7;

      expect(component.valueChange.emit).toHaveBeenCalledWith(7);
    });

    it('should not emit valueChange event when invalid value is set', () => {
      jest.spyOn(component.valueChange, 'emit');
      component.min = 0;
      component.max = 10;
      component.value = null;

      expect(component.valueChange.emit).not.toHaveBeenCalled();
    });
  });

  describe('color generation', () => {
    it('should return correct color for minimum value', () => {
      component.min = 0;
      component.max = 10;
      component.invert = false;

      const color = component.getColorForValue(0);
      const expectedColor = `rgb(${rangeSliderColors[0].r}, ${rangeSliderColors[0].g}, ${rangeSliderColors[0].b})`;

      expect(color).toBe(expectedColor);
    });

    it('should return correct color for maximum value', () => {
      component.min = 0;
      component.max = 10;
      component.invert = false;

      const color = component.getColorForValue(10);
      const expectedColor = `rgb(${rangeSliderColors[10].r}, ${rangeSliderColors[10].g}, ${rangeSliderColors[10].b})`;

      expect(color).toBe(expectedColor);
    });

    it('should invert colors when invert is true', () => {
      component.min = 0;
      component.max = 10;
      component.invert = true;

      const colorMin = component.getColorForValue(0);
      const expectedColorMin = `rgb(${rangeSliderColors[10].r}, ${rangeSliderColors[10].g}, ${rangeSliderColors[10].b})`;

      const colorMax = component.getColorForValue(10);
      const expectedColorMax = `rgb(${rangeSliderColors[0].r}, ${rangeSliderColors[0].g}, ${rangeSliderColors[0].b})`;

      expect(colorMin).toBe(expectedColorMin);
      expect(colorMax).toBe(expectedColorMax);
    });

    it('should interpolate colors for intermediate values', () => {
      component.min = 0;
      component.max = 10;
      component.invert = false;

      const color = component.getColorForValue(5.5);

      const lowerColor = rangeSliderColors[5];
      const upperColor = rangeSliderColors[6];
      const ratio = 0.5;

      const expectedR = Math.round(lowerColor.r + (upperColor.r - lowerColor.r) * ratio);
      const expectedG = Math.round(lowerColor.g + (upperColor.g - lowerColor.g) * ratio);
      const expectedB = Math.round(lowerColor.b + (upperColor.b - lowerColor.b) * ratio);

      expect(color).toBe(`rgb(${expectedR}, ${expectedG}, ${expectedB})`);
    });
  });

  describe('ngOnChanges', () => {
    it('should update options when min or max changes', () => {
      component.min = 2;
      component.max = 8;

      component.ngOnChanges({
        min: { previousValue: 0, currentValue: 2, firstChange: false, isFirstChange: () => false },
        max: { previousValue: 10, currentValue: 8, firstChange: false, isFirstChange: () => false }
      });

      expect(component.options.floor).toBe(2);
      expect(component.options.ceil).toBe(8);
    });

    it('should update mid value when min or max changes and mid is set', () => {
      component.min = 2;
      component.max = 8;
      component.mid = true;

      component.ngOnChanges({
        min: { previousValue: 0, currentValue: 2, firstChange: false, isFirstChange: () => false },
        max: { previousValue: 10, currentValue: 8, firstChange: false, isFirstChange: () => false }
      });

      expect(component.mid).toBe(5);
    });

    it('should clamp value within new bounds when min or max changes', () => {
      component.min = 0;
      component.max = 10;
      component.value = 3;

      component.ngOnChanges({
        min: { previousValue: 0, currentValue: 5, firstChange: false, isFirstChange: () => false }
      });

      expect(component.value).toBe(5);
      expect(component.sliderDisplayValue).toBe('5');
    });

    it('should keep display value as "?" when min/max changes with invalid value', () => {
      component.min = 0;
      component.max = 10;

      expect(component.sliderDisplayValue).toBe('?');
      expect(component['isInvalidlidValue']).toBe(true);

      component.ngOnChanges({
        min: { previousValue: 0, currentValue: 5, firstChange: false, isFirstChange: () => false },
        max: { previousValue: 10, currentValue: 15, firstChange: false, isFirstChange: () => false }
      });

      expect(component.sliderDisplayValue).toBe('?');
      expect(component['isInvalidlidValue']).toBe(true);

      expect(component.value).toBe(10);
    });
  });

  describe('aria accessibility', () => {
    it('should set correct aria-label on slider element', () => {
      const sliderDiv = document.createElement('div');
      const ngxSlider = document.createElement('div');
      ngxSlider.className = 'ngx-slider';
      sliderDiv.appendChild(ngxSlider);

      const setAttributeSpy = jest.spyOn(ngxSlider, 'setAttribute');

      Object.defineProperty(component, 'sliderElement', {
        get: jest.fn().mockReturnValue({
          nativeElement: sliderDiv
        })
      });

      jest.spyOn(component, 'attachCustomPointer').mockImplementation(() => {});

      component.labelMin = 'Low';
      component.labelMax = 'High';
      component.min = 0;
      component.max = 10;

      component.ngAfterViewInit();

      expect(i18nPipeMock.transform).toHaveBeenCalledWith('general.input.slider.a11y.label', {
        min: 0,
        max: 10,
        labelMin: 'Low',
        labelMax: 'High'
      });

      expect(setAttributeSpy).toHaveBeenCalledWith('aria-label', 'general.input.slider.a11y.label');
    });

    it('should return correct aria value text with indicator label', () => {
      component.sliderDisplayValue = '5';
      component.indicatorLabel = 'Pain Level';

      expect(component.getAriaValueText()).toBe('5: Pain Level');
    });

    it('should return display value without indicator label if not provided', () => {
      component.sliderDisplayValue = '5';
      component.indicatorLabel = '';

      expect(component.getAriaValueText()).toBe('5');
    });

    it('should set aria-hidden and tabindex on .ngx-slider-pointer-max element', () => {
      const sliderDiv = document.createElement('div');
      const ngxSlider = document.createElement('div');
      ngxSlider.className = 'ngx-slider';
      sliderDiv.appendChild(ngxSlider);

      const maxPointer = document.createElement('div');
      maxPointer.className = 'ngx-slider-pointer-max';
      sliderDiv.appendChild(maxPointer);

      const setAttributeSpy = jest.spyOn(maxPointer, 'setAttribute');

      Object.defineProperty(component, 'sliderElement', {
        get: jest.fn().mockReturnValue({
          nativeElement: sliderDiv
        })
      });

      jest.spyOn(component, 'attachCustomPointer').mockImplementation(() => {});

      component.labelMin = 'Low';
      component.labelMax = 'High';
      component.min = 0;
      component.max = 10;

      component.ngAfterViewInit();

      expect(setAttributeSpy).toHaveBeenCalledWith('aria-hidden', 'true');
      expect(setAttributeSpy).toHaveBeenCalledWith('tabindex', '-1');
    });
  });

  describe('custom pointer', () => {
    it('should create pointer component when attachCustomPointer is called', () => {
      const mockComponentRef = {
        instance: {
          displayValue: '',
          backgroundColor: '',
          indicatorLabel: ''
        },
        location: {
          nativeElement: document.createElement('div')
        },
        changeDetectorRef: {
          detectChanges: jest.fn()
        },
        destroy: jest.fn()
      };

      Object.defineProperty(component, 'viewContainerRef', {
        get: jest.fn().mockReturnValue({
          createComponent: jest.fn().mockReturnValue(mockComponentRef)
        })
      });

      Object.defineProperty(component, 'sliderElement', {
        get: jest.fn().mockReturnValue({
          nativeElement: {
            querySelector: () => {
              const div = document.createElement('div');
              div.appendChild = jest.fn();
              return div;
            }
          }
        })
      });

      component.attachCustomPointer();

      expect(component['viewContainerRef'].createComponent).toHaveBeenCalled();
    });
    it('should update pointer values when updatePointerValue is called', () => {
      const mockInstance = {
        displayValue: '',
        backgroundColor: '',
        indicatorLabel: ''
      };

      const mockChangeDetectorRef = {
        detectChanges: jest.fn()
      };

      component['pointerComponentRef'] = {
        instance: mockInstance,
        changeDetectorRef: mockChangeDetectorRef
      };

      component.sliderDisplayValue = '7';
      component.indicatorLabel = 'Test Label';
      component.value = 7;
      const bgColor = component.getColorForValue(7);

      component.updatePointerValue();

      expect(mockInstance.displayValue).toBe('7');
      expect(mockInstance.indicatorLabel).toBe('Test Label');
      expect(mockInstance.backgroundColor).toBe(bgColor);
      expect(mockChangeDetectorRef.detectChanges).toHaveBeenCalled();
    });
  });

  describe('cleanup', () => {
    it('should destroy pointerComponentRef on ngOnDestroy if it exists', () => {
      const mockDestroy = jest.fn();
      component['pointerComponentRef'] = {
        destroy: mockDestroy
      };

      component.ngOnDestroy();

      expect(mockDestroy).toHaveBeenCalled();
    });

    it('should not try to destroy pointerComponentRef if it does not exist', () => {
      component['pointerComponentRef'] = null;

      component.ngOnDestroy();

      expect(component).toBeTruthy();
    });
  });
});
