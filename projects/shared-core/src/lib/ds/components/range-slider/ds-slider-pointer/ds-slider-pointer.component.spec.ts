import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DsSliderPointerComponent } from './ds-slider-pointer.component';
import { CommonModule } from '@angular/common';
import { By } from '@angular/platform-browser';

describe('DsSliderPointerComponent', () => {
  let component: DsSliderPointerComponent;
  let fixture: ComponentFixture<DsSliderPointerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, DsSliderPointerComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(DsSliderPointerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should render slider-pointer div with correct data-testid', () => {
    const sliderPointer = fixture.debugElement.query(By.css('[data-testid="slider-pointer"]'));

    expect(sliderPointer).toBeTruthy();
    expect(sliderPointer.nativeElement.classList.contains('custom-slider-pointer')).toBe(true);
  });

  it('should render slider-line div with correct data-testid', () => {
    const sliderLine = fixture.debugElement.query(By.css('[data-testid="slider-line"]'));

    expect(sliderLine).toBeTruthy();
    expect(sliderLine.nativeElement.classList.contains('slider-line')).toBe(true);
  });

  it('should render value-indicator with default displayValue, backgroundColor, and textColor', () => {
    const valueIndicator = fixture.debugElement.query(By.css('[data-testid="value-indicator"]'));

    expect(valueIndicator).toBeTruthy();
    expect(valueIndicator.nativeElement.textContent.trim()).toBe('?');
    expect(valueIndicator.nativeElement.style.backgroundColor).toBe('rgb(239, 239, 239)'); // #EFEFEF
  });

  it('should update value-indicator when displayValue is set', () => {
    component.displayValue = '50';
    fixture.detectChanges();
    const valueIndicator = fixture.debugElement.query(By.css('[data-testid="value-indicator"]'));

    expect(valueIndicator.nativeElement.textContent.trim()).toBe('50');
  });

  it('should update value-indicator styles when backgroundColor is set', () => {
    component.backgroundColor = '#FF0000';
    fixture.detectChanges();
    const valueIndicator = fixture.debugElement.query(By.css('[data-testid="value-indicator"]'));

    expect(valueIndicator.nativeElement.style.backgroundColor).toBe('rgb(255, 0, 0)');
  });

  it('should not render indicator-label when indicatorLabel is empty', () => {
    component.indicatorLabel = '';
    fixture.detectChanges();
    const indicatorLabel = fixture.debugElement.query(By.css('[data-testid="indicator-label"]'));

    expect(indicatorLabel).toBeNull();
  });

  it('should render indicator-label when indicatorLabel is set', () => {
    component.indicatorLabel = 'Min Value';
    fixture.detectChanges();
    const indicatorLabel = fixture.debugElement.query(By.css('[data-testid="indicator-label"]'));

    expect(indicatorLabel).toBeTruthy();
    expect(indicatorLabel.nativeElement.textContent.trim()).toBe('Min Value');
  });

  it('should apply correct classes to indicator-label when rendered', () => {
    component.indicatorLabel = 'Max Value';
    fixture.detectChanges();
    const indicatorLabel = fixture.debugElement.query(By.css('[data-testid="indicator-label"]'));

    expect(indicatorLabel.nativeElement.classList.contains('indicator-label')).toBe(true);
  });
});
