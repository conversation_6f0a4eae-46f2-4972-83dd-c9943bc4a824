import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'ds-slider-pointer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './ds-slider-pointer.component.html',
  styleUrl: './ds-slider-pointer.component.scss'
})
export class DsSliderPointerComponent {
  @Input() indicatorLabel = '';
  @Input() backgroundColor: string = '#EFEFEF';
  @Input() displayValue: string = '?';
}
