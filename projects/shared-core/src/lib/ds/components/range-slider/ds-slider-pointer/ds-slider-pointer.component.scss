$value-indicator-position: 150px;

.custom-slider-pointer {
  cursor: pointer;
  pointer-events: none;
  z-index: 99;
  display: block;
  position: absolute;

  width: 56px;
  height: 56px;
  border-radius: 50%;

  background: #fff
    url(data:image/svg+xml;base64,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)
    no-repeat;
  background-size: cover;
  box-shadow: 0 0 8px rgba(60, 60, 60, 0.2);
}

.slider-line {
  position: absolute;
  border-top: 1px dashed #d4d4d4;
  width: calc($value-indicator-position * 1.3);
  top: 50%;
}

.value-indicator {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-radius: 50%;
  font-weight: var(--font-weight-semibold, 600);
  font-size: var(--range-slider-value-font-size, 19px);
  color: var(--color-grey-darken-5);
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
  width: var(--range-slider-value-indicator-size, 56px);
  height: var(--range-slider-value-indicator-size, 56px);
  order: 2;
  position: absolute;
  top: 50%;
  left: $value-indicator-position;
  margin-top: calc((var(--range-slider-value-indicator-size, 56px) / 2) * -1);
}

.indicator-label {
  position: absolute;
  width: 150px;
  font-size: 1.3rem;
  left: calc((var(--range-slider-value-indicator-left, 190px)) - var(--range-slider-value-indicator-text-width, 120px) / 1.75);
  top: -40px;
  height: var(--range-slider-2lines-height, 46px);
  text-transform: uppercase;
  align-items: center;
}
