import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  Injector,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  SimpleChanges,
  ViewChild,
  ViewContainerRef
} from '@angular/core';
import { DsSliderPointerComponent } from './ds-slider-pointer/ds-slider-pointer.component';
import { I18NPipe } from '../../../pipes/i18n.pipe';

export const rangeSliderColors = [
  { r: 250, g: 112, b: 120 }, // 0: Reddish
  { r: 248, g: 134, b: 102 }, // 1
  { r: 245, g: 157, b: 83 }, // 2
  { r: 244, g: 178, b: 74 }, // 3
  { r: 244, g: 197, b: 74 }, // 4
  { r: 244, g: 217, b: 74 }, // 5: Yellowish
  { r: 202, g: 207, b: 89 }, // 6
  { r: 161, g: 196, b: 104 }, // 7
  { r: 131, g: 196, b: 112 }, // 8
  { r: 113, g: 207, b: 111 }, // 9
  { r: 95, g: 218, b: 110 } // 10: Greenish
];

@Component({
  selector: 'ds-range-slider',
  templateUrl: './ds-range-slider.component.html',
  styleUrl: './ds-range-slider.component.scss'
})
export class DsRangeSliderComponent implements OnChanges, OnDestroy, AfterViewInit {
  @Input() labelMax = '';
  @Input() labelMin = '';
  @Input() indicatorLabel = '';
  @Input() min = 0;
  @Input() max = 10;
  @Input() invert = false;
  @Output() valueChange = new EventEmitter<number>();

  @HostBinding('attr.invert') get invertAttribute() {
    return this.invert;
  }
  private _value: number;
  sliderDisplayValue: string = '?';
  private isInvalidlidValue = true;
  private midValue: number | null = null;

  @Input()
  get mid(): number | null {
    return this.midValue;
  }
  set mid(val: number | null | boolean) {
    if (!val) {
      this.midValue = null;
      return;
    }

    const middleValue = (this.min + this.max) / 2;
    this.midValue = val === true ? middleValue : val;
  }

  @Input()
  get value(): number {
    return this._value;
  }
  set value(val: number | null | undefined) {
    const isValidNumber = val !== null && val !== undefined && !isNaN(val);
    this.sliderDisplayValue = isValidNumber ? val.toString() : '?';
    this.isInvalidlidValue = !isValidNumber;

    const newValue = isValidNumber ? val : (this.min + this.max) / 2;
    if (newValue !== this._value) {
      this._value = Math.max(this.min, Math.min(this.max, newValue));
      if (isValidNumber) {
        this.valueChange.emit(this._value);
      }
      this.updatePointerValue();
    }
  }

  @ViewChild('sliderElement') sliderElement: ElementRef;

  options = {
    floor: 0,
    ceil: 10,
    vertical: true,
    showTicks: false,
    showTicksValues: false,
    hideLimitLabels: true,
    hidePointerLabels: true,
    ariaLabel: undefined,
    ariaLabelledBy: 'slider-value-indicator',
    translate: (value: number): string => {
      return this.isInvalidlidValue ? '?' : value.toString();
    }
  };

  private pointerComponentRef: any;

  constructor(
    private readonly injector: Injector,
    private readonly i18nPipe: I18NPipe,
    private readonly viewContainerRef: ViewContainerRef
  ) {
    const middleValue = (this.min + this.max) / 2;
    this._value = middleValue;
    if (this.midValue) {
      this.mid = middleValue;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['min'] || changes['max']) {
      const newMin = changes['min'] ? changes['min'].currentValue : this.min;
      const newMax = changes['max'] ? changes['max'].currentValue : this.max;
      const middleValue = (newMin + newMax) / 2;

      this.options = {
        ...this.options,
        floor: newMin,
        ceil: newMax
      };

      if (this.midValue) {
        this.mid = middleValue;
      }

      if (!this.isInvalidlidValue) {
        this.value = Math.max(newMin, Math.min(newMax, this._value));
      } else {
        this._value = middleValue;
      }
      this.updatePointerValue();
    }
  }

  ngAfterViewInit(): void {
    if (!this.pointerComponentRef) {
      this.attachCustomPointer();
    }

    this.setSliderAriaLabel();
  }

  ngOnDestroy(): void {
    if (this.pointerComponentRef && typeof this.pointerComponentRef.destroy === 'function') {
      this.pointerComponentRef.destroy();
    }
  }

  getAriaValueText(): string {
    if (this.indicatorLabel) {
      return `${this.sliderDisplayValue}: ${this.indicatorLabel}`;
    }
    return this.sliderDisplayValue;
  }

  attachCustomPointer(): void {
    if (!this.sliderElement) {
      return;
    }
    const pointerElement = this.sliderElement.nativeElement.querySelector('.ngx-slider-span.ngx-slider-pointer');

    if (!pointerElement) {
      return;
    }

    this.pointerComponentRef = this.viewContainerRef.createComponent(DsSliderPointerComponent, {
      injector: this.injector
    });

    pointerElement.appendChild(this.pointerComponentRef.location.nativeElement);
    this.updatePointerValue();
  }

  updatePointerValue(): void {
    if (this.pointerComponentRef) {
      this.pointerComponentRef.instance.displayValue = this.sliderDisplayValue;
      this.pointerComponentRef.instance.backgroundColor = this.isInvalidlidValue ? '#EFEFEF' : this.getColorForValue(this.value);
      this.pointerComponentRef.instance.indicatorLabel = this.indicatorLabel;
      this.pointerComponentRef.changeDetectorRef.detectChanges();
    }
  }

  onSliderValueChange(newValue: number): void {
    this.setFixedValue(newValue);
  }

  setFixedValue(value: number) {
    let newValue: number;
    const minNumber = Number(this.min);
    const maxNumber = Number(this.max);

    if (value <= minNumber) {
      newValue = minNumber;
    } else if (value >= maxNumber) {
      newValue = maxNumber;
    } else {
      newValue = value;
    }

    this.value = newValue;
  }

  getColorForValue(value: number): string {
    const normalizedValue = ((value - this.min) / (this.max - this.min)) * 10;
    const effectiveValue = this.invert ? 10 - normalizedValue : normalizedValue;

    const lowerIndex = Math.floor(effectiveValue);
    const upperIndex = Math.min(lowerIndex + 1, 10);

    if (lowerIndex === effectiveValue) {
      const color = rangeSliderColors[lowerIndex];
      return `rgb(${color.r}, ${color.g}, ${color.b})`;
    }

    const ratio = effectiveValue - lowerIndex;
    const lowerColor = rangeSliderColors[lowerIndex];
    const upperColor = rangeSliderColors[upperIndex];
    const color = {
      r: Math.round(lowerColor.r + (upperColor.r - lowerColor.r) * ratio),
      g: Math.round(lowerColor.g + (upperColor.g - lowerColor.g) * ratio),
      b: Math.round(lowerColor.b + (upperColor.b - lowerColor.b) * ratio)
    };

    return `rgb(${color.r}, ${color.g}, ${color.b})`;
  }

  /**
   * ngx-slider has a bug with setting top level aria-label when ariaLabelledBy is set, this forces the correct label
   */
  private setSliderAriaLabel(): void {
    const ngxSliderElement = this.sliderElement?.nativeElement?.querySelector('.ngx-slider');
    if (ngxSliderElement) {
      const label = this.i18nPipe.transform('general.input.slider.a11y.label', {
        min: this.min,
        max: this.max,
        labelMin: this.labelMin,
        labelMax: this.labelMax
      });
      ngxSliderElement.setAttribute('aria-label', label);
    }

    const maxPointer = this.sliderElement?.nativeElement?.querySelector('.ngx-slider-pointer-max');
    if (maxPointer) {
      maxPointer.setAttribute('aria-hidden', 'true');
      maxPointer.setAttribute('tabindex', '-1');
    }
  }
}
