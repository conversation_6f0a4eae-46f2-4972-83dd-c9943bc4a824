<div class="slider__wrapper" data-testid="slider-wrapper">
  <span class="slider__wrapper--label label-max" id="slider-label-max" data-testid="slider-label-max" aria-hidden="true">{{
    labelMax
  }}</span>
  <div class="slider" #sliderElement data-testid="slider-element">
    <div class="top-and-bottom-values" aria-hidden="true" data-testid="slider-boundaries">
      <div class="max" data-testid="slider-max-value">{{ max }}</div>
      <div *ngIf="mid != null" class="mid" data-testid="slider-mid-value">{{ mid }}</div>
      <div class="min" data-testid="slider-min-value">{{ min }}</div>
    </div>
    <ngx-slider [(value)]="value" [options]="options" (valueChange)="onSliderValueChange($event)" data-testid="ngx-slider"> </ngx-slider>
  </div>
  <span class="slider__wrapper--label label-min" id="slider-label-min" data-testid="slider-label-min" aria-hidden="true">{{
    labelMin
  }}</span>
</div>
