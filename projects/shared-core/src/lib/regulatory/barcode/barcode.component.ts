import { Component, ElementRef, OnInit, ChangeDetectionStrategy, Input, ViewChild } from '@angular/core';
import { toCanvas } from '@bwip-js/browser';

const DEFAULT_BARCODE_TYPE = 'gs1datamatrix';

@Component({
  selector: 'ns-barcode',
  templateUrl: './barcode.component.html',
  styleUrls: ['./barcode.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BarcodeComponent implements OnInit {
  @Input() text: string;
  @Input() barcodeType: string;
  @ViewChild('canvas', { static: true }) canvasEl: ElementRef;

  ngOnInit() {
    // Text should we always exactly 39 characters in length for real versions
    // for example: (01)00855141006226(11)211014(10)0610000
    if (!this.text || this.text.length !== 39) {
      return;
    }

    toCanvas('udi', {
      bcid: this.barcodeType || DEFAULT_BARCODE_TYPE, // Barcode type
      text: this.text,
      scale: 3, // 3x scaling factor
      height: 10, // Bar height, in millimeters
      paddingwidth: 0,
      paddingheight: 0,
      includetext: false, // Show human-readable text
      textxalign: 'center'
    });

    // force square styling for IE
    this.canvasEl.nativeElement.style.width = this.canvasEl.nativeElement.getAttribute('width') + 'px';
    this.canvasEl.nativeElement.style.height = this.canvasEl.nativeElement.getAttribute('width') + 'px';
  }
}
