import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BarcodeComponent } from './barcode.component';
import bwipjs from '@bwip-js/browser';

jest.mock('@bwip-js/browser', () => ({
  toCanvas: jest.fn()
}));

describe('BarcodeComponent', () => {
  let component: BarcodeComponent;
  let fixture: ComponentFixture<BarcodeComponent>;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [BarcodeComponent]
    });

    fixture = TestBed.createComponent(BarcodeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('#ngOnInit', () => {
    beforeEach(() => {
      (bwipjs.toCanvas as any).mockClear();
    });

    it('should not use bwip-js when text is less than 39 characters', () => {
      component.text = '(01)00855141006226(11)211014(10)';
      component.ngOnInit();

      expect(bwipjs.toCanvas).not.toHaveBeenCalled();
    });

    it('should use bwip-js when text is exactly 39 characters', () => {
      component.text = '(01)00855141006226(11)211014(10)0610000';
      component.ngOnInit();

      expect(bwipjs.toCanvas).toHaveBeenCalled();
    });

    it('should not use bwip-js when text is more than 39 characters', () => {
      component.text = '(01)00855141006226(11)211014(10)XXXXXXXX';
      component.ngOnInit();

      expect(bwipjs.toCanvas).not.toHaveBeenCalled();
    });
  });
});
