import { Component, OnInit, ChangeDetectionStrategy, Input, ElementRef, ViewChild, HostListener } from '@angular/core';
import moment from 'moment';
import { VersionInformation } from './version-information.interface';
import { I18NPipe } from '../../pipes/i18n.pipe';

@Component({
  selector: 'ns-about',
  templateUrl: './about.component.html',
  styleUrls: ['./about.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AboutComponent implements OnInit {
  @Input() versionInformation: VersionInformation;
  cleanVersionNumber: number;
  cleanFullVersion: string;
  currentYear: string;
  udiText: string; // The UDI text from which to generate the qr code
  udiTextSections: Array<string>;
  // temporary tooltip variables, component not yet ready
  tooltipVisible = false;
  tooltipTop: number;
  tooltipLeft: number;
  @ViewChild('tooltipButton', { read: ElementRef, static: true }) tooltipButtonEl: ElementRef;
  @HostListener('window:resize', ['$event']) onResize(event) {
    this.tooltipVisible = false;
  }

  constructor(private readonly i18n: I18NPipe) {}

  ngOnInit() {
    const fullVersion = this.versionInformation.fullVersion;

    this.currentYear = moment().format('YYYY');
    this.udiText = this.generateUDI();
    const versionNumber = Number(this.versionInformation.versionNumber);
    this.cleanVersionNumber = isNaN(versionNumber) ? Number(this.versionInformation.versionNumber.replace(/\D/g, '')) : versionNumber;
    this.cleanFullVersion = fullVersion.substring(0, fullVersion.length - 2);
  }

  private generateUDI(): string {
    const productionDate = this.versionInformation.manufactureDate.replace(/-/g, '').slice(2); // YYYY -> YY
    const deviceIdentifier = `(01)${this.i18n.transform('patient.regulatory.deviceIdentifier')}`;
    const date = `(11)${productionDate}`;
    const version = `(10)${this.udiVersionNumber()}`;

    this.udiTextSections = [deviceIdentifier, date, version];

    return deviceIdentifier + date + version;
  }

  private udiVersionNumber(): string {
    if (!this.versionInformation?.fullVersion || this.versionInformation.fullVersion.indexOf('.') < 0) {
      return '';
    }

    const splitVersion = this.versionInformation.fullVersion.split('.');
    const major = splitVersion[0].padStart(2, '0');
    const minor = splitVersion[1].padEnd(2, '0');
    const maintenance = (splitVersion[2] ? splitVersion[2].slice(0, 2) : '').padEnd(3, '0');

    return `${major}${minor}${maintenance}`.padEnd(7, '0');
  }

  generateTooltipContent() {
    return this.i18n.transform('general.about.myVarianTooltip', {
      url: '<a href="http://www.myvarian.com" target="_blank">www.MyVarian.com</a>'
    });
  }

  showTooltip() {
    const btn = this.tooltipButtonEl.nativeElement;
    this.tooltipTop = btn.offsetTop;
    this.tooltipLeft = btn.clientWidth / 2 + this.tooltipButtonEl.nativeElement.offsetLeft;
    this.tooltipVisible = true;
  }

  hideTooltip(event?: Event) {
    if (event && this.tooltipVisible) {
      // Prevent closing the modal if tooltip is open
      event.stopPropagation();
    }
    this.tooltipVisible = false;
  }

  onTooltipFocusOut(event: FocusEvent) {
    const span = event.currentTarget as HTMLElement;
    if (!span.contains(event.relatedTarget as Node)) {
      this.hideTooltip();
    }
  }

  closeTooltip(event: Event) {
    event.stopPropagation();
    this.tooltipVisible = false;
  }

  toggleTooltip() {
    if (!this.tooltipVisible) {
      this.showTooltip();
    } else {
      this.hideTooltip();
    }
  }
}
