@import '../../../styles/ds-variables.scss';
.about__section-wrapper:not(:last-of-type) {
  margin-bottom: var(--spacing-xl);
}
.about__section-title {
  font-weight: var(--font-weight-semibold);
  padding-right: var(--spacing-xs);
  font-size: var(--font-size-m);
  @media screen and ($screen-from-s) {
    font-weight: inherit;
    padding-right: var(--spacing-m);
    width: 30%;
  }
}
.about__icons-wrapper {
  width: 100%;
  max-width: 42rem;
}
.about__udi-plain {
  word-break: break-all;
  text-align: center;

  span:first-of-type {
    display: block;
  }
}

.about__icon-tooltip-trigger {
  display: flex;
  align-items: center;
}

.tooltip-button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  outline: none;
  height: 64px;
}

.icon-representative {
  width: 60px;
  height: 22px;
}
.icon-myvarian {
  outline: none;
  display: block;
}
.ds-tooltip {
  transform: translate(-50%, calc(-100% - var(--spacing-s)));
}
:host-context(.ie) .ds-tooltip {
  transform: translate(-50%, -100%);
}

@media screen and ($screen-to-s) {
  .ds-tooltip {
    position: fixed;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%);
  }
  .ds-tooltip-content {
    text-align: center;
  }
  .ds-tooltip:before,
  .ds-tooltip:after {
    display: none;
  }
}
