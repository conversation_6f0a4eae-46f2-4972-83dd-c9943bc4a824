<article class="about flex flex--column">
  <h1 class="about-heading">{{ 'doNotReplace.about.aboutService' | i18n }} {{ cleanVersionNumber }}</h1>

  <section class="about__section-wrapper">
    <p id="version-info" class="flex mb-xs">
      <span class="about__section-title">{{ 'general.about.fullVersion' | i18n }}:</span>
      <span>{{ cleanFullVersion }}</span>
    </p>
    <p id="copyright-info" class="flex">
      <span class="about__section-title">{{ 'general.about.copyright' | i18n }}:</span>
      <span>&copy; 2018-{{ currentYear }} {{ 'general.varian.hq.companyName' | i18n }} {{ 'copyright.rights' | i18n }}</span>
    </p>
  </section>

  <section id="manufactured-info" class="about__section-wrapper" aria-labelledby="manufactured-title">
    <h2 id="manufactured-title" class="about__section-title">{{ 'general.about.manufactured' | i18n }}</h2>
    <ds-icon name="icon-manufactured" size="22" aria-hidden="true"></ds-icon>
    <p>{{ versionInformation.manufactureDate }}</p>
  </section>

  <section id="manufacturer-info" class="about__section-wrapper" aria-labelledby="manufacturer-title">
    <h2 id="manufacturer-title" class="about__section-title">{{ 'general.disclaimerInformation.manufacturer' | i18n }}</h2>
    <ds-icon name="icon-manufacturer" size="22" aria-hidden="true"></ds-icon>
    <p>{{ 'general.varian.hq.companyName' | i18n }}</p>
    <p>{{ 'general.varian.hq.street' | i18n }}</p>
    <p>
      {{ 'general.varian.hq.city' | i18n }}, {{ 'general.varian.hq.stateAbbreviation' | i18n }}
      {{ 'general.varian.hq.zip' | i18n }}
    </p>
    <p>{{ 'general.varian.hq.country' | i18n }}</p>
  </section>

  <section id="eu-representative" class="about__section-wrapper" aria-labelledby="eurep-title">
    <h2 id="eurep-title" class="about__section-title">{{ 'general.disclaimerInformation.euRep' | i18n }}</h2>
    <ds-icon class="icon-representative" name="icon-representative" [cssSize]="true" aria-hidden="true"></ds-icon>
    <p>{{ 'general.varian.eu.companyName' | i18n }}</p>
    <p>{{ 'general.varian.eu.street' | i18n }}</p>
    <p>{{ 'general.varian.eu.postCode' | i18n }} {{ 'general.varian.eu.city' | i18n }}</p>
    <p>{{ 'general.varian.eu.country' | i18n }}</p>
  </section>

  <section class="about__section-wrapper flex flex--column flex--align-items-center" [attr.aria-label]="'general.about.icons' | i18n">
    <div id="about-icons" class="about__icons-wrapper flex flex--justify-content-space-around flex--align-items-center">
      <ds-icon [ariaLabel]="'general.a11y.logos.varian' | i18n" name="icon-varian" size="102"></ds-icon>
      <ds-icon [ariaLabel]="'general.a11y.symbols.ce' | i18n" name="icon-ce" size="40"></ds-icon>
      <ds-icon [ariaLabel]="'general.a11y.symbols.md' | i18n" name="icon-md" size="46"></ds-icon>
      <span
        class="about__icon-tooltip-trigger"
        (mouseenter)="showTooltip()"
        (mouseleave)="hideTooltip()"
        (focusout)="onTooltipFocusOut($event)"
      >
        <button
          #tooltipButton
          class="tooltip-button"
          tabindex="0"
          [ariaLabel]="'general.a11y.symbols.myvarian' | i18n"
          [attr.aria-pressed]="tooltipVisible"
          [attr.aria-describedby]="tooltipVisible ? 'myvarian-tooltip' : null"
          (focus)="showTooltip()"
          (keydown.esc)="hideTooltip($event)"
          (click)="toggleTooltip()"
        >
          <ds-icon class="icon-myvarian" name="icon-myvarian" size="68" aria-hidden="true"></ds-icon>
        </button>

        <div
          id="myvarian-tooltip"
          class="ds-tooltip ds-tooltip--top"
          [class.ds-tooltip--show]="tooltipVisible"
          [style.top.px]="tooltipTop"
          [style.left.px]="tooltipLeft"
          role="tooltip"
          [attr.aria-hidden]="!tooltipVisible"
        >
          <div *ngIf="tooltipVisible" class="ds-tooltip__content" [innerHTML]="generateTooltipContent()"></div>
        </div>
      </span>
    </div>
    <div class="about__barcode flex flex--column flex--align-items-center">
      <ns-barcode aria-hidden="true" class="mb-s" [text]="udiText"></ns-barcode>
      <p class="about__udi-plain mb-xl">
        <span *ngFor="let section of udiTextSections">{{ section }}</span>
      </p>
    </div>
  </section>
</article>
