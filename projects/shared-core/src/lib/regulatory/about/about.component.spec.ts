import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AboutComponent } from './about.component';
import { CUSTOM_ELEMENTS_SCHEMA, ElementRef } from '@angular/core';
import { VersionInformation } from './version-information.interface';
import moment from 'moment';
import { MocksModule } from '@shared-core/testing';

describe('AboutComponent', () => {
  let component: AboutComponent;
  let fixture: ComponentFixture<AboutComponent>;

  const versionInfo: VersionInformation = {
    versionNumber: '2.3',
    fullVersion: '2.3.4-rc',
    manufactureDate: '2023-01-15'
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [MocksModule],
      declarations: [AboutComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(AboutComponent);
    component = fixture.componentInstance;

    component.tooltipButtonEl = {
      nativeElement: {
        offsetTop: 100,
        offsetLeft: 50,
        clientWidth: 200
      }
    } as ElementRef;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with proper version information', () => {
    component.versionInformation = versionInfo;
    component.ngOnInit();

    expect(component.cleanVersionNumber).toBe(2.3);
    expect(component.cleanFullVersion).toBe('2.3.4-');
    expect(component.currentYear).toBe(moment().format('YYYY'));
  });

  it('should generate UDI text during initialization', () => {
    component.versionInformation = versionInfo;
    component.ngOnInit();

    expect(component.udiText).toContain('(01)patient.regulatory.deviceIdentifier(11)230115(10)02304-0');
    expect(component.udiText).toContain('(11)230115');
    expect(component.udiTextSections).toHaveLength(3);
  });

  it('should format version number correctly for UDI', () => {
    component.versionInformation = {
      versionNumber: '2.3',
      fullVersion: '2.3.4-rc',
      manufactureDate: '2023-01-15'
    };

    const result = (component as any).udiVersionNumber();

    expect(result).toBe('02304-0');
  });

  it('should handle single digit version parts correctly', () => {
    component.versionInformation = {
      versionNumber: '1.2.3',
      fullVersion: '1.2.3-rc',
      manufactureDate: '2023-01-15'
    };

    const result = (component as any).udiVersionNumber();

    expect(result).toBe('01203-0');
  });

  it('should return empty string if version is missing', () => {
    component.versionInformation = {
      versionNumber: '1.0.0',
      fullVersion: null,
      manufactureDate: '2023-01-15'
    };

    const result = (component as any).udiVersionNumber();

    expect(result).toBe('');
  });

  it('should return empty string if version format is invalid', () => {
    component.versionInformation = {
      versionNumber: '1.0.0',
      fullVersion: 'invalid',
      manufactureDate: '2023-01-15'
    };

    const result = (component as any).udiVersionNumber();

    expect(result).toBe('');
  });

  it('should toggle tooltip visibility', () => {
    expect(component.tooltipVisible).toBeFalsy();

    component.toggleTooltip();

    expect(component.tooltipVisible).toBeTruthy();

    component.toggleTooltip();

    expect(component.tooltipVisible).toBeFalsy();
  });

  it('should set correct tooltip position when shown', () => {
    component.showTooltip();

    expect(component.tooltipTop).toBe(100);
    expect(component.tooltipLeft).toBe(150);
    expect(component.tooltipVisible).toBeTruthy();
  });

  it('should hide tooltip on window resize', () => {
    component.tooltipVisible = true;
    component.onResize(null);

    expect(component.tooltipVisible).toBeFalsy();
  });

  it('should generate tooltip content with link', () => {
    const content = component.generateTooltipContent();

    expect(content).toContain('general.about.myVarianTooltip');
  });

  it('should stop event propagation when closing tooltip', () => {
    const eventMock = {
      stopPropagation: jest.fn()
    };

    component.closeTooltip(eventMock as any);

    expect(eventMock.stopPropagation).toHaveBeenCalled();
    expect(component.tooltipVisible).toBeFalsy();
  });

  it('should prevent modal closing when hiding tooltip with event', () => {
    component.tooltipVisible = true;
    const eventMock = {
      stopPropagation: jest.fn()
    };

    component.hideTooltip(eventMock as any);

    expect(eventMock.stopPropagation).toHaveBeenCalled();
    expect(component.tooltipVisible).toBeFalsy();
  });

  it('should properly format manufacture date in UDI', () => {
    component.versionInformation = {
      versionNumber: '1.0.0',
      fullVersion: '1.0.0',
      manufactureDate: '2023-05-20'
    };

    const result = (component as any).generateUDI();

    expect(result).toContain('(11)230520');
  });

  it('should correctly combine all UDI parts', () => {
    component.versionInformation = {
      versionNumber: '2.0.1',
      fullVersion: '2.0.1',
      manufactureDate: '2022-12-31'
    };

    const result = (component as any).generateUDI();

    expect(result).toMatch(/^\(01\)patient.regulatory.deviceIdentifier\(11\)221231\(10\).+$/);
    expect(component.udiTextSections).toHaveLength(3);
    expect(component.udiTextSections[0]).toBe('(01)patient.regulatory.deviceIdentifier');
    expect(component.udiTextSections[1]).toBe('(11)221231');
  });
});
