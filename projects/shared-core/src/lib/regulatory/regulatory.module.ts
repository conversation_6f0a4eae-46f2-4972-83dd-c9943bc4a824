import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AboutComponent } from './about/about.component';
import { BarcodeComponent } from './barcode/barcode.component';
import { ScPipesModule } from '../pipes/sc-pipes.module';
import { DsIconModule } from '../ds/components/icon/icon.module';

@NgModule({
    imports: [CommonModule, ScPipesModule, DsIconModule],
    declarations: [AboutComponent, BarcodeComponent],
    exports: [AboutComponent, BarcodeComponent],
})
export class RegulatoryModule {}
