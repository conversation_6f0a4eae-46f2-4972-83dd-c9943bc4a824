import { ExtraField, FormFieldConfig } from '../form-engine/models/form-field-config.interface';
import { FieldStatus, FieldStatusType } from '../form-engine/models/field-status.interface';
import { FormItemType } from '../generated/models/form-item-type';
import { SelectWithRadioComponent } from '../form-engine/components/select-with-radio/select-with-radio.component';
import { CheckboxListWithExtraFieldsComponent } from '../form-engine/components/checkbox-list-with-extra-fields/checkbox-list-with-extra-fields.component';
import { YesNoDateComponent } from '../form-engine/components/yes-no-date/yes-no-date.component';
import { INVALID_DATE } from '../form-engine/models/invalid-date';
import { MeasurementComponent } from '../form-engine/components/measurement/measurement.component';
import { FormInputField } from '../form-engine/components/form-input-field.component';
import { MultiselectComponent } from '../form-engine/components/multiselect/multiselect.component';
import {
    FormSubarrayField,
    getArraySubFieldValue,
    getDestructuredSubArrayFields,
    isSubArrayField,
} from './sub-array-field.utils';

interface FormAnswer {
    [fieldKey: string]: any;
}

const TEXT_FIELD_POSTFIX = 'Text';
const DATE_FIELD_POSTFIX = 'Date';
const RADIO_FIELD_POSTFIX = 'Radio';
const DROPDOWN_FIELD_POSTFIX = 'Dropdown';
const EXTRA_FIELD_NUMERIC_FIELD_KEY = 'NUMERIC_FIELD';
const EXTRA_FIELD_DATE_TYPE = 'DATE';
const EXTRA_FIELD_TEXT_TYPE = 'TEXT';
const EXTRA_FIELD_DROPDOWN_TYPE = 'DROPDOWN';
const EXTRA_FIELDS_NUMERIC_FIELD_KEY_POSTFIX = 'Value';

export function checkFieldValidity(fieldConfig: FormFieldConfig, crfValues: FormAnswer): FieldStatus[] {
    let fieldStatuses = [];
    switch (fieldConfig.type) {
        case FormItemType.YES_NO_DATE: {
            fieldStatuses = validateYesNoDateComponent(fieldConfig, crfValues);
            break;
        }
        case FormItemType.DATE:
        case FormItemType.RADIO:
        case FormItemType.NUMERIC_FIELD:
        case FormItemType.TEXT_INPUT_FIELD: {
            return validateSingleValueComponent(fieldConfig, crfValues);
        }
        case FormItemType.RADIO_WITH_EXTRA_FIELDS: {
            fieldStatuses = validateRadioWithExtraFields(fieldConfig, crfValues);
            break;
        }
        case FormItemType.CHECKBOX: {
            fieldStatuses = validateSingleArrayValueComponent(fieldConfig, crfValues);
            break;
        }
        case FormItemType.SELECT_WITH_RADIO: {
            fieldStatuses = validateListWithRadioComponent(
                fieldConfig,
                crfValues,
                SelectWithRadioComponent.RADIO_ATTRIBUTE_POSTFIX
            );
            break;
        }
        case FormItemType.CHECKBOX_WITH_EXTRA_FIELDS: {
            fieldStatuses = validateListWithRadioComponent(
                fieldConfig,
                crfValues,
                SelectWithRadioComponent.RADIO_ATTRIBUTE_POSTFIX
            );
            break;
        }
        case FormItemType.MEASUREMENT: {
            fieldStatuses = validateMeasurementComponent(fieldConfig, crfValues);
            break;
        }
        case FormItemType.MULTISELECT: {
            fieldStatuses = validateMultiSelectList(fieldConfig, crfValues);
            break;
        }
        case FormItemType.EVENT_MODAL: {
            fieldStatuses = validateEventModalComponent(fieldConfig, crfValues);
            break;
        }
        default:
            break;
    }
    return fieldStatuses;
}

export function validateYesNoDateComponent(fieldConfig: FormFieldConfig, crfValues: FormAnswer): FieldStatus[] {
    const radioKey = fieldConfig.key + YesNoDateComponent.BOOLEAN_ATTRIBUTE_POSTFIX;
    const dateKey = fieldConfig.key + YesNoDateComponent.DATE_ATTRIBUTE_POSTFIX;
    const selectedValue = isSubArrayField(fieldConfig.key)
        ? getArraySubFieldValue(crfValues, fieldConfig.key + YesNoDateComponent.BOOLEAN_ATTRIBUTE_POSTFIX)
        : crfValues[radioKey];
    if (selectedValue === null) {
        return [createMissingValueHardErrorStatusForField(fieldConfig)];
    } else if (selectedValue) {
        const dateValue = isSubArrayField(fieldConfig.key)
            ? getArraySubFieldValue(crfValues, fieldConfig.key + YesNoDateComponent.DATE_ATTRIBUTE_POSTFIX)
            : crfValues[dateKey];
        if (!dateValue || dateValue === INVALID_DATE) {
            return [createMissingValueHardErrorStatusForField({ ...fieldConfig, key: dateKey })];
        }
    }
    return [];
}

export function validateSingleValueComponent(fieldConfig: FormFieldConfig, crfValues: FormAnswer): FieldStatus[] {
    const fieldValue = isSubArrayField(fieldConfig.key)
        ? getArraySubFieldValue(crfValues, fieldConfig.key)
        : crfValues[fieldConfig.key];
    if (fieldValue === null || fieldValue === undefined || fieldValue.length === 0 || fieldValue === INVALID_DATE) {
        return [createMissingValueHardErrorStatusForField(fieldConfig)];
    }
    return [];
}

export function validateRadioWithExtraFields(fieldConfig: FormFieldConfig, crfValues: FormAnswer): FieldStatus[] {
    const selectedValue = isSubArrayField(fieldConfig.key)
        ? getArraySubFieldValue(crfValues, fieldConfig.key)
        : crfValues[fieldConfig.key];
    if (selectedValue === null) {
        return [createMissingValueHardErrorStatusForField(fieldConfig)];
    } else {
        let selectedOptionExtraField: ExtraField = null;
        fieldConfig.optionsWithExtraField.forEach((extraField) => {
            if (selectedValue === extraField.key) {
                selectedOptionExtraField = extraField;
                return [];
            }
        });
        if (selectedOptionExtraField !== null) {
            let extraFieldTypeKeyPostfix = null;
            switch (selectedOptionExtraField.type) {
                case EXTRA_FIELD_DATE_TYPE:
                    extraFieldTypeKeyPostfix = DATE_FIELD_POSTFIX;
                    break;
                case EXTRA_FIELD_TEXT_TYPE:
                    extraFieldTypeKeyPostfix = TEXT_FIELD_POSTFIX;
                    break;
                case EXTRA_FIELD_DROPDOWN_TYPE:
                    extraFieldTypeKeyPostfix = DROPDOWN_FIELD_POSTFIX;
                    break;
                case EXTRA_FIELD_NUMERIC_FIELD_KEY:
                    extraFieldTypeKeyPostfix = EXTRA_FIELDS_NUMERIC_FIELD_KEY_POSTFIX;
                    break;
                default:
                    throw Error('Unknown extra field type : ' + selectedOptionExtraField.type);
            }

            const extraFieldValueKey = fieldConfig.key + extraFieldTypeKeyPostfix;
            const extraFieldValue = getExtraFieldValue(crfValues, fieldConfig.key, extraFieldTypeKeyPostfix);
            if (extraFieldValue === null || extraFieldValue === '' || extraFieldValue === INVALID_DATE) {
                return [createMissingValueHardErrorStatusForField({ ...fieldConfig, key: extraFieldValueKey })];
            }
        }
    }
    return [];
}

export function validateSingleArrayValueComponent(fieldConfig: FormFieldConfig, crfValues: FormAnswer): FieldStatus[] {
    const fieldValue: string[] = isSubArrayField(fieldConfig.key)
        ? getArraySubFieldValue(crfValues, fieldConfig.key)
        : crfValues[fieldConfig.key];
    if (!fieldValue || fieldValue.length === 0) {
        return [createMissingValueHardErrorStatusForField(fieldConfig)];
    }
    return [];
}

export function validateListWithRadioComponent(
    fieldConfig: FormFieldConfig,
    crfValues: FormAnswer,
    radioFieldPostfix: string
): FieldStatus[] {
    const radioValue: string[] = getExtraFieldValue(crfValues, fieldConfig.key, radioFieldPostfix);
    if (!radioValue) {
        const fieldValue: string[] = isSubArrayField(fieldConfig.key)
            ? getArraySubFieldValue(crfValues, fieldConfig.key)
            : crfValues[fieldConfig.key];
        if (!fieldValue || (Array.isArray(fieldValue) && fieldValue.length === 0)) {
            return [createMissingValueHardErrorStatusForField(fieldConfig)];
        }
    }
    return [];
}

export function validateMeasurementComponent(fieldConfig: FormFieldConfig, crfValues: FormAnswer): FieldStatus[] {
    const radioFieldValue: string[] = getExtraFieldValue(
        crfValues,
        fieldConfig.key,
        MeasurementComponent.RADIO_FIELD_POSTFIX
    );
    if (radioFieldValue === null) {
        return [createMissingValueHardErrorStatusForField(fieldConfig)];
    } else if (radioFieldValue) {
        const measurements: any[] = isSubArrayField(fieldConfig.key)
            ? getArraySubFieldValue(crfValues, fieldConfig.key)
            : crfValues[fieldConfig.key];
        const measumentsErrors: FieldStatus[] = [];
        measurements.forEach((measurement) => {
            if (!measurement.metric || !measurement.date || !measurement.value || measurement.date === INVALID_DATE) {
                const erroneousRowKey = fieldConfig.key + measurement.rowId;
                measumentsErrors.push(
                    createMissingValueHardErrorStatusForField({ ...fieldConfig, key: erroneousRowKey })
                );
            }
        });
        return measumentsErrors;
    }
    return [];
}

export function validateMultiSelectList(fieldConfig: FormFieldConfig, crfValues: FormAnswer): FieldStatus[] {
    const selectedValues: string[] = isSubArrayField(fieldConfig.key)
        ? getArraySubFieldValue(crfValues, fieldConfig.key)
        : crfValues[fieldConfig.key];
    const selectedAdHocValues: string[] = getExtraFieldValue(
        crfValues,
        fieldConfig.key,
        MultiselectComponent.ADHOC_FIELD_POSTFIX
    );
    if (
        (!selectedValues || selectedValues.length === 0) &&
        (!selectedAdHocValues || selectedAdHocValues.length === 0)
    ) {
        return [createMissingValueHardErrorStatusForField(fieldConfig)];
    }
    return [];
}

export function validateEventModalComponent(fieldConfig: FormFieldConfig, crfValues: FormAnswer): FieldStatus[] {
    if (fieldConfig.required && crfValues[fieldConfig.key].length === 0) {
        return [createMissingValueHardErrorStatusForField(fieldConfig)];
    }

    return [];
}

export function createMissingValueHardErrorStatusForField(fieldConfig: FormFieldConfig): FieldStatus {
    return {
        formType: fieldConfig.formType,
        fieldKey: fieldConfig.key,
        translationKey: FormInputField.MISSING_FIELD_TRANSLATION_KEY,
        statusType: FieldStatusType.MISSING_VALUE,
    };
}

export function getExtraFieldValue(crfValues: FormAnswer, fieldKey: string, postfix: string): any {
    return isSubArrayField(fieldKey)
        ? getArraySubFieldValue(crfValues, fieldKey, postfix)
        : crfValues[fieldKey + postfix];
}
