export class ArrayUtils {
    static remove<T>(arr: T[], objectToRemove: T): T[] {
        const index = arr.indexOf(objectToRemove);
        if (index >= 0) {
            arr.splice(index, 1);
        }

        return arr;
    }

    static toggle<T>(arr: T[], itemToToggle: T) {
        const index = arr.indexOf(itemToToggle);
        if (index > -1) {
            arr.splice(index, 1);
        } else {
            arr.push(itemToToggle);
        }
    }

    static toggleItemByField<T>(arr: T[], itemToToggle: T, fieldToCompare: string) {
        const existingItemIndex = arr
            .map((arrayItem) => {
                return arrayItem[fieldToCompare];
            })
            .indexOf(itemToToggle[fieldToCompare]);

        if (existingItemIndex < 0) {
            arr.push(itemToToggle);
        } else {
            arr.splice(existingItemIndex, 1);
        }
    }

    static last<T>(arr: T[]): T | undefined {
        return arr && arr.length > 0 ? arr[arr.length - 1] : undefined;
    }
}
