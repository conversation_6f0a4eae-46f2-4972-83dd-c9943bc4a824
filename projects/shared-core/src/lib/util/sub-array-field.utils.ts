// E.g. parentKey.0.fieldKey
import { Dictionary } from '../common-types';

export const SUB_ARRAY_FIELD_REGEX = /^([\w-]+)\.(\d+)\.([\w-]+)$/;

/**
 * Checks if field answer belongs to an array of some parent field
 * E.g. parentKey.1.fieldKey ->
 * {
 *     parentKey: [
 *         {...(first child, i.e. index 0 answers)},
 *         {    ...(other index 1 answers)
 *             fieldKey: (insert fieldKey answer here)
 *         }
 *     ]
 * }
 */
export function isSubArrayField(fieldKey: string) {
    return SUB_ARRAY_FIELD_REGEX.test(fieldKey);
}

export interface FormSubarrayField {
    fieldKey: string;
    parentKey: string;
    id: number;
}

export function getDestructuredSubArrayFields(key: string): FormSubarrayField {
    if (!isSubArrayField(key)) {
        return null;
    }
    const [, parentKey, index, fieldKey] = key.match(SUB_ARRAY_FIELD_REGEX);
    return {
        fieldKey,
        parentKey,
        id: parseInt(index, 10),
    };
}

export function getArraySubFieldValue(
    formAnswers: Dictionary<string, any>,
    fieldKey: string,
    postfix: string = null
): any {
    const subarrayFields: FormSubarrayField = getDestructuredSubArrayFields(fieldKey);
    const subFieldValueKey = postfix ? subarrayFields.fieldKey + postfix : subarrayFields.fieldKey;
    const index = formAnswers[subarrayFields.parentKey].findIndex((answer) => {
        return answer.index === subarrayFields.id;
    });
    return formAnswers[subarrayFields.parentKey][index][subFieldValueKey];
}
