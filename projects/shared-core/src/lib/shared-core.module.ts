import { ModuleWithProviders, NgModule } from '@angular/core';

import { ConfigurationProviderService } from './abstract-services/configuration-provider.service';
import { FormSpecificHandlerFactory } from './abstract-services/form-specific-handler-factory.service';
import { LoadingSpinnerService } from './abstract-services/loading-spinner.service';
import { NoonaFormEngineModule } from './form-engine/noona-form-engine.module';
import { NoonaLocaleService } from './abstract-services/noona-locale.service';
import { RegulatoryModule } from './regulatory/regulatory.module';
import { ScPipesModule } from './pipes/sc-pipes.module';
import { SharedApi } from './abstract-services/shared-api.service';
import { SharedAssetService } from './abstract-services/shared-asset.service';
import { SharedCoreConfig } from './shared-core-config.interface';
import { SharedNativePictureSelectionService } from './abstract-services/shared-native-picture-selection.service';
import { SharedSchemaService } from './abstract-services/shared-schema.service';
import { UtilsModule } from './utils/utils.module';
import { DsIconModule } from './ds/components/icon/icon.module';
import { DsModalModule } from './ds/components/modal/modal.module';
import { DsLibraryConfig } from '../public-api';
import { ASSETS_PATH } from './tokens/assets.token';
import { DEFAULT_ASSETS_PATH } from './constants';

const defaultDsConfig: DsLibraryConfig = {
    pathToLibrarySprite: 'assets/images/ds-sprite.svg',
};

@NgModule({
    exports: [UtilsModule, ScPipesModule, NoonaFormEngineModule, RegulatoryModule],
})
export class SharedCoreModule {
    static forRoot(config: SharedCoreConfig): ModuleWithProviders<SharedCoreModule> {
        return {
            ngModule: SharedCoreModule,
            providers: [
                ScPipesModule.forRoot().providers,
                { provide: ConfigurationProviderService, useExisting: config.configProvider },
                { provide: NoonaLocaleService, useExisting: config.localeService },
                { provide: SharedSchemaService, useExisting: config.schemaService },
                { provide: SharedApi, useExisting: config.api },
                { provide: LoadingSpinnerService, useExisting: config.loadingService },
                { provide: SharedAssetService, useExisting: config.assetService },
                { provide: SharedNativePictureSelectionService, useExisting: config.nativePictureService },
                { provide: FormSpecificHandlerFactory, useExisting: config.formSpecificHandlerFactory },
                { provide: 'env', useValue: config.environment },
                DsIconModule.forRoot(config.dsConfig || defaultDsConfig).providers,
                DsModalModule.forRoot().providers,
                { provide: ASSETS_PATH, useValue: config.environment.assetPath || DEFAULT_ASSETS_PATH },
            ],
        };
    }
}
