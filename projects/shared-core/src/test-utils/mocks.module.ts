import { NgModule, Injectable, Pipe, PipeTransform } from '@angular/core';
import { Observable, of, EMPTY } from 'rxjs';
import { provideMockStore } from '@ngrx/store/testing';
import merge from 'lodash/merge';

import {
  CamelCasePipe,
  CapitalFirstLetterPipe,
  HyphenedPipe,
  NoonaEncodePipe,
  DecodePipe,
  DateFormatPipe,
  NoonaDecodePipe,
  I18NPipe,
  SharedSchemaService,
  ConfigurationProviderService,
  NoonaLocaleService,
  Dictionary,
  MeasurementSystem,
  QuestionarySettings,
  Application,
  User,
  ListEntry,
  NoonaSchema,
  DateFormattingPattern,
  getInitialFormEngineState,
  FORM_ENGINE_MODULE_NAME,
  QuestionaryType,
  LowercaseFirstPipe,
  ConvertHyphenedToCapitalPipe,
  FormSpecificHandlerFactory,
  FormSpecificHandler,
  LoadingSpinnerService,
  SafeHtmlPipe
} from '../public-api';
import { FieldService } from '../lib/form-engine/services/field.service';
import { Language } from '../lib/generated/models/language';
import { formEngineStateSnapshot } from './form-engine-snapshot';
import { ModelValue } from '../lib/form-engine/models/model-value.interface';
import { BoldPipe } from '../lib/pipes/bold.pipe';
import { DsDomService } from '../lib/ds/services/dom.service';

@Pipe({ name: 'i18n' })
class MockI18nPipe implements PipeTransform {
  transform = (value: string) => value;
}

@Injectable()
class MockedNoonaLocaleService implements NoonaLocaleService {
  getTranslation = jest.fn();

  setTranslations(t: { [key: string]: string }) {
    throw new Error('Method not implemented.');
  }
  getClinicLanguage(): Promise<Language> {
    return Promise.resolve(Language.FI_FI);
  }
  getLocale(): string {
    return 'fi_FI';
  }
}

@Injectable()
class MockedFieldService {
  getNextFormSectionAsObservable = jest.fn(() => {
    return of();
  });

  getListValues(): ModelValue[] {
    return [
      {
        id: 'test',
        selected: false,
        translationKey: 'test.label',
        value: 'wasPatientPregnantMultipleValue',
        isAdhoc: false
      }
    ];
  }
  getAnswer() {
    return null;
  }
  getValidators() {
    return [];
  }
  listenForValidate() {
    return EMPTY;
  }
  clearViewValues() {}
}

@Injectable()
class MockedConfigurationProviderService implements ConfigurationProviderService {
  dateFormattingPattern(): Observable<DateFormattingPattern> {
    return of({
      shortDatePattern: '',
      timePattern: '',
      datetimeMinsPattern: '',
      datetimeSecsPattern: '',
      datePlaceholder: '',
      patternId: '',
      inputValidatorRegExp: new RegExp(''),
      patientTimelineDate: '',
      fullTimePattern: 'MM/DD/YYYY hh:mm a',
      longDatePattern: 'MM/DD/YYYY'
    });
  }
  authorizationHeader(): Dictionary<string, string> {
    throw new Error('Method not implemented.');
  }
  measurementSystem(): Observable<MeasurementSystem> {
    return of(MeasurementSystem.METRIC);
  }
  questionarySettings(): QuestionarySettings[] {
    throw new Error('Method not implemented.');
  }
  baseUrl(): string {
    throw new Error('Method not implemented.');
  }
  site(): Application {
    throw new Error('Method not implemented.');
  }
  activeUser(): Observable<User> {
    throw new Error('Method not implemented.');
  }
}

@Injectable()
class MockedSharedSchemaService implements SharedSchemaService {
  getSelectLists(): Dictionary<string, ListEntry[]> {
    throw new Error('Method not implemented.');
  }
  getFieldValueStaticConditions(): Dictionary<string, any> {
    throw new Error('Method not implemented.');
  }
  getSchema(): NoonaSchema {
    return {
      formEngineForms: {
        [`${QuestionaryType.BOUNCE_HADS}Form`]: {
          fields: []
        }
      },
      inputGroups: {
        [QuestionaryType.BOUNCE_HADS]: {
          test: 'test'
        }
      }
    };
  }
  getTranslations(): Dictionary<string, Dictionary<string, string>> {
    throw new Error('Method not implemented.');
  }
  getAttributeLists(): Dictionary<string, string[]> {
    throw new Error('Method not implemented.');
  }
  getPhotoFields(): Dictionary<string, string[]> {
    throw new Error('Method not implemented.');
  }
  getBodyDiagrams(): Dictionary<string, string> {
    throw new Error('Method not implemented.');
  }
  overwriteFormSchema(formKey: string, schema: any) {
    throw new Error('Method not implemented.');
  }
}

@Injectable()
class MockedFormSpecificHandlerFactory implements FormSpecificHandlerFactory {
  getHandler(formType: string, studyType: string): FormSpecificHandler {
    throw new Error('Method not implemented.');
  }
}

@Injectable()
class MockedLoadingSpinnerService implements LoadingSpinnerService {
  loading() {}
  ready() {}
}

@NgModule({
  declarations: [MockI18nPipe, SafeHtmlPipe, BoldPipe],
  exports: [MockI18nPipe, SafeHtmlPipe, BoldPipe],
  providers: [
    CamelCasePipe,
    CapitalFirstLetterPipe,
    HyphenedPipe,
    NoonaEncodePipe,
    DecodePipe,
    DateFormatPipe,
    NoonaDecodePipe,
    LowercaseFirstPipe,
    ConvertHyphenedToCapitalPipe,
    { provide: I18NPipe, useClass: MockI18nPipe },
    provideMockStore({
      initialState: {
        config: {
          clinicFieldSetting: [
            {
              fieldType: 'medicalRecordNumber'
            },
            {
              fieldType: 'hipaaCode'
            }
          ]
        },
        [FORM_ENGINE_MODULE_NAME]: merge({}, getInitialFormEngineState(), formEngineStateSnapshot)
      }
    }),
    { provide: SharedSchemaService, useClass: MockedSharedSchemaService },
    { provide: ConfigurationProviderService, useClass: MockedConfigurationProviderService },
    { provide: FieldService, useClass: MockedFieldService },
    { provide: NoonaLocaleService, useClass: MockedNoonaLocaleService },
    { provide: FormSpecificHandlerFactory, useClass: MockedFormSpecificHandlerFactory },
    { provide: LoadingSpinnerService, useClass: MockedLoadingSpinnerService },
    { provide: BoldPipe, useValue: { transform: jest.fn } },
    { provide: DsDomService, useValue: {} },
    { provide: 'env', useValue: {} }
  ]
})
export class MocksModule {}
