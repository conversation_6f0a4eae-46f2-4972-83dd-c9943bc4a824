import { FieldStatusType, FormEngineState, FormVariableType, QuestionaryType } from '../public-api';
import { QuestionnaireInquiryInformationViewModel } from '../lib/form-engine/models/questionnaire-inquiry-information-view-model';

export const formEngineStateSnapshot: FormEngineState = {
    inquiry: {
        loaded: false,
        loading: false,
        error: '',
        inquiryId: '',
        info: {},
        selectedSymptomTypes: {},
        requiredTypeSelections: [],
        symptomTypes: [],
        formVisibility: {},
        symptomOrder: {},
        currentStep: -1,
        showSummary: false,
        formValidity: {},
        additionalQuestions: '',
        nextOfKinAnswer: false,
        symptomInformations: {
            [QuestionaryType.BOUNCE_HADS]: {
                dateRanges: [],
                mostSevere: {},
            },
        },
    },
    patient: {
        patient: {
            id: 'aa-bb-cc',
            firstName: 'Jane',
            lastName: 'Doe',
        },
    },
    form: {
        forms: {
            [QuestionaryType.BOUNCE_HADS]: {
                isDirty: false,
                validFields: {},
                formValid: false,
                uploading: false,
                currentSection: 1,
                totalSections: 1,
                wasPatientPregnantMultipleDate: '2020-04-23T14:31:11.269Z',
                wasPatientPregnantMultiple: 'unknown',
                agentsUsedAdhoc: ['For the Horde!!'],
                phqRadio: null,
                phq: [
                    {
                        rowId: 0,
                        date: null,
                        value: null,
                        metric: 'phq',
                    },
                ],
                wasPatientPregnantMultipleText: '',
                wasPatientPregnantMultipleDropdown: null,
                wasPatientPregnantMultipleValue: null,
                horizontalRadio: null,
                horizontalRadioDate: null,
                horizontalRadioText: '',
                horizontalRadioDropdown: null,
                horizontalRadioValue: null,
                horizontalRadio2: null,
                horizontalRadio2Date: null,
                horizontalRadio2Text: '',
                horizontalRadio2Dropdown: null,
                horizontalRadio2Value: null,
                horizontalRadio3: null,
                horizontalRadio3Date: null,
                horizontalRadio3Text: '',
                horizontalRadio3Dropdown: null,
                horizontalRadio3Value: null,
                race: [],
                raceRadio: null,
                raceText: null,
                wasPatientPregnantRadioStandard: null,
                agentsUsed: [],
                numberOfTherapyLinesBeforeIo: null,
                numberOfTherapyLinesBeforeIoRadio: null,
                disabledDateOfBirth: null,
                inpatientAdmissions: [],
                inpatientAdmissionsLimited: [],
                symptomsRadio: null,
                symptoms: [
                    {
                        rowId: 0,
                        date: null,
                        value: null,
                        metric: null,
                    },
                ],
                numericField: null,
                date: null,
                visibilities: [{ field: 'field', visibility: true }],
            },
        },
    },
    questionnaire: {
        loaded: false,
        loading: false,
        info: new QuestionnaireInquiryInformationViewModel(), // wat
        inquiryId: '',
        nextOfKinAnswer: false,
    },
    forms: {
        questionnaires: {},
    },
    crf: {
        loaded: false,
        loading: false,
        info: {},
        crfFormId: '',
    },
    variables: {
        ids: ['some_variable', 'some_variable_2', 'abstractionStart', 'abstractionEnd'],
        entities: {
            some_variable: {
                id: 'some_variable',
                key: 'some_variable',
                type: FormVariableType.ENUMERATION,
                value: 'myvalue',
                meta: 'variable.translation.{0}',
                modified: new Date('2020-04-23T14:31:11.277Z'),
            },
            some_variable_2: {
                id: 'some_variable_2',
                key: 'some_variable_2',
                type: FormVariableType.ENUMERATION,
                value: 'myvalue',
                meta: 'longDatePattern',
                modified: new Date('2020-04-23T14:31:11.277Z'),
            },
            abstractionStart: {
                id: 'abstractionStart',
                key: 'abstractionStart',
                type: FormVariableType.DATE,
                value: '2020-02-23T15:31:11.275Z',
                meta: 'variable.translation.{0}',
                modified: new Date('2020-04-23T14:31:11.277Z'),
                transient: true,
            },
            abstractionEnd: {
                id: 'abstractionEnd',
                key: 'abstractionEnd',
                type: FormVariableType.DATE,
                value: '2020-06-23T14:31:11.277Z',
                meta: 'longDatePattern',
                modified: new Date('2020-04-23T14:31:11.277Z'),
                transient: true,
            },
        },
        loaded: true,
        loading: false,
    },
    fieldStatuses: {
        ids: [
            'bounceHadsdisabledDateOfBirthformstatusTestNotreadynotReady',
            'bounceHadsfieldStatusTestKeyformstatusTestHarderrorhardError',
            'bounceHadsfieldStatusTestKeyformstatusTestSofterrorsoftError',
            'bounceHadsfieldStatusTestKeyformstatusTestTerminationerrorterminationError',
            'bounceHadsfieldStatusTestKeyformstatusTestNotreadynotReady',
            'bounceHadshorizontalRadioformstatusTestTerminationerrorterminationError',
            'bounceHadswasPatientPregnantMultipleformstatusTestTerminationerrorterminationError',
        ],
        entities: {
            bounceHadsdisabledDateOfBirthformstatusTestNotreadynotReady: {
                formType: QuestionaryType.BOUNCE_HADS,
                fieldKey: 'disabledDateOfBirth',
                translationKey: 'formstatus.test.notready',
                statusType: FieldStatusType.NOT_READY,
            },
            bounceHadsfieldStatusTestKeyformstatusTestHarderrorhardError: {
                formType: QuestionaryType.BOUNCE_HADS,
                fieldKey: 'fieldStatusTestKey',
                translationKey: 'formstatus.test.harderror',
                statusType: FieldStatusType.HARD_ERROR,
            },
            bounceHadsfieldStatusTestKeyformstatusTestSofterrorsoftError: {
                formType: QuestionaryType.BOUNCE_HADS,
                fieldKey: 'fieldStatusTestKey',
                translationKey: 'formstatus.test.softerror',
                accepted: true,
                lastModified: new Date('2020-04-23T14:31:11.273Z'),
                userId: '1234',
                userName: 'Peter Tyler',
                statusType: FieldStatusType.SOFT_ERROR,
            },
            bounceHadsfieldStatusTestKeyformstatusTestTerminationerrorterminationError: {
                formType: QuestionaryType.BOUNCE_HADS,
                fieldKey: 'fieldStatusTestKey',
                translationKey: 'formstatus.test.terminationerror',
                statusType: FieldStatusType.TERMINATION_ERROR,
            },
            bounceHadsfieldStatusTestKeyformstatusTestNotreadynotReady: {
                formType: QuestionaryType.BOUNCE_HADS,
                fieldKey: 'fieldStatusTestKey',
                translationKey: 'formstatus.test.notready',
                statusType: FieldStatusType.NOT_READY,
            },
            bounceHadshorizontalRadioformstatusTestTerminationerrorterminationError: {
                formType: QuestionaryType.BOUNCE_HADS,
                fieldKey: 'horizontalRadio',
                translationKey: 'formstatus.test.terminationerror',
                statusType: FieldStatusType.TERMINATION_ERROR,
            },
            bounceHadswasPatientPregnantMultipleformstatusTestTerminationerrorterminationError: {
                formType: QuestionaryType.BOUNCE_HADS,
                fieldKey: 'wasPatientPregnantMultiple',
                translationKey: 'formstatus.test.terminationerror',
                statusType: FieldStatusType.TERMINATION_ERROR,
            },
        },
        loaded: false,
        loading: false,
    },
    componentState: {
        ids: [],
        entities: {},
    },
};
