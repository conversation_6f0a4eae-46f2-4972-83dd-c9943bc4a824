@import 'ds-variables.scss';

@mixin container-width() {
    width: 100%;
    max-width: var(--grid-container-max-width);

    @media screen and ($screen-from-s) {
        margin: 0 auto;
    }
}

@mixin container-padding() {
    padding-right: var(--spacing-grid);
    padding-left: var(--spacing-grid);
}

@mixin container-padding-all() {
    padding-top: var(--spacing-grid);
    padding-right: var(--spacing-grid);
    padding-bottom: var(--spacing-grid);
    padding-left: var(--spacing-grid);
}

@mixin container-expand() {
    margin-left: calc(var(--spacing-grid) * -1);
    margin-right: calc(var(--spacing-grid) * -1);
}
