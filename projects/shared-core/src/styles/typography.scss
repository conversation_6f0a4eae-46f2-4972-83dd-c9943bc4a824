h1,
.type--heading-1 {
    color: var(--h1-color);
    font-size: var(--h1-font-size);
    font-weight: var(--h1-font-weight);
    line-height: var(--h1-line-height);
    letter-spacing: var(--h1-letter-spacing);
    font-family: var(--h1-font-family);
}

h2,
.type--heading-2 {
    color: var(--h2-color);
    font-size: var(--h2-font-size);
    font-weight: var(--h2-font-weight);
    line-height: var(--h2-line-height);
    letter-spacing: var(--h2-letter-spacing);
    font-family: var(--h2-font-family);
}

h3,
.type--heading-3 {
    color: var(--h3-color);
    font-size: var(--h3-font-size);
    font-weight: var(--h3-font-weight);
    line-height: var(--h3-line-height);
    letter-spacing: var(--h3-letter-spacing);
    font-family: var(--h3-font-family);
}

h4,
.type--heading-4 {
    color: var(--h4-color);
    font-size: var(--h4-font-size);
    font-weight: var(--h4-font-weight);
    line-height: var(--h4-line-height);
    letter-spacing: var(--h4-letter-spacing);
    font-family: var(--h4-font-family);
}

h5,
.type--heading-5 {
    color: var(--h5-color);
    font-size: var(--h5-font-size);
    font-weight: var(--h5-font-weight);
    line-height: var(--h5-line-height);
    letter-spacing: var(--h5-letter-spacing);
    font-family: var(--h5-font-family);
}

h6,
.type--heading-6 {
    color: var(--h6-color);
    font-size: var(--h6-font-size);
    font-weight: var(--h6-font-weight);
    line-height: var(--h6-line-height);
    letter-spacing: var(--h6-letter-spacing);
    font-family: var(--h6-font-family);
}
.type--color-white {
    color: var(--color-white);
}
.type--color-black {
    color: var(--text-black);
}
.type--color-grey {
    color: var(--text-grey);
}
.type--color-primary {
    color: var(--color-primary);
}
.type--color-brand-1 {
    color: var(--color-brand-1);
}
.type--color-brand-2 {
    color: var(--color-brand-2);
}
.type--color-brand-3 {
    color: var(--color-brand-3);
}
.type--color-information-1 {
    color: var(--color-information-1);
}
.type--color-information-2 {
    color: var(--color-information-2);
}
.type--color-information-3 {
    color: var(--color-information-3);
}
.type--color-information-4 {
    color: var(--color-information-4);
}
.type--color-red,
.type--color-information-5,
.type--color-error {
    color: var(--text-red);
}
.type--font-weight-thin {
    font-weight: var(--font-weight-thin);
}
.type--font-weight-light {
    font-weight: var(--font-weight-light);
}
.type--font-weight-semibold {
    font-weight: var(--font-weight-semibold);
}
.type--font-weight-bold {
    font-weight: var(--font-weight-bold);
}
.type--font-style-italic {
    font-style: italic;
}
.type--body {
    font-size: var(--body-font-size);
    line-height: var(--body-line-height);
    &-small {
        font-size: var(--body-small-font-size);
        line-height: var(--body-small-line-height);
    }
    &-large {
        font-size: var(--body-large-font-size);
        line-height: var(--body-large-line-height);
    }
}
