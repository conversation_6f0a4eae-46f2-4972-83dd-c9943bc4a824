@import 'size-variables-base';
@import 'breakpoint-variables-base';
:root {
    --spacing-xxs: #{$spacing-xxs};
    --spacing-xs: #{$spacing-xs};
    --spacing-s: #{$spacing-s};
    --spacing-m: #{$spacing-m};
    --spacing-l: #{$spacing-l};
    --spacing-xl: #{$spacing-xl};
    --spacing-xxl: #{$spacing-xxl};

    --spacing-grid: #{$spacing-l};
    @media screen and ($screen-from-s) {
        --spacing-grid: #{$spacing-xl};
    }
    @media screen and ($screen-from-m) {
        --spacing-grid: #{$spacing-xxl};
    }

    --grid-spacing: #{$spacing-xxl};

    --grid-container-max-width: #{$grid-container-max-width-l};

    --hr-border-width: #{$hr-border-width};

    --form-element-regular-height: #{$form-element-regular-height};
    --form-element-regular-line-height: #{$form-element-regular-line-height};
    --form-element-small-height: #{$form-element-small-height};
    --form-element-large-height: #{$form-element-large-height};
    --form-element-mobile-regular-height: #{$form-element-mobile-regular-height};
    --form-element-mobile-small-height: #{$form-element-mobile-small-height};
    --form-element-mobile-large-height: #{$form-element-mobile-large-height};
    --form-element-border-radius: #{$form-element-border-radius};
    --form-element-mobile-border-radius: #{$form-element-mobile-border-radius};
    --form-element-box-shadow-size: #{$form-element-box-shadow-size};
    --form-element-mobile-box-shadow-size: #{$form-element-mobile-box-shadow-size};
    --form-element-focus-ring-size: #{$form-element-focus-ring-size};
    --form-element-width: #{$form-element-width};
    --form-element-max-width: #{$form-element-max-width};

    --button-width: #{$button-width};
    --button-min-width: #{$button-min-width};
    --button-height: #{$form-element-regular-height};
    --input-height: #{$form-element-regular-height};
    --select-height: #{$form-element-regular-height};

    --form-element-horizontal-padding: #{$form-element-horizontal-padding};
    --form-element-border-width: #{$form-element-border-width};
    --label-margin-bottom: #{$label-margin-bottom};

    --button-primary-border-width: #{$button-primary-border-width};
    --button-secondary-border-width: #{$button-secondary-border-width};
    --button-link-border-width: #{$button-link-border-width};
    --button-primary-regular-height: #{$button-primary-regular-height};
    --button-primary-small-height: #{$button-primary-small-height};
    --button-primary-large-height: #{$button-primary-large-height};
    --button-secondary-regular-height: #{$button-secondary-regular-height};
    --button-secondary-small-height: #{$button-secondary-small-height};
    --button-secondary-large-height: #{$button-secondary-large-height};
    --button-link-regular-height: #{$button-link-regular-height};
    --button-link-small-height: #{$button-link-small-height};
    --button-link-large-height: #{$button-link-large-height};
    --button-primary-regular-padding: #{$button-primary-regular-padding};
    --button-primary-small-padding: #{$button-primary-small-padding};
    --button-primary-large-padding: #{$button-primary-large-padding};
    --button-secondary-regular-padding: #{$button-secondary-regular-padding};
    --button-secondary-small-padding: #{$button-secondary-small-padding};
    --button-secondary-large-padding: #{$button-secondary-large-padding};
    --button-link-regular-padding: #{$button-link-regular-padding};
    --button-link-small-padding: #{$button-link-small-padding};
    --button-link-large-padding: #{$button-link-large-padding};

    --button-icon-margin: #{$spacing-s};
    --button-primary-border-radius: #{$button-primary-border-radius};
    --button-primary-box-shadow-size: #{$button-primary-box-shadow-size};
    --button-primary-min-width: #{$button-min-width};
    --button-secondary-border-radius: #{$button-secondary-border-radius};
    --button-secondary-box-shadow-size: #{$button-secondary-box-shadow-size};
    --button-secondary-min-width: #{$button-min-width};
    --button-link-border-radius: #{$button-link-border-radius};
    --button-link-box-shadow-size: #{$button-link-box-shadow-size};
    --button-link-min-width: #{$button-min-width};

    --datatable-header-border-width: #{$datatable-header-border-width};
    --datatable-border-width: #{$datatable-border-width};
    --datatable-row-border-width: #{$datatable-row-border-width};
    --datatable-border-radius: #{$datatable-border-radius};
    --datatable-fixed-header-height: #{$datatable-fixed-header-height};
    --datatable-fixed-row-height: #{$datatable-fixed-row-height};
    --datatable-cell-horizontal-padding: #{$datatable-cell-horizontal-padding};
    --datatable-cell-vertical-padding: #{$datatable-cell-vertical-padding};
    --datatable-pagination-button-size: #{$datatable-pagination-button-size};

    --modal-host-top: #{$modal-host-top};
    --modal-host-right: #{$modal-host-right};
    --modal-host-bottom: #{$modal-host-bottom};
    --modal-host-left: #{$modal-host-left};
    --modal-window-max-width: calc($grid-container-max-width-l) - ($spacing-xxl * 2);
    --modal-window-border-radius: #{$modal-window-border-radius};
    --modal-window-box-shadow-size: #{$modal-window-box-shadow-size};
    --modal-content-padding-top: #{$spacing-xl};
    --modal-content-padding-right: #{$spacing-xl};
    --modal-content-padding-bottom: #{$spacing-xl};
    --modal-content-padding-left: #{$spacing-xl};
    --modal-footer-padding-top: 0;
    --modal-footer-padding-right: #{$spacing-xl};
    --modal-footer-padding-bottom: #{$spacing-xl};
    --modal-footer-padding-left: #{$spacing-xl};
    --modal-close-button-top: #{$spacing-l};
    --modal-close-button-right: #{$spacing-l};

    --checkbox-wrapper-width: #{$checkbox-wrapper-width};
    --checkbox-width: #{$checkbox-width};
    --checkbox-border-radius: #{$checkbox-border-radius};
    --checkbox-border-width: #{$checkbox-border-width};
    --checkbox-label-margin: #{$checkbox-label-margin};
    --checkbox-wrapper-padding-horizontal: #{$checkbox-wrapper-padding-horizontal};
    --checkbox-wrapper-padding-vertical: #{$checkbox-wrapper-padding-vertical};
    --checkbox-wrapper-border-radius: #{$checkbox-wrapper-border-radius};
    --checkbox-wrapper-box-shadow-size: #{$checkbox-wrapper-box-shadow-size};
    --checkbox-check-spacing: #{$checkbox-check-spacing};

    --radio-width: #{$radio-width};
    --radio-border-radius: #{$radio-border-radius};
    --radio-border-width: #{$radio-border-width};
    --radio-label-margin: #{$radio-label-margin};
    --radio-bullet-spacing: #{$radio-bullet-spacing};

    --dropdown-input-regular-height: #{$form-element-regular-height};
    --dropdown-input-small-height: #{$form-element-small-height};
    --dropdown-input-large-height: #{$form-element-large-height};
    --dropdown-input-border-width: #{$form-element-border-width};
    --dropdown-input-border-radius: #{$form-element-border-radius};
    --dropdown-tooltip-height: #{$dropdown-tooltip-height};
    --dropdown-tooltip-options-panel-border-radius: #{$dropdown-tooltip-options-panel-border-radius};
    --dropdown-border-width: #{$dropdown-border-width};
    --dropdown-multiselect-selected-horizontal-padding: #{$dropdown-multiselect-selected-horizontal-padding};
    --dropdown-clear-button-font-size: #{$dropdown-clear-button-font-size};
    --dropdown-options-panel-border-width: #{$form-element-border-width};
    --dropdown-options-panel-border-radius: #{$form-element-border-radius};

    --range-slider-container-width: #{$range-slider-container-width};
    --range-slider-container-width-vertical: #{$range-slider-container-width-vertical};
    --range-slider-container-height: #{$range-slider-container-height};
    --range-slider-track-width: #{$range-slider-track-width};
    --range-slider-thumb-size: #{$range-slider-thumb-size};
    --range-slider-min-max-label-width: #{$range-slider-min-max-label-width};
    --range-slider-left-container-width: #{$range-slider-left-container-width};
    --range-slider-value-indicator-size: #{$range-slider-value-indicator-size};
    --range-slider-value-indicator-left: #{$range-slider-value-indicator-left};
    --range-slider-value-indicator-text-width: #{$range-slider-value-indicator-text-width};
    --range-slider-2lines-height: #{$range-slider-2lines-height};
    --range-slider-3lines-height: #{$range-slider-3lines-height};
    --range-slider-text-width: #{$range-slider-text-width};

    --actions-dropdown-option-horizontal-padding: #{$spacing-m};
    --actions-dropdown-options-border-radius: #{$form-element-border-radius};
    --actions-dropdown-option-height: 40px;

    --tooltip-max-width: 42rem;
    --tooltip-padding-vertical: #{$spacing-m};
    --tooltip-padding-horizontal: #{$spacing-l};
    --tooltip-border-radius: 3px;
}
