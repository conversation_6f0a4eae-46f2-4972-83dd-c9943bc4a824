/*
 * Legal Disclaimer
 *
 * These Fonts are licensed only for use on these domains and their subdomains:
 * noona.fi
 *
 * It is illegal to download or use them on other websites.
 *
 * While the @font-face statements below may be modified by the client, this
 * disclaimer may not be removed.
 *
 * Lineto.com, 2014
 */

@font-face {
    font-family: 'LLBrownWeb';
    src: url('/assets/fonts/lineto-brown-thin-s.woff') format('woff'),
        url('/assets/fonts/lineto-brown-thin-s.ttf') format('truetype');
    font-weight: 100;
    font-style: normal;
}

@font-face {
    font-family: 'LLBrownWeb';
    src: url('/assets/fonts/lineto-brown-light-s.woff') format('woff'),
        url('/assets/fonts/lineto-brown-light-s.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'LLBrownWeb';
    src: url('/assets/fonts/lineto-brown-regular-s.woff') format('woff'),
        url('/assets/fonts/lineto-brown-regular-s.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'LLBrownWeb';
    src: url('/assets/fonts/lineto-brown-bold-s.woff') format('woff'),
        url('/assets/fonts/lineto-brown-bold-s.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'PaulGroteskSoft';
    src: url('/assets/fonts/artill-paul-grotesk-soft-thin.woff') format('woff'),
        url('/assets/fonts/artill-paul-grotesk-soft-thin.ttf') format('truetype');
    font-weight: 100;
    font-style: normal;
}

@font-face {
    font-family: 'PaulGroteskSoft';
    src: url('/assets/fonts/artill-paul-grotesk-soft-light.woff') format('woff'),
        url('/assets/fonts/artill-paul-grotesk-soft-light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'PaulGroteskSoft';
    src: url('/assets/fonts/artill-paul-grotesk-soft-regular.woff') format('woff'),
        url('/assets/fonts/artill-paul-grotesk-soft-regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'PaulGroteskSoft';
    src: url('/assets/fonts/artill-paul-grotesk-soft-semibold.woff') format('woff'),
        url('/assets/fonts/artill-paul-grotesk-soft-semibold.ttf') format('truetype');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'PaulGroteskSoft';
    src: url('/assets/fonts/artill-paul-grotesk-soft-bold.woff') format('woff'),
        url('/assets/fonts/artill-paul-grotesk-soft-bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
}

/* LEGACY */
@font-face {
    font-family: 'LLBrownWeb-Thin';
    src: url('/assets/fonts/lineto-brown-thin-s.woff') format('woff'),
        url('/assets/fonts/lineto-brown-thin-s.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'LLBrownWeb-Light';
    src: url('/assets/fonts/lineto-brown-light-s.woff') format('woff'),
        url('/assets/fonts/lineto-brown-light-s.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'LLBrownWeb-Regular';
    src: url('/assets/fonts/lineto-brown-regular-s.woff') format('woff'),
        url('/assets/fonts/lineto-brown-regular-s.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'LLBrownWeb-Bold';
    src: url('/assets/fonts/lineto-brown-bold-s.woff') format('woff'),
        url('/assets/fonts/lineto-brown-bold-s.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'PaulGroteskSoft-Thin';
    src: url('/assets/fonts/artill-paul-grotesk-soft-thin.woff') format('woff'),
        url('/assets/fonts/artill-paul-grotesk-soft-thin.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'PaulGroteskSoft-Light';
    src: url('/assets/fonts/artill-paul-grotesk-soft-light.woff') format('woff'),
        url('/assets/fonts/artill-paul-grotesk-soft-light.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'PaulGroteskSoft-Regular';
    src: url('/assets/fonts/artill-paul-grotesk-soft-regular.woff') format('woff'),
        url('/assets/fonts/artill-paul-grotesk-soft-regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'PaulGroteskSoft-SemiBold';
    src: url('/assets/fonts/artill-paul-grotesk-soft-semibold.woff') format('woff'),
        url('/assets/fonts/artill-paul-grotesk-soft-semibold.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'PaulGroteskSoft-Bold';
    src: url('/assets/fonts/artill-paul-grotesk-soft-bold.woff') format('woff'),
        url('/assets/fonts/artill-paul-grotesk-soft-bold.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
