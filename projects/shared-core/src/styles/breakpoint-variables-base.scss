$screen-xs: 480px;
$screen-phone: $screen-xs;

$size-s: 768px;
$size-tablet: $size-s;

$size-m: 992px;
$size-desktop: $size-m;

$size-l: 1200px;
$size-l-desktop: $size-l;

$screen-to-xs: 'only screen and (max-width: 479px)';
$screen-from-xs: 'only screen and (min-width: 480px)';

$screen-to-s: 'only screen and (max-width: 767px)';
$screen-from-s: 'only screen and (min-width: 768px)';

$screen-to-m: 'only screen and (max-width: 991px)';
$screen-from-m: 'only screen and (min-width: 992px)';

$screen-to-l: 'only screen and (max-width: 1199px)';
$screen-from-l: 'only screen and (min-width: 1200px)';
