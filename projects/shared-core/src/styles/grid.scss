@import 'ds-variables.scss';
.container,
.container-fluid {
    &:not(.container--no-padding) {
        @include container-padding();
    }
}
.container {
    @include container-width();
}
.container-expand {
    @include container-expand();
}
.row {
    @include container-expand();
    display: flex;
    flex: 0 1 auto;
    flex-wrap: wrap;
}
.row.reverse {
    flex-direction: row-reverse;
}
.col.reverse {
    flex-direction: column-reverse;
}
.col,
[class*='col-'] {
    box-sizing: border-box;
    flex: 0 0 auto;
    @include container-padding();
}
.col,
.col-xs {
    flex-grow: 1;
    flex-basis: 0;
    max-width: 100%;
}
.col-1,
.col-xs-1 {
    flex-basis: 8.33333333%;
    max-width: 8.33333333%;
}
.col-2,
.col-xs-2 {
    flex-basis: 16.66666667%;
    max-width: 16.66666667%;
}
.col-3,
.col-xs-3 {
    flex-basis: 25%;
    max-width: 25%;
}
.col-4,
.col-xs-4 {
    flex-basis: 33.33333333%;
    max-width: 33.33333333%;
}
.col-5,
.col-xs-5 {
    flex-basis: 41.66666667%;
    max-width: 41.66666667%;
}
.col-6,
.col-xs-6 {
    flex-basis: 50%;
    max-width: 50%;
}
.col-7,
.col-xs-7 {
    flex-basis: 58.33333333%;
    max-width: 58.33333333%;
}
.col-8,
.col-xs-8 {
    flex-basis: 66.66666667%;
    max-width: 66.66666667%;
}
.col-9,
.col-xs-9 {
    flex-basis: 75%;
    max-width: 75%;
}
.col-10,
.col-xs-10 {
    flex-basis: 83.33333333%;
    max-width: 83.33333333%;
}
.col-11,
.col-xs-11 {
    flex-basis: 91.66666667%;
    max-width: 91.66666667%;
}
.row > div:not([class*='col-']),
.col-12,
.col-xs-12 {
    flex-basis: 100%;
    max-width: 100%;
}
.col-offset-0,
.col-xs-offset-0 {
    margin-left: 0;
}
.col-offset-1,
.col-xs-offset-1 {
    margin-left: 8.33333333%;
}
.col-offset-2,
.col-xs-offset-2 {
    margin-left: 16.66666667%;
}
.col-offset-3,
.col-xs-offset-3 {
    margin-left: 25%;
}
.col-offset-4,
.col-xs-offset-4 {
    margin-left: 33.33333333%;
}
.col-offset-5,
.col-xs-offset-5 {
    margin-left: 41.66666667%;
}
.col-offset-6,
.col-xs-offset-6 {
    margin-left: 50%;
}
.col-offset-7,
.col-xs-offset-7 {
    margin-left: 58.33333333%;
}
.col-offset-8,
.col-xs-offset-8 {
    margin-left: 66.66666667%;
}
.col-offset-9,
.col-xs-offset-9 {
    margin-left: 75%;
}
.col-offset-10,
.col-xs-offset-10 {
    margin-left: 83.33333333%;
}
.col-offset-11,
.col-xs-offset-11 {
    margin-left: 91.66666667%;
}

@media screen and ($screen-from-s) {
    .col-sm {
        flex-grow: 1;
        flex-basis: 0;
        max-width: 100%;
    }
    .col-sm-1 {
        flex-basis: 8.33333333%;
        max-width: 8.33333333%;
    }
    .col-sm-2 {
        flex-basis: 16.66666667%;
        max-width: 16.66666667%;
    }
    .col-sm-3 {
        flex-basis: 25%;
        max-width: 25%;
    }
    .col-sm-4 {
        flex-basis: 33.33333333%;
        max-width: 33.33333333%;
    }
    .col-sm-5 {
        flex-basis: 41.66666667%;
        max-width: 41.66666667%;
    }
    .col-sm-6 {
        flex-basis: 50%;
        max-width: 50%;
    }
    .col-sm-7 {
        flex-basis: 58.33333333%;
        max-width: 58.33333333%;
    }
    .col-sm-8 {
        flex-basis: 66.66666667%;
        max-width: 66.66666667%;
    }
    .col-sm-9 {
        flex-basis: 75%;
        max-width: 75%;
    }
    .col-sm-10 {
        flex-basis: 83.33333333%;
        max-width: 83.33333333%;
    }
    .col-sm-11 {
        flex-basis: 91.66666667%;
        max-width: 91.66666667%;
    }
    .col-sm-12 {
        flex-basis: 100%;
        max-width: 100%;
    }
    .col-sm-offset-0 {
        margin-left: 0;
    }
    .col-sm-offset-1 {
        margin-left: 8.33333333%;
    }
    .col-sm-offset-2 {
        margin-left: 16.66666667%;
    }
    .col-sm-offset-3 {
        margin-left: 25%;
    }
    .col-sm-offset-4 {
        margin-left: 33.33333333%;
    }
    .col-sm-offset-5 {
        margin-left: 41.66666667%;
    }
    .col-sm-offset-6 {
        margin-left: 50%;
    }
    .col-sm-offset-7 {
        margin-left: 58.33333333%;
    }
    .col-sm-offset-8 {
        margin-left: 66.66666667%;
    }
    .col-sm-offset-9 {
        margin-left: 75%;
    }
    .col-sm-offset-10 {
        margin-left: 83.33333333%;
    }
    .col-sm-offset-11 {
        margin-left: 91.66666667%;
    }
}

@media screen and ($screen-from-m) {
    .col-md {
        flex-grow: 1;
        flex-basis: 0;
        max-width: 100%;
    }
    .col-md-1 {
        flex-basis: 8.33333333%;
        max-width: 8.33333333%;
    }
    .col-md-2 {
        flex-basis: 16.66666667%;
        max-width: 16.66666667%;
    }
    .col-md-3 {
        flex-basis: 25%;
        max-width: 25%;
    }
    .col-md-4 {
        flex-basis: 33.33333333%;
        max-width: 33.33333333%;
    }
    .col-md-5 {
        flex-basis: 41.66666667%;
        max-width: 41.66666667%;
    }
    .col-md-6 {
        flex-basis: 50%;
        max-width: 50%;
    }
    .col-md-7 {
        flex-basis: 58.33333333%;
        max-width: 58.33333333%;
    }
    .col-md-8 {
        flex-basis: 66.66666667%;
        max-width: 66.66666667%;
    }
    .col-md-9 {
        flex-basis: 75%;
        max-width: 75%;
    }
    .col-md-10 {
        flex-basis: 83.33333333%;
        max-width: 83.33333333%;
    }
    .col-md-11 {
        flex-basis: 91.66666667%;
        max-width: 91.66666667%;
    }
    .col-md-12 {
        flex-basis: 100%;
        max-width: 100%;
    }
    .col-md-offset-0 {
        margin-left: 0;
    }
    .col-md-offset-1 {
        margin-left: 8.33333333%;
    }
    .col-md-offset-2 {
        margin-left: 16.66666667%;
    }
    .col-md-offset-3 {
        margin-left: 25%;
    }
    .col-md-offset-4 {
        margin-left: 33.33333333%;
    }
    .col-md-offset-5 {
        margin-left: 41.66666667%;
    }
    .col-md-offset-6 {
        margin-left: 50%;
    }
    .col-md-offset-7 {
        margin-left: 58.33333333%;
    }
    .col-md-offset-8 {
        margin-left: 66.66666667%;
    }
    .col-md-offset-9 {
        margin-left: 75%;
    }
    .col-md-offset-10 {
        margin-left: 83.33333333%;
    }
    .col-md-offset-11 {
        margin-left: 91.66666667%;
    }
}

@media screen and ($screen-from-l) {
    .col-lg {
        flex-grow: 1;
        flex-basis: 0;
        max-width: 100%;
    }
    .col-lg-1 {
        flex-basis: 8.33333333%;
        max-width: 8.33333333%;
    }
    .col-lg-2 {
        flex-basis: 16.66666667%;
        max-width: 16.66666667%;
    }
    .col-lg-3 {
        flex-basis: 25%;
        max-width: 25%;
    }
    .col-lg-4 {
        flex-basis: 33.33333333%;
        max-width: 33.33333333%;
    }
    .col-lg-5 {
        flex-basis: 41.66666667%;
        max-width: 41.66666667%;
    }
    .col-lg-6 {
        flex-basis: 50%;
        max-width: 50%;
    }
    .col-lg-7 {
        flex-basis: 58.33333333%;
        max-width: 58.33333333%;
    }
    .col-lg-8 {
        flex-basis: 66.66666667%;
        max-width: 66.66666667%;
    }
    .col-lg-9 {
        flex-basis: 75%;
        max-width: 75%;
    }
    .col-lg-10 {
        flex-basis: 83.33333333%;
        max-width: 83.33333333%;
    }
    .col-lg-11 {
        flex-basis: 91.66666667%;
        max-width: 91.66666667%;
    }
    .col-lg-12 {
        flex-basis: 100%;
        max-width: 100%;
    }
    .col-lg-offset-0 {
        margin-left: 0;
    }
    .col-lg-offset-1 {
        margin-left: 8.33333333%;
    }
    .col-lg-offset-2 {
        margin-left: 16.66666667%;
    }
    .col-lg-offset-3 {
        margin-left: 25%;
    }
    .col-lg-offset-4 {
        margin-left: 33.33333333%;
    }
    .col-lg-offset-5 {
        margin-left: 41.66666667%;
    }
    .col-lg-offset-6 {
        margin-left: 50%;
    }
    .col-lg-offset-7 {
        margin-left: 58.33333333%;
    }
    .col-lg-offset-8 {
        margin-left: 66.66666667%;
    }
    .col-lg-offset-9 {
        margin-left: 75%;
    }
    .col-lg-offset-10 {
        margin-left: 83.33333333%;
    }
    .col-lg-offset-11 {
        margin-left: 91.66666667%;
    }
}
