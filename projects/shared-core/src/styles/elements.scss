
*,
*:before,
*:after {
    box-sizing: inherit;
}

html {
    height: 100%;
    overflow: hidden;
    font-size: var(--font-size-root);
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: var(--body-line-height);
    font-size: var(--body-font-size);
    color: var(--body-font-color);
    letter-spacing: var(--body-letter-spacing);
}

a {
    cursor: pointer;
    color: var(--link-color);
    text-decoration: var(--link-text-decoration);
}

a:visited {
    color: var(--link-color-visited);
    text-decoration: var(--link-text-decoration-visited);
}

a:hover {
    color: var(--link-color-hover);
    text-decoration: var(--link-text-decoration-hover);
}

a:active {
    color: var(--link-color-active);
    text-decoration: var(--link-text-decoration-active);
}

a:focus {
    color: var(--link-color-focus);
    text-decoration: var(--link-text-decoration-focus);
}

a:disabled,
a[disabled='true'] {
    color: var(--link-color-disabled);
    text-decoration: var(--link-text-decoration-disabled);
    cursor: not-allowed;
}

p {
    margin: 0;
}

pre,
dl,
dt,
dd {
    margin: 0;
}

hr {
    margin: 0;
    border: 0;
    border-bottom: var(--hr-border-width) var(--hr-border-style) var(--hr-border-color);
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
}

table {
    border-spacing: 0;
    border-collapse: collapse;
    background-color: transparent;
}

td,
th {
    padding: 0;
    text-align: left;
}

form,
fieldset,
legend,
label,
input,
select,
textarea,
button {
    margin: 0;
    padding: 0;
    border: 0;
    background-color: transparent;
}

button,
[type='button'],
[type='reset'],
[type='submit'] {
    -webkit-appearance: none;
}

input[type='email'],
input[type='file'],
input[type='number'],
input[type='password'],
input[type='search'],
input[type='tel'],
input[type='url'],
input[type='text'],
textarea,
select {
    background-color: var(--form-element-background-color);
    border-radius: var(--form-element-border-radius);
    border: var(--form-element-border-width) var(--form-element-border-style) var(--form-element-border-color);
    box-shadow: var(--form-element-box-shadow-size) var(--form-element-box-shadow-color) inset;
    color: var(--form-element-color);
    height: var(--form-element-regular-height);
    line-height: var(--form-element-regular-line-height);
    width: var(--form-element-width);
    max-width: var(--form-element-max-width);
    font-family: var(--form-element-font-family);
    font-weight: var(--form-element-font-weight);
    font-size: var(--body-font-size);
    padding: 0 var(--form-element-horizontal-padding);
    outline: none;
    transition: border-color 150ms, background-color 150ms, box-shadow 150ms, opacity 150ms;
    &::-webkit-input-placeholder {
        color: var(--form-element-placeholder-color);
        font-style: var(--form-element-placeholder-font-style);
    }
    &:-moz-placeholder {
        color: var(--form-element-placeholder-color);
        font-style: var(--form-element-placeholder-font-style);
    }
    &::-moz-placeholder {
        color: var(--form-element-placeholder-color);
        font-style: var(--form-element-placeholder-font-style);
    }
    &:-ms-input-placeholder {
        color: var(--form-element-placeholder-color);
        font-style: var(--form-element-placeholder-font-style);
    }
    &:hover {
        border-color: var(--form-element-border-color-hover);
    }
    &:active {
        border-color: var(--form-element-border-color-active);
    }
    &:focus {
        border-color: var(--form-element-border-color-focus);
        box-shadow: var(--form-element-focus-ring-size) var(--form-element-focus-ring-color);
    }
    &:disabled {
        background-color: var(--form-element-background-color-disabled) !important;
        border-color: var(--form-element-border-color-disabled);
        color: var(--form-element-color-disabled);
        user-select: none;
        cursor: not-allowed;
        &::-webkit-input-placeholder {
            color: var(--form-element-placeholder-color-disabled);
        }
        &:-moz-placeholder {
            color: var(--form-element-placeholder-color-disabled);
        }
        &::-moz-placeholder {
            color: var(--form-element-placeholder-color-disabled);
        }
        &:-ms-input-placeholder {
            color: var(--form-element-placeholder-color-disabled);
        }
    }
}

input[type='email'],
input[type='file'],
input[type='number'],
input[type='password'],
input[type='search'],
input[type='tel'],
input[type='url'],
input[type='text'] {
    height: var(--input-height);
    &:disabled {
        cursor: not-allowed;
    }
}

form.ng-submitted,
form.ng-touched {
    input[type='email'],
    input[type='file'],
    input[type='number'],
    input[type='password'],
    input[type='search'],
    input[type='tel'],
    input[type='url'],
    input[type='text'] {
        &.ng-touched.ng-invalid {
            background-color: var(--form-element-background-color-error);
            border-color: var(--form-element-border-color-error);
        }
    }
}

select {
    height: var(--select-height);
}

button {
    height: var(--button-height);
}

input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-decoration {
    -webkit-appearance: none;
}

textarea {
    resize: none;
    padding: var(--form-element-horizontal-padding);
    height: auto;
    line-height: var(--body-line-height);
}

legend {
    display: var(--legend-display);
    color: var(--legend-color);
    font-weight: var(--legend-font-weight);
    font-size: var(--legend-font-size);
}

label {
    display: var(--label-display);
    color: var(--label-color);
    font-weight: var(--label-font-weight);
    font-size: var(--label-font-size);
    transition: color 150ms, opacity 150ms;
}
.radio input[type='radio'],
.checkbox input[type='checkbox'] {
    position: absolute;
    left: 0;
    opacity: 0;
    width: 0;
    height: 0;
    margin: 0;
    border: none;
    overflow: hidden;
    z-index: -1;
    pointer-events: none;
}
.checkbox input[type='checkbox'] + label,
.radio input[type='radio'] + label {
    position: relative;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    font-size: inherit;
    width: var(--checkbox-wrapper-width);
    padding: var(--checkbox-wrapper-padding-vertical) var(--checkbox-wrapper-padding-horizontal);
    color: var(--checkbox-label-color);
    border-radius: var(--checkbox-wrapper-border-radius);
    box-shadow: var(--checkbox-wrapper-box-shadow-size) var(--checkbox-wrapper-box-shadow-color);
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    > *,
    > *:before,
    > *:after {
        pointer-events: none;
    }
}
.checkbox input[type='checkbox']:disabled + label,
.radio input[type='radio']:disabled + label {
    cursor: not-allowed;
    color: var(--checkbox-label-color-disabled);
}
.checkbox {
    input[type='checkbox'] + label {
        min-height: var(--checkbox-width);
    }

    input[type='checkbox'] + label:before {
        content: '';
        display: inline-block;
        align-self: flex-start;
        width: var(--checkbox-width);
        min-width: var(--checkbox-width);
        height: var(--checkbox-width);
        margin-right: var(--checkbox-label-margin);
        background-color: var(--checkbox-background);
        border-radius: var(--checkbox-border-radius);
        border: var(--checkbox-border-width) solid var(--checkbox-border-color);
        will-change: background-color, border-color;
        transition: background-color 150ms, border-color 150ms;
    }

    input[type='checkbox']:checked + label:before {
        background-color: var(--checkbox-background-checked);
        border-color: var(--checkbox-border-color-checked);
    }

    input[type='checkbox']:disabled + label:before {
        background-color: var(--checkbox-background-disabled) !important;
        border-color: var(--checkbox-border-color-disabled) !important;
    }

    input[type='checkbox']:checked:disabled + label:before {
        background-color: var(--checkbox-background-disabled-checked) !important;
        border-color: var(--checkbox-border-color-disabled) !important;
    }

    input[type='checkbox']:not(:checked) + label:hover:before {
        background-color: var(--checkbox-background-hover);
        border-color: var(--checkbox-border-color-hover);
    }

    input[type='checkbox']:focus + label:before {
        border-color: var(--checkbox-border-color-hover);
    }

    input[type='checkbox'] + label:after {
        content: var(--checkbox-check-content);
        display: block;
        width: calc(var(--checkbox-width) - (var(--checkbox-check-spacing) * 2));
        height: calc(var(--checkbox-width) - (var(--checkbox-check-spacing) * 2));
        position: absolute;
        top: calc(var(--checkbox-wrapper-padding-vertical) + var(--checkbox-check-spacing));
        left: calc(var(--checkbox-wrapper-padding-horizontal) + var(--checkbox-check-spacing));
        visibility: hidden;
    }

    input[type='checkbox']:checked + label:after {
        visibility: visible;
    }
}
.radio {
    input[type='radio'] + label {
        min-height: var(--radio-width);
    }

    input[type='radio'] + label:before {
        content: '';
        display: inline-block;
        align-self: flex-start;
        width: var(--radio-width);
        min-width: var(--radio-width);
        height: var(--radio-width);
        margin-right: var(--radio-label-margin);
        border-radius: var(--radio-border-radius);
        border: var(--radio-border-width) solid var(--radio-border-color);
        background-color: var(--radio-background-color);
        will-change: background-color, border-color;
        transition: background-color 150ms, border-color 150ms;
    }

    input[type='radio']:checked + label:before {
        background-color: var(--radio-background-color-checked);
        border-color: var(--radio-border-color-checked);
    }

    input[type='radio']:disabled + label:before {
        background-color: var(--radio-background-color-disabled) !important;
        border-color: var(--radio-border-color-disabled) !important;
    }

    input[type='radio']:checked:disabled + label:before {
        background-color: var(--radio-background-color-disabled-checked) !important;
        border-color: var(--radio-border-color-disabled) !important;
    }

    input[type='radio']:not(:checked) + label:hover:before {
        background-color: var(--radio-background-color-hover);
        border-color: var(--radio-border-color-hover);
    }

    input[type='radio']:focus + label:before {
        border-color: var(--radio-border-color-hover);
    }

    input[type='radio'] + label:after {
        content: '';
        display: block;
        background-color: var(--radio-bullet-background-color);
        border-radius: var(--radio-border-radius);
        width: calc(var(--radio-width) - (var(--radio-bullet-spacing) * 2));
        height: calc(var(--radio-width) - (var(--radio-bullet-spacing) * 2));
        position: absolute;
        top: calc(var(--checkbox-wrapper-padding-vertical) + var(--radio-bullet-spacing));
        left: calc(var(--checkbox-wrapper-padding-horizontal) + var(--radio-bullet-spacing));
        visibility: hidden;
    }

    input[type='radio']:checked + label:after {
        visibility: visible;
    }
}

input.date {
    max-width: 150px;
}
.ds-table {
    font-size: var(--datatable-font-size);
    font-family: var(--datatable-font-family);
    border-radius: var(--datatable-border-radius);
}
table.ds-table {
    border-collapse: separate;
    width: 100%;
    > thead tr th {
        padding: 0 var(--datatable-cell-horizontal-padding) var(--datatable-cell-vertical-padding);
        font-weight: var(--datatable-header-cell-font-weight);
        font-size: var(--datatable-header-cell-font-size);
    }
    > tbody tr td {
        padding: var(--datatable-cell-vertical-padding) var(--datatable-cell-horizontal-padding);
    }
    > tbody tr td {
        background-color: var(--datatable-row-background-color);
        &.index-column {
            background-color: transparent;
        }
    }
    > tbody > tr > td {
        border-bottom: var(--datatable-border-width) solid var(--datatable-border-color);
        &:last-child {
            border-right: var(--datatable-border-width) solid var(--datatable-border-color);
        }
    }
    &.ds-table--index-column > tbody tr > td:first-child {
        border-bottom: none;
    }
    > tbody tr:first-child td:not(.index-column) {
        border-top: var(--datatable-border-width) solid var(--datatable-border-color);
    }
    &:not(.ds-table--index-column) > tbody tr td:first-child,
    &.ds-table--index-column > tbody tr td:nth-child(2) {
        border-left: var(--datatable-border-width) solid var(--datatable-border-color);
    }
    > tbody tr:first-child td:first-child,
    &.ds-table--index-column > tbody tr:first-child td:nth-child(2) {
        border-top-left-radius: var(--datatable-border-radius);
    }
    > tbody tr:first-child td:last-child {
        border-top-right-radius: var(--datatable-border-radius);
    }
    > tbody tr:last-child td:first-child,
    &.ds-table--index-column > tbody tr:last-child td:nth-child(2) {
        border-bottom-left-radius: var(--datatable-border-radius);
    }
    > tbody tr:last-child td:last-child {
        border-bottom-right-radius: var(--datatable-border-radius);
    }
    > tbody tr:nth-child(even) td:not(.index-column) {
        background-color: var(--datatable-row-even-background-color);
    }
}
div.ds-table {
    display: flex;
    flex-direction: column;
    &--2-columns > div.ds-table__row > div {
        width: 50%;
    }
    &--3-columns > div.ds-table__row > div {
        width: calc(100% / 3);
    }
    &--4-columns > div.ds-table__row > div {
        width: calc(100% / 4);
    }
    &--5-columns > div.ds-table__row > div {
        width: calc(100% / 5);
    }
    &--6-columns > div.ds-table__row > div {
        width: calc(100% / 6);
    }
    &--7-columns > div.ds-table__row > div {
        width: calc(100% / 7);
    }
    &--8-columns > div.ds-table__row > div {
        width: calc(100% / 8);
    }
    &--9-columns > div.ds-table__row > div {
        width: calc(100% / 9);
    }
    &--10-columns > div.ds-table__row > div {
        width: calc(100% / 10);
    }
    > div.ds-table__row {
        display: flex;
        @media screen and ($screen-to-s) {
            flex-direction: column;
        }
        &--header {
            font-weight: var(--datatable-header-cell-font-weight);
            font-size: var(--datatable-header-cell-font-size);
        }
        &--header + div.ds-table__row {
            border-top-left-radius: var(--datatable-border-radius);
            border-top-right-radius: var(--datatable-border-radius);
        }
        &:last-of-type {
            border-bottom-left-radius: var(--datatable-border-radius);
            border-bottom-right-radius: var(--datatable-border-radius);
        }
    }
    > div.ds-table__row > div {
        padding: var(--datatable-cell-vertical-padding) var(--datatable-cell-horizontal-padding);
    }
    > div.ds-table__row:not(.ds-table__row--header) > div {
        background-color: var(--datatable-row-background-color);
        &.index-column {
            background-color: transparent;
        }
    }
    > div.ds-table__row > div:not(:last-of-type) {
        padding-right: 0;
    }
    > div.ds-table__row:not(.ds-table__row--header) > div:not(:last-of-type) {
        @media screen and ($screen-to-s) {
            padding-bottom: 0;
        }
    }
    > div.ds-table__row > div {
        flex: 1;
        display: flex;
        flex-direction: column;
        @media screen and ($screen-to-s) {
            width: auto !important;
        }
        @media screen and ($screen-from-s) {
            flex-direction: row;
            align-items: center;
        }
    }
    > div.ds-table__row:not(.ds-table__row--header) > div {
        @media screen and ($screen-to-s) {
            border-left: var(--datatable-border-width) solid var(--datatable-border-color);
            border-right: var(--datatable-border-width) solid var(--datatable-border-color);
        }
        @media screen and ($screen-from-s) {
            border-bottom: var(--datatable-border-width) solid var(--datatable-border-color);
        }
    }
    > div.ds-table__row:not(.ds-table__row--header):first-of-type > div:first-of-type,
    > div.ds-table__row--header + .ds-table__row > div:first-of-type:not(.index-column) {
        @media screen and ($screen-to-s) {
            border-top: var(--datatable-border-width) solid var(--datatable-border-color);
        }
    }
    > div.ds-table__row:nth-child(even) > div:not(.index-column) {
        background-color: var(--datatable-row-even-background-color);
    }
    > div.ds-table__row:not(.ds-table__row--header) > div:first-of-type {
        border-left: var(--datatable-border-width) solid var(--datatable-border-color);
    }
    > div.ds-table__row:not(.ds-table__row--header) > div:last-of-type {
        border-right: var(--datatable-border-width) solid var(--datatable-border-color);
        @media screen and ($screen-to-s) {
            border-bottom: var(--datatable-border-width) solid var(--datatable-border-color);
        }
    }

    @media screen and ($screen-to-s) {
        > div.ds-table__row:first-of-type:not(.ds-table__row--header) > div:first-of-type,
        > div.ds-table__row:first-of-type:not(.ds-table__row--header) > div:only-of-type,
        > div.ds-table__row--header + .ds-table__row > div:first-of-type,
        > div.ds-table__row--header + .ds-table__row > div:only-of-type {
            border-top-left-radius: var(--datatable-border-radius);
            border-top-right-radius: var(--datatable-border-radius);
        }
        > div.ds-table__row:last-child > div:last-of-type,
        > div.ds-table__row:last-child > div:only-of-type {
            border-bottom-left-radius: var(--datatable-border-radius);
            border-bottom-right-radius: var(--datatable-border-radius);
        }
    }

    @media screen and ($screen-from-s) {
        > div.ds-table__row:not(.ds-table__row--header):first-of-type > div:not(.index-column),
        > div.ds-table__row--header + div.ds-table__row > div:not(.index-column) {
            border-top: var(--datatable-border-width) solid var(--datatable-border-color);
        }
        > div.ds-table__row:first-of-type:not(.ds-table__row--header) > div:last-child,
        > div.ds-table__row--header + div.ds-table__row > div:last-of-type {
            border-top-right-radius: var(--datatable-border-radius);
        }
        > div.ds-table__row:not(.ds-table__row--header):first-of-type > div:first-of-type,
        > div.ds-table__row--header + div.ds-table__row > div:first-child:not(.index-column),
        > div.ds-table__row--header + div.ds-table__row > div.index-column + div {
            border-top-left-radius: var(--datatable-border-radius);
        }
        > div.ds-table__row:last-of-type > div:first-of-type {
            border-bottom-left-radius: var(--datatable-border-radius);
        }
        > div.ds-table__row:last-of-type > div:last-of-type {
            border-bottom-right-radius: var(--datatable-border-radius);
        }
    }
}
