@import 'color-variables-base.scss';
:root {
    --color-primary-lighten-5: #{$color-primary-lighten-5};
    --color-primary-lighten-4: #{$color-primary-lighten-4};
    --color-primary-lighten-3: #{$color-primary-lighten-3};
    --color-primary-lighten-2: #{$color-primary-lighten-2};
    --color-primary-lighten-1: #{$color-primary-lighten-1};
    --color-primary: #{$color-primary};
    --color-primary-darken-1: #{$color-primary-darken-1};
    --color-primary-darken-2: #{$color-primary-darken-2};
    --color-primary-darken-3: #{$color-primary-darken-3};
    --color-primary-darken-4: #{$color-primary-darken-4};
    --color-primary-darken-5: #{$color-primary-darken-5};

    --color-white: #{$color-white};
    --color-black: #{$color-black};
    --color-grey-lighten-5: #{$color-grey-lighten-5};
    --color-grey-lighten-4: #{$color-grey-lighten-4};
    --color-grey-lighten-3: #{$color-grey-lighten-3};
    --color-grey-lighten-2: #{$color-grey-lighten-2};
    --color-grey-lighten-1: #{$color-grey-lighten-1};
    --color-grey: #{$color-grey};
    --color-grey-darken-1: #{$color-grey-darken-1};
    --color-grey-darken-2: #{$color-grey-darken-2};
    --color-grey-darken-3: #{$color-grey-darken-3};
    --color-grey-darken-4: #{$color-grey-darken-4};
    --color-grey-darken-5: #{$color-grey-darken-5};

    --color-brand-1-lighten-5: #{$color-brand-1-lighten-5};
    --color-brand-1-lighten-4: #{$color-brand-1-lighten-4};
    --color-brand-1-lighten-3: #{$color-brand-1-lighten-3};
    --color-brand-1-lighten-2: #{$color-brand-1-lighten-2};
    --color-brand-1-lighten-1: #{$color-brand-1-lighten-1};
    --color-brand-1: #{$color-brand-1};
    --color-brand-1-darken-1: #{$color-brand-1-darken-1};
    --color-brand-1-darken-2: #{$color-brand-1-darken-2};
    --color-brand-1-darken-3: #{$color-brand-1-darken-3};
    --color-brand-1-darken-4: #{$color-brand-1-darken-4};
    --color-brand-1-darken-5: #{$color-brand-1-darken-5};

    --color-brand-2-lighten-5: #{$color-brand-2-lighten-5};
    --color-brand-2-lighten-4: #{$color-brand-2-lighten-4};
    --color-brand-2-lighten-3: #{$color-brand-2-lighten-3};
    --color-brand-2-lighten-2: #{$color-brand-2-lighten-2};
    --color-brand-2-lighten-1: #{$color-brand-2-lighten-1};
    --color-brand-2: #{$color-brand-2};
    --color-brand-2-darken-1: #{$color-brand-2-darken-1};
    --color-brand-2-darken-2: #{$color-brand-2-darken-2};
    --color-brand-2-darken-3: #{$color-brand-2-darken-3};
    --color-brand-2-darken-4: #{$color-brand-2-darken-4};
    --color-brand-2-darken-5: #{$color-brand-2-darken-5};

    --color-brand-3-lighten-5: #{$color-brand-3-lighten-5};
    --color-brand-3-lighten-4: #{$color-brand-3-lighten-4};
    --color-brand-3-lighten-3: #{$color-brand-3-lighten-3};
    --color-brand-3-lighten-2: #{$color-brand-3-lighten-2};
    --color-brand-3-lighten-1: #{$color-brand-3-lighten-1};
    --color-brand-3: #{$color-brand-3};
    --color-brand-3-darken-1: #{$color-brand-3-darken-1};
    --color-brand-3-darken-2: #{$color-brand-3-darken-2};
    --color-brand-3-darken-3: #{$color-brand-3-darken-3};
    --color-brand-3-darken-4: #{$color-brand-3-darken-4};
    --color-brand-3-darken-5: #{$color-brand-3-darken-5};

    --color-information-1-lighten-5: #{$color-information-1-lighten-5};
    --color-information-1-lighten-4: #{$color-information-1-lighten-4};
    --color-information-1-lighten-3: #{$color-information-1-lighten-3};
    --color-information-1-lighten-2: #{$color-information-1-lighten-2};
    --color-information-1-lighten-1: #{$color-information-1-lighten-1};
    --color-information-1: #{$color-information-1};
    --color-information-1-darken-1: #{$color-information-1-darken-1};
    --color-information-1-darken-2: #{$color-information-1-darken-2};
    --color-information-1-darken-3: #{$color-information-1-darken-3};
    --color-information-1-darken-4: #{$color-information-1-darken-4};
    --color-information-1-darken-5: #{$color-information-1-darken-5};

    --color-information-2-lighten-5: #{$color-information-2-lighten-5};
    --color-information-2-lighten-4: #{$color-information-2-lighten-4};
    --color-information-2-lighten-3: #{$color-information-2-lighten-3};
    --color-information-2-lighten-2: #{$color-information-2-lighten-2};
    --color-information-2-lighten-1: #{$color-information-2-lighten-1};
    --color-information-2: #{$color-information-2};
    --color-information-2-darken-1: #{$color-information-2-darken-1};
    --color-information-2-darken-2: #{$color-information-2-darken-2};
    --color-information-2-darken-3: #{$color-information-2-darken-3};
    --color-information-2-darken-4: #{$color-information-2-darken-4};
    --color-information-2-darken-5: #{$color-information-2-darken-5};

    --text-black: #{$text-black};
    --text-grey: #{$text-grey};
    --text-red: #{$text-red};

    --body-font-color: #{$color-body};
    --link-color: #{$link-color};
    --link-color-active: #{$link-color-active};
    --link-color-disabled: #{$link-color-disabled};
    --link-color-focus: #{$link-color-focus};
    --link-color-hover: #{$link-color-hover};
    --link-color-visited: #{$link-color-visited};

    --form-element-color: #{$form-element-color};
    --form-element-color-disabled: #{$form-element-color-disabled};
    --form-element-color-error: #{$form-element-color-error};
    --form-element-background-color: #{$form-element-background-color};
    --form-element-background-color-hover: #{$form-element-background-color-hover};
    --form-element-background-color-active: #{$form-element-background-color-active};
    --form-element-background-color-focus: #{$form-element-background-color-focus};
    --form-element-background-color-disabled: #{$form-element-background-color-disabled};
    --form-element-background-color-error: #{$form-element-background-color-error};
    --form-element-border-color: #{$form-element-border-color};
    --form-element-border-color-hover: #{$form-element-border-color-hover};
    --form-element-border-color-active: #{$form-element-border-color-active};
    --form-element-border-color-focus: #{$form-element-border-color-focus};
    --form-element-border-color-disabled: #{$form-element-border-color-disabled};
    --form-element-border-color-error: #{$form-element-border-color-error};
    --form-element-box-shadow-color: #{$form-element-box-shadow-color};
    --form-element-placeholder-color: #{$form-element-placeholder-color};
    --form-element-placeholder-color-disabled: #{$form-element-placeholder-color-disabled};
    --form-element-focus-ring-color: #{$form-element-focus-ring-color};
    --form-element-check-icon-color: #{$form-element-check-icon-color};
    --label-color: #{$label-color};
    --label-color-disabled: #{$label-color-disabled};
    --label-required-indicator-color: #{$label-required-indicator-color};
    --legend-color: #{$legend-color};
    --hr-border-color: #{$hr-border-color};

    --button-primary-color: #{$color-white};
    --button-primary-color-hover: #{$color-white};
    --button-primary-color-focus: #{$color-white};
    --button-primary-color-active: #{$color-white};
    --button-primary-color-disabled: #{$color-white};
    --button-primary-background-color: #{$color-primary};
    --button-primary-background-color-hover: #{$color-primary-lighten-2};
    --button-primary-background-color-focus: #{$color-primary-lighten-2};
    --button-primary-background-color-active: #{$color-primary};
    --button-primary-background-color-disabled: #{$color-grey-darken-1};
    --button-primary-border-color: #{$color-primary};
    --button-primary-border-color-hover: #{$color-primary-lighten-2};
    --button-primary-border-color-focus: #{$color-primary-lighten-2};
    --button-primary-border-color-active: #{$color-primary};
    --button-primary-border-color-disabled: #{$color-grey-darken-1};
    --button-primary-box-shadow-color: rgba($text-black, 0.15);
    --button-primary-box-shadow-color-hover: rgba($text-black, 0.2);
    --button-primary-box-shadow-color-focus: rgba($text-black, 0.2);
    --button-primary-box-shadow-color-active: rgba($text-black, 0.2);
    --button-primary-box-shadow-color-disabled: rgba($text-black, 0.15);
    --button-primary-text-shadow-color: transparent;
    --button-primary-text-shadow-color-hover: transparent;
    --button-primary-text-shadow-color-focus: transparent;
    --button-primary-text-shadow-color-active: transparent;
    --button-primary-text-shadow-color-disabled: #{$color-grey-darken-1};
    --button-primary-icon-fill: #{$color-white};
    --button-primary-icon-fill-hover: #{$color-white};
    --button-primary-icon-fill-focus: #{$color-white};
    --button-primary-icon-fill-active: #{$color-white};
    --button-primary-icon-fill-disabled: #{$color-white};
    --button-secondary-color: #{$color-primary};
    --button-secondary-color-hover: #{$color-primary-lighten-2};
    --button-secondary-color-focus: #{$color-primary-lighten-2};
    --button-secondary-color-active: #{$color-primary};
    --button-secondary-color-disabled: #{$color-grey-darken-1};
    --button-secondary-background-color: transparent;
    --button-secondary-background-color-hover: transparent;
    --button-secondary-background-color-focus: transparent;
    --button-secondary-background-color-active: transparent;
    --button-secondary-background-color-disabled: transparent;
    --button-secondary-border-color: #{$color-primary};
    --button-secondary-border-color-hover: #{$color-primary-lighten-2};
    --button-secondary-border-color-focus: #{$color-primary-lighten-2};
    --button-secondary-border-color-active: #{$color-primary};
    --button-secondary-border-color-disabled: #{$color-grey-darken-1};
    --button-secondary-box-shadow-color: rgba($text-black, 0.15);
    --button-secondary-box-shadow-color-hover: rgba($text-black, 0.2);
    --button-secondary-box-shadow-color-focus: rgba($text-black, 0.2);
    --button-secondary-box-shadow-color-active: rgba($text-black, 0.2);
    --button-secondary-box-shadow-color-disabled: rgba($text-black, 0.15);
    --button-secondary-text-shadow-color: transparent;
    --button-secondary-text-shadow-color-hover: transparent;
    --button-secondary-text-shadow-color-focus: transparent;
    --button-secondary-text-shadow-color-active: transparent;
    --button-secondary-text-shadow-color-disabled: transparent;
    --button-secondary-icon-fill: #{$color-primary};
    --button-secondary-icon-fill-hover: #{$color-primary-lighten-2};
    --button-secondary-icon-fill-focus: #{$color-primary-lighten-2};
    --button-secondary-icon-fill-active: #{$color-primary};
    --button-secondary-icon-fill-disabled: #{$color-grey-darken-1};
    --button-link-color: #{$color-primary};
    --button-link-color-hover: #{$color-primary-lighten-2};
    --button-link-color-focus: #{$color-primary-lighten-2};
    --button-link-color-active: #{$color-primary};
    --button-link-color-disabled: #{$color-grey-darken-1};
    --button-link-background-color: transparent;
    --button-link-background-color-hover: transparent;
    --button-link-background-color-focus: transparent;
    --button-link-background-color-active: transparent;
    --button-link-background-color-disabled: transparent;
    --button-link-border-color: transparent;
    --button-link-border-color-hover: transparent;
    --button-link-border-color-focus: transparent;
    --button-link-border-color-active: transparent;
    --button-link-border-color-disabled: transparent;
    --button-link-box-shadow-color: transparent;
    --button-link-box-shadow-color-hover: transparent;
    --button-link-box-shadow-color-focus: transparent;
    --button-link-box-shadow-color-active: transparent;
    --button-link-box-shadow-color-disabled: transparent;
    --button-link-text-shadow-color: transparent;
    --button-link-text-shadow-color-hover: transparent;
    --button-link-text-shadow-color-focus: transparent;
    --button-link-text-shadow-color-active: transparent;
    --button-link-text-shadow-color-disabled: transparent;
    --button-link-icon-fill: #{$color-primary};
    --button-link-icon-fill-hover: #{$color-primary-lighten-2};
    --button-link-icon-fill-focus: #{$color-primary-lighten-2};
    --button-link-icon-fill-active: #{$color-primary};
    --button-link-icon-fill-disabled: #{$color-grey-darken-1};
    --button-loading-dash-color: transparent;
    --button-loading-shadow-color: rgba(#fff, 0.5);

    --modal-window-background-color: #{$color-white};
    --modal-window-box-shadow-color: #{$color-primary};
    --modal-backdrop-background-color: rgba(0, 0, 0, 0.1);

    --search-icon-fill-color: #{$color-grey-lighten-1};
    --search-icon-fill-color-hover: #{$color-primary};

    --datatable-border-color: #{$color-grey-lighten-1};
    --datatable-footer-background-color: #{$color-grey-lighten-1};
    --datatable-header-background-color: transparent;
    --datatable-header-border-color: transparent;
    --datatable-pagination-button-icon-fill: #{$color-primary};
    --datatable-row-background-color: transparent;
    --datatable-row-background-color-selected: transparent;
    --datatable-row-border-color: #{$color-grey-lighten-1};
    --datatable-row-border-color-hover: #{$color-grey-lighten-1};
    --datatable-row-even-background-color: transparent;

    --spinner-dash-color: #{$color-primary};
    --spinner-shadow-color: rgba($text-black, 0.1);

    --checkbox-background: transparent;
    --checkbox-background-checked: #{$color-information-1};
    --checkbox-background-disabled: #{$form-element-background-color-disabled};
    --checkbox-background-disabled-checked: #{$form-element-background-color-disabled};
    --checkbox-background-focused: transparent;
    --checkbox-background-hover: transparent;
    --checkbox-border-color: #{$form-element-border-color};
    --checkbox-border-color-checked: --color-information-1-darken;
    --checkbox-border-color-disabled: #{$form-element-border-color-disabled};
    --checkbox-border-color-hover: #{$form-element-border-color-hover};
    --checkbox-check-icon-color: #{$form-element-check-icon-color};
    --checkbox-label-color: #{$form-element-color};
    --checkbox-label-color-checked: #{$form-element-color};
    --checkbox-label-color-disabled: #{$form-element-color-disabled};
    --checkbox-label-color-disabled-checked: transparent;
    --checkbox-label-color-hover: #{$form-element-color};
    --checkbox-wrapper-box-shadow-color: transparent;

    --radio-background-color: #{$form-element-background-color};
    --radio-background-color-checked: #{$color-information-1};
    --radio-background-color-disabled: #{$form-element-background-color-disabled};
    --radio-background-color-disabled-checked: #{$form-element-background-color-disabled};
    --radio-background-color-focused: #{$form-element-background-color};
    --radio-background-color-hover: #{$form-element-background-color};
    --radio-border-color: #{$form-element-border-color};
    --radio-border-color-checked: #{$color-primary-darken-1};
    --radio-border-color-disabled: #{$form-element-border-color-disabled};
    --radio-border-color-hover: #{$form-element-border-color-hover};
    --radio-label-color: #{$form-element-color};
    --radio-label-color-checked: #{$form-element-color};
    --radio-label-color-disabled: #{$form-element-color-disabled};
    --radio-label-color-hover: #{$form-element-color};
    --radio-bullet-background-color: #{$form-element-background-color};
    --radio-bullet-background-color-checked: #{$form-element-check-icon-color};
    --radio-bullet-background-color-disabled: #{$form-element-background-color-disabled};
    --radio-bullet-background-color-disabled-checked: #{$form-element-background-color-disabled};

    --dropdown-color: inherit;
    --dropdown-input-background-color: #{$form-element-background-color};
    --dropdown-input-border-color: #{$form-element-border-color};
    --dropdown-options-panel-background-color: #{$form-element-background-color};
    --dropdown-options-panel-border-color: #{$form-element-border-color};
    --dropdown-option-background-color: #fff;
    --dropdown-option-background-color-hover: rgba($color-primary, 0.08);
    --dropdown-option-background-color-selected: rgba($color-primary, 0.15);
    --dropdown-option-background-color-selected-hover: rgba($color-primary, 0.15);
    --dropdown-clear-button-color: #{$color-grey-lighten-1};
    --dropdown-clear-button-color-hover: #{$color-primary};
    --dropdown-arrow-color: #{$color-grey-darken-2};
    --dropdown-arrow-color-hover: #{$color-primary};
    --dropdown-arrow-color-open: #{$color-primary};
    --dropdown-multiselect-selected-background: #{$color-grey-lighten-3};
    --dropdown-multiselect-selected-color: #{$color-primary-darken-1};

    --icon-fill: #{$color-grey-darken-5};

    --actions-dropdown-toggle-fill: #{$color-grey-darken-3};
    --actions-dropdown-toggle-fill-hover: #{$color-primary};
    --actions-dropdown-option-color-hover: #{$color-primary};

    --color-severity-high: #{$color-severity-high};
    --color-wellbeing-great: #{$color-wellbeing-great};
    --color-wellbeing-ok: #{$color-wellbeing-ok};
    --color-severity-low: #{$color-severity-low};
    --color-severity-medium: #{$color-severity-medium};
    --color-severity-high: #{$color-severity-high};
}
