@import 'colors';
$animation-duration-normal-in: 0.4s;
$animation-duration-short-in: 0.2s;
$animation-duration-all-out: 0.05s;
$animation-stamp-in-easing: cubic-bezier(0.18, 0.48, 0.36, 0.98);

@-webkit-keyframes pinFocusAnimation {
    0% {
        background: $color-primary-background-accent1;
    }
    50% {
        background: $color-primary-background-accent3;
    }
    100% {
        background: $color-primary-background-accent1;
    }
}

@-moz-keyframes pinFocusAnimation {
    0% {
        background: $color-primary-background-accent1;
    }
    50% {
        background: $color-primary-background-accent3;
    }
    100% {
        background: $color-primary-background-accent1;
    }
}

@-ms-keyframes pinFocusAnimation {
    0% {
        background: $color-primary-background-accent1;
    }
    50% {
        background: $color-primary-background-accent3;
    }
    100% {
        background: $color-primary-background-accent1;
    }
}

@-o-keyframes pinFocusAnimation {
    0% {
        background: $color-primary-background-accent1;
    }
    50% {
        background: $color-primary-background-accent3;
    }
    100% {
        background: $color-primary-background-accent1;
    }
}

@keyframes pinFocusAnimation {
    0% {
        background: $color-primary-background-accent1;
    }
    50% {
        background: $color-primary-background-accent3;
    }
    100% {
        background: $color-primary-background-accent1;
    }
}

@-webkit-keyframes placeHolderShimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

@-moz-keyframes placeHolderShimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

@-ms-keyframes placeHolderShimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

@-o-keyframes placeHolderShimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

@keyframes placeHolderShimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

@-webkit-keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@-moz-keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@-ms-keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@-o-keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
