$white: #fff !default;
$dark: #202020 !default;
$gray: #bebfc2 !default;
$gray-light: #f1f1f2 !default;
$gray-light2: #f0f1f2 !default;
$gray-mid-dark: #8f9093 !default;
$gray-dark: #5d5e60 !default;
$gray-very-dark: #333 !default;
$gray-medium: #dfe0e1 !default;
$gray-strong: #b2b3b4 !default;
$gray-very-light: #f9f9fa !default;
$body-text-color: #333333 !default;
$body-text-light-color: #5d5e60 !default;

$brand-n-pink: #ff8485;
$brand-n-blue: #2277ba;
$brand-n-light-blue: #36b3ea;
$brand-n-light-pink: rgb(255, 172, 173);
$brand-n-orange: rgb(255, 194, 47);
$brand-n-turquoise: #2cceb5;
$turquoise-hover: lighten($brand-n-turquoise, 45%);
$turquoise-active: lighten($brand-n-turquoise, 40%);
$brand-n-light-turquoise: rgb(133, 224, 207);
$brand-n-very-light-turquoise: rgb(207, 243, 236);
$brand-n-purple: #832ab8;
$brand-n-light-purple: rgb(227, 210, 239);
$brand-n-very-light-purple: rgb(249, 246, 252);
$brand-n-light-gray: #bdbec0;
$brand-n-medium-gray: #aeafb1;
$brand-n-gray: #87888a;
$turquoise-button: #2dceb5;

$color-brand-2-lighten-4: #e8f7fc;
$color-brand-2-lighten-3: #b4e4f3;
$color-brand-2-lighten-2: #83cfe8;
$color-brand-2-lighten-1: #53badb;
$color-brand-2: #25a4cc;
$color-brand-2-darken-1: #1d8cb2;
$color-brand-2-darken-2: #167498;
$color-brand-2-darken-3: #105d7c;
$color-brand-2-darken-4: #0a4660;
$color-brand-2-darken-5: #062f42;

$color-brand-clinic-warning: $color-brand-2-lighten-2;

$brand-new-red: #d0021b;
$brand-new-pink: #ff8585;
$brand-new-purple: #7e56a6;
$brand-new-light-purple: #b89ed1;

$background-color-purple: #f6eaf9;
$chat-background-received: #e8fcf9;

$orangey-red: rgb(246, 58, 52);
$purple: rgb(131, 42, 184);

$patient-priority-0: $brand-n-pink;
$patient-priority-1: $brand-n-pink;
$patient-priority-2: $brand-n-pink;
$patient-priority-3: $brand-n-orange;
$patient-priority-4: $brand-n-orange;
$patient-priority-5: rgb(33, 150, 243);
$patient-priority-6: $brand-n-light-gray;

$intensity-0: rgb(195, 195, 195) !default;
$intensity-1: rgb(54, 179, 234) !default;
$intensity-2: rgb(34, 119, 186) !default;
$intensity-3: rgb(58, 200, 156) !default;
$intensity-4: rgb(150, 204, 100) !default;
$intensity-5: rgb(254, 215, 77) !default;
$intensity-6: rgb(250, 189, 76) !default;
$intensity-7: rgb(247, 102, 57) !default;
$intensity-8: rgb(246, 58, 52) !default;
$intensity-9: rgb(207, 44, 36) !default;
$intensity-10: $intensity-9 !default;

$color-grade-1: $intensity-1;
$color-grade-2: $intensity-5;
$color-grade-3: $intensity-10;

/**
  FUNCTION hovered colors are computed from the base colors
*/
@function compute-hover-color($base) {
    $currentSat: saturation($base);
    $currentLt: lightness($base);
    @return saturate(lighten($base, 1.1 * $currentLt), $currentSat);
}

$intensity-1-hover: compute-hover-color($intensity-1);
$intensity-2-hover: compute-hover-color($intensity-2);
$intensity-3-hover: compute-hover-color($intensity-3);
$intensity-4-hover: compute-hover-color($intensity-4);
$intensity-5-hover: compute-hover-color($intensity-5);
$intensity-6-hover: compute-hover-color($intensity-6);
$intensity-7-hover: compute-hover-color($intensity-7);
$intensity-8-hover: compute-hover-color($intensity-8);
$intensity-9-hover: compute-hover-color($intensity-9);
$intensity-10-hover: compute-hover-color($intensity-10);

$color-grade-1-hover: compute-hover-color($color-grade-1);
$color-grade-2-hover: compute-hover-color($color-grade-2);
$color-grade-3-hover: compute-hover-color($color-grade-3);

$symptom-eased: rgb(121, 206, 167);

$text-color: $gray-dark !default;

$link-color: $brand-n-turquoise !default;
$link-hover-color: $brand-n-purple !default;

$normal-hover-color: rgba(0, 0, 0, 0.05);
$panel-open-color: rgba(255, 194, 47, 0.4);
$panel-open-hover-color: rgba(255, 194, 47, 0.55);

$hover-effect-factor: 8 !default;
$active-effect-factor: 15 !default;
$background-effect-factor: 0.25 !default;

$color-primary: #885db3;
$color-primary-grad1: #ad86cd;
$color-primary-grad2: #643299;
$color-primary-hover: darken($color-primary, $hover-effect-factor);
$color-primary-active: darken($color-primary, $active-effect-factor);

$color-primary-background: #f4f0f8;
$color-primary-background-accent1: saturate(darken(#f4f0f8, 3), 10%);
$color-primary-background-accent2: saturate(darken(#f4f0f8, 6), 20%);
$color-primary-background-accent3: saturate(darken(#f4f0f8, 9), 20%);

$color-primary-background-dark: $color-primary;
$color-primary-background-dark-accent1: saturate(darken($color-primary, 5), 5%);
$color-primary-background-dark-accent2: saturate(darken($color-primary, 10), 5%);
$color-primary-background-dark-accent3: saturate(darken($color-primary, 15), 5%);

$background-primary-grad: linear-gradient(130deg, $color-primary-grad1, $color-primary-grad2);
$background-primary-grad-hover: linear-gradient(
    130deg,
    saturate(darken($color-primary-grad1, 5), 10%),
    saturate(darken($color-primary-grad2, 5), 10%)
);
$background-primary-grad-active: linear-gradient(
    130deg,
    saturate(darken($color-primary-grad1, 10), 15%),
    saturate(darken($color-primary-grad2, 10), 15%)
);
$background-primary-grad-sm: linear-gradient(130deg, $color-primary, $color-primary-grad2);

$color-clinical: #25a4cc;
$color-clinical-grad1: #6fc8e4;
$color-clinical-grad2: #25a4cc;
$color-clinical-hover: darken($color-clinical, $hover-effect-factor);
$color-clinical-active: darken($color-clinical, $active-effect-factor);

$color-clinical-background: #e8f8fa;
$color-clinical-background-accent1: saturate(darken($color-clinical-background, 5), 10%);
$color-clinical-background-accent2: saturate(darken($color-clinical-background, 10), 15%);
$color-clinical-background-accent3: saturate(darken($color-clinical-background, 15), 15%);

$background-clinical-grad: linear-gradient(130deg, $color-clinical-grad1, $color-clinical-grad2);
$background-clinical-grad-hover: linear-gradient(
    130deg,
    saturate(darken($color-clinical-grad1, 5), 10%),
    saturate(darken($color-clinical-grad2, 5), 10%)
);
$background-clinical-grad-active: linear-gradient(
    130deg,
    saturate(darken($color-clinical-grad1, 10), 15%),
    saturate(darken($color-clinical-grad2, 10), 15%)
);
$background-clinical-grad-sm: linear-gradient(130deg, $color-primary, $color-clinical-grad2);

$color-gray: #808080;
$color-gray-light: #ccc;
$color-gray-lighter: #eee;
$color-transparent: rgba(255, 255, 255, 0);
$color-gray-background: #f4f4f4;
$color-gray-background-hover: darken($color-gray-background, 0.1 * $hover-effect-factor);
$color-gray-background-active: darken($color-gray-background, 0.5 * $active-effect-factor);

$color-read-gray: #e8e8e8;

$color-white-background-hover: darken(#ffffff, 2%);
$color-white-background-active: darken(#ffffff, 5%);
$color-white-background-hint-hover: darken(#ffffff, 1%);
$color-white-background-hint-active: darken(#ffffff, 2%);

$color-text: #333333;
$color-text-muted: #a4a4a4;

$color-error-text: #fa7078;
$color-severity-low: #f4d94a;
$color-severity-medium: #f4a84a;
$color-severity-high: #fa7078;
$color-danger: #f77279;
$color-danger-background: opacify($color-danger, $background-effect-factor);
$color-wellbeing-ok: #8cbf70;
$color-wellbeing-great: #5fda6e;

$color-info-message-green: #7ad1b9;

$color-info-green: #79d385;
$color-info-green-hover: darken($color-info-green, $hover-effect-factor);
$color-info-green-active: darken($color-info-green, $active-effect-factor);
$color-info-green-background: opacify($color-info-green, $background-effect-factor);

$color-info-red: #f66d74;
$color-info-red-hover: darken($color-info-red, $hover-effect-factor);
$color-info-red-active: darken($color-info-red, $active-effect-factor);
$color-info-red-background: opacify($color-info-red, $background-effect-factor);

$btn-link-disabled-color: $gray-light !default;
