$screen-xs: 480px !default;
$screen-phone: $screen-xs !default;

$screen-large-phone: 370px !default;

$screen-sm: 768px !default;
$screen-tablet: $screen-sm !default;

$screen-md: 992px !default;
$screen-desktop: $screen-md !default;

$screen-lg: 1200px !default;
$screen-lg-desktop: $screen-lg !default;

$screen-xs-max: $screen-sm - 1 !default;
$screen-sm-max: $screen-md - 1 !default;
$screen-md-max: $screen-lg - 1 !default;

@function minw($w) {
    @return '(min-width:' + $w + ')';
}

@function maxw($w) {
    @return '(max-width:' + $w + ')';
}

@function min-max($min-w, $max-w) {
    @return minw($min-w) + ' and ' + maxw($max-w);
}

@function screen-min($w) {
    @return 'screen and ' + minw($w);
}

@function screen-max($w) {
    @return 'screen and ' + maxw($w);
}

@function screen-min-max($min-w, $max-w) {
    @return 'screen and ' + min-max($min-w, $max-w);
}

$media-min-phone: minw($screen-xs) !default;
$media-min-tablet: minw($screen-sm) !default;
$media-min-desktop: minw($screen-md) !default;
$media-min-lg-desktop: minw($screen-lg) !default;

$media-max-sm: maxw($screen-sm) !default;

$media-max-phone-only: maxw($screen-phone + 1) !default;
$media-max-phone: maxw($screen-xs-max) !default;
$media-max-tablet: maxw($screen-sm-max) !default;
$media-max-desktop: maxw($screen-md-max) !default;

$media-screen-min-phone: screen-min($screen-xs) !default;
$media-screen-min-tablet: screen-min($screen-sm) !default;
$media-screen-min-desktop: screen-min($screen-md) !default;
$media-screen-min-lg-desktop: screen-min($screen-lg) !default;

$media-large-phones: screen-min-max($screen-large-phone, $screen-xs) !default;

$media-tablet: min-max($screen-sm, $screen-sm-max) !default;
$media-desktop: min-max($screen-md, $screen-md-max) !default;

$media-phone-or-larger: $media-min-phone !default;
$media-tablet-or-larger: $media-min-tablet !default;
