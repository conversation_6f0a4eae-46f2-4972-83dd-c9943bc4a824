@import 'ds-variables.scss';
.clearfix {
    zoom: 1;
}
.clearfix:before,
.clearfix:after {
    content: '';
    display: table;
}
.clearfix:after {
    clear: both;
}
.sticky {
    position: relative;
    position: sticky;
    &--important {
        position: sticky !important;
    }
}
.hidden,
[hidden] {
    display: none;
}
.hide {
    display: none !important;
}
.show {
    display: block !important;
}
.invisible {
    visibility: hidden;
}

@media screen and ($screen-from-xs) {
    .hide-from-xs {
        display: none !important;
    }
}

@media screen and ($screen-from-xs) {
    .hide-to-xs,
    .hidden-xs {
        display: none !important;
    }
}

@media screen and ($screen-from-s) {
    .hide-from-s {
        display: none !important;
    }
}

@media screen and ($screen-to-s) {
    .hide-to-s,
    .hidden-s {
        display: none !important;
    }
}

@media screen and ($screen-from-m) {
    .hide-from-m {
        display: none !important;
    }
}

@media screen and ($screen-to-m) {
    .hide-to-m,
    .hidden-m {
        display: none !important;
    }
}

@media screen and ($screen-from-l) {
    .hide-from-l {
        display: none !important;
    }
}
/*
@media screen and ($screen-to-l) {
  .hide-to-l,
  .hidden-l {
    display: none !important;
  }
}

 @media screen and ($screen-from-xl) {
  .hide-from-xl {
    display: none !important;
  }
}
*/
/*
@media screen and ($screen-to-xl) {
  .hide-to-xl,
  .hidden-xl {
    display: none !important;
  }
}.sr-only {
  border: 0 !important;
  clip: rect(1px, 1px, 1px, 1px) !important;
  -webkit-clip-path: inset(50%) !important;
  clip-path: inset(50%) !important;
  height: 1px !important;
  margin: -1px !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  width: 1px !important;
  white-space: nowrap !important;
}.sr-only-focusable:focus,
.sr-only-focusable:active {
  clip: auto !important;
  -webkit-clip-path: none !important;
  clip-path: none !important;
  height: auto !important;
  margin: auto !important;
  overflow: visible !important;
  width: auto !important;
  white-space: normal !important;
}.flex {
  display: flex;&-item--1 {
    flex: 1;
  }&--column {
    flex-direction: column;
  }&--align-items-top {
    align-items: flex-start;
  }&--align-items-center {
    align-items: center;
  }&--align-items-baseline {
    align-items: baseline;
  }&--align-items-flex-end {
    align-items: flex-end;
  }&--align-items-stretch {
    align-items: stretch;
  }&--justify-content-center {
    justify-content: center;
  }&--justify-content-flex-start {
    justify-content: flex-start;
  }&--justify-content-flex-end {
    justify-content: flex-end;
  }&--justify-content-space-between {
    justify-content: space-between;
  }&--justify-content-space-around {
    justify-content: space-around;
  }
}.ie .flex-item--1 {
  flex: 1 1 auto;
}
*/
@media screen and ($screen-from-xs) {
    .flex-item--1-to-xs {
        flex: 1;
    }
    .flex--column-to-xs {
        flex-direction: column;
    }
    .flex--justify-content-flex-start-to-xs {
        justify-content: flex-start;
        text-align: start;
    }
    .flex--justify-content-center-to-xs {
        justify-content: center;
        text-align: center;
    }
    .flex--justify-content-flex-end-to-xs {
        justify-content: flex-end;
        text-align: end;
    }
    .flex--justify-content-space-around-to-xs {
        justify-content: space-around;
    }
    .flex--justify-content-space-between-to-xs {
        justify-content: space-between;
    }
    .flex--align-items-top-to-xs {
        align-items: flex-start;
    }
    .flex--align-items-center-to-xs {
        align-items: center;
    }
    .flex--align-items-flex-end-to-xs {
        align-items: flex-end;
    }
}

@media screen and ($screen-from-xs) {
    .flex-item--1-from-xs {
        flex: 1;
    }
    .flex--column-from-xs {
        flex-direction: column;
    }
    .flex--justify-content-flex-start-from-xs {
        justify-content: flex-start;
        text-align: start;
    }
    .flex--justify-content-center-from-xs {
        justify-content: center;
        text-align: center;
    }
    .flex--justify-content-flex-end-from-xs {
        justify-content: flex-end;
        text-align: end;
    }
    .flex--justify-content-space-around-from-xs {
        justify-content: space-around;
    }
    .flex--justify-content-space-between-from-xs {
        justify-content: space-between;
    }
    .flex--align-items-top-from-xs {
        align-items: flex-start;
    }
    .flex--align-items-center-from-xs {
        align-items: center;
    }
    .flex--align-items-flex-end-from-xs {
        align-items: flex-end;
    }
}

@media screen and ($screen-to-s) {
    .flex-item--1-to-s {
        flex: 1;
    }
    .flex--column-to-s {
        flex-direction: column;
    }
    .flex--justify-content-flex-start-to-s {
        justify-content: flex-start;
        text-align: start;
    }
    .flex--justify-content-center-to-s {
        justify-content: center;
        text-align: center;
    }
    .flex--justify-content-flex-end-to-s {
        justify-content: flex-end;
        text-align: end;
    }
    .flex--justify-content-space-around-to-s {
        justify-content: space-around;
    }
    .flex--justify-content-space-between-to-s {
        justify-content: space-between;
    }
    .flex--align-items-top-to-s {
        align-items: flex-start;
    }
    .flex--align-items-center-to-s {
        align-items: center;
    }
    .flex--align-items-flex-end-to-s {
        align-items: flex-end;
    }
}

@media screen and ($screen-from-s) {
    .flex-item--1-from-s {
        flex: 1;
    }
    .flex--column-from-s {
        flex-direction: column;
    }
    .flex--justify-content-flex-start-from-s {
        justify-content: flex-start;
        text-align: start;
    }
    .flex--justify-content-center-from-s {
        justify-content: center;
        text-align: center;
    }
    .flex--justify-content-flex-end-from-s {
        justify-content: flex-end;
        text-align: end;
    }
    .flex--justify-content-space-around-from-s {
        justify-content: space-around;
    }
    .flex--justify-content-space-between-from-s {
        justify-content: space-between;
    }
    .flex--align-items-top-from-s {
        align-items: flex-start;
    }
    .flex--align-items-center-from-s {
        align-items: center;
    }
    .flex--align-items-flex-end-from-s {
        align-items: flex-end;
    }
}

@media screen and ($screen-to-m) {
    .flex-item--1-to-m {
        flex: 1;
    }
    .flex--column-to-m {
        flex-direction: column;
    }
    .flex--justify-content-flex-start-to-m {
        justify-content: flex-start;
        text-align: start;
    }
    .flex--justify-content-center-to-m {
        justify-content: center;
        text-align: center;
    }
    .flex--justify-content-flex-end-to-m {
        justify-content: flex-end;
        text-align: end;
    }
    .flex--justify-content-space-around-to-m {
        justify-content: space-around;
    }
    .flex--justify-content-space-between-to-m {
        justify-content: space-between;
    }
    .flex--align-items-top-to-m {
        align-items: flex-start;
    }
    .flex--align-items-center-to-m {
        align-items: center;
    }
    .flex--align-items-flex-end-to-m {
        align-items: flex-end;
    }
}

@media screen and ($screen-from-m) {
    .flex-item--1-from-m {
        flex: 1;
    }
    .flex--column-from-m {
        flex-direction: column;
    }
    .flex--justify-content-flex-start-from-m {
        justify-content: flex-start;
        text-align: start;
    }
    .flex--justify-content-center-from-m {
        justify-content: center;
        text-align: center;
    }
    .flex--justify-content-flex-end-from-m {
        justify-content: flex-end;
        text-align: end;
    }
    .flex--justify-content-space-around-from-m {
        justify-content: space-around;
    }
    .flex--justify-content-space-between-from-m {
        justify-content: space-between;
    }
    .flex--align-items-top-from-m {
        align-items: flex-start;
    }
    .flex--align-items-center-from-m {
        align-items: center;
    }
    .flex--align-items-flex-end-from-m {
        align-items: flex-end;
    }
}

@media screen and ($screen-to-l) {
    .flex-item--1-to-l {
        flex: 1;
    }
    .flex--column-to-l {
        flex-direction: column;
    }
    .flex--justify-content-flex-start-to-l {
        justify-content: flex-start;
    }
    .flex--justify-content-center-to-l {
        justify-content: center;
    }
    .flex--justify-content-flex-end-to-l {
        justify-content: flex-end;
    }
    .flex--justify-content-space-around-to-l {
        justify-content: space-around;
    }
    .flex--justify-content-space-between-to-l {
        justify-content: space-between;
    }
    .flex--align-items-top-to-l {
        align-items: flex-start;
    }
    .flex--align-items-center-to-l {
        align-items: center;
    }
    .flex--align-items-flex-end-to-l {
        align-items: flex-end;
    }
}

@media screen and ($screen-from-l) {
    .flex-item--1-from-l {
        flex: 1;
    }
    .flex--column-from-l {
        flex-direction: column;
    }
    .flex--justify-content-flex-start-from-l {
        justify-content: flex-start;
    }
    .flex--justify-content-center-from-l {
        justify-content: center;
    }
    .flex--justify-content-flex-end-from-l {
        justify-content: flex-end;
    }
    .flex--justify-content-space-around-from-l {
        justify-content: space-around;
    }
    .flex--justify-content-space-between-from-l {
        justify-content: space-between;
    }
    .flex--align-items-top-from-l {
        align-items: flex-start;
    }
    .flex--align-items-center-from-l {
        align-items: center;
    }
    .flex--align-items-flex-end-from-l {
        align-items: flex-end;
    }
}
/*
@media screen and ($screen-to-xl) {
  .flex-item--1-to-xl {
    flex: 1;
  }.flex--column-to-xl {
    flex-direction: column;
  }.flex--justify-content-flex-start-to-xl {
    justify-content: flex-start;
  }.flex--justify-content-center-to-xl {
    justify-content: center;
  }.flex--justify-content-flex-end-to-xl {
    justify-content: flex-end;
  }.flex--justify-content-space-around-to-xl {
    justify-content: space-around;
  }.flex--justify-content-space-between-to-xl {
    justify-content: space-between;
  }.flex--align-items-top-to-xl {
    align-items: flex-start;
  }.flex--align-items-center-to-xl {
    align-items: center;
  }.flex--align-items-flex-end-to-xl {
    align-items: flex-end;
  }
}

 @media screen and ($screen-from-xl) {
  .flex-item--1-from-xl {
    flex: 1;
  }.flex--column-from-xl {
    flex-direction: column;
  }.flex--justify-content-flex-start-from-xl {
    justify-content: flex-start;
  }.flex--justify-content-center-from-xl {
    justify-content: center;
  }.flex--justify-content-flex-end-from-xl {
    justify-content: flex-end;
  }.flex--justify-content-space-around-from-xl {
    justify-content: space-around;
  }.flex--justify-content-space-between-from-xl {
    justify-content: space-between;
  }.flex--align-items-top-from-xl {
    align-items: flex-start;
  }.flex--align-items-center-from-xl {
    align-items: center;
  }.flex--align-items-flex-end-from-xl {
    align-items: flex-end;
  }
}.mt-grid {
  margin-top: var(--spacing-grid);
}.mt-0 {
  margin-top: 0 !important;
}.mt-auto {
  margin-top: auto;
}.mt-xxs {
  margin-top: var(--spacing-xxs);
}.mt-xs {
  margin-top: var(--spacing-xs);
}.mt-s {
  margin-top: var(--spacing-s);
}.mt-m {
  margin-top: var(--spacing-m);
}.mt-l {
  margin-top: var(--spacing-l);
}.mt-xl {
  margin-top: var(--spacing-xl);
}.mt-xxl {
  margin-top: var(--spacing-xxl);
}.mr-grid {
  margin-right: var(--spacing-grid);
}.mr-0 {
  margin-right: 0 !important;
}.mr-auto {
  margin-right: auto;
}.mr-xxs {
  margin-right: var(--spacing-xxs);
}.mr-xs {
  margin-right: var(--spacing-xs);
}.mr-s {
  margin-right: var(--spacing-s);
}.mr-m {
  margin-right: var(--spacing-m);
}.mr-l {
  margin-right: var(--spacing-l);
}.mr-xl {
  margin-right: var(--spacing-xl);
}.mr-xxl {
  margin-right: var(--spacing-xxl);
}.mb-grid {
  margin-bottom: var(--spacing-grid);
}.mb-0 {
  margin-bottom: 0 !important;
}.mb-auto {
  margin-bottom: auto;
}.mb-xxs {
  margin-bottom: var(--spacing-xxs);
}.mb-xs {
  margin-bottom: var(--spacing-xs);
}.mb-s {
  margin-bottom: var(--spacing-s);
}.mb-m {
  margin-bottom: var(--spacing-m);
}.mb-l {
  margin-bottom: var(--spacing-l);
}.mb-xl {
  margin-bottom: var(--spacing-xl);
}.mb-xxl {
  margin-bottom: var(--spacing-xxl);
}.ml-grid {
  margin-left: var(--spacing-grid);
}.ml-0 {
  margin-left: 0 !important;
}.ml-auto {
  margin-left: auto;
}.ml-xxs {
  margin-left: var(--spacing-xxs);
}.ml-xs {
  margin-left: var(--spacing-xs);
}.ml-s {
  margin-left: var(--spacing-s);
}.ml-m {
  margin-left: var(--spacing-m);
}.ml-l {
  margin-left: var(--spacing-l);
}.ml-xl {
  margin-left: var(--spacing-xl);
}.ml-xxl {
  margin-left: var(--spacing-xxl);
}.pt-grid {
  padding-top: var(--spacing-grid);
}.pt-0 {
  padding-top: 0 !important;
}.pt-xxs {
  padding-top: var(--spacing-xxs);
}.pt-xs {
  padding-top: var(--spacing-xs);
}.pt-s {
  padding-top: var(--spacing-s);
}.pt-m {
  padding-top: var(--spacing-m);
}.pt-l {
  padding-top: var(--spacing-l);
}.pt-xl {
  padding-top: var(--spacing-xl);
}.pt-xxl {
  padding-top: var(--spacing-xxl);
}.pr-grid {
  padding-right: var(--spacing-grid);
}.pr-0 {
  padding-right: 0 !important;
}.pr-xxs {
  padding-right: var(--spacing-xxs);
}.pr-xs {
  padding-right: var(--spacing-xs);
}.pr-s {
  padding-right: var(--spacing-s);
}.pr-m {
  padding-right: var(--spacing-m);
}.pr-l {
  padding-right: var(--spacing-l);
}.pr-xl {
  padding-right: var(--spacing-xl);
}.pr-xxl {
  padding-right: var(--spacing-xxl);
}.pb-grid {
  padding-bottom: var(--spacing-grid);
}.pb-0 {
  padding-bottom: 0 !important;
}.pb-xxs {
  padding-bottom: var(--spacing-xxs);
}.pb-xs {
  padding-bottom: var(--spacing-xs);
}.pb-s {
  padding-bottom: var(--spacing-s);
}.pb-m {
  padding-bottom: var(--spacing-m);
}.pb-l {
  padding-bottom: var(--spacing-l);
}.pb-xl {
  padding-bottom: var(--spacing-xl);
}.pb-xxl {
  padding-bottom: var(--spacing-xxl);
}.pl-grid {
  padding-left: var(--spacing-grid);
}.pl-0 {
  padding-left: 0 !important;
}.pl-xxs {
  padding-left: var(--spacing-xxs);
}.pl-xs {
  padding-left: var(--spacing-xs);
}.pl-s {
  padding-left: var(--spacing-s);
}.pl-m {
  padding-left: var(--spacing-m);
}.pl-l {
  padding-left: var(--spacing-l);
}.pl-xl {
  padding-left: var(--spacing-xl);
}.pl-xxl {
  padding-left: var(--spacing-xxl);
}
*/
