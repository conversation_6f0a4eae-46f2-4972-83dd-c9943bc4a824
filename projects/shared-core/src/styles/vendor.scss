@import 'ds-variables.scss';
.ds-tooltip {
    position: absolute;
    width: calc(100% - var(--spacing-grid) * 2);
    font-size: var(--tooltip-font-size);
    user-select: none;
    z-index: var(--z-above-all);
    opacity: 0;
    pointer-events: none;
    transition: opacity 150ms;
    @media screen and ($screen-from-s) {
        width: 100%;
        max-width: var(--tooltip-max-width);
    }
    &--show {
        opacity: 1;
        pointer-events: all;
    }
}
.ds-tooltip__backdrop {
    background: rgba(255, 255, 255, 0.9);
    @media screen and ($screen-from-s) {
        background: transparent;
    }
}
.ds-tooltip__content {
    position: relative;
    background: #fff;
    padding: var(--tooltip-padding-vertical) var(--tooltip-padding-horizontal);
    box-shadow: var(--tooltip-box-shadow);
    border-radius: var(--tooltip-border-radius);
    z-index: 2;
}
.ds-tooltip:before,
.ds-tooltip:after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    transform: translate(-8px, -10px) rotate(45deg);
    border-radius: var(--tooltip-border-radius);
    background: #fff;
    z-index: 3;
}
.ds-tooltip:before {
    z-index: 1;
}
.ds-tooltip--top:before,
.ds-tooltip--top:after {
    top: 100%;
    left: 50%;
}
.ds-tooltip--top:before {
    box-shadow: var(--tooltip-box-shadow);
}
