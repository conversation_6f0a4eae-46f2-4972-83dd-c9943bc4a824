$font-family: 'PaulGroteskSoft' helvetica arial sans-serif;

$font-family-thin: 'PaulGroteskSoft-Thin' helvetica arial sans-serif;
$font-family-light: 'PaulGroteskSoft-Light' helvetica arial sans-serif;
$font-family-regular: 'Paul<PERSON>roteskSoft-Regular' helvetica arial sans-serif;
$font-family-semibold: 'PaulGroteskSoft-SemiBold' helvetica arial sans-serif;
$font-family-bold: 'PaulGroteskSoft-Bold' helvetica arial sans-serif;

$font-size-root: 10px;
$font-size-xxl: 4.8rem;
$font-size-xl: 3.2rem;
$font-size-l: 1.6rem;
$font-size-m: 1.4rem;
$font-size-s: 1.2rem;
$font-size-xs: 1.1rem;

$font-weight-thin: 100;
$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-normal: $font-weight-regular;
$font-weight-semibold: 600;
$font-weight-bold: 700;

$body-font-size: 1.6rem;
$body-small-font-size: 1.4rem;
$body-large-font-size: 1.8rem;
$body-line-height: 2.2rem;
$body-small-line-height: 2rem;
$body-large-line-height: 2.4rem;
$body-letter-spacing: 0;

$link-text-decoration: none;
$link-text-decoration-active: underline;
$link-text-decoration-disabled: none;
$link-text-decoration-focus: none;
$link-text-decoration-hover: underline;
$link-text-decoration-visited: none;

$h1-font-family: $font-family;
$h2-font-family: $font-family;
$h3-font-family: $font-family;
$h4-font-family: $font-family;
$h5-font-family: $font-family;
$h6-font-family: $font-family;
$h1-font-weight: $font-weight-light;
$h2-font-weight: $font-weight-light;
$h3-font-weight: $font-weight-light;
$h4-font-weight: $font-weight-light;
$h5-font-weight: $font-weight-light;
$h6-font-weight: $font-weight-light;

$h1-s-color: inherit;
$h1-s-font-family: $h1-font-family;
$h1-s-font-size: 3.2rem;
$h1-s-font-weight: $h1-font-weight;
$h1-s-letter-spacing: 0;
$h1-s-line-height: 3.8rem;
$h1-m-color: inherit;
$h1-m-font-family: $h1-font-family;
$h1-m-font-size: 3.6rem;
$h1-m-font-weight: $h1-font-weight;
$h1-m-letter-spacing: 0;
$h1-m-line-height: 4.2rem;
$h1-l-color: inherit;
$h1-l-font-family: $h1-font-family;
$h1-l-font-size: 4rem;
$h1-l-font-weight: $h1-font-weight;
$h1-l-letter-spacing: 0;
$h1-l-line-height: 4.8rem;

$h2-s-color: inherit;
$h2-s-font-family: $h2-font-family;
$h2-s-font-size: 2.4rem;
$h2-s-font-weight: $h2-font-weight;
$h2-s-letter-spacing: 0;
$h2-s-line-height: 2.8rem;
$h2-m-color: inherit;
$h2-m-font-family: $h2-font-family;
$h2-m-font-size: 2.6rem;
$h2-m-font-weight: $h2-font-weight;
$h2-m-letter-spacing: 0;
$h2-m-line-height: 3rem;
$h2-l-color: inherit;
$h2-l-font-family: $h2-font-family;
$h2-l-font-size: 2.8rem;
$h2-l-font-weight: $h2-font-weight;
$h2-l-letter-spacing: 0;
$h2-l-line-height: 3.2rem;

$h3-s-color: inherit;
$h3-s-font-family: $h3-font-family;
$h3-s-font-size: 2rem;
$h3-s-font-weight: $h3-font-weight;
$h3-s-letter-spacing: 0;
$h3-s-line-height: 2.4rem;
$h3-m-color: inherit;
$h3-m-font-family: $h3-font-family;
$h3-m-font-size: 2.2rem;
$h3-m-font-weight: $h3-font-weight;
$h3-m-letter-spacing: 0;
$h3-m-line-height: 2.6rem;
$h3-l-color: inherit;
$h3-l-font-family: $h3-font-family;
$h3-l-font-size: 2.4rem;
$h3-l-font-weight: $h3-font-weight;
$h3-l-letter-spacing: 0;
$h3-l-line-height: 2.8rem;

$h4-s-color: inherit;
$h4-s-font-family: $h4-font-family;
$h4-s-font-size: 1.8rem;
$h4-s-font-weight: $h4-font-weight;
$h4-s-letter-spacing: 0;
$h4-s-line-height: 2.2rem;
$h4-m-color: inherit;
$h4-m-font-family: $h4-font-family;
$h4-m-font-size: 2rem;
$h4-m-font-weight: $h4-font-weight;
$h4-m-letter-spacing: 0;
$h4-m-line-height: 2.4rem;
$h4-l-color: inherit;
$h4-l-font-family: $h4-font-family;
$h4-l-font-size: 2.2rem;
$h4-l-font-weight: $h4-font-weight;
$h4-l-letter-spacing: 0;
$h4-l-line-height: 2.6rem;

$h5-s-color: inherit;
$h5-s-font-family: $h5-font-family;
$h5-s-font-size: 1.6rem;
$h5-s-font-weight: $h5-font-weight;
$h5-s-letter-spacing: 0;
$h5-s-line-height: 2rem;
$h5-m-color: inherit;
$h5-m-font-family: $h5-font-family;
$h5-m-font-size: 1.8rem;
$h5-m-font-weight: $h5-font-weight;
$h5-m-letter-spacing: 0;
$h5-m-line-height: 2.2em;
$h5-l-color: inherit;
$h5-l-font-family: $h5-font-family;
$h5-l-font-size: 2rem;
$h5-l-font-weight: $h5-font-weight;
$h5-l-letter-spacing: 0;
$h5-l-line-height: 2.4rem;

$h6-s-color: inherit;
$h6-s-font-family: $h6-font-family;
$h6-s-font-size: 1.5rem;
$h6-s-font-weight: $h6-font-weight;
$h6-s-letter-spacing: 0;
$h6-s-line-height: 1.8rem;
$h6-m-color: inherit;
$h6-m-font-family: $h6-font-family;
$h6-m-font-size: 1.6rem;
$h6-m-font-weight: $h6-font-weight;
$h6-m-letter-spacing: 0;
$h6-m-line-height: 2rem;
$h6-l-color: inherit;
$h6-l-font-family: $h6-font-family;
$h6-l-font-size: 1.8rem;
$h6-l-font-weight: $h6-font-weight;
$h6-l-letter-spacing: 0;
$h6-l-line-height: 2.4rem;

$form-element-font-family: inherit;
$form-element-font-weight: inherit;
$form-element-regular-font-size: 1.4rem;
$form-element-small-font-size: 1.3rem;
$form-element-large-font-size: 1.8rem;
$form-element-placeholder-font-style: normal;
$label-font-weight: $font-weight-semibold;
$label-font-size: inherit;
$legend-font-weight: $font-weight-semibold;
$legend-font-size: inherit;

$button-primary-regular-font-size: 1.6rem;
$button-primary-small-font-size: 1.4rem;
$button-primary-large-font-size: 1.6rem;
$button-secondary-regular-font-size: $button-primary-regular-font-size;
$button-secondary-small-font-size: $button-primary-small-font-size;
$button-secondary-large-font-size: $button-primary-large-font-size;
$button-link-regular-font-size: 1.5rem;
$button-link-small-font-size: 1.4rem;
$button-link-large-font-size: 1.4rem;
$button-primary-regular-font-weight: $font-weight-semibold;
$button-primary-small-font-weight: $font-weight-semibold;
$button-primary-large-font-weight: $font-weight-semibold;
$button-secondary-regular-font-weight: regular;
$button-secondary-small-font-weight: regular;
$button-secondary-large-font-weight: regular;
$button-link-regular-font-weight: $font-weight-semibold;
$button-link-small-font-weight: $font-weight-semibold;
$button-link-large-font-weight: $font-weight-semibold;
$button-primary-text-transform: uppercase;
$button-secondary-text-transform: uppercase;
$button-link-text-transform: uppercase;
$button-primary-text-align: inherit;
$button-secondary-text-align: inherit;
$button-link-text-align: inherit;
$button-link-text-decoration: none;
$button-link-text-decoration-hover: none;
$button-link-text-decoration-focus: none;
$button-primary-font-family: inherit;
$button-secondary-font-family: inherit;
$button-link-font-family: inherit;
$button-primary-font-weight: $font-weight-semibold;
$button-secondary-font-weight: $font-weight-semibold;
$button-link-font-weight: $font-weight-semibold;

$datatable-font-size: inherit;
$datatable-font-family: inherit;
$datatable-header-cell-font-weight: $font-weight-semibold;
$datatable-header-cell-font-size: 1.3rem;

$checkbox-label-line-height: 1.4;
$checkbox-label-font-weight: $label-font-weight;

$dropdown-value-font-style: normal;
$dropdown-value-font-weight: normal;
$dropdown-tooltip-value-font-style: normal;
$dropdown-tooltip-value-font-weight: normal;
$dropdown-option-label-font-weight: normal;
$dropdown-option-label-font-weight-selected: 600;
$dropdown-multiselect-selected-font-weight: 600;

$range-slider-value-font-size: 19px;
