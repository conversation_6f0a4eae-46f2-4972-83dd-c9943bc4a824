@import '../color-variables-base';
@import '../color-variables';
.ng-select {
    display: inline-block;
    width: var(--form-element-width);
    max-width: var(--form-element-max-width);
    height: var(--form-element-regular-height);
    font-family: var(--form-element-font-family);
    font-weight: var(--form-element-font-weight);
    color: var(--dropdown-color);
    &.ds-dropdown--small,
    &--size-small {
        font-size: var(--form-element-small-font-size);
        height: var(--form-element-small-height);
    }
    &.ds-dropdown--large,
    &--size-large {
        font-size: var(--form-element-large-font-size);
        height: var(--form-element-large-height);
    }
    &.ng-select-disabled .ng-placeholder {
        color: var(--form-element-placeholder-color-disabled);
    }
    &:hover .ng-select-container {
        border-color: var(--form-element-border-color-hover);
    }
    &.ng-select-disabled .ng-select-container {
        background-color: var(--form-element-background-color-disabled);
        border-color: var(--form-element-border-color-disabled);
        color: var(--form-element-color-disabled) !important;
    }
    &.ng-select-opened .ng-select-container {
        background-color: var(--form-element-background-color-active);
        border-color: var(--form-element-border-color-active);
        z-index: var(--z-form-element-open);
    }
    &.ng-select-focused:not(.ng-select-opened) > .ng-select-container {
        border-color: var(--form-element-border-color-focus);
        background-color: var(--form-element-background-color-focus);
        box-shadow: var(--form-element-focus-ring-size) var(--form-element-focus-ring-color);
    }
    .ng-select-container {
        background-color: var(--dropdown-input-background-color);
        border-style: var(--dropdown-input-border-style);
        border-width: var(--dropdown-input-border-width);
        border-color: var(--dropdown-input-border-color);
        border-radius: var(--dropdown-input-border-radius);
        height: var(--dropdown-input-regular-height);
        cursor: pointer;
        color: inherit;
        padding: 0 var(--form-element-horizontal-padding);
        transition: border-color 150ms, box-shadow 150ms;
    }
    .ng-arrow-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 150ms;
        &:after {
            display: block;
            content: var(--dropdown-arrow-icon);
            width: 16px;
            height: 16px;
        }
    }
    &.ng-select-opened .ng-arrow-wrapper {
        transform: rotateX(-180deg);
    }
    .ng-arrow {
        display: none;
    }
    .ng-value-container {
        display: flex;
        align-items: center;
    }
    .ng-value {
        font-style: var(--dropdown-value-font-style);
        font-weight: var(--dropdown-value-font-weight);
    }
    .ng-value-container .ng-placeholder {
        color: var(--form-element-placeholder-color);
        font-style: var(--form-element-placeholder-font-style);
        line-height: 1;
    }
    .ng-has-value .ng-placeholder {
        display: none;
    }
    .ng-select-container .ng-value-container .ng-input {
        top: 0;
    }
    .ng-select-container .ng-value-container .ng-input input {
        padding: 0;
        padding-left: var(--form-element-horizontal-padding);
        min-width: auto;
        cursor: pointer;
    }
    .ng-dropdown-panel {
        z-index: var(--z-form-element-open);
    }
}
.ng-dropdown-panel {
    background-color: var(--dropdown-options-panel-background-color);
    border-style: var(--dropdown-options-panel-border-style);
    border-width: var(--dropdown-options-panel-border-width);
    border-color: var(--dropdown-options-panel-border-color);
    border-radius: var(--dropdown-options-panel-border-radius);
    box-shadow: var(--form-element-box-shadow-size) var(--form-element-box-shadow-color);
    .ng-dropdown-panel-items {
        margin: 0;
        max-height: 33vh;
    }
    .ng-option {
        background-color: var(--dropdown-option-background-color);
        font-weight: var(--dropdown-option-label-font-weight);
        padding: var(--spacing-s) var(--form-element-horizontal-padding);
        transition: background-color 150ms, box-shadow 150ms;
    }
    .ng-option:first-of-type {
        border-top-left-radius: var(--form-element-border-radius);
        border-top-right-radius: var(--form-element-border-radius);
    }
    .ng-option:last-child {
        border-bottom-left-radius: var(--form-element-border-radius);
        border-bottom-right-radius: var(--form-element-border-radius);
    }
    .ng-option:not(.ng-option-disabled):hover,
    .ng-option.ng-option-marked {
        background-color: var(--dropdown-option-background-color-hover);
    }
    .ng-option-disabled {
        cursor: not-allowed;
        background-color: var(--form-element-background-color-disabled);
        color: var(--form-element-color-disabled);
    }
    .ng-option.ng-option-selected {
        background-color: var(--dropdown-option-background-color-selected);
        font-weight: var(--dropdown-option-label-font-weight-selected);
    }
    .ng-option:hover.ng-option-selected {
        background-color: var(--dropdown-option-background-color-selected-hover);
    }
    .ng-dropdown-header {
        border: none;
        padding: var(--spacing-m);
        padding-bottom: 0;
        white-space: nowrap;
    }
}
.ng-select.ng-select-clearable .ng-clear-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: var(--dropdown-regular-width);
    margin-left: var(--spacing-m);
    margin-right: var(--spacing-xs);
    .ng-clear {
        font-size: var(--dropdown-clear-button-font-size);
        color: var(--dropdown-clear-button-color);
    }
    &:hover .ng-clear {
        color: var(--dropdown-clear-button-color-hover);
    }
}
.ng-select.ng-select-multiple {
    .ng-value {
        user-select: none;
        display: flex;
        flex-direction: column-reverse;
        line-height: 2.6rem;
        border-radius: 1.3rem;
        background: var(--dropdown-multiselect-selected-background);
        padding: 0 var(--dropdown-multiselect-selected-horizontal-padding);
        font-weight: var(--dropdown-multiselect-selected-font-weight);
        color: var(--dropdown-multiselect-selected-color);
    }
    .ng-value-label {
        padding-right: 4px;
    }
}
.ng-select.ds-dropdown--tooltip {
    display: inline-block;
    min-width: auto;
    width: auto;
    height: var(--dropdown-tooltip-height);
    -webkit-tap-highlight-color: transparent;
    .ng-select-container {
        min-height: 100%;
        height: 100%;
        width: auto;
        border: none;
        padding: 0;
        box-shadow: none !important;
        background-color: transparent;
        overflow: visible;
    }
    &.ng-select-opened .ng-select-container {
        box-shadow: none;
        z-index: calc(var(--z-form-element-open) + 1);
    }
    .ng-select-container .ng-value-container {
        padding: 0;
        top: 0;
    }
    .ng-value-container .ng-value,
    .ng-value-container .ng-placeholder {
        font-size: var(--form-element-small-font-size);
        color: var(--color-brand-1);
        font-weight: var(--dropdown-tooltip-value-font-weight);
        font-style: var(--dropdown-tooltip-value-font-style);
    }
    .ng-select-container .ng-value-container .ng-input {
        padding: 0;
    }
    .ng-arrow-wrapper {
        padding: 0;
        width: 10px;
        margin-left: var(--spacing-xs);
    }
    &.ng-select-opened .ng-arrow-wrapper {
        z-index: calc(var(--z-form-element-open) + 1);
    }
    .ng-arrow-wrapper:before,
    .ng-arrow-wrapper:after {
        display: block;
        content: '';
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 10px 10px 10px;
        border-color: transparent transparent var(--color-grey) transparent;
        position: absolute;
        top: calc(100% - 4px);
        left: -5px;
        opacity: 0;
    }
    .ng-arrow-wrapper:after {
        border-color: transparent transparent #fff transparent;
        top: calc(100% - 3px);
    }
    &.ng-select-opened .ng-arrow-wrapper:before,
    &.ng-select-opened .ng-arrow-wrapper:after {
        opacity: 1;
    }
    .ng-value {
        line-height: 1;
    }
}
.ng-select.ds-dropdown--tooltip .ng-dropdown-panel {
    width: auto;
    min-width: calc(100% + 10px);
    border-radius: var(--dropdown-tooltip-options-panel-border-radius);
    border-top-width: 1px;
    &.ng-select-bottom {
        top: calc(100% + 5px);
    }
}
.ng-select.ds-dropdown--external-trigger {
    .ng-select-container .ng-value-container .ng-input {
        padding: 0;
    }
}
.ng-dropdown-panel.ds-dropdown--center-options {
    left: 50%;
    transform: translateX(-50%);
}
.ng-select.ds-dropdown--tooltip.ng-select-top {
    .ng-dropdown-panel {
        bottom: calc(100% + 5px);
    }
    .ng-arrow-wrapper:before,
    .ng-arrow-wrapper:after {
        top: -5px;
        transform: rotateX(-180deg);
    }
    .ng-arrow-wrapper:after {
        top: -6px;
    }
}
