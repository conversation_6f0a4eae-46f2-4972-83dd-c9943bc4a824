/*
Creates fully round (circle) button

Usage example (create large outlined primary color button):

  round-button(large, outline, primary)

Possible arguments

  Size:
    large       large button (navbar)
    medium      medium sized
    small       small

  Color:
    primary     primary color (purple)
    clinical    clinical color (turquoise)

  Other:
    outline - white/transparent bg and colored border + text

*/
@mixin round-button($args...) {
  @include flex-layout($column, $center, $center);
  text-align: center;
  border-radius: 50%;
  font-weight: normal;
  overflow: hidden;
  text-decoration: none;
  cursor: pointer;

  @if (large in $args) {
    .noona-icon {
      font-size: $button-round-large-icon-font-size;
    }
    width: $button-round-large-width;
    height: $button-round-large-width;
  }

  @if (medium in $args) {
    .noona-icon {
      font-size: $button-round-medium-icon-font-size;
      width: $button-round-medium-icon-font-size;
      height: $button-round-medium-icon-font-size;
    }
    width: $button-round-medium-width;
    height: $button-round-medium-width;
    //shadow-lg()
  }

  @if (small in $args) {
    .noona-icon {
      font-size: $button-round-small-icon-font-size;
      width: $button-round-small-icon-font-size;
      height: $button-round-small-icon-font-size;
    }
    width: $button-round-small-width;
    height: $button-round-small-width;
        //shadow-md()
  }



  @if (outline in $args) {
    border-width: 2px;
    border-style: solid;
    background-color: $color-transparent;
    @if (primary in $args) {
      color: var(--color-primary);
    }
    @if (clinical in $args) {

      @include round-button-interactive-background($background-clinical-grad, $background-clinical-grad-hover, $background-clinical-grad-active);
    }
    @if (white in $args) {
      color: white;

      @include round-button-interactive-background(alpha($white, 0%), alpha($white, 2%), alpha($white, 4%));
    }
  }

  @if not(outline in $args) {
    color: white;
    @if (primary in $args) {

      @include round-button-interactive-background($background-primary-grad, $background-primary-grad-hover, $background-primary-grad-active);
    }
    @if (clinical in $args) {

      @include round-button-interactive-background($background-clinical-grad, $background-clinical-grad-hover, $background-clinical-grad-active);
    }
    @if (white in $args) {

      @include round-button-interactive-background($white, $color-white-background-hover, $color-white-background-active);
    }
  }
}



/**
  Clicking transition movement for the round button
  @param  baseline scale value used in animations
 */
@mixin round-button-click-transition($baseScale: 1) {
  transform: scale($baseScale) translateY(0);
  transition: transform 0.1s;&:hover {
    @media screen and ($media-min-desktop) {
      transform: scale($baseScale) translateY(0);
    }
  }&:active {
    transform: scale(0.96 * $baseScale) translateY(1.2%);
  }
}



/**
  Changing visuals of a round button when user interacting with the button
 */
@mixin round-button-interactive-background($normalBackground, $hoverBackground: undefined, $activeBackground: undefined) {
  background: $normalBackground;
  @include shadow-md();
  @include text-selection-disable();

  @if $hoverBackground != undefined {
    &:hover {
      @media screen and ($media-min-desktop) {
        background: $hoverBackground;
      }
    }
  }

  @if $activeBackground != undefined {
    &:active {
      background: $activeBackground;
                 // shadow-md-active();
    }
  }
}
