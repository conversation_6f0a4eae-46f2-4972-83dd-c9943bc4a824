/**
  MOTIVATION
  ----------

  When there's a fixed and concise set of text styles audience understand the hierarchy of content presented.


  HOW TO USE
  ----------

  Use top higher level text styles whenever possible. Make exceptions for good reason. When exception needed
  more than once, establish a new class of text.

*/
/**
 Normal and most widely used text
*/
@mixin text-normal($alignment: left, $textColor: $color-text) {
    font-family: $font-family-regular;
    font-size: $font-size-normal;
    line-height: $line-height-lg;
    text-align: $alignment;
    color: $textColor;
    text-transform: unset;
    letter-spacing: normal;
}

/**
 Normal text used in lists being more compact
 @oaram    alignment     for text-alignment
*/
@mixin text-normal-list($alignment: left) {
    @include text-normal($alignment);
    font-family: $font-family-regular;
    line-height: $line-height-sm;
}

/**
 Same as normal, but with bold font
*/
@mixin text-normal-bold($alignment: left) {
    @include text-normal($alignment);
    font-family: $font-family-semibold;
    line-height: $line-height-sm;
}

/**
 Small text, like footnotes, instructions etc
*/
@mixin text-sm($alignment: left) {
    font-family: $font-family-regular;
    font-size: $font-size-small;
    line-height: $line-height-sm;
    letter-spacing: $letter-spacing-normal;
    text-transform: unset;
}

/**
 Medium text that's scaled down from the normal size, some secondary content
*/
@mixin text-md($alignment: left) {
    font-family: $font-family-regular;
    font-size: $font-size-medium;
    line-height: $line-height-md;
    letter-spacing: $letter-spacing-normal;
    text-transform: unset;
}

/**
 Faded down text, typically for secondary labels that assist
*/
@mixin text-faint($alignment: left) {
    font-family: $font-family-regular;
    font-size: $font-size-normal;
    line-height: $line-height-md;
    letter-spacing: $letter-spacing-normal;
    text-transform: unset;
    color: $color-gray-light;
    text-align: $alignment;
}

/**
Style for user input, text fields etc. Used in forms to separate user input content from the rest
*/
@mixin text-user-input() {
    @include font-regular2();
    font-size: $font-size-normal;
    line-height: $line-height-md;
    color: $color-text;
    text-transform: unset;
    font-weight: normal;
}

/**
 Smaller form label when above the user input that has already text
*/
@mixin text-form-label-above() {
    @include font-regular2();
    color: $color-primary;
    font-size: $font-size-xs;
    text-transform: unset;
}

/**
 Form label, placeholder text in text input when nothing input
*/
@mixin text-form-placeholder() {
    @include font-regular2();
    color: $color-primary;
    font-size: $font-size-normal;
    text-transform: unset;
}

/**
 Text style used for links inside text or less-emphasized command
 @param  "clinical" if clinical cyan colors are to be used
*/
@mixin text-link($args...) {
    @include font-bold2();
    @include text-selection-disable();
    font-size: $font-size-small;
    color: $color-primary;
    text-transform: uppercase;
    text-decoration: none;
    @include component-interactive();

    @if clinical in $args {
        color: $color-clinical;
        &:hover {
            color: $color-clinical-hover;
        }
        &:active {
            color: $color-clinical-active;
        }
    }

    @if white in $args {
        color: white;
        &:hover {
            color: $color-primary-background-accent1;
        }
        &:active {
            color: $color-primary-background-accent2;
        }
    } @else {
        color: $color-primary;
        &:hover {
            color: $color-primary-hover;
        }
        &:active {
            color: $color-primary-active;
        }
    }
}

/**
 Text style for heading
*/
@mixin text-heading-xs($alignment: left) {
    @include font-regular2();
    font-size: $font-size-xs;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: $letter-spacing-md;
    text-align: $alignment;
}

@mixin text-heading-sm($alignment: left) {
    @include font-bold2();
    font-size: $font-size-small;
    line-height: $line-height-md;
    text-transform: uppercase;
    letter-spacing: $letter-spacing-sm;
    text-align: $alignment;
}

@mixin text-heading-md($alignment: left, $textColor: $color-text) {
    @include font-semibold2();
    font-size: $font-size-normal;
    font-weight: bold;
    line-height: $line-height-md;
    text-transform: unset;
    text-align: $alignment;
    color: $textColor;
}

@mixin text-heading($alignment: left) {
    @include font-semibold2();
    font-size: $font-size-normal;
    text-transform: unset;
    text-align: $alignment;
    line-height: $line-height-md;
    color: $color-text;
}

@mixin text-heading-lg() {
    @include font-bold2();
    font-size: $font-size-normal;
    text-transform: uppercase;
}

@mixin text-heading-main($alignment: left, $textColor: $color-text) {
    @include font-light2();
    font-size: $font-size-heading-main;
    line-height: $line-height-sm;
    font-weight: normal;
    text-transform: unset;
    color: $textColor;
    text-align: $alignment;
}

@mixin text-heading-section($alignment: left, $textColor: $color-text) {
    @include font-regular2();
    font-size: $font-size-heading-section;
    line-height: $line-height-md;
    font-weight: normal;
    color: $textColor;
    text-align: $alignment;

    @media screen and ($media-min-phone) {
        font-size: $font-size-heading-section;
        text-transform: unset;
        @include font-bold();
        font-weight: normal;
        letter-spacing: $letter-spacing-base;
    }
}

@mixin font-regular() {
    font-weight: normal;
    font-family: 'LLBrownWeb-regular';
    letter-spacing: $letter-spacing-base;
}

@mixin font-light() {
    font-weight: normal;
    font-family: 'LLBrownWeb-Light';
    letter-spacing: $letter-spacing-base;
}

@mixin font-small-caps() {
    @include font-light();
    text-transform: uppercase;
    font-size: 0.95em;
}

@mixin font-italic-light() {
    font-family: 'LLBrownWeb-Light';
    font-style: italic;
}

@mixin font-italic-bold() {
    font-family: 'LLBrownWeb-Bold';
    font-style: italic;
}

@mixin font-size-base() {
    font-size: $font-size-base;
}

@mixin font-size-small() {
    font-size: 14px;
}

@mixin body-text-normal() {
    @include font-regular();
    @include font-size-base();
    line-height: 25px;
    letter-spacing: $letter-spacing-base;
    color: $body-text-color;
}

@mixin heading1-text() {
    @include font-bold();
    font-size: 26px;
    line-height: 30px;
    letter-spacing: $letter-spacing-base;
    text-align: center;
    color: $brand-n-purple;
}

@mixin heading2-text() {
    @include font-bold();
    @include font-size-base();
    line-height: 25px;
    letter-spacing: $letter-spacing-base;
    text-align: center;
    color: $brand-n-purple;
}

@mixin font-bold() {
    font-weight: normal;
    font-family: 'LLBrownWeb-Bold';
    letter-spacing: $letter-spacing-base;
}

@mixin font-thin2() {
    font-family: $font-family-thin;
}

@mixin font-light2() {
    font-family: $font-family-light;
}

@mixin font-regular2() {
    font-family: $font-family-regular;
    font-stretch: normal;
    line-height: normal;
    letter-spacing: normal;
}

@mixin font-semibold2() {
    font-family: $font-family-semibold;
}

@mixin font-bold2() {
    font-family: $font-family-bold;
}

/**
  Disable user text selection
*/
@mixin text-selection-disable() {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
