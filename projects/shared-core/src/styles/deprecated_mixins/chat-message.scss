@mixin sent-chat-message() {
    margin-right: 15px;
    text-align: left;
    float: right;
    background-color: $chat-background-received;
    border-radius: 20px 20px 0 20px;
}

@mixin received-chat-message() {
    margin-left: 15px;
    float: left;
    border-radius: 20px 20px 20px 0;
}

@mixin chat-message-base() {
    @include font-size-base();
    @include font-regular();
    width: auto;
    min-width: 1px;
    max-width: 80%;
    display: block;
    padding: 10px 15px;
    box-shadow: 1px 1px 0 0 rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
}
