@mixin caret($args...) {
    display: inline-block;
    width: 0;
    height: 0;

    @if (primary in $args) {
        border-color: var(--color-primary);
    }
    @if (secondary in $args) {
        border-color: $color-clinical;
    }
    @if (muted in $args) {
        border-color: $color-text-muted;
    }
    @if (danger in $args) {
        border-color: $color-danger;
    }
    @if (gray in $args) {
        border-color: $color-read-gray;
    }
    @if (white in $args) {
        border-color: white;
    }
    @if (green in $args) {
        border-color: green;
    }
    @if (orange in $args) {
        border-color: orange;
    }
    @if (brand_1_lighten_2 in $args) {
        border-color: var(--color-brand-1-lighten-2);
    }
    @if (form_element_background_color_disabled in $args) {
        border-color: var(--form-element-background-color-disabled);
    }

    @if large in $args {
        border-width: 8px;
    }
    @if medium in $args {
        border-width: 6px;
    }
    @if small in $args {
        border-width: 4px;
    }

    @if up in $args {
        border-bottom-style: solid;
        border-left-style: solid;
        border-right-style: solid;
        border-right-color: transparent;
        border-left-color: transparent;
    }
    @if down in $args {
        border-top-style: solid;
        border-left-style: solid;
        border-right-style: solid;
        border-right-color: transparent;
        border-left-color: transparent;
    }
    @if left in $args {
        border-top-style: solid;
        border-right-style: solid;
        border-bottom-style: solid;
        border-top-color: transparent;
        border-bottom-color: transparent;
    }
    @if right in $args {
        border-top-style: solid;
        border-left-style: solid;
        border-bottom-style: solid;
        border-top-color: transparent;
        border-bottom-color: transparent;
    }
}
