/**
  Generic styles for text input boxes
 */
@mixin form-input-text() {
    &,
    &.ng-invalid {
        display: block;
        width: 100%;
        height: 60px;
        border: 0;
        outline: none;
        @include text-user-input();
        background: $color-primary-background;
        border-radius: $border-radius-md;
        transition: all ease-out $signup-input-transition-time;
        border-bottom: 2px solid $color-primary-background;

        margin: $spacing-small -2px -2px -2px;
        padding: $spacing-lg;
        box-shadow: none;
    }
    &:focus,
    &.ng-invalid:focus {
        border: 0;
        box-shadow: none;
        border-bottom-left-radius: 0px;
        border-bottom: 2px solid $color-primary;
        border-bottom-right-radius: 0px;
        background: $color-primary-background;
    }
    &::placeholder {
        @include text-form-placeholder();
        opacity: 1;
        transition: opacity linear 0.4s 0.1s;
    }
    &:focus::placeholder {
        @include text-form-placeholder();
        opacity: 0.4;
    }
}

/**
  Generic styles with embedded label that's shown above the text once text has been input.
  If no user input showing label as placeholder

  For this to work we assume following from DOM & JS:
  - <label> next to input that's hidden when user has input value
  - <input class="filled"> when user has input value
 */
@mixin form-input-text-embedded-label() {
    display: block;

    width: calc(100% + 2px);
    border: none;
    outline: none;
    @include text-user-input();
    background: $color-transparent;
    border-radius: $border-radius-md;
    transition: all ease-out $signup-input-transition-time;
    border-bottom: 2px solid $color-primary-background;

    margin: $spacing-small -2px -2px -2px;
    padding: $spacing-small $spacing-lg $spacing-lg $spacing-lg;
    &:focus:not(.filled) {
        margin: 0 -2px -2px -2px;
        padding: $spacing-lg $spacing-lg $spacing-lg $spacing-lg;
    }
    &.filled:focus {
        margin: 0 -2px -2px -2px;
        padding: $spacing-small $spacing-lg $spacing-small $spacing-lg;
    }
    &.filled {
        margin: 0 -2px -2px -2px;
        padding: $spacing-small $spacing-lg $spacing-small $spacing-lg;
    }
    &:focus {
        border-bottom-left-radius: 0px;
        border-bottom: 2px solid $color-primary;
        border-bottom-right-radius: 0px;
        background: $color-primary-background;
    }
    &::placeholder {
        @include text-form-placeholder();
        opacity: 1;
        transition: opacity linear 0.4s 0.1s;
    }
    &:focus::placeholder {
        @include text-form-placeholder();
        opacity: 0.4;
    }
    &.ng-invalid {
        background: $color-danger-background;
    }
}
