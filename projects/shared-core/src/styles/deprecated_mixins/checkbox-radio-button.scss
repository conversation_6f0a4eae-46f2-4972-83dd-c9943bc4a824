/**
  Checkbox/radio's white frame
 */
@mixin checkbox-base() {
    padding: 0 $checkbox-spacing 0 $checkbox-spacing;
    position: relative;
    margin: $spacing-small 0;
    border-radius: $border-radius-md;
    min-height: $checkbox-frame-height;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.13);
    background: white;
    @include flex-layout($row, $center, $center);
    @include text-selection-disable();
    @include component-interactive($color-white-background-hint-hover, $color-white-background-hint-active);
}

/**
  Checkbox's label receives the clicks.
  Click area is grown to be close to the visual borders of the checkable item
 */
@mixin checkbox-label($checkmark-border-radius) {
    display: inline-block;
    cursor: pointer;
    padding: $spacing-lg 0 $spacing-lg 70px;
    margin-top: -($spacing-small);
    margin-right: $spacing-small;
    margin-bottom: -($spacing-small);
    margin-left: 0;
    line-height: 16px;
    font-size: $font-size-normal;
    @include font-regular2();
    text-transform: none;
    width: 100%;
    color: $color-text;
    @include text-selection-disable();
    &:before {
        content: '';
        display: inline-block;
        width: $checkbox-checkmark-size;
        height: $checkbox-checkmark-size;
        position: absolute;
        background: white;
        border: 2px solid #ddd;
        margin: auto $checkbox-spacing auto $checkbox-spacing;
        border-radius: $checkmark-border-radius;
        transition: all 0.1s ease-out;
        top: 0;
        bottom: 0;
        left: 0;
    }
    &:hover:before {
        border: 2px solid darken(#ddd, $hover-effect-factor);
    }
    &:active:before {
        border: 2px solid darken(#ddd, $active-effect-factor);
    }
    &:after {
        color: #fff;
        content: ' ';
        transform: scale(0.5, 0.5) translate(0px, 5px);
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
    }
}

@mixin checkbox-checked($checkmark-border-radius, $margin-left) {
    & + label {
        &:after {
            transition: all 0.1s ease-out;
            content: '✓';
            @include font-regular2();
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            transform: scale(1, 1) translate(0, 0);
            margin: auto $margin-left;
            height: $checkbox-checkmark-size;
            width: $checkbox-checkmark-size;
            border-radius: $checkmark-border-radius;
            background: $color-info-green;
            border: none;
            font-size: 40px;
            line-height: 52px;
            text-align: center;
            color: white;
        }
        &:hover:after {
            background: $color-info-green-hover;
        }
        &:active:after {
            background: $color-info-green-active;
        }
    }
}

@mixin checkbox-disabled() {
    cursor: default;
    background: $color-white-background-hint-hover;
    & + label {
        opacity: 0.3;
        cursor: default;
        &:before {
            background: #eee;
        }
        &:hover:before {
            border: 1px solid $brand-n-light-gray;
        }
    }
}
