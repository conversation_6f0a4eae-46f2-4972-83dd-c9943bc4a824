/**
  ----------------------------------------------------------------------
  ------------------------------- mixin --------------------------------
  ----------------------------------------------------------------------

  Util to quickly animate element's visibility.
  Use by assigning DOM element to class ".invisible" to hide, or remove class to show again

  Usage example:

    .element
      animate-fade()


  @param short      (optional) use short timing
 */
@mixin animate-fade($args...) {
    @if (short in $args) {
        transition: opacity $animation-duration-short-in ease-in-out;
    } @else {
        transition: opacity $animation-duration-normal-in ease-in-out;
    }
    opacity: 1;
    visibility: visible;
    &.invisible {
        opacity: 0;
        visibility: hidden;
        transition: opacity $animation-duration-all-out ease-in;
    }
}

/**
  ----------------------------------------------------------------------
  ------------------------------- mixin --------------------------------
  ----------------------------------------------------------------------

  Util to quickly animate element having stamp-in kind of transition,
  a great way to animate success / fail type of small indicators.
  Use by assigning DOM element to class ".invisible" to hide, or remove class to show again

  @param short      (optional) use short timing
 */
@mixin animate-slide-right($args...) {
    @if (short in $args) {
        transition: opacity $animation-duration-short-in $animation-stamp-in-easing,
            transform $animation-duration-short-in $animation-stamp-in-easing;
    } @else {
        transition: opacity $animation-duration-normal-in $animation-stamp-in-easing,
            transform $animation-duration-normal-in $animation-stamp-in-easing;
    }
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateX(0);
    transform-origin: center;
    &.invisible {
        opacity: 0;
        visibility: hidden;
        transform: scale(0) translateX(-10%);
        transition: opacity $animation-duration-all-out ease-in, transform $animation-duration-all-out ease-in;
    }
}
