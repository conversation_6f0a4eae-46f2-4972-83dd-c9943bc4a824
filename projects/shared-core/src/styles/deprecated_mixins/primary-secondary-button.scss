/**
  Collection of mixins to style typical and often used components
 */

/**
  Primary-navigation button section, typically next/prev button pair
 */
@mixin navigation-button-section($buttonOrder: undefined) {
    @include flex-layout($row, $center);
    box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    padding: 0;
    @media screen and ($media-min-tablet) {
        margin: 0 auto 0 auto;
        .btn,
        button {
            &:first-child {
                margin-right: $spacing-small;
            }
        }
    }
    .btn,
    button {
        flex: 1;
        min-width: 150px;
    }
    @media screen and ($media-min-phone) {
        box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.2);
        width: auto;
        .btn,
        button {
            margin: 0;
            flex: 1;
        }
    }
    @if $buttonOrder {
        .btn.btn-primary {
            order: 2;
        }
        .btn.btn-secondary {
            order: 1;
        }
    }
}

@mixin navigation-button-column-overwritte() {
    flex-direction: column;
    .btn {
        width: 100%;
    }
}

/*
  Style primary button: violet solid background and interactive colors
*/
@mixin button-primary() {
    @include button-common();
    background-image: $background-primary-grad;
    color: white;
    &:hover {
        @media screen and ($media-min-desktop) {
            background: $background-primary-grad-hover;
        }
    }
    &:active {
        background: $background-primary-grad-active;
    }
    &:disabled {
        background: lighten($color-gray, $hover-effect-factor);
        color: darken($color-gray, $hover-effect-factor);
        cursor: not-allowed;
    }
}

/**
  Style inverted primary button, having white background and primary text color
 */
@mixin button-primary-inverted() {
    @include button-common();
    background: white;
    color: $color-primary;
    &:hover {
        @media ($media-max-phone) {
            background: white;
        }
        background: $color-primary-background-accent1;
    }
    &:active {
        background: $color-primary-background-accent2;
    }
    &:disabled {
        background: white;
        color: $color-text-muted;
        cursor: not-allowed;
    }
}

/**
  Style for smaller primary button
 */
@mixin button-primary-small() {
    @include button-common();
    @include font-regular2();
    text-transform: uppercase;
    padding: $spacing-small;
    height: 40px;
    border-radius: $border-radius-sm;
    color: white;
    background: $background-primary-grad;
    border: 0;
    min-width: 150px;
    &:active {
        background: $color-primary-active;
    }
}

/**
  Style secondary button
 */
@mixin button-secondary() {
    @include button-common();
    background: white;
    color: $color-primary;
    &:hover {
        @media screen and ($media-min-phone) {
            background: white;
        }
        background: darken($white, $hover-effect-factor);
    }
    &:active {
        background: darken($white, $hover-effect-factor);
    }
    &:disabled {
        background: lighten($color-gray, 30);
        color: darken($color-gray, $hover-effect-factor);
    }
}

/**
  Primary button for clinical theme (turquoise)
 */
@mixin button-primary-clinic-theme() {
    @include button-common();
    background-image: $background-clinical-grad;
    color: white;
    &:hover {
        @media screen and ($media-max-phone) {
            background: $color-clinical;
        }
        background: $color-clinical-hover;
    }
    &:active {
        background: $color-clinical-active;
    }
    &:disabled {
        background: lighten($color-gray, $hover-effect-factor);
        color: darken($color-gray, $hover-effect-factor);
    }
}

/**
  Style secondary button for clinical theme (turquoise)
 */
@mixin button-secondary-clinic-theme() {
    @include button-common();
    background: white;
    color: $color-clinical;
    &:hover {
        @media screen and ($media-min-phone) {
            background: white;
        }
        background: darken($white, $hover-effect-factor);
    }
    &:active {
        background: darken($white, $hover-effect-factor);
    }
    &:disabled {
        background: lighten($color-gray, 30);
        color: darken($color-gray, $hover-effect-factor);
    }
}

/**
  Small close x button shown e.g in the top-right corner to close a dialog
 */
@mixin button-close-dialog() {
    @include button-small-common();
    position: fixed;
    transform: translate3d(0, 0, 0);
    right: $padding-modal + $spacing-small;
    top: $padding-modal + $spacing-small;
    background: white;
    @include shadow();
    &:after {
        font-size: 30px;
        line-height: 28px;
        content: $symbol-cross-close;
    }
}

/**
  Small close x button shown e.g in the top-right corner to close a tooltip
  It's positioned to the very top-right corner of it's container
 */
@mixin button-close-small() {
    @include button-small-common();
    &:after {
        content: $symbol-cross-close;
    }
}

/**
  Small close x button shown e.g in the top-right corner to close a tooltip
  It's positioned to the very top-right corner of it's container
 */
@mixin button-info-small() {
    @include button-small-common();
    &:after {
        content: 'ℹ';
    }
}

/*
  Base mixin for all small buttons
 */
@mixin button-small-common() {
    display: block;
    opacity: 1;
    color: $color-primary;
    float: none;
    padding: 1px;
    width: $button-close-small-size;
    height: $button-close-small-size;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 50%;
    text-align: center;
    position: relative;
    right: 0;
    top: 0;
    cursor: pointer;
    @include round-button-click-transition();
    &:hover {
        color: $color-primary-hover;
        background: $color-white-background-hover;
    }
    &:active {
        color: $color-primary-active;
        background: $color-white-background-active;
    }
    &:after {
        @include font-regular2();
        font-size: 30px;
        line-height: 1;
        text-align: center;
        @media screen and ($media-min-phone) {
            font-size: 24px;
        }
    }
}

/**
  Base mixin for top level buttons
 */
@mixin button-common() {
    text-align: center;
    font-weight: normal;
    overflow: hidden;
    text-decoration: none;
    cursor: pointer;
    height: 65px;
    min-height: 65px;
    font-size: $font-size-medium;
    margin: 0;
    border-radius: 0;
    white-space: normal;
    @include font-regular2();
    &:disabled {
        cursor: not-allowed;
    }
}

@mixin floating-button-container() {
    margin-top: auto;
}

/**
  Action button, surrounded with a box, typically in the middle of dialog to do an action
 */
@mixin button-action() {
    padding: 0 $spacing-base;
    margin-bottom: 1px;
    background: $color-primary-background;
    height: 60px;
    box-shadow: none;
    &:active {
        background: $color-primary-background-accent1;
    }
}

/**
  Secondary action button, same as action button but with white background and shadow
 */
@mixin button-action-secondary() {
    padding: 0 $spacing-base;
    margin-bottom: 1px;
    background: $color-primary-background;
    height: 60px;
    @include shadow();
    &:active {
        background: $color-primary-background-accent1;
    }
}

/**
  Changing visuals of a round button when user interacting with the button
 */
@mixin button-interactive-background($normalBackground, $hoverBackground: undefined, $activeBackground: undefined) {
    background: $normalBackground;

    @if $hoverBackground != undefined {
        &:hover {
            @media screen and ($media-min-desktop) {
                background: $hoverBackground;
            }
        }
    }

    @if $activeBackground != undefined {
        &:active {
            background: $activeBackground;
        }
    }
}
