/**
  Quickly configure flex layout, for example

    flex-layout(column, center, center)

    // by default gives a row
    flex-layout()
 */
@mixin flex-layout($direction: row, $content-justification: normal, $item-alignment: normal) {
    display: flex;
    flex-direction: $direction;
    @if $content-justification {
        justify-content: $content-justification;
    }
    @if $item-alignment {
        align-items: $item-alignment;
    }
}

@mixin flex-layout-centered() {
    align-items: center;
    justify-content: center;
    text-align: center;
}

@mixin flex-item-flexible() {
    flex: auto;
}

@mixin flex-item-inflexible() {
    flex: none;
}
