/**
 Compensate container's padding so that content inside can grow to full page width
*/
@mixin container-expand() {
    margin-left: -($spacing-base);
    margin-right: -($spacing-base);
}

/**
 Apply standard paddings to the given container
*/
@mixin container-contract() {
    padding-left: $spacing-base;
    padding-right: $spacing-base;
}

@mixin modal-expand() {
    margin-left: -($padding-modal);
    margin-right: -($padding-modal);
}

@mixin main-page-container-breakpoints() {
    @media screen and ($media-tablet) {
        width: $content-width-tablet;
        margin-left: auto;
        margin-right: auto;
    }

    @media screen and ($media-desktop) {
        width: $content-width-desktop;
        margin-left: auto;
        margin-right: auto;
    }

    @media screen and ($media-screen-min-lg-desktop) {
        width: $content-width-lg-desktop;
        margin-left: auto;
        margin-right: auto;
    }
}

@mixin main-page-container-widths() {
    @media screen and ($media-tablet) {
        width: $content-width-tablet;
    }

    @media screen and ($media-desktop) {
        width: $content-width-desktop;
    }

    @media screen and ($media-screen-min-lg-desktop) {
        width: $content-width-lg-desktop;
    }
}
