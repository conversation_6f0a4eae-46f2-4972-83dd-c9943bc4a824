/**
 Toggle component styles
*/
@mixin toggle-button-group() {
    @include checkbox-base();
    &:hover,
    &:active {
        background: white;
    }

    flex-direction: row-reverse;
    padding: 0 0 0 $spacing-base;
    .toggles {
        height: 100%;
        width: unset;
        .toggle-container {
            &:nth-child(2) {
                .toggle-option {
                    border-bottom-right-radius: $border-radius-md;
                    border-top-right-radius: $border-radius-md;
                }
            }

            margin: 0;

            label {
                padding: 0;
                margin: 0;
            }
            &.selected {
                .toggle-option {
                    background: $background-primary-grad-sm;
                    &:hover,
                    &:active {
                        background: $background-primary-grad-sm;
                    }
                }
                .toggle-option {
                    label {
                        color: white;
                    }
                }
            }
            .toggle-option {
                &:hover {
                    background: $color-primary-background-accent1;
                }
                &:active {
                    background: $color-primary-background-accent3;
                }

                cursor: pointer;
                height: 100%;
                min-height: 80px;
                width: $fe-toggle-button-width;
                border: none;
                border-left: 1px solid $color-gray-lighter;

                label {
                    top: calc(100% - 20px / 2);
                    position: relative;
                    text-align: center;
                    text-transform: uppercase;
                    font-size: $font-size-small;
                    color: $color-primary;
                }
            }
        }
        &.disabled {
            .toggle-option {
                background: $color-gray-lighter;
                &:hover,
                &:active {
                    background: $color-gray-lighter;
                }
            }
            &.selected {
                .toggle-option {
                    background: $background-primary;
                    &:hover,
                    &:active {
                        background: $background-primary;
                    }
                }
            }
            .toggle-option {
                label {
                    color: $color-gray-light;
                }
            }
        }
    }
    .toggle-label {
        margin: 0;
        padding: 0;
    }
}
