/**
  in symptom & item selector renders the green checkmark indicating a selection
*/
@mixin symptom-selector-selected-checkmark() {
    content: '✓';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    @include font-regular2();
    width: $checkbox-frame-height;
    height: $checkbox-frame-height;
    background: $color-info-green;
    border-bottom-right-radius: $border-radius-md;
    border-top-right-radius: $border-radius-md;
    font-size: 40px;
    line-height: $checkbox-frame-height;
    text-align: center;
    color: white;
    @include text-selection-disable();
}

@mixin main-page-header() {
    position: relative;
    z-index: 2;
    margin-bottom: -15px;
    @include container-expand();

    background-color: $color-clinical-grad1;
    border-radius: 0 0 $border-radius-md $border-radius-md;
    @media scren and ($media-max-phone-only) {
        clip-path: circle(1200px at 50% calc(100% - 1200px));
    }
    @include flex-layout($column, $center, $center);
    .title {
        @include text-heading-section($center, $white);
        @include font-regular2();
        margin-top: $spacing-base;
        margin-bottom: $spacing-base;
    }
}

/**
  Sets desktop pointer to hand-pointer and disables user selection
  @param hoverBackground    hover background for desktop resolutions or none
  @param activeBackground   active background when holding touch/mouse down or none
 */

@mixin component-interactive($hoverBackground: undefined, $activeBackground: undefined) {
    &:not(.disabled) {
        cursor: pointer;
    }
    @include text-selection-disable();
    &:hover:not(.disabled) {
        @if $hoverBackground != undefined {
            @media screen and ($media-min-desktop) {
                background: $hoverBackground;
            }
        }
    }
    &:active:not(.disabled) {
        @if $activeBackground != undefined {
            background: $activeBackground;
        }
    }
}

/**
  Sets desktop pointer to hand-pointer and disables user selection
  @param hoverColor     hover color for desktop resolutions or none
  @param activeColor    active color when holding touch/mouse down or none
 */
@mixin component-interactive-color($hoverColor: undefined, $activeColor: undefined) {
    cursor: pointer;
    @include text-selection-disable();
    &:hover {
        @if $hoverColor != undefined {
            @media screen and ($media-min-desktop) {
                color: $hoverColor;
            }
        }
    }
    &:active {
        @if $activeColor != undefined {
            color: $activeColor;
        }
    }
}
