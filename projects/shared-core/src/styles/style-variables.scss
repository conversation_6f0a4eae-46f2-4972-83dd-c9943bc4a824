@import 'style-variables-base.scss';
:root {
    --form-element-border-style: #{$form-element-border-style};
    --label-display: #{$label-display};
    --button-primary-border-style: #{$button-primary-border-style};
    --button-secondary-border-style: #{$button-secondary-border-style};
    --button-link-border-style: #{$button-link-border-style};

    --checkbox-check-content: url('data:image/svg+xmlbase64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIzLjAuMywgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCAxNS45IDEyIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCAxNS45IDEyOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6I0ZGRkZGRjt9Cjwvc3R5bGU+CjxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik01LjgsNy45TDIsNC4xTDAsNi4yTDUuOCwxMkwxNS45LDEuOWwtMi0xLjlMNS44LDcuOXoiLz4KPC9zdmc+Cg==');

    --hr-border-style: #{$hr-border-style};

    --modal-host-align-items: #{$modal-host-align-items};
    --modal-host-justify-content: #{$modal-host-justify-content};
    --modal-close-button-box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
    --modal-close-button-z-index: initial;

    --dropdown-input-border-style: var($form-element-border-style);
    --dropdown-options-panel-border-style: var($form-element-border-style);

    --actions-dropdown-options-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);

    --tooltip-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.15);

    --range-slider-track-background: linear-gradient(
        to right,
        var(--color-wellbeing-great),
        var(--color-wellbeing-ok) 27%,
        var(--color-severity-low) 51%,
        var(--color-severity-medium) 75%,
        var(--color-severity-high)
    );
    --range-slider-track-background-vertical: linear-gradient(
        to bottom,
        var(--color-wellbeing-great),
        var(--color-wellbeing-ok) 27%,
        var(--color-severity-low) 51%,
        var(--color-severity-medium) 75%,
        var(--color-severity-high)
    );
    --range-slider-track-background-invert: linear-gradient(
        to left,
        var(--color-wellbeing-great),
        var(--color-wellbeing-ok) 27%,
        var(--color-severity-low) 51%,
        var(--color-severity-medium) 75%,
        var(--color-severity-high)
    );
    --range-slider-track-background-invert-vertical: linear-gradient(
        to top,
        var(--color-wellbeing-great),
        var(--color-wellbeing-ok) 27%,
        var(--color-severity-low) 51%,
        var(--color-severity-medium) 75%,
        var(--color-severity-high)
    );

    --dropdown-arrow-icon: url('data:image/svg+xmlbase64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMiAxMiI+PHBhdGggZmlsbD0iIzI1QTRDQyIgZD0iTTEwLjYgMi4yNUw2IDcgMS40IDIuMjUgMCAzLjY1bDYgNi4xIDYtNi4xeiIvPjwvc3ZnPgo=');
}
