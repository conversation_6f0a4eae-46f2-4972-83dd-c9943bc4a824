$color-white: #ffffff;
$color-black: #000000;
$color-grey-lighten-5: #f9f9f9;
$color-grey-lighten-4: #f1f1f1;
$color-grey-lighten-3: #efefef;
$color-grey-lighten-2: #e0e0e0;
$color-grey-lighten-1: #d1d1d1;
$color-grey: #c2c2c2;
$color-grey-darken-1: #b3b3b3;
$color-grey-darken-2: #939393;
$color-grey-darken-3: #737373;
$color-grey-darken-4: #525252;
$color-grey-darken-5: #1f1f1f;

$color-brand-1-lighten-5: #f7f4fa;
$color-brand-1-lighten-4: #f0e9f6;
$color-brand-1-lighten-3: #d6c5e7;
$color-brand-1-lighten-2: #bca1d7;
$color-brand-1-lighten-1: #a27fc5;
$color-brand-1: #885db3;
$color-brand-1-darken-1: #754f9b;
$color-brand-1-darken-2: #624183;
$color-brand-1-darken-3: #4f336b;
$color-brand-1-darken-4: #3c2652;
$color-brand-1-darken-5: #291938;

$color-brand-2-lighten-5: #f1fafd;
$color-brand-2-lighten-4: #e3f6fc;
$color-brand-2-lighten-3: #b4e4f3;
$color-brand-2-lighten-2: #83cfe8;
$color-brand-2-lighten-1: #53badb;
$color-brand-2: #25a4cc;
$color-brand-2-darken-1: #1d8cb2;
$color-brand-2-darken-2: #167498;
$color-brand-2-darken-3: #105d7c;
$color-brand-2-darken-4: #0a4660;
$color-brand-2-darken-5: #062f42;

$color-brand-3-lighten-5: #fff2f2;
$color-brand-3-lighten-4: #ffe6e6;
$color-brand-3-lighten-3: #ffbbbb;
$color-brand-3-lighten-2: #ff9d9d;
$color-brand-3-lighten-1: #ff8585;
$color-brand-3: #ff6b6b;
$color-brand-3-darken-1: #fa5858;
$color-brand-3-darken-2: #e63d3d;
$color-brand-3-darken-3: #cc2626;
$color-brand-3-darken-4: #b31313;
$color-brand-3-darken-5: #990404;

$color-information-1-lighten-5: #f2fdf3;
$color-information-1-lighten-4: #e5fbe8;
$color-information-1-lighten-3: #b6f3b8;
$color-information-1-lighten-2: #90e897;
$color-information-1-lighten-1: #6dda7a;
$color-information-1: #4dc861;
$color-information-1-darken-1: #3cb455;
$color-information-1-darken-2: #2e9e49;
$color-information-1-darken-3: #21863e;
$color-information-1-darken-4: #176a33;
$color-information-1-darken-5: #0e4d26;

$color-information-2-lighten-5: #fffdeb;
$color-information-2-lighten-4: #fffcd8;
$color-information-2-lighten-3: #fff5a7;
$color-information-2-lighten-2: #ffeb7a;
$color-information-2-lighten-1: #fadf50;
$color-information-2: #f6c749;
$color-information-2-darken-1: #eca93d;
$color-information-2-darken-2: #e69137;
$color-information-2-darken-3: #dd7831;
$color-information-2-darken-4: #d55f2c;
$color-information-2-darken-5: #cc4627;

$form-element-background-color: white;
$form-element-background-color-hover: white;
$form-element-background-color-active: white;
$form-element-background-color-focus: white;
$form-element-box-shadow-color: rgba(0, 0, 0, 0.15);
$form-element-check-icon-color: white;

$label-color: inherit;
$label-color-disabled: fade($label-color, 50%);
$legend-color: inherit;

$hr-border-color: $color-grey-darken-1;

$color-severity-high: #fa7078;
$color-wellbeing-great: #5fda6e;
$color-wellbeing-ok: #ffff00;
$color-severity-low: #f4d94a;
$color-severity-medium: #f4a84a;
$color-severity-high: #fa7078;

$color-primary-lighten-5: $color-brand-2-lighten-5;
$color-primary-lighten-4: $color-brand-2-lighten-4;
$color-primary-lighten-3: $color-brand-2-lighten-3;
$color-primary-lighten-2: $color-brand-2-lighten-2;
$color-primary-lighten-1: $color-brand-2-lighten-1;
$color-primary: $color-brand-2;
$color-primary-darken-1: $color-brand-2-darken-1;
$color-primary-darken-2: $color-brand-2-darken-2;
$color-primary-darken-3: $color-brand-2-darken-3;
$color-primary-darken-4: $color-brand-2-darken-4;
$color-primary-darken-5: $color-brand-2-darken-5;

$text-black: $color-grey-darken-5;
$text-grey: $color-grey;
$text-red: $color-brand-3-darken-2;

$color-body: $text-black;
$link-color: $color-primary;
$link-color-active: $color-primary-lighten-2;
$link-color-disabled: fade($link-color, 50%);
$link-color-focus: $color-primary-lighten-2;
$link-color-hover: $color-primary-lighten-2;
$link-color-visited: $color-primary;

$form-element-color: $text-black;
$form-element-color-disabled: $color-grey-darken-3;
$form-element-color-error: $text-red;

$form-element-background-color-disabled: $color-grey-lighten-3;
$form-element-background-color-error: $color-brand-3-lighten-3;
$form-element-border-color: $color-grey-lighten-1;
$form-element-border-color-hover: $color-primary;
$form-element-border-color-active: $color-primary;
$form-element-border-color-focus: $color-primary;
$form-element-border-color-disabled: $color-grey-lighten-1;
$form-element-border-color-error: $color-brand-3-lighten-2;
$form-element-focus-ring-color: $color-primary-darken-1;
$form-element-placeholder-color: $color-grey-darken-1;
$form-element-placeholder-color-disabled: fade($color-grey-darken-1, 50%);

$label-required-indicator-color: $text-red;
