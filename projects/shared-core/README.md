# SharedCore

This library was generated with [Angular CLI](https://github.com/angular/angular-cli) version 7.2.0.

## Code scaffolding

Run `ng generate component component-name --project shared-core` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module --project shared-core`.

> Note: Don't forget to add `--project shared-core` or else it will be added to the default project in your `angular.json` file.

## Build

Run `ng build shared-core` to build the project. The build artifacts will be stored in the `dist/` directory.

## Publishing

### Manual

After building your library with `ng build shared-core`, go to the dist folder `cd dist/shared-core` and run `npm publish`.

### Automatic

The library is published on MR merges to the `master` branch or a release branch which name complies with the [regexp](https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string) by the pipeline [publish job](../../.gitlab-ci.yml).

## Running unit tests

Run `ng test shared-core` to execute the unit tests via [Jest](https://jestjs.io/).

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI README](https://github.com/angular/angular-cli/blob/master/README.md).

## Versioning 
Given a version number MAJOR.MINOR.PATCH, increment the:
1. MAJOR version when you make incompatible API changes, Angular version bumps, etc
2. MINOR version when you add functionality in a backwards compatible manner,
3. PATCH version when you make backwards compatible bug fixes.
