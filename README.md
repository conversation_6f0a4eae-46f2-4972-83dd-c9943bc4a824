# NoonaLibrary

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 7.3.5 and has later been upgraded to Angular 10.2.4 using the `ng upgrade` command.

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory. Use the `--prod` flag for a production build.

## Running unit tests

Run `ng test` to execute the unit tests via [Jest](https://jestjs.io/).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via [Protractor](http://www.protractortest.org/).

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI README](https://github.com/angular/angular-cli/blob/master/README.md).

## Creating new form engine components

Create new components in the [form engine components folder](./projects/shared-core/src/lib/form-engine/components). All form engine components have to extend the
[FormInputFieldComponent](./projects/shared-core/src/lib/form-engine/components/form-input-field.component.ts), which automatically sets the `visible` field
of the component (check existing components for example). The component needs to be added to the component list in [DynamicFormField](./projects/shared-core/src/lib/form-engine/components/dynamic-form-field.directive.ts).
It should also be added in [NoonaFormEngine](./projects/shared-core/src/lib/form-engine/noona-form-engine.module.ts), both in `declarations` as well as in `entryComponents`.

In addition to the front end component, the component also needs a back end representation. These are defined in `noona-common`, see
[MeasurementComponent](../../noona-common/src/main/java/adelmann/api/model/schema/form/items/fields/MeasurementComponent.java) for an example. This component should extend either
`ListField` or `FieldItem`, depending on if they require a select list or not. Remember to also add your new component to the parent class' `JsonSubTypes`. Finally,
[FieldType](../../noona-tools/src/main/java/noona/tools/form/markup/model/FieldType.java) and [FormItemType](../../noona-common/src/main/java/adelmann/api/model/schema/form/FormItemType.java) should be updated with
the new component.

## Publishing answers to the form engine store

To add an answer to the store you will dispatch an `AddAnswer` action with the payload given by the `getAnswer()` method of [FieldService](./projects/shared-core/src/lib/form-engine/services/field.service.ts).
The `getAnswer()` method takes 3 arguments with the following signature:

```typescript
getAnswer(
  config: FormFieldConfig,
  value: any,
  initialValue: boolean
)
```

If your component has many fields, e.g. one radio button and one select list, they need to be saved under different keys in the form state. The key is given by `config.key` - typically
the `FormFieldConfig` for different fields differ only by the `key` and `value` values. The convention here is to create different `FormFieldConfig` objects for different fields.
This key should match the key used on the back end form model, as the back end will try to use the key in the form engine state to populate the back end model. Compare
[BMS2019S1StudyEligibilityForm](../../noona-study/src/main/kotlin/com/noona/noonastudy/form/generated/bms2019/crfform/model/BMS2019S1StudyEligibilityForm.kt) and the static fields
in [YesNoDate](./projects/shared-core/src/lib/form-engine/components/yes-no-date/yes-no-date.component.ts) for a better understanding of how they tie together. The base key
is defined in the [form template](../../noona-study/src/main/java/com/noona/noonastudy/form/generated/engine/forms/BMS2019S1StudyEligibility.java). The flow is described
briefly below.

1. The form template declares a `YesNoDateComponent` on the form with the key 'documentedUseOfPembrolizumab':
   `final YesNoDateComponent yesNoDateComponent14 = new YesNoDateComponent("documentedUseOfPembrolizumab");`
2. The form engine creates a `YesNoDate` component and provides it with a `FormFieldConfig` object which has a `key` field among others:

```typescript
{
    ...
    key: 'documentedUseOfPembrolizumab'
}
```

3. The `YesNoDate` component creates new a new `FormFieldConfig` object for both the date field and the radio field (`booleanAttributeConfig` and
   `dateAttributeConfig`). The new configs are copies of the original `config` except for the `key` value, which has the postfix appended to them:

```typescript
booleanAttributeConfig = {
    ...this.config,
    key: this.config.key + BOOLEAN_ATTRIBUTE_POSTFIX,
};
dateAttributeConfig = {
    ...this.config,
    key: this.config.key + DATE_ATTRIBUTE_POSTFIX,
};
```

4. When adding answers, these configs are passed to `getAnswer()`:

```
this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.dateAttributeConfig, value, null)));
```

5. The submit object will then contain something like this:

```typescript
{
  documentedUseOfPembrolizumabSelected: true,
  documentedUseOfPembrolizumabDate: 123456789
}
```

, which will be used to populate the new form.

## FormFieldConfig keys

### labelKey

The translation key for the label of the component e.g. `fe.agentsUsed.label`, which has a corresponding
`fe.agentsUsed.label=Agents used in this line of therapy\:` line in a translation file
(see [example](../../noona-study/src/main/resources/i18n_form.properties)). By convention, this key is often used as the base for other label translation keys. See
`initTranslationKeys()` in [Multiselect](./projects/shared-core/src/lib/form-engine/components/multiselect/multiselect.component.ts) for an example on how to use the
`labelKey` as base for other keys.

### valuesKey

This key is used by `getListValues()` in [FieldService](./projects/shared-core/src/lib/form-engine/services/field.service.ts) to get the translation keys for a list of options. The
valuesKey is stored in `generalSelectLists` inside the schema object. The list is created from an enum, see
[NoonaStudySelectListFactory](../../noona-study/src/main/java/com/noona/noonastudy/form/generated/engine/schema/NoonaStudySelectListFactory.java) for examples on how
they are initialized. The generalSelectList object may look like this:

```typescript
{
    generalSelectLists: {
        symptoms: [
            { index: 0, key: 'pain', orderIndex: 0 },
            { index: 1, key: 'diarrhea', orderIndex: 1 },
        ];
    }
}
```

The translations for the selectList options are stored elsewhere. The `FieldService` uses the `translationPrefix` together with the `key` to create the translation key:

```typescript
{
    translationKey: `${config.translationPrefix}.${value.key}.label`;
}
```

These keys can then be piped into [I18NPipe](./projects/shared-core/src/lib/pipes/i18n.pipe.ts) to get the actual translation values.

Some components, such as [Measurement](.projects/shared-core/src/lib/form-engine/components/measurement/measurement.component.ts), requires several select lists. The
convention in this case is to specify a field on [FormFieldConfig](./projects/shared-core/src/lib/form-engine/models/form-field-config.interface.ts) or use an existing
field from there to get the `translationPrefix` for the other options; `measurementTranslationPrefix` is one example of this. See the [Measurement](.projects/shared-core/src/lib/form-engine/components/measurement/measurement.component.ts)
component for an example on how to handle this case. Note that you need to add these keys to the corresponding Java class as well in `noona-common`, see
[MeasurementComponent](../../noona-common/src/main/java/adelmann/api/model/schema/form/items/fields/MeasurementComponent.java) for an example.

## Developer environment setup

-   Set up shared-core
    1. Clone or pull the latest from [Noona Shared Core repository](https://gitlab.com/varian-noona/development/core/shared-core) and navigate to the source folder
    2. Execute `npm i`
    3. Execute `npm run build:shared-core`
    4. Execute `cd dist/shared-core && npm link`
        - Make sure the output is similar to
          `/usr/local/lib/node_modules/@varian-noona/shared -> /YOUR_SOURCE_FOLDER/shared-core/dist/shared-core` or `success Registered "@varian-noona/shared".
-   For project which is using shared-core:
    1. Navigate to project source folder
    2. Execute `npm link @varian-noona/shared`

## Modifying local shared-core and watching for changes

1. Make sure you have `link`ed your project properly (see above)
2. Navigate to _`shared-core`_ source folder
3. Execute `npm run build:shared-core:watch`
4. (Your project's watch will automatically pick up changes made to node_modules/@varian-noona/shared)

## Contributing

### Linting

Always check your code for linting errors before pushing your changes to GitLab.

Running Prettier
Before running linting, ensure that your code is formatted properly using Prettier. You can format specific directories or files by running the following command:

```s
./node_modules/.bin/prettier ./projects/shared-core/<the directory you want format to be run> --write
```

This will automatically format the code in the specified directory.

Linting is done by running the following command:

```s
$ npm run lint
```

You can make the linting process more efficient by installing tooling for both Eslint and Prettier in your editor. Proper plugins will highlight linting errors in the editor, provide tips for how to fix the errors and even allow for auto-formatting.

Linting is configured in [.eslintrc.json](./.eslintrc.json), [.prettier](./prettierrc) and [angular.json](./angular.json).

**Note:** We have disabled some of the rules in order to avoid making unnecessary changes to the codebase. However, these rules should be enabled at some point once we have proper means to ensure that we do not break anything.

### Running unit tests

Run `npm run test` to execute the unit tests via [Jest](https://jestjs.io/).

Run `npm run test:watch` to watch files for changes during development.

## Bump version

Run `npm run bump-version:shared-core` to add a commit hash to the library version.
