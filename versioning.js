const fs = require('fs');

const argv = require('minimist')(process.argv.slice(2));
const semver = require('semver');
const inquirer = require('inquirer');

const QUESTIONS = ['Bump or Set the version?', 'What version do you want to set?', 'What version do you want to bump?'];

const isBump = argv.bump;
const isSet = argv.set;
const verbose = argv.v;

const updateJsonFile = (path, packageJson) => {
    fs.writeFileSync(path, JSON.stringify(packageJson, null, 4));
    if (verbose) {
        console.log(`Package: ${packageJson.name} - Version was set to: ${packageJson.version}`);
    }
};

const writeToFile = (packageJson) => {
    updateJsonFile('./package.json', packageJson);

    const otherPaths = argv.paths;
    if (otherPaths && otherPaths.length > 0) {
        const parsedPaths = otherPaths.split(',');
        parsedPaths.forEach((path) => {
            const otherPackage = require(path + '/package.json');
            otherPackage.version = packageJson.version;
            updateJsonFile(path + '/package.json', otherPackage);
        });
    }
};

const getBumpableVersion = (versionToBump, currentVersion) => {
    if (!semver.valid(currentVersion)) {
        throw new Error('Invalid SemVer found in package.json. This tool is only for semantic versioning');
    }

    const releaseType = versionToBump === 'major' || versionToBump === 'minor' ? versionToBump : 'patch';
    const versionToSet = semver.inc(currentVersion, releaseType);
    return versionToSet;
};

if (isSet) {
    const packageJson = require('./package.json');
    if (semver.valid(isSet)) {
        packageJson.version = isSet;
        writeToFile(packageJson);
    }
} else if (isBump) {
    const packageJson = require('./package.json');
    packageJson.version = getBumpableVersion(isBump, packageJson.version);
    writeToFile(packageJson);
} else {
    inquirer
        .prompt([
            {
                type: 'list',
                name: QUESTIONS[0],
                choices: ['Bump', 'Set'],
            },
        ])
        .then((answer) => {
            let isSet = false;
            let question = {
                type: 'list',
                name: QUESTIONS[2],
                choices: [
                    {
                        name: 'Patch (default)',
                        value: 'patch',
                    },
                    {
                        name: 'Minor',
                        value: 'minor',
                    },
                    {
                        name: 'Major',
                        value: 'major',
                    },
                ],
            };

            if (answer[QUESTIONS[0]] === 'Set') {
                isSet = true;
                question = {
                    type: 'input',
                    name: QUESTIONS[1],
                };
            }

            return inquirer.prompt([question]).then((secondAnswer) => ({
                isSet,
                secondAnswer,
            }));
        })
        .then(({ isSet, secondAnswer }) => {
            const packageJson = require('./package.json');

            if (isSet) {
                const versionToSet = secondAnswer[QUESTIONS[1]];
                packageJson.version = versionToSet;
            } else {
                const versionToBump = secondAnswer[QUESTIONS[2]];
                packageJson.version = getBumpableVersion(versionToBump, packageJson.version);
            }
            writeToFile(packageJson);
        });
}
