{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "es2020", "moduleResolution": "node", "esModuleInterop": true, "experimentalDecorators": true, "importHelpers": true, "target": "ES2022", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom", "esnext"], "paths": {"shared-core": ["dist/shared-core"], "shared-core/*": ["dist/shared-core/*"], "@angular/*": ["./node_modules/@angular/*"], "rxjs/*": ["./node_modules/rxjs/*"], "tslib/*": ["./node_modules/tslib/*"], "@shared-core/testing": ["projects/shared-core/src/test-utils/public-api.ts"]}, "useDefineForClassFields": false, "emitDecoratorMetadata": true, "types": ["jest"]}, "angularCompilerOptions": {"strictTemplates": true}}