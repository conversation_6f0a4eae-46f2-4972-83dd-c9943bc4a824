/* You can add global styles to this file, and also import other style files */
@import 'projects/shared-core/src/styles/ds-styles';
@import 'projects/shared-core/src/styles/deprecated_variables';
@import 'projects/shared-core/src/styles/deprecated_mixins';
@import 'projects/shared-core/src/styles/ds-variables';

$border-color: lightgray;
:root {
  --form-element-box-shadow-size: 0;
  --modal-window-max-width: 634px;
  --checkbox-border-color: var(--color-grey-darken-1);
  --checkbox-border-color-hover: var(--color-grey-darken-2);
  --radio-border-color-checked: var(--color-information-1-darken-1);
  --radio-border-color-hover: var(--color-information-1-darken-1);
  --radio-box-shadow-color-checked: var(--color-information-1);

  --h1-font-size: 36px;
  --h2-font-size: 30px;
  --h3-font-size: 26px;
  --h4-font-size: 24px;
  --h5-font-size: 20px;
  --h6-font-size: 16px;

  --h1-font-family: #{$font-family-light};
  --h2-font-family: #{$font-family-light};
  --h3-font-family: #{$font-family-light};
  --h4-font-family: #{$font-family-light};
  --h5-font-family: #{$font-family-regular};
  --h6-font-family: #{$font-family-semibold};

  --h1-font-color: #{$color-grey-darken-5};
  --h2-font-color: #{$color-grey-darken-5};
  --h3-font-color: #{$color-grey-darken-5};
  --h4-font-color: #{$color-grey-darken-5};
  --h5-font-color: #{$color-grey-darken-5};
  --h6-font-color: #{$color-grey-darken-5};

  --h1-font-weight: 300;
  --h2-font-weight: 300;
  --h3-font-weight: 300;
  --h4-font-weight: 300;
  --h5-font-weight: normal;
  --h6-font-weight: 600;

  --text-grey: #{$color-grey-darken-2};
}

html,
body {
  background-color: #fafafa;
  position: relative;
  overflow: visible;
}

button {
  cursor: pointer;
}
.border {
  &--top {
    border-top: 1px solid $border-color;
  }
  &--right {
    border-right: 1px solid $border-color;
  }
  &-bottom {
    border-bottom: 1px solid $border-color;
  }
  &--left {
    border-left: 1px solid $border-color;
  }
  &--all {
    border: 1px solid $border-color;
  }
}
.ng-select.ng-select-multiple.ds-dropdown .ng-value {
  background-color: #{$color-grey-lighten-3};
  border-radius: 17px;
  color: #{$color-grey-darken-5};
  font-weight: normal;
  margin-right: #{$spacing-xxs};
}
.ng-select.ds-dropdown .ng-option.ng-option-selected {
  color: #{$color-grey-darken-3};
  font-weight: normal;
  & ds-icon {
    fill: #{$color-information-1-darken-1};
  }
}
.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon {
  margin-right: #{$spacing-xxs};
}
.ng-select.ds-dropdown .ng-select-container .ng-value-container .ng-input input {
  padding-left: 0;
}
.date-picker {
  position: relative;
  width: calc(var(--spacing-m) + 175px);

  ds-icon {
    fill: #{$color-primary};
    height: var(--input-height);
    position: absolute;
    right: #{$spacing-m};
    top: 0;
  }
}

nh-radio-list .radio {
  padding: 0 10px 0 10px;
  position: relative;
  margin: 10px 0;
  border-radius: 10px;
  min-height: 80px;
  box-shadow: 0 0 11px 0 rgba(0, 0, 0, 0.13);
  background: #fff;
  display: flex;
  flex-direction: row;
  justify-content: left;
  align-items: center;
  user-select: none;

  label {
    padding: 20px 0 20px 20px;
    margin-top: -10px;
    margin-right: 10px;
    margin-bottom: -10px;
    margin-left: 0;
    width: 100%;
  }
}
.wizard-section {
  h4 {
    font-family: PaulGroteskSoft-SemiBold;
    font-size: 18px;
    text-transform: unset;
    text-align: left;
    line-height: 1.2;
    color: #333;
    margin: 30px 0 5px 0;
  }
}
