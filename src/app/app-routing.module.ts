import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AboutDemoComponent } from './about-demo/about-demo.component';

const routes: Routes = [
    {
        path: 'about',
        component: AboutDemoComponent,
    },
    {
        path: 'demo',
        loadChildren: () => import('../demo/demo.module').then((m) => m.DemoModule),
    },
];

@NgModule({
    imports: [RouterModule.forRoot(routes, {})],
    exports: [RouterModule],
})
export class AppRoutingModule {}
