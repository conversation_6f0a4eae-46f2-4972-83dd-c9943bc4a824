import { Action } from '@ngrx/store';
import { NavigationExtras } from '@angular/router';

export enum RouterActionTypes {
    Go = '[Router] Go',
    Forward = '[Router] Forward',
    Back = '[Router] Back',
}

export interface NavigationProperties {
    path: any[];
    query?: object;
    extras?: NavigationExtras;
}

export class Go implements Action {
    readonly type = RouterActionTypes.Go;
    constructor(public payload: NavigationProperties) {}
}

export class Forward implements Action {
    readonly type = RouterActionTypes.Forward;
}

export class Back implements Action {
    readonly type = RouterActionTypes.Back;
}

export type RouterActions = Go | Forward | Back;
