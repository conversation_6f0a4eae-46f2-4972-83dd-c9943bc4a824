import { RouterActions, RouterActionTypes } from '../actions/router.actions';

export interface State {}

export const initialState: State = {};

export function reducer(state = initialState, action: RouterActions): State {
    switch (action.type) {
        case RouterActionTypes.Go:
            return state;

        case RouterActionTypes.Forward:
            return state;

        case RouterActionTypes.Back:
            return state;

        default:
            return state;
    }
}
