import { ActionReducerMap, MetaReducer, createFeatureSelector, createSelector } from '@ngrx/store';
import * as fromRouter from '@ngrx/router-store';

import { environment } from '../../../environments/environment';
import { Params } from '@angular/router';
import { clearState } from './clear-state.reducer';

export const ROUTER_REDUCER_NAME = 'routerReducer';

export interface RouterStateUrl {
    url: string;
    queryParams: Params;
    params: Params;
}

export interface State {
    [ROUTER_REDUCER_NAME]: fromRouter.RouterReducerState<RouterStateUrl>;
}

// Angular AOT compiler can not work with '[ROUTER_REDUCER_NAME]' expression
// Hence, direct usage of 'routerReducer' here
export const reducers: ActionReducerMap<State> = {
    routerReducer: fromRouter.routerReducer,
};

const commonMetaReducers = [clearState];

export const metaReducers: Array<MetaReducer<State>> = !environment.production
    ? [...commonMetaReducers]
    : commonMetaReducers;

export const getRouterState = createFeatureSelector<fromRouter.RouterReducerState<RouterStateUrl>>(ROUTER_REDUCER_NAME);
export const getRouterStateUrl = createSelector(getRouterState, (routerState) => routerState.state);
