import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppComponent } from './app.component';
import { SharedCoreModule } from '../../projects/shared-core/src/lib/shared-core.module';
import { SharedCoreApi } from './services/shared-core-api';
import { DemoLocaleService } from './services/locale.service';
import { SchemaService } from './services/schema.service';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { RouterEffects } from './store/effects/router.effects';
import { metaReducers, reducers } from './store/reducers';
import { FullRouterStateSerializer, RouterState, StoreRouterConnectingModule } from '@ngrx/router-store';
import { AppRoutingModule } from './app-routing.module';
import { DemoConfigService } from './services/configuration.service';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AboutDemoComponent } from './about-demo/about-demo.component';
import { environment } from 'src/environments/environment';

@NgModule({
  declarations: [AppComponent, AboutDemoComponent],
  imports: [
    CommonModule,
    AppRoutingModule,
    BrowserModule,
    BrowserAnimationsModule,
    StoreModule.forRoot(reducers, {
      metaReducers,
      runtimeChecks: {
        // Required for routing to work, see https://github.com/ngrx/platform/issues/2109
        strictStateImmutability: false
      }
    }),
    StoreRouterConnectingModule.forRoot({
      serializer: FullRouterStateSerializer,
      routerState: RouterState.Minimal
    }),
    EffectsModule.forRoot([RouterEffects]),
    SharedCoreModule.forRoot({
      api: SharedCoreApi,
      configProvider: DemoConfigService,
      localeService: DemoLocaleService,
      schemaService: SchemaService,
      environment
    }),
    StoreDevtoolsModule.instrument()
  ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule {}
