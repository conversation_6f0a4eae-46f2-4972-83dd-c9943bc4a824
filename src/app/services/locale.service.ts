import { Injectable } from '@angular/core';
import { NoonaLocaleService } from '../../../projects/shared-core/src/lib/abstract-services/noona-locale.service';
import { MOCK_TRANSLATIONS } from '../mock-translations';

@Injectable({
    providedIn: 'root',
})
export class DemoLocaleService implements NoonaLocaleService {
    private translations: { [key: string]: any } = { ...MOCK_TRANSLATIONS };

    getLocale(): string {
        return '';
    }

    getTranslation(key: string, match: any = this.translations): string {
        if (typeof key !== 'string') {
            return undefined;
        }

        const keys = key.split('.');
        if (match === null) {
            return undefined;
        }

        const originalMatch = match;
        for (let i = 0; i < keys.length; i++) {
            if (match && match.hasOwnProperty(keys[i])) {
                match = match[keys[i]];
            } else {
                match = undefined;
                break;
            }
        }

        if (!match || (typeof match === 'string' && match.trim() === '')) {
            return `?${key}?`;
        }

        if (typeof match === 'string' && match.charAt(0) === '=') {
            return this.getTranslation(match.slice(1), originalMatch);
        }

        return match;
    }

    setTranslations(t: { [key: string]: string }) {
        this.translations = t;
    }
}
