import { Injectable } from '@angular/core';
import { Dictionary } from '../../../projects/shared-core/src/lib/common-types';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { QuestionarySettings } from '../../../projects/shared-core/src/lib/generated/models/questionary-settings';
import { Application } from '../../../projects/shared-core/src/lib/models/application';
import { DateFormattingPattern } from '../../../projects/shared-core/src/lib/abstract-services/interfaces/date-formatting-pattern.interface';
import { MeasurementSystem } from '../../../projects/shared-core/src/lib/generated/models/measurement-system';
import { ConfigurationProviderService } from '../../../projects/shared-core/src/lib/abstract-services/configuration-provider.service';
import { User } from '../../../projects/shared-core/src/lib/generated/models/user';

@Injectable({
    providedIn: 'root',
})
export class DemoConfigService implements ConfigurationProviderService {
    private dateFormatting: DateFormattingPattern = {
        shortDatePattern: '',
        timePattern: '',
        datetimeMinsPattern: '',
        datetimeSecsPattern: '',
        datePlaceholder: '',
        patternId: '',
        inputValidatorRegExp: new RegExp(''),
        patientTimelineDate: '',
        fullTimePattern: 'MM/DD/YYYY hh:mm a',
        longDatePattern: 'MM/DD/YYYY',
    };

    private dateFormattingPattern$ = new BehaviorSubject<DateFormattingPattern>(this.dateFormatting);

    authorizationHeader(): Dictionary<string, string> | undefined {
        return undefined;
    }

    baseUrl(): string {
        return '';
    }

    dateFormattingPattern(): Observable<DateFormattingPattern> {
        return this.dateFormattingPattern$.asObservable();
    }

    measurementSystem(): Observable<MeasurementSystem> {
        return undefined;
    }

    questionarySettings(): QuestionarySettings[] {
        return [];
    }

    site(): Application {
        return undefined;
    }

    activeUser(): Observable<User> {
        return of({
            userId: '1234',
            fullName: 'Peter Tyler',
        });
    }

    set longDatePattern(format: string) {
        this.dateFormatting.longDatePattern = format;
        this.dateFormattingPattern$.next(this.dateFormatting);
    }
}
