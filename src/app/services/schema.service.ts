import { Inject, Injectable } from '@angular/core';
import { NoonaSchema } from '../../../projects/shared-core/src/lib/generated/models/noona-schema';
import { ListEntry } from '../../../projects/shared-core/src/lib/generated/models/list-entry';
import { SharedSchemaService } from '../../../projects/shared-core/src/lib/abstract-services/shared-schema.service';

@Injectable({
    providedIn: 'root',
})
export class SchemaService implements SharedSchemaService {
    private schemaPromise: Promise<NoonaSchema>;
    private schema: NoonaSchema;

    public load(): Promise<NoonaSchema> {
        return Promise.resolve(null);
    }

    public overwriteFormSchema(formKey: string, schema: any) {
        // Not implemented
    }

    public getSchema(): NoonaSchema {
        return this.schema;
    }

    public getPhotoFields(): { [key in string]: string[] } {
        return this.schema.photoFields;
    }

    public getBodyDiagrams(): { [key in string]: string } {
        return this.schema.bodyDiagrams;
    }

    public getSelectLists(): { [key in string]: ListEntry[] } {
        return this.schema.generalSelectLists;
    }

    public getEnums() {
        return this.schema.enums;
    }

    public getPatterns() {
        return this.schema.validationPatterns;
    }

    public getAttributeLists() {
        return this.schema.attributeLists;
    }

    public getTranslations() {
        return this.schema.translationPrefixes;
    }

    public getFieldValueConditions() {
        return this.schema.fieldValueConditions;
    }

    public getFieldValueStaticConditions() {
        return this.schema.fieldValueStaticConditions;
    }

    public getScalingInformation() {
        return this.schema.scalingInformations;
    }

    public setSchema(schema: NoonaSchema) {
        this.schema = schema;
    }
}
