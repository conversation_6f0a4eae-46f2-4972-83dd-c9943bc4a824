import { Component, Inject, OnInit, ViewContainerRef } from '@angular/core';
import { SchemaService } from './services/schema.service';
import { MockSchema } from './mock-schema';
import { VersionInformation } from '../../projects/shared-core/src/lib/regulatory/about/version-information.interface';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
})
export class AppComponent implements OnInit {
    constructor(private schemaService: SchemaService) {}

    ngOnInit(): void {
        this.schemaService.setSchema(new MockSchema());
    }
}
