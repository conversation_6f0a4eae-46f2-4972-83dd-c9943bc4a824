import { NoonaSchema } from '../../projects/shared-core/src/lib/generated/models/noona-schema';
import { MultiselectComponent } from '../../projects/shared-core/src/lib/form-engine/components/multiselect/multiselect.component';
import { MeasurementComponent } from '../../projects/shared-core/src/lib/form-engine/components/measurement/measurement.component';
import { DevelopmentEntryComponent } from '../demo/development-entry/development-entry.component';

export class MockSchema implements NoonaSchema {
    generalSelectLists = {
        [DevelopmentEntryComponent.MULTI_SELECT_VALUES_KEY]: [
            { key: 'afatinib', value: 'Afatinib (Gilotrif)' },
            { key: 'pactilaxel', value: 'Albumin-bound pactilaxel (nab-pactilaxel; Abraxane)' },
            { key: 'atezolizumab', value: 'Atezolizumab (Tecentriq)' },
            { key: 'nivolumab', value: '<PERSON><PERSON><PERSON><PERSON> (Opdivo)' },
            { key: 'carboplatin', value: 'Carboplatin' },
        ],

        [DevelopmentEntryComponent.PHQ_VALUES_KEY + MeasurementComponent.MEASUREMENT_VALUES_POSTFIX]: [
            { key: '0', value: 0 },
            { key: '1', value: 1 },
            { key: '2', value: 2 },
            { key: '3', value: 3 },
            { key: '4', value: 4 },
            { key: '5', value: 5 },
            { key: '6', value: 6 },
        ],
        [DevelopmentEntryComponent.PHQ_VALUES_KEY]: [{ key: 'phq', value: 'PHQ-2' }],

        [DevelopmentEntryComponent.SYMPTOMS_VALUES_KEY + MeasurementComponent.MEASUREMENT_VALUES_POSTFIX]: [
            { key: 'mild', value: 'mild' },
            { key: 'moderate', value: 'moderate' },
            { key: 'severe', value: 'severe' },
        ],
        [DevelopmentEntryComponent.SYMPTOMS_VALUES_KEY]: [
            { key: 'diarrhea', value: 'diarrhea' },
            { key: 'pain', value: 'pain' },
            { key: 'fever', value: 'fever' },
            { key: 'swelling', value: 'swelling' },
        ],

        [DevelopmentEntryComponent.RADIO_LIST_WITH_DATE_VALUES_KEY]: [
            { key: 'yes', value: 'yes' },
            { key: 'no', value: 'no' },
            { key: 'unknown', value: 'unknown' },
            { key: 'list', value: 'list' },
        ],

        [DevelopmentEntryComponent.CHECKBOX_LIST_WITH_RADIO_VALUES_KEY]: [
            { key: 'americanIndianOrAlaskaNative', value: '1' },
            { key: 'asian', value: '2' },
            { key: 'blackOrAfricanAmerican', value: '3' },
            { key: 'nativeHawaiianOrOtherPacificIslander', value: '4' },
            { key: 'white', value: '5' },
            { key: 'unknown', value: '6' },
        ],

        [DevelopmentEntryComponent.SELECT_LIST_WITH_RADIO_VALUES_KEY]: [
            { key: '0', value: 0 },
            { key: '1', value: 1 },
            { key: '2', value: 2 },
            { key: '3', value: 3 },
            { key: '4', value: 4 },
            { key: '5', value: 5 },
            { key: '6', value: 6 },
            { key: '7', value: 7 },
            { key: '8', value: 8 },
            { key: '9', value: 9 },
            { key: '10', value: 10 },
            { key: 'unknown', value: 11 },
        ],

        doseAgent: [
            { key: 'nivolumab', value: 'Nivolumab (Opdivo)' },
            { key: 'albumin', value: 'Albumin-bound pactilaxel (nab-pactilaxel; Abraxane)' },
            { key: 'lorem', value: 'Lorem ipsum' },
        ],

        doseEvent: [
            { key: 'doseModification', value: 'Dose modification' },
            { key: 'doseInterruption', value: 'Dose interruption' },
        ],

        doseAgentReason: [
            { key: 'severeAdverse', value: 'Severe adverse event' },
            { key: 'nonSevereAdverse', value: 'Non-severe adverse event' },
            { key: 'patientRequest', value: 'Patient request' },
            { key: 'other', value: 'other' },
            { key: 'unknown', value: 'unknown' },
        ],

        yesNoUnknown: [
            { key: 'yes', value: 'yes' },
            { key: 'no', value: 'no' },
            { key: 'unknown', value: 'unknown' },
        ],
    };

    fieldValueStaticConditions = {};
}
