import { Component } from '@angular/core';
import { FormFieldConfig } from '../../../projects/shared-core/src/lib/form-engine/models/form-field-config.interface';

@Component({
    selector: 'ns-checkbox-list-demo',
    templateUrl: './checkbox-list-demo.component.html',
    styleUrls: ['./checkbox-list-demo.component.css'],
})
export class CheckboxListDemoComponent {
    config: FormFieldConfig = {
        labelKey: 'checkboxDemo.labelKey',
        templateOptions: {
            options: [
                {
                    id: '1',
                    translationKey: 'checkboxDemo.options.1',
                    selected: false,
                    value: 1
                }
            ]
        }
    };

    configXss: FormFieldConfig = {
        labelKey: 'checkboxDemo.labelKeyXss'
    }
}
