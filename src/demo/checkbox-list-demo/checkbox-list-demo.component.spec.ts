import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { CheckboxListDemoComponent } from './checkbox-list-demo.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('CheckboxListDemoComponent', () => {
  let component: CheckboxListDemoComponent;
  let fixture: ComponentFixture<CheckboxListDemoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [CheckboxListDemoComponent],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CheckboxListDemoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
