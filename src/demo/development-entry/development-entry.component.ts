import moment from 'moment';
import { ChangeDetector<PERSON>ef, Component, OnInit } from '@angular/core';
import { select, Store } from '@ngrx/store';
import { FormEngineState } from '../../../projects/shared-core/src/lib/form-engine/store/reducers/state';
import { NoonaLocaleService } from '../../../projects/shared-core/src/lib/abstract-services/noona-locale.service';
import { FieldService } from '../../../projects/shared-core/src/lib/form-engine/services/field.service';
import { DemoConfigService } from '../../app/services/configuration.service';
import { QuestionaryType } from '../../../projects/shared-core/src/lib/generated/models/questionary-type';
import { Application } from '../../../projects/shared-core/src/lib/models/application';
import { FormFieldConfig, UnknownDateDefault } from '../../../projects/shared-core/src/lib/form-engine/models/form-field-config.interface';
import { FormItemType } from '../../../projects/shared-core/src/lib/generated/models/form-item-type';
import { ConditionType } from '../../../projects/shared-core/src/lib/generated/models/condition-type';
import { FieldConditionMatcher } from '../../../projects/shared-core/src/lib/form-engine/models/field-condition-matcher.enum';
import { FieldConditionType } from '../../../projects/shared-core/src/lib/form-engine/models/field-condition-type.enum';
import { AddAnswer, StartNewForm } from '../../../projects/shared-core/src/lib/form-engine/store/actions/form.actions';
import { RadioListWithExtraFieldsComponent } from '../../../projects/shared-core/src/lib/form-engine/components/radio-list-with-extra-fields/radio-list-with-extra-fields.component';
import { AddOrUpdateFieldStatus } from '../../../projects/shared-core/src/lib/form-engine/store/actions/field-status.actions';
import { FieldStatusType } from '../../../projects/shared-core/src/lib/form-engine/models/field-status.interface';
import { ResetFormVariables, UpdateFormVariable } from '../../../projects/shared-core/src/lib/form-engine/store/actions/variables.action';
import { FormVariableType } from '../../../projects/shared-core/src/lib/form-engine/models/form-var-type';
import { selectFormVariable } from '../../../projects/shared-core/src/lib/form-engine/store/selectors/variables.selectors';
import { MultiselectComponent } from '../../../projects/shared-core/src/lib/form-engine/components/multiselect/multiselect.component';
import { RadioListWithExtraFieldsConfig } from 'projects/shared-core/src/public-api';

/*
 * This component is only used for development purpose.
 *
 *
 */

@Component({
  selector: 'lib-development-form-entry',
  templateUrl: './development-entry.component.html',
  styleUrl: './development-entry.component.scss'
})
export class DevelopmentEntryComponent implements OnInit {
  constructor(
    protected store: Store<FormEngineState>,
    private localeService: NoonaLocaleService,
    private fieldService: FieldService,
    private configService: DemoConfigService,
    private cd: ChangeDetectorRef
  ) {}
  static readonly FORM_TYPE = QuestionaryType.BOUNCE_HADS;
  static readonly FORM_KEY = QuestionaryType.BOUNCE_HADS + 'Form';

  static readonly YES_NO_DATE_KEY = 'documentedDiagnosisOfMelanoma';
  static readonly YES_NO_DATE_LABEL_KEY = 'documentedDiagnosisOfMelanoma';

  static readonly VARIABLE_TEST_KEY = 'variableTestKey';

  static readonly FIELD_STATUS_TEST_KEY = 'fieldStatusTestKey';

  static readonly DATE_KEY = 'dateOfBirth';
  static readonly DATE_LABEL_KEY = 'dateOfBirth.label';
  static readonly DATE_SUB_LABEL_KEY = 'dateOfBirth.sublabel';

  static readonly DISABLED_DATE_KEY = 'disabledDateOfBirth';

  static readonly MULTI_SELECT_KEY = 'agentsUsed';
  static readonly MULTI_SELECT_LABEL_KEY = 'fe.agentsUsed.label';
  static readonly MULTI_SELECT_VALUES_KEY = 'fe.agent';

  static readonly PHQ_MEASUREMENT_KEY = 'phq';
  static readonly PHQ_MEASUREMENT_LABEL_KEY = 'fe.phq.label';
  static readonly PHQ_VALUES_KEY = 'bms2019S1StudyEligibilityPHQ2';

  static readonly SYMPTOMS_MEASUREMENT_KEY = 'symptoms';
  static readonly SYMPTOMS_MEASUREMENT_LABEL_KEY = 'fe.symptoms.label';
  static readonly SYMPTOMS_VALUES_KEY = 'symptoms';

  static readonly RADIO_LIST = 'wasPatientPregnantRadioStandard';

  static readonly RADIO_LIST_WITH_DATE_KEY = 'wasPatientPregnant';
  static readonly RADIO_LIST_WITH_DATE_LABEL_KEY = 'fe.wasPatientPregnant.label';
  static readonly RADIO_LIST_WITH_DATE_VALUES_KEY = 'wasPatientPregnant';

  static readonly CHECKBOX_LIST_WITH_RADIO_KEY = 'race';
  static readonly CHECKBOX_LIST_WITH_RADIO_LABEL_KEY = 'fe.race.label';
  static readonly CHECKBOX_LIST_WITH_RADIO_VALUES_KEY = 'race';

  static readonly SELECT_LIST_WITH_RADIO_KEY = 'numberOfTherapyLinesBeforeIo';
  static readonly SELECT_LIST_WITH_RADIO_LABEL_KEY = 'fe.numberOfTherapyLinesBeforeIo.label';
  static readonly SELECT_LIST_WITH_RADIO_VALUES_KEY = 'numberOfTherapyLinesBeforeIo';

  private static BASE_FIELD_CONFIG = {
    formKey: DevelopmentEntryComponent.FORM_KEY,
    formType: DevelopmentEntryComponent.FORM_TYPE,
    site: Application.CLINIC
  };

  private readonly EVENTS_SUBFORM_KEY = 'inpatientAdmissions';
  private readonly EVENTS_SUBFORM_LABEL_KEY = 'fe.inpatientAdmissions.label';

  public yesNoDateConfig: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: DevelopmentEntryComponent.YES_NO_DATE_KEY,
    labelKey: DevelopmentEntryComponent.YES_NO_DATE_LABEL_KEY,
    type: FormItemType.YES_NO_DATE
  };

  public dateConfig: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: DevelopmentEntryComponent.DATE_KEY,
    labelKey: DevelopmentEntryComponent.DATE_LABEL_KEY,
    subLabelKey: DevelopmentEntryComponent.DATE_SUB_LABEL_KEY,
    type: FormItemType.DATE,
    required: null,
    unknownDayDefault: UnknownDateDefault.LAST
  };

  public multiSelectConfig: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: DevelopmentEntryComponent.MULTI_SELECT_KEY,
    labelKey: DevelopmentEntryComponent.MULTI_SELECT_LABEL_KEY,
    valuesKey: DevelopmentEntryComponent.MULTI_SELECT_VALUES_KEY,
    type: FormItemType.MULTISELECT,
    translationPrefix: 'fe.agent'
  };

  public phqMeasurementConfig: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: DevelopmentEntryComponent.PHQ_MEASUREMENT_KEY,
    labelKey: DevelopmentEntryComponent.PHQ_MEASUREMENT_LABEL_KEY,
    valuesKey: DevelopmentEntryComponent.PHQ_VALUES_KEY,
    translationPrefix: 'fe.phq',
    secondaryTranslationPrefix: 'fe.zeroToSixScale',
    type: FormItemType.MEASUREMENT,
    dateRangeValidationConfig: {
      fromVariableKey: 'abstractionStart',
      toVariableKey: 'abstractionEnd',
      outOfRangeTranslationKey: 'fe.daterange.outofrange',
      afterToTranslationKey: 'fe.daterange.afterto',
      beforFromTranslationKey: 'fe.daterange.beforefrom'
    }
  };

  public symptomsMeasurementConfig: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    multiRow: true,
    key: DevelopmentEntryComponent.SYMPTOMS_MEASUREMENT_KEY,
    labelKey: DevelopmentEntryComponent.SYMPTOMS_MEASUREMENT_LABEL_KEY,
    valuesKey: DevelopmentEntryComponent.SYMPTOMS_VALUES_KEY,
    translationPrefix: 'fe.symptoms',
    secondaryTranslationPrefix: 'fe.painLevel',
    type: FormItemType.MEASUREMENT
  };

  public fieldStatusTestConfig: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: DevelopmentEntryComponent.FIELD_STATUS_TEST_KEY
  };

  public radioListWithExtras: RadioListWithExtraFieldsConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: DevelopmentEntryComponent.RADIO_LIST_WITH_DATE_KEY + 'Multiple',
    labelKey: DevelopmentEntryComponent.RADIO_LIST_WITH_DATE_LABEL_KEY,
    valuesKey: DevelopmentEntryComponent.RADIO_LIST_WITH_DATE_VALUES_KEY,
    type: FormItemType.RADIO_WITH_EXTRA_FIELDS,
    optionsWithExtraField: [
      { key: 'yes', type: 'DATE', allowFutureDate: false },
      { key: 'unknown', type: 'DATE', allowFutureDate: false },
      { key: 'no', type: 'TEXT', allowFutureDate: false },
      {
        key: 'list',
        type: 'DROPDOWN',
        allowFutureDate: false,
        valuesKey: DevelopmentEntryComponent.RADIO_LIST_WITH_DATE_VALUES_KEY,
        valuesTranslationPrefix: 'fe.yesNoUnknown',
        placeholderTranslationKey: 'fe.radiolistwithextradropdown.demo.placeholder'
      }
    ],
    translationPrefix: 'fe.yesNoUnknown',
    extraWrapperClasses: []
  };

  public standardRadioList: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: DevelopmentEntryComponent.RADIO_LIST,
    labelKey: DevelopmentEntryComponent.RADIO_LIST_WITH_DATE_LABEL_KEY,
    valuesKey: DevelopmentEntryComponent.RADIO_LIST_WITH_DATE_VALUES_KEY,
    type: FormItemType.RADIO,
    translationPrefix: 'fe.yesNoUnknown'
  };

  public checkboxListWithRadio: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: DevelopmentEntryComponent.CHECKBOX_LIST_WITH_RADIO_KEY,
    labelKey: DevelopmentEntryComponent.CHECKBOX_LIST_WITH_RADIO_LABEL_KEY,
    valuesKey: DevelopmentEntryComponent.CHECKBOX_LIST_WITH_RADIO_VALUES_KEY,
    type: FormItemType.CHECKBOX_WITH_EXTRA_FIELDS,
    translationPrefix: 'fe.race',
    radioOption: 'unknown',
    optionsWithExtraField: [{ key: 'asian', type: 'TEXT' }]
  };

  public disabledDateConfig: FormFieldConfig = {
    ...this.dateConfig,
    key: DevelopmentEntryComponent.DISABLED_DATE_KEY
  };

  public selectListWithRadio: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: DevelopmentEntryComponent.SELECT_LIST_WITH_RADIO_KEY,
    labelKey: DevelopmentEntryComponent.SELECT_LIST_WITH_RADIO_LABEL_KEY,
    valuesKey: DevelopmentEntryComponent.SELECT_LIST_WITH_RADIO_VALUES_KEY,
    type: FormItemType.SELECT_WITH_RADIO,
    translationPrefix: 'fe.numberOfTherapyLinesBeforeIo',
    radioOption: 'unknown'
  };

  public variableTestConfig: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: DevelopmentEntryComponent.VARIABLE_TEST_KEY,
    labelKey: DevelopmentEntryComponent.VARIABLE_TEST_KEY,
    type: FormItemType.QUESTION_GROUP
  };

  public eventModalEventType: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: 'doseEvent',
    labelKey: 'fe.doseEvent.label',
    valuesKey: 'doseEvent',
    translationPrefix: 'fe.doseEvent',
    type: FormItemType.RADIO
  };
  public eventModalEventAgent: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: 'doseAgent',
    labelKey: 'fe.doseAgent.label',
    valuesKey: 'doseAgent',
    translationPrefix: 'fe.doseAgent',
    type: FormItemType.RADIO
  };
  public eventModalEventDateStart: FormFieldConfig = {
    ...this.dateConfig,
    key: 'doseAgentDateStart'
  };
  public eventModalIndexCancerRelated: FormFieldConfig = {
    ...this.standardRadioList,
    key: 'wasRelatedToIndexCancer',
    type: FormItemType.RADIO,
    labelKey: 'fe.wasRelatedToIndexCancer.label'
  };
  public eventModalEventReason: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: 'doseAgentReason',
    type: FormItemType.CHECKBOX_WITH_EXTRA_FIELDS,
    labelKey: 'fe.doseAgentReason.label',
    valuesKey: 'doseAgentReason',
    translationPrefix: 'fe.doseAgentReason',
    radioOption: 'unknown',
    textOption: 'other',
    showCondition: {
      conditionType: ConditionType.FIELD,
      fieldConditionMatcher: FieldConditionMatcher.CONTAINS,
      fieldConditionType: FieldConditionType.REMOVE,
      fieldName: 'wasRelatedToIndexCancer',
      values: ['no']
    }
  };
  public eventModalConfig: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: this.EVENTS_SUBFORM_KEY,
    labelKey: this.EVENTS_SUBFORM_LABEL_KEY,
    type: FormItemType.EVENT_MODAL,
    children: [
      this.eventModalEventType,
      this.eventModalEventAgent,
      this.eventModalEventDateStart,
      this.eventModalIndexCancerRelated,
      this.eventModalEventReason
    ],
    eventConfig: {
      typeField: 'doseEvent'
    },
    enableFieldStatus: true
  };

  public eventModalConfigLimited: FormFieldConfig = {
    ...this.eventModalConfig,
    key: this.EVENTS_SUBFORM_KEY + 'Limited',
    eventConfig: {
      typeField: 'doseEvent',
      maxNumberOfEvents: 1
    }
  };

  public numericFieldConfig: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: 'numeric-field',
    type: FormItemType.NUMERIC_FIELD,
    enableFieldStatus: true,
    min: null,
    max: 100,
    unit: 'umol/L'
  };

  public horizontalRadioList: RadioListWithExtraFieldsConfig = {
    ...this.radioListWithExtras,
    key: 'horizontalRadio',
    valuesKey: 'yesNoUnknown',
    optionsWithExtraField: [
      {
        key: 'yes',
        type: 'NUMERIC_FIELD',
        unit: 'mm',
        min: 0,
        max: 1000
      }
    ],
    horizontal: true,
    extraWrapperClasses: []
  };

  public horizontalRadioList2: RadioListWithExtraFieldsConfig = {
    ...this.radioListWithExtras,
    key: 'horizontalRadio2',
    labelKey: DevelopmentEntryComponent.CHECKBOX_LIST_WITH_RADIO_LABEL_KEY,
    valuesKey: 'yesNoUnknown',
    optionsWithExtraField: [],
    horizontal: true,
    extraWrapperClasses: []
  };

  public horizontalRadioList3: RadioListWithExtraFieldsConfig = {
    ...this.radioListWithExtras,
    key: 'horizontalRadio3',
    labelKey: DevelopmentEntryComponent.CHECKBOX_LIST_WITH_RADIO_LABEL_KEY,
    valuesKey: 'yesNoUnknown',
    optionsWithExtraField: [
      {
        key: 'yes',
        type: 'DROPDOWN',
        allowFutureDate: false,
        valuesKey: DevelopmentEntryComponent.RADIO_LIST_WITH_DATE_VALUES_KEY,
        valuesTranslationPrefix: 'fe.yesNoUnknown',
        placeholderTranslationKey: 'fe.radiolistwithextradropdown.demo.placeholder'
      }
    ],
    horizontal: true,
    extraWrapperClasses: []
  };

  public dateWithFormatConfig: FormFieldConfig = {
    ...DevelopmentEntryComponent.BASE_FIELD_CONFIG,
    key: 'date',
    labelKey: DevelopmentEntryComponent.DATE_LABEL_KEY,
    subLabelKey: DevelopmentEntryComponent.DATE_SUB_LABEL_KEY,
    type: FormItemType.DATE,
    required: null,
    unknownDayDefault: UnknownDateDefault.LAST
  };

  public variableSub1Count = 0;
  public variableSub2Count = 0;

  public longDatePatterns = ['MM/DD/YYYY', 'DD.MM.YYYY', 'YYYY-MM-DD', 'DD/MM/YYYY'];

  handleDatePatternChange(pattern: string) {
    this.configService.longDatePattern = pattern;
  }

  ngOnInit(): void {
    const t: { [key: string]: string } = {
      documentedDiagnosisOfMelanomaSelectedYes: 'Yes, date of diagnosis:',
      documentedDiagnosisOfMelanomaSelectedNo: 'No documented diagnosis',
      documentedDiagnosisOfMelanoma: 'Melanom',

      'dateOfBirth.label': 'Date of birth',
      'dateOfBirth.sublabel': 'If the exact day is unknown, please enter the 15th as the day.',

      'fe.agentsUsed.label': 'Agents used in this line of therapy:',
      'fe.agentsUsed.subtitle.label': 'Please select all that apply.',
      'fe.agentsUsed.placeholder.label': 'Start typing an agent',
      'fe.agentsUsed.notFound.label': 'No result for "{search_term}" among the listed agents.',
      'fe.agentsUsed.notFoundDescription.label': 'Please revise your answer or add this agent as a free text answer.',
      'fe.agentsUsed.notFoundAction.label': 'Add "{search_term}" as an answer',
      'fe.agent.afatinib.label': 'Afatinib (Gilotrif)',
      'fe.agent.pactilaxel.label': 'Albumin-bound pactilaxel (nab-pactilaxel; Abraxane)',
      'fe.agent.atezolizumab.label': 'Atezolizumab (Tecentriq)',
      'fe.agent.nivolumab.label': 'Nivolumab (Opdivo)',
      'fe.agent.carboplatin.label': 'Carboplatin',

      'fe.phq.label': 'PHQ-2 baseline measurement ',
      'fe.phq.description.label':
        'Please enter all the PHQ-2 measurements measured during this time period.\n' +
        'Please find this clinician-documented information in the EMR data. If the exact day is unknown, please enter the 15th as the day.',
      'fe.phq.metricHeader.label': 'Metric',
      'fe.phq.dateHeader.label': 'Date',
      'fe.phq.measurementHeader.label': 'Measurement',
      'fe.phq.measurementPlaceholder.label': 'Select a value',
      'fe.phq.addMore.label': 'Add measurement',
      'fe.phq.radioYes.label': 'Documented measurements:',
      'fe.phq.radioNo.label': 'Not documented / Unknown',
      'fe.phq.rowRemove.label': 'Remove measurement',
      'fe.phq.phq.label': 'PHQ-2',
      'fe.zeroToSixScale.0.label': '0',
      'fe.zeroToSixScale.1.label': '1',
      'fe.zeroToSixScale.2.label': '2',
      'fe.zeroToSixScale.3.label': '3',
      'fe.zeroToSixScale.4.label': '4',
      'fe.zeroToSixScale.5.label': '5',
      'fe.zeroToSixScale.6.label': '6',

      'fe.symptoms.label': 'Symptoms baseline measurement',
      'fe.symptoms.description.label':
        'Measurement closest to I-O therapy initiation on or within 3 months before {date_end}. Please find this clinician-documented information in the EMR data. If the exact day is unknown, please enter the 15th as the day.',
      'fe.symptoms.metricHeader.label': 'Symptom',
      'fe.symptoms.metricPlaceholder.label': 'Select a symptom',
      'fe.symptoms.dateHeader.label': 'Date',
      'fe.symptoms.measurementHeader.label': 'Severity',
      'fe.symptoms.measurementPlaceholder.label': 'Select a value',
      'fe.symptoms.addMore.label': 'Add symptom',
      'fe.symptoms.radioYes.label': 'Documented measurements:',
      'fe.symptoms.radioNo.label': 'Not documented / Unknown',
      'fe.symptoms.rowRemove.label': 'Remove symptom',
      'fe.symptoms.diarrhea.label': 'Diarrhea',
      'fe.symptoms.pain.label': 'Pain',
      'fe.symptoms.fever.label': 'Fever',
      'fe.symptoms.swelling.label': 'Swelling',
      'fe.painLevel.mild.label': 'Mild',
      'fe.painLevel.moderate.label': 'Moderate',
      'fe.painLevel.severe.label': 'Severe',

      'formstatus.test.softerror': 'This value might be correct, please confirm!',
      'formstatus.test.harderror': 'This value is invalid!',
      'formstatus.test.notready': 'This question can not be filed, yet!',
      'formstatus.test.terminationerror': 'Submitting this value will have a side effect!',

      'fe.wasPatientPregnant.label': 'Was the patient pregnant between {date_start} and {date_end}?',
      'fe.wasPatientPregnant.description.label': 'If the exact day is unknown, please enter the 15th as the day',
      'fe.yesNoUnknown.yes.label': 'Yes',
      'fe.yesNoUnknown.no.label': 'No',
      'fe.yesNoUnknown.unknown.label': 'Not documented / unknown',
      'fe.yesNoUnknown.list.label': 'List demo',

      'fe.race.label': 'Race',
      'fe.race.description.label': 'Please select all that apply.',
      'fe.race.americanIndianOrAlaskaNative.label': 'American Indian or Alaska Native',
      'fe.race.asian.label': 'Asian',
      'fe.race.blackOrAfricanAmerican.label': 'Black or African American',
      'fe.race.nativeHawaiianOrOtherPacificIslander.label': 'Native Hawaiian or Other Pacific Islander',
      'fe.race.white.label': 'White',
      'fe.race.unknown.label': 'Not documented / unknown',

      'fe.numberOfTherapyLinesBeforeIo.label':
        'Number of lines of prior therapy for {index_cancer} before I-O therapy initiation on {index_date}',
      'fe.numberOfTherapyLinesBeforeIo.description.label':
        'Please include any prior lines of therapy that occurred at TN Oncology or another facility for {index_cancer}.',
      'fe.numberOfTherapyLinesBeforeIo.placeholder.label': 'Select number of lines',
      'fe.numberOfTherapyLinesBeforeIo.0.label': '0',
      'fe.numberOfTherapyLinesBeforeIo.1.label': '1',
      'fe.numberOfTherapyLinesBeforeIo.2.label': '2',
      'fe.numberOfTherapyLinesBeforeIo.3.label': '3',
      'fe.numberOfTherapyLinesBeforeIo.4.label': '4',
      'fe.numberOfTherapyLinesBeforeIo.5.label': '5',
      'fe.numberOfTherapyLinesBeforeIo.6.label': '6',
      'fe.numberOfTherapyLinesBeforeIo.7.label': '7',
      'fe.numberOfTherapyLinesBeforeIo.8.label': '8',
      'fe.numberOfTherapyLinesBeforeIo.9.label': '9',
      'fe.numberOfTherapyLinesBeforeIo.10.label': '10',
      'fe.numberOfTherapyLinesBeforeIo.unknown.label': 'Not documented / unknown',

      'fe.error.generic.missingFieldOrQuestion': 'All fields need to be answered before submission.',
      'fe.error.generic.invalidFieldOrQuestion': 'Please fix the invalid value.',

      variableTestKey: 'Variable value: {some_variable}',
      'variable.translation.myvalue': 'My Value',

      'fe.error.generic.unkownDayDate': 'I confirm the exact date is unknown.',

      'fe.inpatientAdmissions.label': 'Inpatient admissions during the time from 09/15/2019 to 03/14/2020',
      'fe.inpatientAdmissions.description.label': 'If the exact day is unknown, please enter the 15th as the day.\n',
      'fe.inpatientAdmissions.addEventButton.label': 'Add an Inpatient Admission',
      'fe.inpatientAdmissions.modalSaveButton.label': 'Save and Close',
      'fe.inpatientAdmissions.modalAddAnotherButton.label': 'Add Another',
      'fe.inpatientAdmissions.modalCancelButton.label': 'Cancel',
      'fe.inpatientAdmissions.radioNo.label': 'No records of inpatient admissions',
      'fe.inpatientAdmissions.radioYes.label': 'Inpatient admissions:',
      inpatientAdmissionsType: 'Inpatient admission',
      inpatientAdmissionsDetailsHeader: 'Bladder cancer related?',
      inpatientAdmissionsReasonHeader: 'Reason for admission',
      'fe.doseEvent.label': 'Event type',
      'fe.doseEvent.doseModification.label': 'Dose modification',
      'fe.doseEvent.doseInterruption.label': 'Dose interruption',
      'fe.doseAgent.label': 'Agent',
      'fe.doseAgent.nivolumab.label': 'Nivolumab (Opdivo)',
      'fe.doseAgent.albumin.label': 'Albumin something something',
      'fe.doseAgent.lorem.label': 'Lorem ipsum',
      'fe.doseAgentReason.label': 'Reason for dose event',
      'fe.doseAgentReason.severeAdverse.label': 'Severe adverse event',
      'fe.doseAgentReason.nonSevereAdverse.label': 'Non-severe adverse event',
      'fe.doseAgentReason.patientRequest.label': 'Patient request',
      'fe.doseAgentReason.other.label': 'Other, please specify',
      'fe.doseAgentReason.unknown.label': 'Unknown',
      'fe.inpatientAdmissions.removeEvent.label': 'Remove inpatient admission',
      'fe.inpatientAdmissions.dateHeader.label': 'Dates',
      'fe.inpatientAdmissions.detailsHeader.label': 'Details',
      'fe.inpatientAdmissions.eventTypeHeader.label': 'Visit type',

      'fe.radiolistwithextradropdown.demo.placeholder': 'This a demo placeholder',
      'fe.wasRelatedToIndexCancer.label': 'Was related to {some_variable}?',
      'fe.daterange.outofrange': 'Out for range {from} - {to}',
      'fe.daterange.afterto': 'This is after {to}',
      'fe.daterange.beforefrom': 'This is before {from}',
      'fe.numericField.outOfRange': 'Value must be between {min} and {max}',
      'fe.numericField.minViolation': 'Value must be greater than or equal to {min}',
      'fe.numericField.maxViolation': 'Value must be less than or equal to {max}'
    };

    this.localeService.setTranslations(t);

    this.store.dispatch(new StartNewForm({ type: DevelopmentEntryComponent.FORM_TYPE, totalSections: 1 }));

    // ----------------------------------------------------
    // ---- Radio With Extras: Date and Text --------------
    // ----------------------------------------------------

    const dateFieldConfig = {
      ...this.radioListWithExtras,
      type: FormItemType.DATE,
      key: this.radioListWithExtras.key + RadioListWithExtraFieldsComponent.DATE_FIELD_CONFIG_POSTFIX
    };
    const textFieldConfig = {
      ...this.radioListWithExtras,
      type: FormItemType.TEXT,
      key: this.radioListWithExtras.key + RadioListWithExtraFieldsComponent.TEXT_FIELD_CONFIG_POSTFIX
    };

    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(dateFieldConfig, moment(), true)));
    this.store.dispatch(new AddAnswer(this.fieldService.getAnswer(this.radioListWithExtras, 'unknown', true)));

    // ----------------------------------------------------
    // ---------------- Field Status ----------------------
    // ----------------------------------------------------

    this.store.dispatch(
      new AddOrUpdateFieldStatus({
        formType: DevelopmentEntryComponent.FORM_TYPE,
        fieldKey: this.disabledDateConfig.key,
        translationKey: 'formstatus.test.notready',
        statusType: FieldStatusType.NOT_READY
      })
    );

    this.store.dispatch(
      new AddOrUpdateFieldStatus({
        formType: DevelopmentEntryComponent.FORM_TYPE,
        fieldKey: this.fieldStatusTestConfig.key,
        translationKey: 'formstatus.test.harderror',
        statusType: FieldStatusType.HARD_ERROR
      })
    );

    this.store.dispatch(
      new AddOrUpdateFieldStatus({
        formType: DevelopmentEntryComponent.FORM_TYPE,
        fieldKey: this.fieldStatusTestConfig.key,
        translationKey: 'formstatus.test.softerror',
        accepted: true,
        lastModified: new Date(),
        userId: '1234',
        userName: 'Peter Tyler',
        statusType: FieldStatusType.SOFT_ERROR
      })
    );

    this.store.dispatch(
      new AddOrUpdateFieldStatus({
        formType: DevelopmentEntryComponent.FORM_TYPE,
        fieldKey: this.fieldStatusTestConfig.key,
        translationKey: 'formstatus.test.terminationerror',
        statusType: FieldStatusType.TERMINATION_ERROR
      })
    );

    this.store.dispatch(
      new AddOrUpdateFieldStatus({
        formType: DevelopmentEntryComponent.FORM_TYPE,
        fieldKey: this.fieldStatusTestConfig.key,
        translationKey: 'formstatus.test.notready',
        statusType: FieldStatusType.NOT_READY
      })
    );

    // ----------------------------------------------------
    // ---------------- Variable Handling -----------------
    // ----------------------------------------------------

    const TEST_VAR_KEY = 'some_variable';
    const TEST_VAR_KEY2 = 'some_variable_2';
    const TEST_VAR_KEY3 = 'abstractionStart';
    const TEST_VAR_KEY4 = 'abstractionEnd';

    const d1 = moment().add(-2, 'month');
    const d2 = moment().add(2, 'month');

    this.store.dispatch(
      new ResetFormVariables([
        {
          id: TEST_VAR_KEY,
          key: TEST_VAR_KEY,
          type: FormVariableType.ENUMERATION,
          value: 'myvalue',
          meta: 'variable.translation.{0}',
          modified: new Date()
        },
        {
          id: TEST_VAR_KEY2,
          key: TEST_VAR_KEY2,
          type: FormVariableType.ENUMERATION,
          value: 'myvalue',
          meta: 'longDatePattern',
          modified: new Date()
        },
        {
          id: TEST_VAR_KEY3,
          key: TEST_VAR_KEY3,
          type: FormVariableType.DATE,
          value: d1.toDate(),
          meta: 'variable.translation.{0}',
          modified: new Date(),
          transient: true
        },
        {
          id: TEST_VAR_KEY4,
          key: TEST_VAR_KEY4,
          type: FormVariableType.DATE,
          value: d2.toDate(),
          meta: 'longDatePattern',
          modified: new Date(),
          transient: true
        }
      ])
    );

    this.store.pipe(select(selectFormVariable(TEST_VAR_KEY))).subscribe(_ => {
      this.variableSub1Count += 1;
    });

    this.store.pipe(select(selectFormVariable(TEST_VAR_KEY2))).subscribe(_ => {
      this.variableSub2Count += 1;
    });

    this.store.dispatch(
      new UpdateFormVariable({
        id: TEST_VAR_KEY,
        changes: {
          value: 'mynewvalue'
        }
      })
    );

    this.store.dispatch(
      new UpdateFormVariable({
        id: TEST_VAR_KEY,
        changes: {
          value: 'myvalue'
        }
      })
    );

    this.store.dispatch(
      new AddAnswer(
        this.fieldService.getAnswer(
          {
            ...this.multiSelectConfig,
            key: this.multiSelectConfig.key + MultiselectComponent.ADHOC_FIELD_POSTFIX
          },
          ['For the Horde!!'],
          true
        )
      )
    );

    this.store.dispatch(
      new AddOrUpdateFieldStatus({
        formType: DevelopmentEntryComponent.FORM_TYPE,
        fieldKey: this.horizontalRadioList.key,
        translationKey: 'formstatus.test.terminationerror',
        statusType: FieldStatusType.TERMINATION_ERROR
      })
    );

    this.store.dispatch(
      new AddOrUpdateFieldStatus({
        formType: DevelopmentEntryComponent.FORM_TYPE,
        fieldKey: this.radioListWithExtras.key,
        translationKey: 'formstatus.test.terminationerror',
        statusType: FieldStatusType.TERMINATION_ERROR
      })
    );
  }
}
