<div class="development-form container">
    <div class="demo-component">
        <div class="component-title">
            <h5>Measurement Component: Single measurement</h5>
        </div>
        <nh-measurement [config]="phqMeasurementConfig" [visible]="true"></nh-measurement>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Radio With Extras: Date and Text</h5>
        </div>
        <nh-radio-list-with-extra-fields
            [config]="radioListWithExtras"
            [visible]="true"
        ></nh-radio-list-with-extra-fields>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Horizontal radio list</h5>
        </div>
        <nh-radio-list-with-extra-fields
            [config]="horizontalRadioList"
            [visible]="true"
        ></nh-radio-list-with-extra-fields>
        <nh-radio-list-with-extra-fields
            [config]="horizontalRadioList2"
            [visible]="true"
        ></nh-radio-list-with-extra-fields>
        <nh-radio-list-with-extra-fields
            [config]="horizontalRadioList3"
            [visible]="true"
        ></nh-radio-list-with-extra-fields>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Checkbox List With Extra Fields</h5>
        </div>
        <nh-checkbox-list-with-extra-fields
            [config]="checkboxListWithRadio"
            [visible]="true"
        ></nh-checkbox-list-with-extra-fields>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Standard Radio List</h5>
        </div>
        <nh-radio-list [config]="standardRadioList" [visible]="true"></nh-radio-list>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Multiselect List</h5>
        </div>
        <nh-multiselect [config]="multiSelectConfig" [visible]="true"></nh-multiselect>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Dropdown With Radio</h5>
        </div>
        <nh-select-with-radio [config]="selectListWithRadio" [visible]="true"></nh-select-with-radio>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Field Status Indicator Versions</h5>
        </div>
        <br />
        <nh-field-status-indicator [config]="fieldStatusTestConfig"> </nh-field-status-indicator>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Disabled Date Component</h5>
        </div>
        <nh-date-component [config]="disabledDateConfig" [visible]="true"></nh-date-component>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Variable Handling Test</h5>
        </div>
        <nh-question-group [config]="variableTestConfig" [visible]="true"></nh-question-group>
        <div>Variable Subscription 1 Count: {{ variableSub1Count }} Expected: 3</div>
        <div>Variable Subscription 2 Count: {{ variableSub2Count }} Expected: 1</div>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Event Modal</h5>
        </div>
        <nh-event-modal [config]="eventModalConfig" [visible]="true"></nh-event-modal>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Event Modal</h5>
        </div>
        <nh-event-modal [config]="eventModalConfigLimited" [visible]="true"></nh-event-modal>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Measurement Component: Multi measurement</h5>
        </div>
        <nh-measurement [config]="symptomsMeasurementConfig" [visible]="true"> </nh-measurement>
    </div>

    <div class="demo-component">
        <div class="component-title">
            <h5>Numeric Field</h5>
        </div>
        <nh-numeric-field [config]="numericFieldConfig" [visible]="true"></nh-numeric-field>
    </div>

    <div class="demo-component">
        <ng-select
            class="date-formatting-pattern"
            placeholder="Choose a date formatting pattern"
            (change)="handleDatePatternChange($event)"
            [items]="longDatePatterns"
            [clearable]="false"
        ></ng-select>
        <p>Note: Because of change detection, the new date format will show after you focus a date field</p>
        <div class="component-title mt-m">
            <h5>Date Field</h5>
        </div>
        <nh-date-component [config]="dateWithFormatConfig" [visible]="true"></nh-date-component>
    </div>
</div>
