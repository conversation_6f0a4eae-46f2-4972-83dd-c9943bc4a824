import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StoreModule } from '@ngrx/store';
import { RouterModule } from '@angular/router';
import { DemoRoutes } from './demo-routing';
import { NoonaFormEngineModule } from '../../projects/shared-core/src/lib/form-engine/noona-form-engine.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DevelopmentEntryComponent } from './development-entry/development-entry.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { CheckboxListDemoComponent } from './checkbox-list-demo/checkbox-list-demo.component';

@NgModule({
    declarations: [DevelopmentEntryComponent, CheckboxListDemoComponent],
    imports: [CommonModule, RouterModule.forChild(DemoRoutes), NoonaFormEngineModule, NgSelectModule],
    providers: [],
})
export class DemoModule {}
