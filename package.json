{"name": "@varian-noona/noona-library", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "jest", "test:coverage": "jest --collectCoverage", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lint": "ng lint", "format": "prettier ./projects/shared-core/ --write", "lint:shared-core": "ng lint shared-core", "build:shared-core": "ng build shared-core --configuration production && cp -r projects/shared-core/src/styles dist/shared-core/styles && cp -r projects/shared-core/src/styles/deprecated_mixins dist/shared-core/styles/deprecated_mixins && cp -r projects/shared-core/src/styles/deprecated_variables dist/shared-core/styles/deprecated_variables && cp -r projects/shared-core/src/styles/vendor dist/shared-core/styles/vendor", "build:shared-core:watch": "npx ng build shared-core --watch", "bump:patch": "node versioning.js --bump=patch --paths='./projects/shared-core' -v", "bump:minor": "node versioning.js --bump=minor --paths='./projects/shared-core' -v", "bump:major": "node versioning.js --bump=major --paths='./projects/shared-core' -v", "versioner": "node versioning.js --paths='./projects/shared-core' -v", "bump-version:shared-core": "cd projects/shared-core && version-from-git --template short --no-git-tag-version", "print-version:shared-core": "node -p \"require('./projects/shared-core/package.json').version\"", "lint-staged": "lint-staged", "prepare": "node .husky/install.mjs"}, "dependencies": {"@angular/animations": "^18.2.13", "@angular/common": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/language-service": "^18.2.13", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@angular-slider/ngx-slider": "^18.0.0", "@bwip-js/browser": "^4.5.1", "@ng-select/ng-select": "^13.9.1", "@ngrx/effects": "^18.1.1", "@ngrx/entity": "^18.1.1", "@ngrx/router-store": "^18.1.1", "@ngrx/store": "^18.1.1", "@ngrx/store-devtools": "^18.1.1", "angular-mydatepicker": "^0.11.2", "apply-loader": "^2.0.0", "core-js": "^3.6.5", "css-vars-ponyfill": "^2.0.2", "jquery": "^3.4.1", "lodash": "^4.17.21", "minimist": "^1.2.0", "moment": "^2.24.0", "rxjs": "^7.8.0", "tinygradient": "^1.0.0", "tslib": "^2.0.0", "uuid": "^8.3.2", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.0", "@angular-eslint/builder": "^18.2.0", "@angular-eslint/eslint-plugin": "^18.2.0", "@angular-eslint/eslint-plugin-template": "^18.2.0", "@angular-eslint/schematics": "^18.2.0", "@angular-eslint/template-parser": "^18.2.0", "@angular/cli": "^18.2.12", "@angular/compiler": "^18.2.13", "@angular/compiler-cli": "^18.2.13", "@types/bootstrap-datepicker": "^0.0.14", "@types/bwip-js": "^3.2.3", "@types/jest": "^29.5.13", "@types/jquery": "^3.3.29", "@types/lodash": "^4.17.7", "@types/node": "^13.13.2", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@typescript-eslint/utils": "^7.2.0", "eslint": "^8.56.0", "eslint-plugin-import": "latest", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jsdoc": "latest", "eslint-plugin-prefer-arrow": "latest", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "latest", "husky": "^9.1.7", "inquirer": "^7.0.1", "jest": "^29.7.0", "jest-preset-angular": "^14.2.4", "lint-staged": "^15.2.10", "ng-mocks": "^14.4.0", "ng-packagr": "^18.2.1", "prettier": "^2.8.0", "semver": "^7.1.1", "stylelint": "^14.15.0", "stylelint-config-prettier": "^9.0.4", "stylelint-config-standard": "^29.0.0", "stylelint-prettier": "^2.0.0", "ts-node": "~8.9.0", "typescript": "~5.4.5", "url-join": "^5.0.0", "version-from-git": "^1.1.2"}, "publishConfig": {"@varian-noona:registry": "https://gitlab.com/api/v4/projects/16085590/packages/npm/"}, "lint-staged": {"!(*.ts)": "prettier --write", "*.ts": ["eslint --fix", "prettier --write"]}}